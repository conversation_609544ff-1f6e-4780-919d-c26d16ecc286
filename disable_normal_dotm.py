#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
�����T�� Normal.dotm ���}��
"""

import os
import sys
import shutil
import logging
from pathlib import Path

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def disable_normal_dotm():
    """�����T�� Normal.dotm"""
    
    logger.info("�}�l�T�� Normal.dotm...")
    
    try:
        # 1. ��� Normal.dotm ����m
        templates_path = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Templates"
        normal_dotm_path = templates_path / "Normal.dotm"
        
        logger.info(f"�ҪO���|: {templates_path}")
        logger.info(f"Normal.dotm ���|: {normal_dotm_path}")
        
        # 2. �T�O�ҪO�ؿ��s�b
        templates_path.mkdir(parents=True, exist_ok=True)
        
        # 3. �p�G Normal.dotm �s�b�A�ƥ��çR��
        if normal_dotm_path.exists():
            backup_path = normal_dotm_path.with_suffix('.dotm.backup')
            logger.info(f"�ƥ� Normal.dotm ��: {backup_path}")
            shutil.copy2(normal_dotm_path, backup_path)
            
            logger.info("�R�� Normal.dotm")
            normal_dotm_path.unlink()
        
        # 4. �Ыؤ@�ӪŪ� Normal.dotm ���ó]���uŪ
        logger.info("�ЫتŪ��uŪ Normal.dotm")
        normal_dotm_path.touch()
        
        # �]���uŪ
        import stat
        normal_dotm_path.chmod(stat.S_IREAD)
        
        logger.info("? Normal.dotm �w�Q�T��")
        return True
        
    except Exception as e:
        logger.error(f"�T�� Normal.dotm ����: {str(e)}")
        return False

def create_word_startup_template():
    """�Ы� Word �ҰʼҪO"""
    
    try:
        # Word �Ұʥؿ�
        startup_path = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Word" / "STARTUP"
        startup_path.mkdir(parents=True, exist_ok=True)
        
        # �Ыؤ@�ӸT�� Normal.dotm ���ҰʼҪO
        startup_template = startup_path / "DisableNormalDotm.dotm"
        
        if not startup_template.exists():
            logger.info(f"�ЫرҰʼҪO: {startup_template}")
            startup_template.touch()
            
        logger.info("? �ҰʼҪO�w�Ы�")
        return True
        
    except Exception as e:
        logger.error(f"�ЫرҰʼҪO����: {str(e)}")
        return False

def test_word_without_normal_dotm():
    """���� Word �O�_�ॿ�`�u�@"""
    
    logger.info("���� Word �\��...")
    
    try:
        import pythoncom
        import win32com.client
        
        # ��l�� COM
        pythoncom.CoInitialize()
        
        # �Ы� Word ���ε{��
        logger.info("�Ы� Word ���ε{��...")
        word_app = win32com.client.DispatchEx("Word.Application")
        
        # �򥻳]�m
        word_app.Visible = False
        word_app.DisplayAlerts = 0
        
        # �]�m Word ���ϥ� Normal.dotm
        try:
            # �]�m Word �ϥΪťռҪO
            word_app.Options.DoNotPromptForConvert = True
            word_app.Options.ConfirmConversions = False
            word_app.Options.UpdateLinksAtOpen = False
            
            # �T�μҪO�����\��
            word_app.Options.CheckGrammarAsYouType = False
            word_app.Options.CheckSpellingAsYouType = False
            
            # �]�m�۰ʤƦw���ŧO
            word_app.AutomationSecurity = 3
            
            logger.info("Word �ﶵ�]�m����")
        except Exception as e:
            logger.warning(f"�]�m Word �ﶵ�ɥX��: {str(e)}")
        
        # ���ճЫؤ���
        logger.info("���ճЫؤ���...")
        doc = word_app.Documents.Add()
        
        # �K�[�@�Ǥ��e
        doc.Content.Text = "Test document without Normal.dotm"
        
        # ��������
        doc.Close(SaveChanges=False)
        
        # ���� Word
        word_app.Quit()
        
        logger.info("? Word ���զ��\ - �S���ϥ� Normal.dotm")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "Normal.dotm" in error_msg:
            logger.error(f"? ���M�X�{ Normal.dotm ���~: {error_msg}")
        else:
            logger.error(f"? Word ���ե���: {error_msg}")
        return False
    
    finally:
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def main():
    """�D���"""
    
    logger.info("=== �����T�� Normal.dotm �u�� ===")
    
    # �T�� Normal.dotm
    disable_success = disable_normal_dotm()
    
    if disable_success:
        # �ЫرҰʼҪO
        template_success = create_word_startup_template()
        
        # ���� Word �\��
        logger.info("\n=== ���� Word �\�� ===")
        test_success = test_word_without_normal_dotm()
        
        if test_success:
            logger.info("\n? Normal.dotm �w���\�T�ΡI")
            logger.info("��ĳ�G")
            logger.info("1. ���s�Ұʱz�����ε{��")
            logger.info("2. ���դU���\��")
            logger.info("3. �p�G�ݭn��_�A�N Normal.dotm.backup ���R�W�� Normal.dotm")
            return True
        else:
            logger.error("\n?? Normal.dotm �T�Φ��\�A�� Word ���ե���")
            return False
    else:
        logger.error("\n? �T�� Normal.dotm ����")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("�ާ@�Q�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"�o�ͥ��w�������~: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
