#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
���� Normal.dotm ���~�״_
"""

import os
import sys
import logging
import time

# �K�[���ظ��|
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_word_creation_methods():
    """���դ��P�� Word �Ыؤ�k"""
    
    methods = [
        ("DispatchEx", "win32com.client.DispatchEx"),
        ("Dispatch", "win32com.client.Dispatch"),
        ("gencache.EnsureDispatch", "win32com.client.gencache.EnsureDispatch")
    ]
    
    results = {}
    
    for method_name, method_code in methods:
        logger.info(f"���� {method_name} ��k...")
        
        try:
            import pythoncom
            import win32com.client
            
            # ��l�� COM
            pythoncom.CoInitialize()
            
            # �Ы� Word ���ε{��
            if method_name == "DispatchEx":
                word_app = win32com.client.DispatchEx("Word.Application")
            elif method_name == "Dispatch":
                word_app = win32com.client.Dispatch("Word.Application")
            elif method_name == "gencache.EnsureDispatch":
                word_app = win32com.client.gencache.EnsureDispatch("Word.Application")
            
            # �]�m�򥻿ﶵ
            word_app.Visible = False
            word_app.DisplayAlerts = 0
            
            # ���� Normal.dotm ���~���]�m
            try:
                word_app.Options.DoNotPromptForConvert = True
                word_app.Options.ConfirmConversions = False
                word_app.Options.UpdateLinksAtOpen = False
                word_app.Options.CheckGrammarAsYouType = False
                word_app.Options.CheckSpellingAsYouType = False
                word_app.Options.AutoRecover = False
                word_app.AutomationSecurity = 3
            except Exception as e:
                logger.warning(f"{method_name}: �]�m�ﶵ�ɥX��: {str(e)}")
            
            # ���ճЫطs����
            doc = word_app.Documents.Add()
            doc.Close(SaveChanges=False)
            
            # ���� Word
            word_app.Quit()
            
            results[method_name] = "���\"
            logger.info(f"? {method_name} ���զ��\")
            
        except Exception as e:
            error_msg = str(e)
            results[method_name] = f"����: {error_msg}"
            
            if "Normal.dotm" in error_msg:
                logger.error(f"? {method_name} �X�{ Normal.dotm ���~: {error_msg}")
            else:
                logger.error(f"? {method_name} ���ե���: {error_msg}")
        
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass
        
        # ���ݤ@�U�A���դU�@�Ӥ�k
        time.sleep(2)
    
    return results

def test_word_processors():
    """���զU�� Word �B�z��"""
    
    processors = [
        ("word_com_initializer", "apps.documents.word_com_initializer"),
        ("word_com_safe_processor", "apps.documents.word_com_safe_processor"),
        ("word_queue_processor", "apps.documents.word_queue_processor")
    ]
    
    results = {}
    
    for processor_name, module_path in processors:
        logger.info(f"���� {processor_name}...")
        
        try:
            if processor_name == "word_com_initializer":
                from apps.documents.word_com_initializer import create_word_app_safe
                word_app = create_word_app_safe()
                if word_app:
                    word_app.Quit()
                    results[processor_name] = "���\"
                    logger.info(f"? {processor_name} ���զ��\")
                else:
                    results[processor_name] = "����: ��^ None"
                    logger.error(f"? {processor_name} ��^ None")
            
            elif processor_name == "word_com_safe_processor":
                from apps.documents.word_com_safe_processor import get_com_safe_processor
                processor = get_com_safe_processor()
                if processor:
                    results[processor_name] = "���\"
                    logger.info(f"? {processor_name} ���զ��\")
                else:
                    results[processor_name] = "����: �L�k����B�z��"
                    logger.error(f"? {processor_name} �L�k����B�z��")
            
            elif processor_name == "word_queue_processor":
                from apps.documents.word_queue_processor import WordQueueProcessor
                processor = WordQueueProcessor()
                if processor:
                    results[processor_name] = "���\"
                    logger.info(f"? {processor_name} ���զ��\")
                else:
                    results[processor_name] = "����: �L�k�ЫسB�z��"
                    logger.error(f"? {processor_name} �L�k�ЫسB�z��")
        
        except Exception as e:
            error_msg = str(e)
            results[processor_name] = f"����: {error_msg}"
            
            if "Normal.dotm" in error_msg:
                logger.error(f"? {processor_name} �X�{ Normal.dotm ���~: {error_msg}")
            else:
                logger.error(f"? {processor_name} ���ե���: {error_msg}")
        
        time.sleep(1)
    
    return results

def main():
    """�D���ը��"""
    logger.info("�}�l���� Normal.dotm ���~�״_...")
    
    # ���� Word �Ыؤ�k
    logger.info("\n=== ���� Word �Ыؤ�k ===")
    creation_results = test_word_creation_methods()
    
    # ���� Word �B�z��
    logger.info("\n=== ���� Word �B�z�� ===")
    processor_results = test_word_processors()
    
    # ��X���յ��G
    logger.info("\n=== ���յ��G�K�n ===")
    
    logger.info("\nWord �Ыؤ�k���յ��G:")
    for method, result in creation_results.items():
        logger.info(f"  {method}: {result}")
    
    logger.info("\nWord �B�z�����յ��G:")
    for processor, result in processor_results.items():
        logger.info(f"  {processor}: {result}")
    
    # �ˬd�O�_�� Normal.dotm ���~
    normal_dotm_errors = []
    for method, result in creation_results.items():
        if "Normal.dotm" in result:
            normal_dotm_errors.append(f"{method}: {result}")
    
    for processor, result in processor_results.items():
        if "Normal.dotm" in result:
            normal_dotm_errors.append(f"{processor}: {result}")
    
    if normal_dotm_errors:
        logger.error("\n??  �o�{ Normal.dotm ���~:")
        for error in normal_dotm_errors:
            logger.error(f"  - {error}")
        logger.error("\n��ĳ����״_�}��: fix_word_environment.bat")
        return False
    else:
        logger.info("\n? �Ҧ����ճ��q�L�A�S���o�{ Normal.dotm ���~�I")
        return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("���ճQ�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"���չL�{���o�ͥ��w�������~: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
