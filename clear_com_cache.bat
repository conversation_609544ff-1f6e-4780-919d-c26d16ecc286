@echo off
chcp 65001 >nul 2>&1
echo === Clearing win32com cache ===

echo Stopping any running Word processes...
taskkill /f /im WINWORD.EXE >nul 2>&1

echo Clearing temporary gen_py directories...
if exist "%TEMP%\gen_py" (
    echo Found temp gen_py directory: %TEMP%\gen_py
    rmdir /s /q "%TEMP%\gen_py"
    echo Deleted temp gen_py directory
)

echo Clearing user-specific cache...
for /d %%i in ("%USERPROFILE%\AppData\Local\Temp\gen_py*") do (
    echo Found user cache: %%i
    rmdir /s /q "%%i"
    echo Deleted user cache
)

echo Rebuilding COM cache using Python...
python -c "import win32com.client; win32com.client.gencache.is_readonly = False; win32com.client.gencache.Rebuild(); print('COM cache rebuilt successfully')"

echo === Cache clearing completed ===
echo Please restart your application and test the download function.
pause
