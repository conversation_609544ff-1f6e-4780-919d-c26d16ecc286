import os
from dotenv import load_dotenv

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-svg^+r%wy%*laq@9-5jw9#fjwjao+k(@)o@y!6wp7x*%7vu9ts'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True
# 郵件是否寄給真實收件者
SEND_EMAIL_TO_REAL_RECIPIENT = False

# 載入環境變數
if not DEBUG:
    load_dotenv(os.path.join(BASE_DIR, 'env', 'production.env'))
    strCORS_URL = 'https://' + os.getenv('PUBLIC_URL') + ':' + os.getenv('SERVER_PORT')
    WEB_URL = 'https://' + os.getenv('PUBLIC_URL') + ':' + os.getenv('WEB_PORT')
else:
    load_dotenv(os.path.join(BASE_DIR, 'env', 'development.env'))
    strCORS_URL = 'http://' + os.getenv('HOST_IP2') + ':' + os.getenv('SERVER_PORT')
    WEB_URL = 'http://' + os.getenv('HOST_IP2') + ':' + os.getenv('WEB_PORT')

strPrivateIP1 = os.getenv('HOST_IP1')

strPrivateIP2 = os.getenv('HOST_IP2')

strPublicIP1 = os.getenv('PUBLIC_IP')

strPublicUrl = os.getenv('PUBLIC_URL')

strApiPublicUrl = os.getenv('API_PUBLIC_URL')

strPort = os.getenv('SERVER_PORT')

ALLOWED_HOSTS = [strPrivateIP1, strPrivateIP2, strPublicIP1, strPublicUrl, strApiPublicUrl, 'b2bapi.heysong.dev']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'apps.users',
    'apps.accounts',
    'apps.products',
    'apps.payments',
    'apps.dealers',
    'apps.allowances',
    'apps.orders',
    'apps.invoices',
    'apps.documents',
    'apps.pallet',
    'apps.blog',
    'django_celery_results',
    'corsheaders',
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'apps.throttles.BlockIPMiddleware',
    # 'apps.documents.cors_middleware.EnsureCORSMiddleware',  # 暫時停用自定義 CORS 中間件
]

CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',  # Redis伺服器地址和使用的數據庫
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
    }
}

CORS_ORIGIN_ALLOW_ALL = False
# COOKIE
CORS_ALLOW_CREDENTIALS = True

CORS_ALLOWED_ORIGINS = [
    strCORS_URL
] # If this is used, then not need to use `CORS_ALLOW_ALL_ORIGINS = True`
CORS_ALLOWED_ORIGIN_REGEXES = [
    strCORS_URL
]
CORS_ALLOW_METHODS = [
    'GET',
    'POST',
    'DELETE',
    'OPTIONS',
]

CORS_ALLOW_HEADERS = [
    'content-type',
    'access_token',
    'X-Refresh-Token',
]

CORS_EXPOSE_HEADERS = ["Content-Disposition"]

REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_RATES': {
        'high_rate': '500/min',  # 每分鐘最多1000次請求
        'medium_rate': '200/min',  # 每分鐘最多500次請求
        'low_rate': '100/min',  # 每分鐘最多100次請求
        'super_low_rate': '10/min',  # 每分鐘最多10次請求
    }
    # 'DEFAULT_PERMISSION_CLASSES': (
    #     'apps.users._PermissionsUtils.CustomPermission',
    # ),
}

ROOT_URLCONF = 'HEYSONG_ERP_HY_API.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR, 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'HEYSONG_ERP_HY_API.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR + 'db.sqlite3',
#     }
# }

# Oracle使用
DATABASES = {
    'default': {
        'ENGINE': os.getenv('ENGINE'),
        'NAME': os.getenv('NAME'),
        'USER': os.getenv('USER'),
        'PASSWORD': os.getenv('PASSWORD'),
        'HOST': os.getenv('HOST'),
        'PORT': os.getenv('PORT'),
    }
}

# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Taipei'

USE_I18N = True

USE_L10N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 日誌
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{asctime} {module}.{funcName} {lineno:3} {levelname:7} => {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'verbose',
            'filename': 'logs/django.log',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'delay': True,
            'when': 'midnight',  # rotates the log file at midnight
            'interval': 1,      # interval is 1 day by default
            'backupCount': 100, # keeps the last 100 log files
            'encoding': "utf-8",
        },
    },
    'loggers': {
        '': {
            'handlers': ['console', 'file'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
        },
        'django': {
            'handlers': ['console', 'file'],
            'level': os.getenv('DJANGO_LOG_LEVEL', 'INFO'),
            'propagate': False,
        },
    },
}


