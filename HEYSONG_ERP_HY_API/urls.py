# from django.contrib import admin
from django.conf import settings
from django.conf.urls import url, include
from django.conf.urls.static import static
from django.urls import re_path, path
from rest_framework import routers

from apps.accounts.views import AccountViewSet
from apps.allowances.views import AllowanceViewSet
from apps.blog.views import BlogViewSet
from apps.dealers.views import DealerViewSet
from apps.documents.views import DocumentViewSet
from apps.invoices.views import InvoiceViewSet
from apps.orders.views import OrderViewSet
from apps.pallet.views import PalletViewSet
from apps.products.views import ProductViewSet
from apps.users.views import UserViewSet
from apps.payments.views import PaymentViewSet

router = routers.SimpleRouter()
router.register(r'users', UserViewSet)
router.register(r'accounts', AccountViewSet)
router.register(r'products', ProductViewSet)
router.register(r'payments', PaymentViewSet)
router.register(r'dealers', DealerViewSet)
router.register(r'allowances', AllowanceViewSet)
router.register(r'orders', OrderViewSet)
router.register(r'invoices', InvoiceViewSet)
router.register(r'documents', DocumentViewSet)
router.register(r'pallet', PalletViewSet)
router.register(r'blogs', BlogViewSet)


urlpatterns = [
    # path('admin/', admin.site.urls),
    path('api/blogs/get_blog_image/<str:filename>', BlogViewSet.as_view({'get': 'get_blog_image'}), name='get_blog_image'),
    url(r'^api/', include(router.urls)),
]
