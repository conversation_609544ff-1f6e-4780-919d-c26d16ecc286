# celery.py

import os
from celery import Celery
from kombu import Exchange, Queue

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HEYSONG_ERP_HY_API.settings')

app = Celery('HEYSONG_ERP_HY_API', broker='amqp://root:root@192.168.50.207:5672//')

app.config_from_object('django.conf:settings', namespace='CELERY')

app.conf.update(
    task_always_eager=False,
    task_eager_propagates=True,
    task_remote_tracebacks=True,
    task_ignore_result=False,
    result_backend='rpc://',  # 如果需要存儲任務結果
    CELERYD_LOG_LEVEL='INFO',
    CELERYD_LOG_FILE='celery.log',
)

app.autodiscover_tasks()