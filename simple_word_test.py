#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
²�檺 Word ���ո}��
"""

import os
import sys
import logging
import time

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_word_creation():
    """���� Word �Ы�"""
    
    logger.info("�}�l���� Word �Ы�...")
    
    try:
        import pythoncom
        import win32com.client
        
        # ��l�� COM
        pythoncom.CoInitialize()
        
        # �Ы� Word ���ε{��
        logger.info("�ϥ� DispatchEx �Ы� Word...")
        word_app = win32com.client.DispatchEx("Word.Application")
        
        # �]�m�򥻿ﶵ
        word_app.Visible = False
        word_app.DisplayAlerts = 0
        
        # ���� Normal.dotm ���~���]�m
        try:
            word_app.Options.DoNotPromptForConvert = True
            word_app.Options.ConfirmConversions = False
            word_app.Options.UpdateLinksAtOpen = False
            word_app.Options.CheckGrammarAsYouType = False
            word_app.Options.CheckSpellingAsYouType = False
            word_app.Options.AutoRecover = False
            word_app.AutomationSecurity = 3
            logger.info("Word �ﶵ�]�m���\")
        except Exception as e:
            logger.warning(f"�]�m Word �ﶵ�ɥX��: {str(e)}")
        
        # ���ճЫطs����
        logger.info("���ճЫطs����...")
        doc = word_app.Documents.Add()
        doc.Close(SaveChanges=False)
        
        # ���� Word
        word_app.Quit()
        
        logger.info("? Word ���զ��\ - �S���X�{ Normal.dotm ���~")
        return True
        
    except Exception as e:
        error_msg = str(e)
        
        if "Normal.dotm" in error_msg:
            logger.error(f"? �X�{ Normal.dotm ���~: {error_msg}")
        else:
            logger.error(f"? ���ե���: {error_msg}")
        
        return False
    
    finally:
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def test_com_safe_processor():
    """���� COM �w���B�z��"""
    
    logger.info("���� COM �w���B�z��...")
    
    try:
        # �K�[���ظ��|
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from apps.documents.word_com_safe_processor import get_com_safe_processor
        processor = get_com_safe_processor()
        
        if processor:
            logger.info("? COM �w���B�z�����զ��\")
            return True
        else:
            logger.error("? �L�k��� COM �w���B�z��")
            return False
    
    except Exception as e:
        error_msg = str(e)
        
        if "Normal.dotm" in error_msg:
            logger.error(f"? COM �w���B�z���X�{ Normal.dotm ���~: {error_msg}")
        else:
            logger.error(f"? COM �w���B�z�����ե���: {error_msg}")
        
        return False

def main():
    """�D���ը��"""
    logger.info("�}�l���� Normal.dotm ���~�״_...")
    
    # ���� Word �Ы�
    logger.info("\n=== ���� Word �Ы� ===")
    word_test_result = test_word_creation()
    
    # ���� COM �w���B�z��
    logger.info("\n=== ���� COM �w���B�z�� ===")
    processor_test_result = test_com_safe_processor()
    
    # ��X���յ��G
    logger.info("\n=== ���յ��G�K�n ===")
    logger.info(f"Word �Ыش���: {'���\' if word_test_result else '����'}")
    logger.info(f"COM �w���B�z������: {'���\' if processor_test_result else '����'}")
    
    if word_test_result and processor_test_result:
        logger.info("\n? �Ҧ����ճ��q�L�A�S���o�{ Normal.dotm ���~�I")
        return True
    else:
        logger.error("\n??  �����ե��ѡA���ˬd�״_�O�_����")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("���ճQ�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"���չL�{���o�ͥ��w�������~: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
