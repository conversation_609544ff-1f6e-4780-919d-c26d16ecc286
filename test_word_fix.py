#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
���� Word �״_�\��
"""

import os
import sys
import logging

# �K�[���ظ��|
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('word_fix_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_word_environment_fixer():
    """���� Word ���ҭ״_��"""
    try:
        from apps.documents.word_environment_fixer import WordEnvironmentFixer
        
        logger.info("=== ���� Word ���ҭ״_�� ===")
        
        fixer = WordEnvironmentFixer()
        
        # �E�_���D
        logger.info("�E�_ Word ���Ұ��D...")
        issues = fixer.diagnose_issues()
        
        if issues:
            logger.warning("�o�{�H�U���D:")
            for issue in issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("���o�{������D")
        
        # ����״_
        logger.info("�������ҭ״_...")
        success = fixer.fix_environment()
        
        if success:
            logger.info("? Word ���ҭ״_���\")
        else:
            logger.error("? Word ���ҭ״_����")
        
        return success
        
    except Exception as e:
        logger.error(f"���� Word ���ҭ״_���ɥX��: {str(e)}")
        return False

def test_word_com_initializer():
    """���� Word COM ��l�ƾ�"""
    try:
        from apps.documents.word_com_initializer import (
            ensure_com_initialized, 
            com_context, 
            create_word_app_safe
        )
        
        logger.info("=== ���� Word COM ��l�ƾ� ===")
        
        # ���� COM ��l��
        logger.info("���� COM ��l��...")
        ensure_com_initialized()
        logger.info("? COM ��l�Ʀ��\")
        
        # ���� COM �W�U��
        logger.info("���� COM �W�U��...")
        with com_context():
            logger.info("? COM �W�U�奿�`")
        
        # ���զw���Ы� Word ���ε{��
        logger.info("���զw���Ы� Word ���ε{��...")
        try:
            word_app = create_word_app_safe()
            if word_app:
                logger.info("? Word ���ε{���Ыئ��\")
                try:
                    word_app.Quit()
                    logger.info("? Word ���ε{���������\")
                except:
                    pass
            else:
                logger.error("? Word ���ε{���Ыإ���")
                return False
        except Exception as e:
            logger.error(f"? �Ы� Word ���ε{���ɥX��: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"���� Word COM ��l�ƾ��ɥX��: {str(e)}")
        return False

def test_word_com_safe_processor():
    """���� Word COM �w���B�z��"""
    try:
        from apps.documents.word_com_safe_processor import get_com_safe_processor
        
        logger.info("=== ���� Word COM �w���B�z�� ===")
        
        # ����B�z�����
        logger.info("��� COM �w���B�z�����...")
        processor = get_com_safe_processor()
        
        if processor:
            logger.info("? COM �w���B�z�����������\")
        else:
            logger.error("? COM �w���B�z������������")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"���� Word COM �w���B�z���ɥX��: {str(e)}")
        return False

def main():
    """�D���ը��"""
    logger.info("�}�l���� Word �״_�\��...")
    
    results = []
    
    # �������ҭ״_��
    results.append(("Word ���ҭ״_��", test_word_environment_fixer()))
    
    # ���� COM ��l�ƾ�
    results.append(("Word COM ��l�ƾ�", test_word_com_initializer()))
    
    # ���� COM �w���B�z��
    results.append(("Word COM �w���B�z��", test_word_com_safe_processor()))
    
    # ��X���յ��G
    logger.info("\n=== ���յ��G�K�n ===")
    all_passed = True
    
    for test_name, result in results:
        status = "? �q�L" if result else "? ����"
        logger.info(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        logger.info("\n? �Ҧ����ճ��q�L�F�IWord �״_�\�ॿ�`�u�@�C")
    else:
        logger.warning("\n??  �������ե��ѡA���ˬd�����ե�C")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("���ճQ�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"���չL�{���o�ͥ��w�������~: {str(e)}")
        sys.exit(1)
