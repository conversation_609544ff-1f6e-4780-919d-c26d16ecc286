#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
���դ��P�� Word �Ыؤ�k
"""

import sys
import time
import logging
import pythoncom
import win32com.client

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_word_creation_method(method_name, create_func):
    """���կS�w�� Word �Ыؤ�k"""
    
    logger.info(f"\n=== ���դ�k: {method_name} ===")
    
    try:
        # ��l�� COM
        pythoncom.CoInitialize()
        
        # �Ы� Word ���ε{��
        logger.info("�Ы� Word ���ε{��...")
        word_app = create_func()
        
        if not word_app:
            logger.error("Word ���ε{���Ыإ���")
            return False
        
        # �򥻳]�m
        word_app.Visible = False
        word_app.DisplayAlerts = 0
        
        logger.info("�]�m Word �ﶵ...")
        
        # �]�m�ﶵ�]�C�ӳ���W�B�z�^
        try:
            word_app.Options.DoNotPromptForConvert = True
            logger.info("? DoNotPromptForConvert �]�m���\")
        except Exception as e:
            logger.warning(f"? DoNotPromptForConvert �]�m����: {e}")
        
        try:
            word_app.Options.ConfirmConversions = False
            logger.info("? ConfirmConversions �]�m���\")
        except Exception as e:
            logger.warning(f"? ConfirmConversions �]�m����: {e}")
        
        # ���ճЫؤ���
        logger.info("���ճЫؤ���...")
        doc = word_app.Documents.Add()
        
        # �K�[�@�Ǥ��e
        doc.Content.Text = f"Test document created with {method_name}"
        
        # ��������
        doc.Close(SaveChanges=False)
        
        # ���� Word
        word_app.Quit()
        
        logger.info(f"? {method_name} ���զ��\")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "Normal.dotm" in error_msg:
            logger.error(f"? {method_name} �X�{ Normal.dotm ���~: {error_msg}")
        else:
            logger.error(f"? {method_name} ���ե���: {error_msg}")
        return False
    
    finally:
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def create_with_dispatch():
    """�ϥ� Dispatch �Ы�"""
    return win32com.client.Dispatch("Word.Application")

def create_with_dispatch_ex():
    """�ϥ� DispatchEx �Ы�"""
    return win32com.client.DispatchEx("Word.Application")

def create_with_gencache():
    """�ϥ� gencache.EnsureDispatch �Ы�"""
    import win32com.client.gencache
    return win32com.client.gencache.EnsureDispatch("Word.Application")

def create_with_fallback():
    """�ϥ� fallback ��k�Ы�"""
    try:
        return win32com.client.Dispatch("Word.Application")
    except:
        try:
            return win32com.client.DispatchEx("Word.Application")
        except:
            import win32com.client.gencache
            return win32com.client.gencache.EnsureDispatch("Word.Application")

def main():
    """�D���"""
    
    logger.info("=== Word �Ыؤ�k���դu�� ===")
    
    # ���դ��P���Ыؤ�k
    methods = [
        ("Dispatch", create_with_dispatch),
        ("DispatchEx", create_with_dispatch_ex),
        ("gencache.EnsureDispatch", create_with_gencache),
        ("Fallback Method", create_with_fallback),
    ]
    
    results = {}
    
    for method_name, create_func in methods:
        try:
            success = test_word_creation_method(method_name, create_func)
            results[method_name] = success
            
            # ���ݤ@�U�A���դU�@�Ӥ�k
            time.sleep(2)
            
        except KeyboardInterrupt:
            logger.info("���ճQ�Τᤤ�_")
            break
        except Exception as e:
            logger.error(f"���� {method_name} �ɵo�ͥ��w�����~: {e}")
            results[method_name] = False
    
    # ��ܵ��G�`��
    logger.info("\n=== ���յ��G�`�� ===")
    
    successful_methods = []
    failed_methods = []
    
    for method_name, success in results.items():
        if success:
            logger.info(f"? {method_name}: ���\")
            successful_methods.append(method_name)
        else:
            logger.info(f"? {method_name}: ����")
            failed_methods.append(method_name)
    
    if successful_methods:
        logger.info(f"\n? ���˨ϥ�: {successful_methods[0]}")
        logger.info("��ĳ�b�{�����u���ϥΦ��\����k")
    else:
        logger.error("\n?? �Ҧ���k�����ѤF")
        logger.error("�i��ݭn:")
        logger.error("1. ���s�w�� Microsoft Office")
        logger.error("2. ���s�w�� pywin32")
        logger.error("3. �ˬd�t���v��")
    
    return len(successful_methods) > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("���ճQ�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"�o�ͥ��w�������~: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
