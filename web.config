<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- CORS 設定 -->
        <httpProtocol>
            <customHeaders>
                <!-- 移除預設的 CORS 標頭 -->
                <remove name="Access-Control-Allow-Origin" />
                <remove name="Access-Control-Allow-Headers" />
                <remove name="Access-Control-Allow-Methods" />
                <remove name="Access-Control-Allow-Credentials" />
                
                <!-- 添加 CORS 標頭 -->
                <add name="Access-Control-Allow-Origin" value="https://b2b.heysong.dev:9000" />
                <add name="Access-Control-Allow-Headers" value="content-type, access_token, X-Refresh-Token, Authorization" />
                <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS, PATCH" />
                <add name="Access-Control-Allow-Credentials" value="true" />
                <add name="Access-Control-Max-Age" value="3600" />
            </customHeaders>
        </httpProtocol>
        
        <!-- 處理 OPTIONS 預檢請求 -->
        <handlers>
            <add name="OPTIONSVerbHandler" path="*" verb="OPTIONS" modules="ProtocolSupportModule" requireAccess="None" />
        </handlers>
        
        <!-- URL 重寫規則（如果使用） -->
        <rewrite>
            <rules>
                <!-- 處理 OPTIONS 請求 -->
                <rule name="HandleOPTIONS" stopProcessing="true">
                    <match url=".*" />
                    <conditions>
                        <add input="{REQUEST_METHOD}" pattern="OPTIONS" />
                    </conditions>
                    <action type="CustomResponse" statusCode="200" statusReason="OK" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- 如果使用 FastCGI 運行 Django -->
        <handlers>
            <add name="Python FastCGI" 
                 path="*" 
                 verb="*" 
                 modules="FastCgiModule" 
                 scriptProcessor="C:\Python\python.exe|C:\Python\Scripts\wfastcgi.py"
                 resourceType="Unspecified" 
                 requireAccess="Script" />
        </handlers>
        
        <!-- 應用程式設定 -->
        <appSettings>
            <add key="WSGI_HANDLER" value="HEYSONG_ERP_HY_API.wsgi.application" />
            <add key="PYTHONPATH" value="C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API" />
            <add key="DJANGO_SETTINGS_MODULE" value="HEYSONG_ERP_HY_API.settings" />
        </appSettings>
    </system.webServer>
    
    <!-- 安全設定 -->
    <system.web>
        <authentication mode="None" />
    </system.web>
</configuration>