import json  # -*- coding: UTF8 -*-
from rest_framework import status
from rest_framework.throttling import SimpleRateThrottle
from django.http import JsonResponse
from django.db import connection
from datetime import datetime

class HighRateThrottle(SimpleRateThrottle):
    scope = 'high_rate'

class MediumRateThrottle(SimpleRateThrottle):
    scope = 'medium_rate'

class LowRateThrottle(SimpleRateThrottle):
    scope = 'low_rate'

class SuperLowRateThrottle(SimpleRateThrottle):
    scope = 'super_low_rate'

class BlockIPMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # print('request:', request.path)

        # 只對 select_login API 進行阻斷判斷
        if request.path == '/api/accounts/login/':
            username = ''
            if request.method == 'POST':
                data = json.loads(request.body)
                username = data.get('username')

            ip = request.META.get('REMOTE_ADDR')

            with connection.cursor() as cursor:
                # 檢查 IP 是否被永久阻斷
                cursor.execute("SELECT 1 FROM B2B.BLOCKED_IPS WHERE IP_ADDRESS = %s AND USERID = %s", [ip, username])
                if cursor.fetchone():
                    # 如果存在記錄，則返回418狀態碼
                    return JsonResponse({'message': '此 IP 已被永久阻斷'}, status=status.HTTP_418_IM_A_TEAPOT)

        response = self.get_response(request)
        return response