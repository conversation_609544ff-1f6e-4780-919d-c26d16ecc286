from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.payments.PaymentInfo import select_erp_hy_arv201_300_main_payment_detail
from apps.payments.models import mainPayment


class PaymentViewSet(viewsets.ModelViewSet ):
    queryset = mainPayment.objects.all()

    @action(detail=False, methods=['post'])
    def erp_hy_arv201_300_main_payment_detail(self, request):
        # sql查詢結果
        sql_result, http_status = select_erp_hy_arv201_300_main_payment_detail(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        # print(sql_result, http_status)
        return JsonResponse(
            {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
            status=http_status,
            json_dumps_params={'ensure_ascii': False})