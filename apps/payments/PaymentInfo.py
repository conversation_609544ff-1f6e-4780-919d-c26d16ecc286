import itertools

import cx_<PERSON>

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data, get_previous_month_dates
from utils.token_utils import validate_access_token_and_params_with_pagination, \
    get_pagination_params

''' Payment Start  '''
ocv200_201_main_payment_sql = """
    SELECT B.BANK_ID300, B.BRANCH_ID300 BANK_BRANCH, --行庫編號
           B.TRACK_CHECK300 TRACK_CHECK, --票據號碼
           B.PAY_ACCOUNT_NO300 PAY_ACCOUNT_NO, --帳號
           -- ROWNUM                         line_no,         --序號
           TO_CHAR (A.AR_DATE201, 'YYYY/MM/DD') AR_DATE, --銷售日期
           A.AGENT_CODE201 AGENT_CODE, --經銷商代號
           A.BRIEF_NAME201 AGENT_NAME, --經銷商名稱
           DECODE(A<PERSON>AR_TYPE201,
                  '8', <PERSON><PERSON><PERSON>OX_REC_NO201,
                  <PERSON><PERSON>TRA<PERSON>K_INVOICE201) TRACK_INVOICE, --統一發票號碼
           DECODE(A.AR_TYPE201,
                  '8', '',
                  A.PRODUCT_ID201) PRODUCT_ID, --產品代號
           A.PRODUCT_NAME201 PRODUCT_NAME, --產品名稱
           DECODE(A.AR_TYPE201,
                  '8', 0,
                  A.QTY201) QTY, --數量
           A.TOT_STD_AMT201 TOT_STD_AMT, --金額
           A.AR_TYPE201 AR_TYPE, --帳款類別
           A.AR_TYPE_DESC201 AR_TYPE_DESC, --帳款類別名稱
           C.CITY_ID025 CITY_ID, --經銷商縣市代號
           A.PRDC201 COMP_CODE, --廠別代號
           DECODE(A.PRDC201, '2', '斗六廠', '中壢廠') COMP_NAME, --廠別名稱
           A.PRODUCT_MARK201 PRODUCT_MARK, --包裝代號
           A.DAY201 DAY, --票期
           TO_CHAR (B.RECEIVE_DATE300, 'YYYY/MM/DD') RECEIVE_DATE, --入帳日
           TO_CHAR (B.EXECUTE_DATE300, 'YYYY/MM/DD') AGAINST_DATE, --繳款日期
           B.PAY_ACCOUNT_NAME300 PAY_ACCOUNT_NAME,--發票人
           B.BRANCH_NAME300 BRANCH_NAME, --行庫名稱
           TO_CHAR (B.DUE_DATE300, 'YYYY/MM/DD') DUE_DATE --到期日
      FROM ARV201@B2B A,
           ARV300@B2B B,
           OCV025@B2B C
     WHERE NVL(B.RECEIVE_CONFIRM_FLAG300, 'N') = 'Y'
       AND A.ARF300_SEQ201 = B.ARF300_SEQ300
       AND A.OCF025_SEQ201 = C.OCF025_SEQ025
       AND A.AR_TYPE201 IN ('1', '8')
       AND B.EXECUTE_DATE300 BETWEEN TO_DATE(:qEXECUTE_START_DATE300, 'YYYYMMDD') AND TO_DATE(:qEXECUTE_END_DATE300, 'YYYYMMDD')
"""

''' Payment End  '''

@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_arv201_300_main_payment_detail(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = 'select_erp_hy_arv201_300_main_payment_detail'

    if request.method == "POST":

        # 取得前12個月的第一天和最後一天
        start_date, end_date = get_previous_month_dates(12)

        # SQL 查詢基本結構
        sql_base = ocv200_201_main_payment_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        sorted_params = json_data.get('sort', '')

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "A.AGENT_CODE201",
            "track_check": "B.TRACK_CHECK300",
        }

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" A.AGENT_CODE201 = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        params['qEXECUTE_START_DATE300'] = start_date
        params['qEXECUTE_END_DATE300'] = end_date
        params2['qEXECUTE_START_DATE300'] = start_date
        params2['qEXECUTE_END_DATE300'] = end_date

        if 'due_start_date' in json_data and json_data['due_start_date'] is not None and \
                'due_end_date' in json_data and json_data['due_end_date'] is not None:
            params['qDUE_START_DATE'] = json_data['due_start_date']
            params['qDUE_END_DATE'] = json_data['due_end_date']
            params2['qDUE_START_DATE'] = json_data['due_start_date']
            params2['qDUE_END_DATE'] = json_data['due_end_date']
            sql_conditions.append(f" B.DUE_DATE300 BETWEEN TO_DATE(:qDUE_START_DATE, 'YYYYMMDD') AND TO_DATE(:qDUE_END_DATE, 'YYYYMMDD') ")

        # 構建完整的 SQL 查詢
        if len(sql_conditions) == 0:
            sql_query = f" {sql_base} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} "

        # 增加使用者是否讀取此筆資料
        sql_query = """ 
            SELECT BANK_ID300, BANK_BRANCH, TRACK_CHECK, PAY_ACCOUNT_NO, AR_DATE, AGENT_CODE, AGENT_NAME, TRACK_INVOICE, PRODUCT_ID, 
                   PRODUCT_NAME, QTY, TOT_STD_AMT, AR_TYPE, AR_TYPE_DESC, CITY_ID, COMP_CODE, COMP_NAME, PRODUCT_MARK, DAY,
                   RECEIVE_DATE, AGAINST_DATE, PAY_ACCOUNT_NAME, BRANCH_NAME, DUE_DATE, SOURCESERNO, USERID, ACTIONTYPE
              FROM ( """ + sql_query + """ ) 
              LEFT JOIN (
                  SELECT SOURCESERNO, USERID, ACTIONTYPE
                    FROM USERLOG
                   WHERE PARENTID = 'F0100000' AND CHILDID = 'F001_1')
                ON TRACK_CHECK = SOURCESERNO(+) AND AGENT_CODE = USERID(+)
        """

        # 排序
        sql_sorted = ""
        if sorted_params == '開票日':
            sql_query = sql_query + " ORDER BY AGAINST_DATE DESC, TRACK_CHECK DESC, AGENT_CODE, TRACK_INVOICE, PRODUCT_ID "
            sql_sorted = "DENSE_RANK() OVER (ORDER BY AGAINST_DATE DESC, TRACK_CHECK DESC) RNK"

        elif sorted_params == '到期日':
            sql_query = sql_query + " ORDER BY DUE_DATE DESC, TRACK_CHECK DESC, AGENT_CODE, TRACK_INVOICE, PRODUCT_ID "
            sql_sorted = "DENSE_RANK() OVER (ORDER BY DUE_DATE DESC, TRACK_CHECK DESC) RNK"

        ranked_sql = f"""
                    SELECT BANK_ID300, BANK_BRANCH, TRACK_CHECK, PAY_ACCOUNT_NO, AR_DATE, AGENT_CODE, AGENT_NAME, TRACK_INVOICE, PRODUCT_ID,
                           PRODUCT_NAME, QTY, TOT_STD_AMT, AR_TYPE, AR_TYPE_DESC, CITY_ID, COMP_CODE, COMP_NAME, PRODUCT_MARK, DAY,
                           RECEIVE_DATE, AGAINST_DATE, PAY_ACCOUNT_NAME, BRANCH_NAME, DUE_DATE, SOURCESERNO, USERID, ACTIONTYPE,
                           {sql_sorted}
                      FROM (  """ + sql_query + """)
                     WHERE 1 = 1
            """

        ranked_sql1 = """
                    SELECT BANK_ID300, BANK_BRANCH, TRACK_CHECK, PAY_ACCOUNT_NO, AR_DATE, AGENT_CODE, AGENT_NAME, TRACK_INVOICE, PRODUCT_ID,
                           PRODUCT_NAME, QTY, TOT_STD_AMT, AR_TYPE, AR_TYPE_DESC, CITY_ID, COMP_CODE, COMP_NAME, PRODUCT_MARK, DAY,
                           RECEIVE_DATE, AGAINST_DATE, PAY_ACCOUNT_NAME, BRANCH_NAME, DUE_DATE, SOURCESERNO, USERID, ACTIONTYPE,
                           RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size

                """

        # print(ranked_sql1, params)

        ranked_sql2 = """ 
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行資料轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]
                # data.sort(reverse=True, key=lambda x: (x['AGENT_CODE'], x['TRACK_CHECK']))

                # 依照支票號碼分組資料
                grouped_by_column = [
                    list(group)
                    for _, group in itertools.groupby(data, key=lambda x: x['TRACK_CHECK'])
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_payment_master_detail(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def generate_payment_master_detail(data, start_index=0):
    report = []
    for track_check, group in itertools.groupby(data, lambda x: x["TRACK_CHECK"]):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE"],  # 經銷商代碼
            "dealer_name": group[0]["AGENT_NAME"],  # 經銷商名稱
            "expiry_date": group[0]["DUE_DATE"],  # 到期日
            "track_check": track_check,  # 支票號碼
            "total_amount": sum(item["TOT_STD_AMT"] for item in group),  # 金額
            "start_date": min(item["RECEIVE_DATE"] for item in group),  # 起算日
            "issue_date": max(item["AGAINST_DATE"] for item in group),  # 開票日
            "payee": group[0]["PAY_ACCOUNT_NAME"],  # 發票人
            "payment_bank": group[0]["BRANCH_NAME"],  # 付款行庫
            "payment_account": group[0]["PAY_ACCOUNT_NO"],  # 付款帳號
            "readStatus": group[0]["ACTIONTYPE"],  # 使用者是否讀取此筆資料
            "detail_button": "明細",
            "summary_button": "彙總"
        }

        details = []
        for invoice, invoice_group in itertools.groupby(group, lambda x: x["TRACK_INVOICE"]):
            invoice_group = list(invoice_group)

            detail = {
                "index": len(details) + 1,
                "dealer_code2": group[0]["AGENT_CODE"],  # 經銷商代碼
                "track_check2": track_check,  # 支票號碼
                "voucher_type": invoice_group[0]["AR_TYPE"],  # 帳款類別
                "voucher_type_name": invoice_group[0]["AR_TYPE_DESC"],  # 帳款類別名稱
                "issue_date": invoice_group[0]["AR_DATE"],  # 開票日期
                "expiry_date": invoice_group[0]["DUE_DATE"],  # 到期日
                "factory": invoice_group[0]["COMP_CODE"],  # 廠別
                "factory_name": invoice_group[0]["COMP_NAME"],  # 廠別名稱
                "voucher_number": invoice,  # 憑證號碼
                "amount": sum(item["TOT_STD_AMT"] for item in invoice_group),  # 金額
            }

            product_details = []
            for product, product_group in itertools.groupby(invoice_group, lambda x: x["PRODUCT_ID"]):
                product_group = list(product_group)

                product_detail = {
                    "index": len(product_details) + 1,
                    "voucher_type": product_group[0]["AR_TYPE"],  # 帳款類別
                    "voucher_type_name": product_group[0]["AR_TYPE_DESC"],  # 帳款類別名稱
                    "product_code": product,  # 產品代號
                    "product_name": product_group[0]["PRODUCT_NAME"],  # 產品名稱
                    "quantity": sum(int(item["QTY"]) for item in product_group),  # 數量
                    "amount": sum(item["TOT_STD_AMT"] for item in product_group),  # 金額
                }

                product_details.append(product_detail)

            detail["child_details"] = product_details
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report
