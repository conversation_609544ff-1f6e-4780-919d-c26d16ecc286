# -*- coding: UTF8 -*-
from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.pallet._PalletAccountInfo import select_pallet_account_method
from apps.pallet.models import mainPallet

PALLET_ACTIONS = {
    'pallet_account': {
        'select': select_pallet_account_method,
    },
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class PalletViewSet(viewsets.ModelViewSet ):
    queryset = mainPallet.objects.all()
    # permission_classes = [CustomTokenPermission]

    def _handle_action(self, resource, action):
        sql_result, http_status = PALLET_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 查詢棧板帳
    @action(detail=False, methods=['post'])
    def select_pallet_account(self, request):
        return self._handle_action('pallet_account', 'select')