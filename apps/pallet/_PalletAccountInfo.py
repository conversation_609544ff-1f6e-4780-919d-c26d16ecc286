# -*- coding: UTF8 -*-
import itertools
import json
import logging
import math

import cx_<PERSON>
from datetime import datetime, timedelta
import calendar

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data, get_custom_date_range
from utils.token_utils import validate_access_token_and_params_with_pagination, \
    get_pagination_params

''' PALLET_ACCOUNT SELECT START '''
select_begin_end_date_sql_1 = f"""
    -- 檢查臨時表是否存在，如果不存在則創建
    BEGIN
        EXECUTE IMMEDIATE 'CREATE GLOBAL TEMPORARY TABLE TEMP_MBV2015 ON COMMIT PRESERVE ROWS AS
            SELECT * FROM MBV2015@B2B WHERE 1 = 0'; -- 創建一個空結構的臨時表
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -955 THEN -- 如果錯誤不是因為表已存在
                RAISE;
            END IF;
    END;
"""

select_begin_end_date_sql_2 = """
    -- 每次查詢之前，清空臨時表中的數據，並插入更新臨時表中的數據
    BEGIN
        EXECUTE IMMEDIATE 'DELETE FROM TEMP_MBV2015';
        INSERT INTO TEMP_MBV2015
        SELECT *
          FROM MBV2015@B2B
         WHERE SLIP_DATE2015 BETWEEN TO_DATE(:qBEGIN_DATE, 'YYYYMMDD') AND TO_DATE(:qEND_DATE, 'YYYYMMDD');
    END;
"""
# select_棧板資料資料
def select_pallet_account_data_sql(strCond):
    return """
-- 使用全域臨時表 temp_mbv2015 的優化查詢
  WITH ORIGINALDATA AS
           (SELECT A.AGENT_CODE025,
                   A.SLIP_DATE200,
                   A.BOX_ID010,
                   A.QTY_IN,
                   A.QTY_OUT,
                   B.QTY1 - A.QTY_OUT + A.QTY_IN AS QTY_FINAL
              FROM (SELECT AGENT_CODE2015 AS AGENT_CODE025,
                           TRUNC(SLIP_DATE2015) AS SLIP_DATE200,
                           BOX_ID2015 AS BOX_ID010,
                           SUM(CASE WHEN ID2015 IN ('002', '053') THEN QTY2015 ELSE 0 END) AS QTY_OUT,
                           -SUM(CASE WHEN ID2015 IN ('102', '153') THEN QTY2015 ELSE 0 END) AS QTY_IN
                      FROM TEMP_MBV2015
                     GROUP BY AGENT_CODE2015, TRUNC(SLIP_DATE2015), BOX_ID2015) A,
                   (SELECT C.AGENT_CODE2015,
                           C.SLIP_DATE,
                           C.BOX_ID2015,
                           SUM(CASE WHEN B.ID2015 <> '052' THEN B.QTY2015 ELSE 0 END) AS QTY1
                      FROM MBV2015@B2B B,
                           (SELECT AGENT_CODE2015,
                                   TRUNC(SLIP_DATE2015) AS SLIP_DATE,
                                   BOX_ID2015
                              FROM TEMP_MBV2015
                             WHERE ID2015 IN ('002', '102', '053', '153', '072')
                             GROUP BY AGENT_CODE2015, TRUNC(SLIP_DATE2015), BOX_ID2015) C
                     WHERE TRUNC(B.SLIP_DATE2015) <= C.SLIP_DATE
                       AND C.AGENT_CODE2015 = B.AGENT_CODE2015
                       AND C.BOX_ID2015 = B.BOX_ID2015 """ + strCond + """
                     GROUP BY C.AGENT_CODE2015, C.SLIP_DATE, C.BOX_ID2015) B
             WHERE A.AGENT_CODE025 = B.AGENT_CODE2015
               AND A.BOX_ID010 = B.BOX_ID2015
               AND A.SLIP_DATE200 = B.SLIP_DATE),
       P_MAXDATES AS (SELECT AGENT_CODE025,                           
                             BOX_ID010,
                             MAX(SLIP_DATE200) AS MAX_DATE
                        FROM ORIGINALDATA
                       WHERE SLIP_DATE200 BETWEEN TO_DATE(:qP_BEGIN_DATE, 'YYYYMMDD') AND TO_DATE(:qP_END_DATE, 'YYYYMMDD')
                       GROUP BY AGENT_CODE025, BOX_ID010),
       T_MAXDATES AS (SELECT AGENT_CODE025,
                             BOX_ID010,
                             MAX(SLIP_DATE200) AS MAX_DATE
                        FROM ORIGINALDATA
                       WHERE SLIP_DATE200 BETWEEN TO_DATE(:qT_BEGIN_DATE, 'YYYYMMDD') AND TO_DATE(:qT_END_DATE, 'YYYYMMDD')
                       GROUP BY AGENT_CODE025, BOX_ID010),
       P_T_TOTAL_QTY AS (SELECT AGENT_CODE025, BOX_ID010, SUM(P_TOTAL_QTY) P_TOTAL_QTY, SUM(T_TOTAL_QTY) T_TOTAL_QTY
                           FROM (SELECT O.AGENT_CODE025,
                                        --O.SLIP_DATE200,
                                        O.BOX_ID010,
                                        --O.QTY_OUT,
                                        --O.QTY_IN,
                                        --O.QTY_FINAL,
                                        O.QTY_FINAL + O.QTY_OUT - O.QTY_IN AS P_TOTAL_QTY,
                                        0 T_TOTAL_QTY
                                   FROM ORIGINALDATA O, P_MAXDATES P
                                  WHERE O.AGENT_CODE025 = P.AGENT_CODE025 AND O.BOX_ID010 = P.BOX_ID010 AND O.SLIP_DATE200 = P.MAX_DATE
                                  UNION ALL
                                 SELECT O.AGENT_CODE025,
                                        --O.SLIP_DATE200,
                                        O.BOX_ID010,
                                        --O.QTY_OUT,
                                        --O.QTY_IN,
                                        --O.QTY_FINAL,
                                        0 P_TOTAL_QTY,
                                        O.QTY_FINAL + O.QTY_OUT - O.QTY_IN AS T_TOTAL_QTY
                                   FROM ORIGINALDATA O, T_MAXDATES T
                                  WHERE O.AGENT_CODE025 = T.AGENT_CODE025 AND O.BOX_ID010 = T.BOX_ID010 AND O.SLIP_DATE200 = T.MAX_DATE)
                          GROUP BY AGENT_CODE025, BOX_ID010)
"""

''' PALLET_ACCOUNT SELECT END '''

''' PALLET_ACCOUNT SELECT START '''

def transform_to_pallet_account_frontend_structure(data, start_index=0):
    # 為原始數據集中的每條記錄分配一個唯一的detail_index
    for index, item in enumerate(data, start=1):
        item["MONTH"] = (item["SLIP_DATE200"].year - 1911) * 100 + item["SLIP_DATE200"].month

    # 根據月份、代理商代碼和箱子類型分組
    grouped_data = itertools.groupby(sorted(data, key=lambda x: (x["MONTH"], x["AGENT_CODE200"], x["BOX_ID201"])),
                                     key=lambda x: (x["MONTH"], x["AGENT_CODE200"], x["BOX_ID201"]))

    # 每個分組計算進廠總量、期末結存以及詳細明細
    summary_report = []
    for (month, agent_code, box_id), group in grouped_data:
        group_list = sorted(list(group), key=lambda x: (x["SLIP_DATE200"], x["SOURCE_NO200"]))  # 根據日期排序明細

        total_in = sum(int(item["QTY201"]) for item in group_list if item["IO_TYPE200"] == 'I')
        total_out = sum(int(item["QTY201"]) for item in group_list if item["IO_TYPE200"] != 'I')

        details = []
        for item in group_list:
            details.append({
                "index": start_index + len(details) + 1,
                "dealer_code": agent_code,
                "entry_date": item["SLIP_DATE200"],
                "order_number": item["SOURCE_NO200"],
                "pallet_type": item["BOX_ID201"],
                "change_description": item["ID200"] + ' ' + item["NAME200"],
                'factory': '中壢廠',
                "incomingQuantity": item["QTY201"] if item["IO_TYPE200"] == 'I' else '',
                "outgoingQuantity": item["QTY201"] if item["IO_TYPE200"] != 'I' else '',
                "return_date": item["BACKF_DATE200"],
            })

        # 添加到報告列表
        summary_report.append({
            "index": start_index + len(summary_report) + 1,
            "dealer_code": agent_code,
            "dealer_name": group_list[0]["AGENT_BRIEF_NAME200"],
            "year_month": str(month),
            "box_id": box_id,
            "box_name": group_list[0]["BOX_NAME201"],
            "total_in": total_in,
            "total_out": total_out,
            "p_final_balance": group_list[0]["P_TOTAL_QTY"],
            "t_final_balance": group_list[0]["T_TOTAL_QTY"],
            "details": details
        })

    return summary_report

def select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk):

    if len(sql_conditions) == 0:
        sql_query = f" {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" AND {' AND '.join(sql_conditions)} "

    ranked_sql = """
                SELECT SLIP_NO200, SOURCE_NO200, AGENT_CODE200, AGENT_BRIEF_NAME200, BOX_ID201, BOX_NAME201, SLIP_DATE200, 
                       ID200, NAME200, IO_TYPE200, COMP_CODE200, QTY201, TO_CHAR(BACKF_DATE200, 'YYYY/MM/DD') BACKF_DATE200, 
                       P_TOTAL_QTY, T_TOTAL_QTY,
                       DENSE_RANK() OVER (ORDER BY AGENT_CODE200, BOX_ID201) AS RNK
                  FROM ( SELECT B.SLIP_NO200,
                                CASE
                                    WHEN B.SOURCE_NO200 IS NULL THEN
                                        B.SLIP_NO200
                                    ELSE
                                        B.SOURCE_NO200
                                END SOURCE_NO200,
                                B.AGENT_CODE200, B.AGENT_BRIEF_NAME200, A.BOX_ID201, A.BOX_NAME201, B.SLIP_DATE200, 
                                B.ID200, B.NAME200, B.IO_TYPE200, B.COMP_CODE200, A.QTY201, B.BACKF_DATE200,
                                P_T.P_TOTAL_QTY, P_T.T_TOTAL_QTY
                           FROM MBV201@B2B A, MBV200@B2B B, P_T_TOTAL_QTY P_T
                          WHERE A.MBF200_SEQ201 = B.MBF200_SEQ200
                            AND B.SLIP_DATE200 BETWEEN TO_DATE(:qT_BEGIN_DATE, 'YYYYMMDD') AND TO_DATE(:qT_END_DATE, 'YYYYMMDD')
                            AND B.PLACE_ID200 = '03'--異動對象為03經銷商
                            AND B.AGENT_CODE200 = P_T.AGENT_CODE025 AND A.BOX_ID201 = P_T.BOX_ID010
                          ORDER BY B.AGENT_CODE200, B.SOURCE_NO200, A.BOX_ID201 )
                 WHERE 1 = 1
            """

    ranked_sql1 = """
                SELECT SLIP_NO200, SOURCE_NO200, AGENT_CODE200, AGENT_BRIEF_NAME200, BOX_ID201, BOX_NAME201, SLIP_DATE200, 
                       ID200, NAME200, IO_TYPE200, COMP_CODE200, QTY201, BACKF_DATE200, 
                       P_TOTAL_QTY, T_TOTAL_QTY, RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = select_pallet_account_data_sql(sql_query) + """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """

    try:
        with connection.cursor() as cursor:
            # print(select_pallet_account_data_sql(sql_query) + ranked_sql1, params)
            cursor.execute(select_pallet_account_data_sql(sql_query) + ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE200"], x["BOX_ID201"]))]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_pallet_account_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return  handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params_with_pagination(None)
def select_pallet_account_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_pallet_account_method"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 上個月第一天~本月最後一天
        first_day_of_last_month, last_day_of_this_month = get_custom_date_range(json_data['begin_date'], -1, 0)

        # 本月第一天~~本月最後一天
        first_day_of_this_month, last_day_of_this_month = get_custom_date_range(json_data['begin_date'], 0, 0)

        # 上個月第一天~上個月最後一天
        first_day_of_last_month, last_day_of_last_month = get_custom_date_range(json_data['begin_date'], -1, -1)

        # 定義可能的參數列表
        params_mapping = {
        }

        # 處理 dealers_code 數組
        if 'dealers_code' in json_data and json_data['dealers_code']:
            if isinstance(json_data['dealers_code'], list) and len(json_data['dealers_code']) > 0:
                # 如果是代碼列表，則構造 SQL 條件以匹配任一代碼
                dealer_code_conditions = [f"C.AGENT_CODE2015 = '{dealer_code}'" for dealer_code in json_data['dealers_code']]
                sql_conditions.append(f"({' OR '.join(dealer_code_conditions)})")
            elif isinstance(json_data['dealers_code'], str):
                # 如果是單個代碼，則直接構造條件
                sql_conditions.append(f"C.AGENT_CODE2015 = '{json_data['dealers_code']}'")

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" C.AGENT_CODE2015 = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍起
        if 'begin_date' in json_data and json_data['begin_date'] is not None:
            # 上個月
            params['qP_BEGIN_DATE'] = first_day_of_last_month
            params2['qP_BEGIN_DATE'] = first_day_of_last_month
            # 本月
            params['qT_BEGIN_DATE'] = first_day_of_this_month
            params2['qT_BEGIN_DATE'] = first_day_of_this_month

        # 處理日期範圍迄
        if 'end_date' in json_data and json_data['end_date'] is not None:
            # 上個月
            params['qP_END_DATE'] = last_day_of_last_month
            params2['qP_END_DATE'] = last_day_of_last_month
            # 本月
            params['qT_END_DATE'] = last_day_of_this_month
            params2['qT_END_DATE'] = last_day_of_this_month

        try:

            try:
                with connection.cursor() as cursor:
                    # print(select_begin_end_date_sql_1)
                    # 檢查臨時表是否存在，如果不存在則創建
                    cursor.execute(select_begin_end_date_sql_1)

                    params_sql_1 = {
                        'qBEGIN_DATE': first_day_of_last_month,
                        'qEND_DATE': last_day_of_this_month,
                    }

                    # print(select_begin_end_date_sql_2)
                    # print(params_sql_1)

                    # 每次查詢之前，清空臨時表中的數據，並插入更新臨時表中的數據
                    cursor.execute(select_begin_end_date_sql_2, params_sql_1)

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


            result = select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk)
            # print(result)
            return result
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' PALLET_ACCOUNT SELECT END '''