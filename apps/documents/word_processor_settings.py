# -*- coding: utf-8 -*-
"""
Word 處理器設置
處理 Django 設置和環境配置
"""

import os
import tempfile

# 嘗試從 Django 獲取設置
try:
    from django.conf import settings as django_settings
    BASE_DIR = getattr(django_settings, 'BASE_DIR', os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    DEBUG = getattr(django_settings, 'DEBUG', False)
except:
    # 如果 Django 不可用，使用默認設置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    DEBUG = False

# Word 處理器特定設置
WORD_PROCESSOR_SETTINGS = {
    # 基本路徑
    'BASE_DIR': BASE_DIR,
    'TEMP_DIR': os.path.join(tempfile.gettempdir(), 'word_processor'),
    'CACHE_DIR': os.path.join(BASE_DIR, '.word_processor_cache'),
    'LOG_DIR': os.path.join(BASE_DIR, 'logs'),
    
    # 緩存設置
    'CACHE_BACKEND': 'auto',  # 'auto', 'file', 'memory', 'django'
    'CACHE_TIMEOUT': 3600,    # 默認緩存時間（秒）
    
    # 性能設置
    'MAX_WORKERS': 20,        # 最大 Word 實例數
    'TASK_TIMEOUT': 300,      # 任務超時時間（秒）
    'MONITOR_INTERVAL': 30,   # 監控間隔（秒）
    
    # 調試設置
    'DEBUG': DEBUG,
    'LOG_LEVEL': 'DEBUG' if DEBUG else 'INFO',
}

# 確保必要的目錄存在
for dir_key in ['TEMP_DIR', 'CACHE_DIR', 'LOG_DIR']:
    dir_path = WORD_PROCESSOR_SETTINGS[dir_key]
    if not os.path.exists(dir_path):
        try:
            os.makedirs(dir_path)
        except:
            pass

def get_setting(key, default=None):
    """獲取設置值"""
    return WORD_PROCESSOR_SETTINGS.get(key, default)

def update_setting(key, value):
    """更新設置值"""
    WORD_PROCESSOR_SETTINGS[key] = value

# 導出常用設置
TEMP_DIR = get_setting('TEMP_DIR')
CACHE_DIR = get_setting('CACHE_DIR')
LOG_DIR = get_setting('LOG_DIR')
MAX_WORKERS = get_setting('MAX_WORKERS')
TASK_TIMEOUT = get_setting('TASK_TIMEOUT')