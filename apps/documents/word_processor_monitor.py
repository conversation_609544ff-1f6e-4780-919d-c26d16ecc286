# -*- coding: utf-8 -*-
"""
Word 處理器監控和管理工具
提供系統狀態監控、性能分析和動態調整功能
"""

import json
import logging
import os
import psutil
import time
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.core.cache import cache
from .word_enterprise_processor import enterprise_processor
from .word_processor_config import (
    get_config, apply_load_profile, RESOURCE_LIMITS
)

class ProcessorMonitor:
    """處理器監控類"""
    
    def __init__(self):
        self.stats_history = []
        self.alert_history = []
        self.start_time = datetime.now()
    
    def collect_stats(self):
        """收集系統統計信息"""
        stats = {
            'timestamp': datetime.now().isoformat(),
            'uptime': str(datetime.now() - self.start_time),
            'processor': self._get_processor_stats(),
            'system': self._get_system_stats(),
            'performance': self._get_performance_stats(),
        }
        
        self.stats_history.append(stats)
        
        # 保持最近 24 小時的歷史記錄
        cutoff = datetime.now() - timedelta(hours=24)
        self.stats_history = [
            s for s in self.stats_history 
            if datetime.fromisoformat(s['timestamp']) > cutoff
        ]
        
        return stats
    
    def _get_processor_stats(self):
        """獲取處理器統計信息"""
        if hasattr(enterprise_processor, 'worker_pool'):
            pool = enterprise_processor.worker_pool
            return {
                'available_workers': pool.workers.qsize(),
                'total_workers': pool.pool_size,
                'pending_tasks': enterprise_processor.task_queue.qsize(),
                'processing_tasks': len(enterprise_processor.processing_tasks),
                'worker_stats': dict(pool.worker_stats),
            }
        return {}
    
    def _get_system_stats(self):
        """獲取系統資源統計"""
        process = psutil.Process()
        return {
            'cpu_percent': process.cpu_percent(interval=1),
            'memory_usage_mb': process.memory_info().rss / 1024 / 1024,
            'memory_percent': process.memory_percent(),
            'num_threads': process.num_threads(),
            'disk_usage': self._get_disk_usage(),
        }
    
    def _get_disk_usage(self):
        """獲取磁盤使用情況"""
        cache_dir = getattr(enterprise_processor.file_cache, 'cache_dir', None)
        if cache_dir and os.path.exists(cache_dir):
            total_size = 0
            file_count = 0
            for root, dirs, files in os.walk(cache_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                    file_count += 1
            return {
                'cache_size_mb': total_size / 1024 / 1024,
                'cache_files': file_count,
            }
        return {}
    
    def _get_performance_stats(self):
        """獲取性能統計"""
        # 從緩存中獲取性能數據
        perf_data = cache.get('word_processor_performance', {})
        return {
            'avg_processing_time': perf_data.get('avg_time', 0),
            'success_rate': perf_data.get('success_rate', 0),
            'cache_hit_rate': perf_data.get('cache_hit_rate', 0),
            'throughput': perf_data.get('throughput', 0),
        }
    
    def check_alerts(self, stats):
        """檢查是否需要發出警報"""
        alerts = []
        
        # 檢查可用工作執行緒
        processor_stats = stats.get('processor', {})
        available_workers = processor_stats.get('available_workers', 0)
        if available_workers < 2:
            alerts.append({
                'level': 'WARNING',
                'message': f'可用工作執行緒不足: {available_workers}',
                'timestamp': datetime.now().isoformat(),
            })
        
        # 檢查待處理任務
        pending_tasks = processor_stats.get('pending_tasks', 0)
        if pending_tasks > 50:
            alerts.append({
                'level': 'WARNING',
                'message': f'待處理任務過多: {pending_tasks}',
                'timestamp': datetime.now().isoformat(),
            })
        
        # 檢查系統資源
        system_stats = stats.get('system', {})
        cpu_percent = system_stats.get('cpu_percent', 0)
        if cpu_percent > RESOURCE_LIMITS['max_cpu_percentage']:
            alerts.append({
                'level': 'ERROR',
                'message': f'CPU 使用率過高: {cpu_percent}%',
                'timestamp': datetime.now().isoformat(),
            })
        
        memory_mb = system_stats.get('memory_usage_mb', 0)
        max_memory_mb = RESOURCE_LIMITS['max_memory_usage_gb'] * 1024
        if memory_mb > max_memory_mb:
            alerts.append({
                'level': 'ERROR',
                'message': f'記憶體使用過高: {memory_mb:.2f}MB',
                'timestamp': datetime.now().isoformat(),
            })
        
        # 記錄警報
        self.alert_history.extend(alerts)
        
        return alerts
    
    def auto_scale(self, stats):
        """根據負載自動調整系統配置"""
        processor_stats = stats.get('processor', {})
        pending_tasks = processor_stats.get('pending_tasks', 0)
        available_workers = processor_stats.get('available_workers', 0)
        
        # 根據負載調整配置
        if pending_tasks > 100 and available_workers < 5:
            # 高負載
            apply_load_profile('peak')
            logging.info("切換到高峰期配置")
        elif pending_tasks < 10 and available_workers > 15:
            # 低負載
            apply_load_profile('low')
            logging.info("切換到低峰期配置")
        else:
            # 正常負載
            apply_load_profile('normal')
    
    def generate_report(self):
        """生成監控報告"""
        if not self.stats_history:
            return "暫無統計數據"
        
        latest_stats = self.stats_history[-1]
        report = []
        
        report.append("=== Word 處理器監控報告 ===")
        report.append(f"報告時間: {latest_stats['timestamp']}")
        report.append(f"運行時間: {latest_stats['uptime']}")
        
        # 處理器狀態
        processor = latest_stats.get('processor', {})
        report.append("\n處理器狀態:")
        report.append(f"  可用工作執行緒: {processor.get('available_workers', 0)}/{processor.get('total_workers', 0)}")
        report.append(f"  待處理任務: {processor.get('pending_tasks', 0)}")
        report.append(f"  處理中任務: {processor.get('processing_tasks', 0)}")
        
        # 系統資源
        system = latest_stats.get('system', {})
        report.append("\n系統資源:")
        report.append(f"  CPU 使用率: {system.get('cpu_percent', 0):.1f}%")
        report.append(f"  記憶體使用: {system.get('memory_usage_mb', 0):.1f}MB ({system.get('memory_percent', 0):.1f}%)")
        report.append(f"  執行緒數: {system.get('num_threads', 0)}")
        
        # 性能指標
        performance = latest_stats.get('performance', {})
        report.append("\n性能指標:")
        report.append(f"  平均處理時間: {performance.get('avg_processing_time', 0):.2f}秒")
        report.append(f"  成功率: {performance.get('success_rate', 0):.1f}%")
        report.append(f"  緩存命中率: {performance.get('cache_hit_rate', 0):.1f}%")
        report.append(f"  吞吐量: {performance.get('throughput', 0):.1f} 文件/分鐘")
        
        # 最近警報
        recent_alerts = self.alert_history[-10:]
        if recent_alerts:
            report.append("\n最近警報:")
            for alert in recent_alerts:
                report.append(f"  [{alert['level']}] {alert['timestamp']}: {alert['message']}")
        
        return "\n".join(report)

# Django 管理命令
class Command(BaseCommand):
    help = '監控 Word 處理器狀態'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--interval',
            type=int,
            default=60,
            help='監控間隔（秒）'
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='生成報告並退出'
        )
    
    def handle(self, *args, **options):
        monitor = ProcessorMonitor()
        
        if options['report']:
            # 生成報告模式
            stats = monitor.collect_stats()
            print(monitor.generate_report())
            return
        
        # 持續監控模式
        interval = options['interval']
        self.stdout.write(f"開始監控 Word 處理器，間隔: {interval}秒")
        
        try:
            while True:
                stats = monitor.collect_stats()
                alerts = monitor.check_alerts(stats)
                
                # 顯示當前狀態
                processor = stats.get('processor', {})
                system = stats.get('system', {})
                
                self.stdout.write(
                    f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] "
                    f"Workers: {processor.get('available_workers', 0)}/{processor.get('total_workers', 0)} | "
                    f"Tasks: {processor.get('pending_tasks', 0)} | "
                    f"CPU: {system.get('cpu_percent', 0):.1f}% | "
                    f"Memory: {system.get('memory_usage_mb', 0):.1f}MB"
                )
                
                # 顯示警報
                for alert in alerts:
                    self.stdout.write(
                        self.style.WARNING(f"[{alert['level']}] {alert['message']}")
                    )
                
                # 自動調整
                monitor.auto_scale(stats)
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write("\n監控已停止")
            self.stdout.write(monitor.generate_report())

# 便利函數
def get_processor_status():
    """獲取處理器當前狀態"""
    monitor = ProcessorMonitor()
    return monitor.collect_stats()

def get_processor_report():
    """獲取處理器報告"""
    monitor = ProcessorMonitor()
    monitor.collect_stats()
    return monitor.generate_report()