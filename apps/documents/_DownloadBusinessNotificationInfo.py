import itertools
import json
import logging
import os
import re
import shutil
import uuid
import time
import glob  # 添加這行來導入 glob 模塊
from contextlib import contextmanager
import pythoncom
import win32com.client

# 導入 COM 初始化修復
try:
    from .word_com_initializer import ensure_com_initialized, com_context, create_word_app_safe
    from .word_environment_fixer import fix_word_environment
except ImportError:
    # 如果導入失敗，提供基本的 fallback
    def ensure_com_initialized():
        try:
            pythoncom.CoInitialize()
        except:
            pass

    from contextlib import contextmanager

    @contextmanager
    def com_context():
        ensure_com_initialized()
        yield

    def create_word_app_safe():
        return win32com.client.Dispatch("Word.Application")

    def fix_word_environment():
        logging.warning("Word 環境修復功能不可用")
        return False
from django.conf import settings
from django.db import connection
from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from rest_framework import status
from django.http import FileResponse, HttpResponseNotFound
from django.utils.encoding import escape_uri_path
from urllib.parse import quote

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params
from .word_queue_processor import process_word_task
from .word_enterprise_processor import process_word_document_enterprise
from .word_safe_processor import process_document_with_safety
from .word_simple_processor import process_document_simple
from .word_direct_processor import process_document_direct
from .word_thread_safe_processor import process_document_thread_safe
from .word_com_safe_processor import process_document_com_safe

# 設置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def file_path(file_name, serial_number):
    """
    生成文件的完整路徑
    :param file_name: 文件名
    :param serial_number: 序列號
    :return: 完整的文件路徑
    """
    folder_path = os.path.join(settings.BASE_DIR, 'uploads', '業務通報', serial_number)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
    return os.path.join(folder_path, file_name)


# SQL 查詢語句
DOWNLOAD_BPUDCHT_SQL = """
                       SELECT ATTACHMENT_NAME, FILEPASSWORD
                       FROM (SELECT FILEARTICLE ATTACHMENT_NAME, FILEPASSWORD \
                             FROM BPUDCHT)
                       WHERE 1 = 1 \
                       """

DOWNLOAD_ATTACHMENT_SQL = """
                          SELECT ATTACHMENT_NAME, '' FILEPASSWORD
                          FROM BPUDCHT_ATTACHMENT A
                          WHERE 1 = 1 \
                          """


def fetch_from_db(sql, params=None):
    """
    執行 SQL 查詢並返回結果
    :param sql: SQL 查詢語句
    :param params: 查詢參數
    :return: 查詢結果
    """
    with connection.cursor() as cursor:
        cursor.execute(sql, params or {})
        return cursor.fetchall()


def get_user_role(user_id):
    """
    獲取使用者角色
    :param user_id: 使用者 ID
    :return: 使用者角色
    """
    sql = """
          SELECT SYSROLE
          FROM USERS
          WHERE USERID = :qUSERID \
          """
    data = fetch_from_db(sql, {'qUSERID': user_id})
    return data[0][0] if data else None


def get_file_statistics(user_id, pudcno):
    """
    獲取文件統計信息
    :param user_id: 使用者 ID
    :param pudcno: PUDCNO
    :return: 文件統計信息字典
    """
    sql = """
          SELECT REMARKNUMBER, REMARKVALUE
          FROM BPUDCHT_FILESTATISTICS
          WHERE PUDCNO = :qPUDCNO
            AND DEALER = :qUSERID
          ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0') \
          """
    data = fetch_from_db(sql, {'qUSERID': user_id, 'qPUDCNO': pudcno})
    return {row[0]: row[1] for row in data} if data else {}


@contextmanager
def word_application():
    """
    創建 Word 應用程序的上下文管理器，確保自動清理
    """
    app = None
    retry_count = 0
    max_retries = 2

    while retry_count <= max_retries:
        try:
            pythoncom.CoInitialize()
            # 嘗試使用安全的創建方法
            try:
                app = create_word_app_safe()
            except Exception as safe_error:
                logging.warning(f"安全創建方法失敗: {str(safe_error)}")
                # 回退到傳統方法
                try:
                    app = win32com.client.DispatchEx("Word.Application")
                except:
                    app = win32com.client.Dispatch("Word.Application")

                app.Visible = False
                app.DisplayAlerts = 0

            yield app
            break  # 成功創建，跳出重試循環

        except Exception as e:
            error_msg = str(e)
            logging.error(f"Word 應用程序創建失敗 (嘗試 {retry_count + 1}/{max_retries + 1}): {error_msg}")

            # 檢查是否是 Normal.dotm 相關錯誤
            if "Normal.dotm" in error_msg and retry_count < max_retries:
                logging.info("檢測到 Normal.dotm 問題，嘗試自動修復...")
                try:
                    if fix_word_environment():
                        logging.info("環境修復成功，重試創建 Word 應用程序")
                        retry_count += 1
                        continue
                    else:
                        logging.error("環境修復失敗")
                except Exception as fix_error:
                    logging.error(f"環境修復過程中出錯: {str(fix_error)}")

            if retry_count >= max_retries:
                logging.error("已達到最大重試次數，返回 None")
                yield None
                break
            else:
                retry_count += 1
                time.sleep(1)  # 等待 1 秒後重試

    # 清理資源
    if app:
        try:
            # 關閉所有文檔
            for doc in app.Documents:
                try:
                    doc.Close(SaveChanges=False)
                except:
                    pass
            app.Quit()
        except Exception as e:
            logging.warning(f"關閉 Word 應用程序失敗: {str(e)}")
    try:
        pythoncom.CoUninitialize()
    except:
        pass


def unprotect_and_protect_docx_worker(file_path, file_name, pudcno, user_id, file_password, word_app=None):
    """
    在佇列中執行的文件處理函數
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param pudcno: PUDCNO
    :param user_id: 使用者 ID
    :param file_password: 文件密碼
    :param word_app: Word 應用程序實例（由佇列處理器提供）
    :return: 新的文件路徑和文件名
    """
    if not word_app:
        logging.error("未提供 Word 應用程序實例")
        return None, None

    if file_password is None:
        try:
            new_path, new_name = modify_file_content(user_id, pudcno, file_path, file_name, word_app)
            return new_path, new_name
        except Exception as e:
            logging.error("錯誤: " + str(e))
            import traceback
            logging.debug(traceback.format_exc())
            return None, None
    else:
        doc = None
        try:
            # 打開受密碼保護的文件
            doc = word_app.Documents.Open(FileName=file_path, PasswordDocument=file_password)

            # 檢查保護類型
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
            except:
                pass

            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                except Exception as e:
                    logging.warning(f"解除保護時發生錯誤: {str(e)}")

            # 保存解密後的文件
            temp_uuid = str(uuid.uuid4())
            unprotected_path = f"{file_path}.{temp_uuid}.unprotected.docx"
            doc.SaveAs2(FileName=unprotected_path, FileFormat=16)
            doc.Close()
            doc = None

            # 修改文件內容
            new_path, new_name = modify_file_content(user_id, pudcno, unprotected_path, file_name, word_app)

            if new_path and os.path.exists(new_path):
                # 重新保護文件
                doc = word_app.Documents.Open(FileName=new_path)

                # 檢查保護類型
                protection_type = -1
                try:
                    protection_type = doc.ProtectionType
                except:
                    pass

                if protection_type == -1:
                    try:
                        doc.Protect(Type=3, Password=file_password)
                    except Exception as e:
                        logging.warning(f"重新保護時發生錯誤: {str(e)}")

                protected_new_path = f"{new_path}.{temp_uuid}.protected.docx"
                doc.SaveAs2(FileName=protected_new_path, FileFormat=16)
                doc.Close()
                doc = None

                # 清理臨時文件
                if os.path.exists(new_path):
                    os.remove(new_path)
                if os.path.exists(unprotected_path):
                    os.remove(unprotected_path)
                os.rename(protected_new_path, new_path)

                return new_path, new_name
            else:
                # 清理臨時文件
                if os.path.exists(unprotected_path):
                    os.remove(unprotected_path)
                return None, None

        except Exception as e:
            logging.error("錯誤: " + str(e))
            import traceback
            logging.debug(traceback.format_exc())
            return None, None
        finally:
            # 確保文檔被關閉
            if doc:
                try:
                    doc.Close()
                except:
                    pass


def unprotect_and_protect_docx(file_path, file_name, pudcno, user_id, file_password, modify_func):
    """
    解除文件保護，修改內容，然後重新保護文件
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param pudcno: PUDCNO
    :param user_id: 使用者 ID
    :param file_password: 文件密碼
    :param modify_func: 修改文件的函數
    :return: 新的文件路徑和文件名
    """
    # 使用 COM 安全的處理器，完全避免 COM 線程模型錯誤
    try:
        result = process_document_com_safe(
            file_path, file_name, pudcno, user_id, file_password, modify_func
        )
        if result and result[0]:
            return result
        else:
            logging.error("COM 安全處理器返回空結果")
            return None, None
    except Exception as e:
        logging.error(f"COM 安全處理器失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None


def modify_file_content(user_id, pudcno, file_path, file_name, word_app):
    """
    修改文件內容
    :param user_id: 使用者 ID
    :param pudcno: PUDCNO
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param word_app: Word 應用程序實例
    :return: 新的文件路徑和文件名
    """
    notes_data = get_file_statistics(user_id, pudcno)
    doc = word_app.Documents.Open(file_path)

    try:
        # 處理段落
        for paragraph in doc.Paragraphs:
            replace_and_format(paragraph, notes_data)

        # 處理表格
        for table in doc.Tables:
            for cell in table.Range.Cells:
                replace_and_format(cell.Range, notes_data)

        # 保存修改後的文件
        base_name, ext = os.path.splitext(file_name)
        new_file_name = f"{user_id}_{base_name}{ext}"
        new_file_path = os.path.join(os.path.dirname(file_path), new_file_name)
        doc.SaveAs(new_file_path)
        doc.Close()

        return new_file_path, new_file_name

    except Exception as e:
        logging.error("錯誤: " + str(e))
        import traceback
        logging.debug(traceback.format_exc())
    finally:
        pass


def replace_and_format(paragraph_or_cell, notes_data):
    """
    替換和格式化文本內容
    :param paragraph_or_cell: 段落或單元格對象
    :param notes_data: 註釋數據
    """
    if hasattr(paragraph_or_cell, 'Range'):
        text_content = paragraph_or_cell.Range.Text
        target_range = paragraph_or_cell.Range
    else:
        return

    for note_id, real_data in sorted(notes_data.items(), key=lambda x: len(x[0]), reverse=True):
        for pattern in [
            f'(註{note_id})',
            f'（註{note_id}）',
            f'註{note_id}',
            f'(註 {note_id})',
            f'註{note_id}點',
            f'註{note_id}元',
            f'註{note_id}%',
        ]:
            try:
                while pattern in text_content:
                    logging.info(f"找到模式: {pattern}")
                    if pattern == f'註{note_id}':
                        formatted_text = real_data
                    else:
                        formatted_text = f'({real_data})'
                    format_replacement(target_range, pattern, formatted_text)
                    text_content = target_range.Text
                    target_range = paragraph_or_cell.Range
            except Exception as e:
                logging.debug(f"替換模式時發生錯誤: {str(e)}")
                continue


def format_replacement(paragraph_or_cell_range, pattern, replacement_text):
    """
    執行文本替換和格式化
    :param paragraph_or_cell_range: 段落或單元格範圍
    :param pattern: 要替換的模式
    :param replacement_text: 替換文本
    """
    search_range = paragraph_or_cell_range.Duplicate
    search_range.Find.Text = pattern
    search_range.Find.Execute()
    while search_range.Find.Found:
        search_range.Text = replacement_text
        search_range.Font.Name = "微軟正黑體"
        search_range.Font.Size = 12
        search_range.Font.Bold = True
        search_range.Collapse(Direction=2)
        search_range.Find.Text = pattern
        search_range.Find.Execute()


@validate_access_token_and_params(None)
def select_business_notification_download(request, json_data, role, user_id):
    """
    處理業務通知下載請求
    :param request: HTTP 請求
    :param json_data: JSON 格式的請求數據
    :param role: 使用者角色
    :param user_id: 使用者 ID
    :return: 文件下載響應或錯誤信息
    """
    if request.method == "POST":
        pudcno = json_data['pudcno']
        noModifyFile = json_data['noModifyFile']

        strCond = ''
        condition = {}
        params = {
            "file_name": "ATTACHMENT_NAME",
        }
        for param, field in params.items():
            if param in json_data and json_data[param]:
                condition[f'q{param.upper()}'] = json_data[param]
                strCond += f' AND {field} = :{f"q{param.upper()}"} '

        sSTM = DOWNLOAD_BPUDCHT_SQL + strCond + 'UNION ALL' + DOWNLOAD_ATTACHMENT_SQL + strCond
        data = fetch_from_db(sSTM, condition)

        if not data:
            return "找不到資料", status.HTTP_404_NOT_FOUND

        file_name = data[0][0]
        file_url = file_path(file_name, pudcno)
        file_password = data[0][1]

        if noModifyFile == 'N':
            file_password = None

        if (role == '0') and get_file_statistics(user_id, pudcno) and (noModifyFile == 'Y'):
            result = unprotect_and_protect_docx(file_url, file_name, pudcno, user_id, file_password,
                                                modify_file_content)
            if result and result[0] and result[1]:
                file_url, file_name = result
            else:
                logging.error(f"文件處理失敗")
                return "文件處理失敗", status.HTTP_500_INTERNAL_SERVER_ERROR

        if not os.path.exists(file_url):
            logging.error(f"找不到文件: {file_url}")
            return "找不到檔案", status.HTTP_404_NOT_FOUND

        response = FileResponse(open(file_url, 'rb'))
        file_display_name = quote(file_name.encode('utf-8'))
        response["Content-Disposition"] = f"attachment; filename*=UTF-8''{file_display_name}"

        return response, status.HTTP_200_OK


@validate_access_token_and_params('pudcno')
def select_business_notification_file_statistics(request, json_data, role, user_id):
    """
    獲取業務通知文件統計信息
    :param request: HTTP 請求
    :param json_data: JSON 格式的請求數據
    :param role: 使用者角色
    :param user_id: 使用者 ID
    :return: 文件統計信息或錯誤信息
    """
    context = "select_business_notification_file_statistics"

    if request.method == "POST":
        try:
            sql_conditions = []
            params = {}

            params_mapping = {
                'pudcno': 'PUDCNO',
                'user_id': 'DEALER',
            }

            for param, db_field in params_mapping.items():
                if param in json_data and json_data[param]:
                    sql_conditions.append(f" {db_field} = :{param}")
                    params[param] = json_data[param]

            if role == '0':
                sql_conditions.append(f" DEALER = :user_id")
                params['user_id'] = user_id

            sql_query = f""" 
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM BPUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND {' AND '.join(sql_conditions)} 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            """

            data = fetch_from_db(sql_query, params)

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
            else:
                result = []

                for row in data:
                    result.append({
                        'DEALER': row[0],
                        'REMARKNUMBER': row[1],
                        'REMARKVALUE': row[2]
                    })
                return result, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)


# 定義每個 rout 對應的貨號和價格位置
rout_positions = {
    '10106': {'product_code_col': 1, 'price_col': 3},
    '10212': {'product_code_col': 1, 'price_col': 3},
    '10247': {'product_code_col': 1, 'price_col': 2},
    '10228': {'product_code_col': 1, 'price_col': 2},
    '11201': {'product_code_col': 1, 'price_col': 2},
    '80901': {'product_code_col': 1, 'price_col': 2},
    '80902': {'product_code_col': 1, 'price_col': 3},
    '80904': {'product_code_col': 1, 'price_col': 2},
    '80905': {'product_code_col': 1, 'price_col': 2},
}


def is_valid_price(value):
    """
    檢查價格是否為有效的數字或None
    :param value: 待檢查的價格值
    :return: 布爾值，表示價格是否有效
    """
    return value is None or bool(re.match(r'^\d+(\.\d+)?$', value))


def is_black_or_automatic(font):
    """
    檢查字體顏色是否為黑色或自動（通常表示為黑色）
    :param font: 字體對象
    :return: 布爾值，表示是否為黑色或自動顏色
    """
    try:
        # 使用硬編碼的常數值，避免 COM 常數訪問問題
        # wdColorAutomatic = -16777216 (0xFF000000)
        # 黑色 = 0
        color = font.Color
        return color == -16777216 or color == 0 or color == 16777215
    except Exception as e:
        logging.warning(f"無法獲取字體顏色，假設為黑色: {str(e)}")
        return True  # 預設為黑色


def get_cell_text_and_color(table, row, col):
    """
    獲取指定單元格的文本和顏色信息，保留換行符
    :param table: 表格對象
    :param row: 行索引
    :param col: 列索引
    :return: 元組，包含單元格文本和是否為黑色
    """
    try:
        cell = table.Cell(Row=row, Column=col)

        # 獲取文本內容
        text = cell.Range.Text.strip(chr(7)).strip() if cell.Range.Text else ""

        # 檢查字體顏色
        is_black = is_black_or_automatic(cell.Range.Font)

        return text, is_black

    except Exception as e:
        # 當單元格不存在或合併時，簡單返回 None
        logging.debug(f"無法獲取單元格 ({row}, {col}): {str(e)}")
        return None, False


def query_product_code(rout, product_number, user_id):
    """
    根據路線和產品編號查詢產品代碼
    :param rout: 路線
    :param product_number: 產品編號
    :return: 查詢到的產品代碼
    """
    # 統一轉換為大寫並移除空格，確保查詢的一致性
    product_number = product_number.replace(" ", "").upper()
    with connection.cursor() as cursor:
        # 使用 UPPER 函數確保資料庫查詢時也是大寫比較
        query = "SELECT TRIM(APITNO) FROM PROD_CODE_MAP WHERE APROUT = :qrout AND UPPER(REPLACE(APOHIT, ' ', '')) = :product_number AND APVENN = :qveen"
        condition = {
            'qrout': rout,
            'product_number': product_number,
            'qveen': user_id
        }
        cursor.execute(query, condition)
        result = cursor.fetchone()
        return result[0] if result else product_number


def extract_table_data(doc, rout, user_id):
    """
    從文件中提取表格數據，支援處理單格包含多個品號的情況
    :param doc: Word文件對象
    :param rout: 路線
    :param user_id: 使用者ID
    :return: 提取的表格數據列表
    """
    # 記錄函數調用參數
    logging.info(f"extract_table_data 被調用，rout: '{rout}', user_id: '{user_id}'")

    data = []
    previous_product_code = None
    previous_price = None
    previous_unit_price = None
    previous_case_price = None
    previous_unit_price_unit = None

    # 驗證 rout 參數
    if not isinstance(rout, str):
        logging.error(f"rout 參數不是字符串類型: {type(rout)}, 值: {rout}")
        return data

    if rout not in rout_positions:
        logging.error(f"未定義 {rout} 的位置，可用的路線: {list(rout_positions.keys())}")
        return data

    positions = rout_positions[rout]
    product_code_col = positions['product_code_col']
    price_col = positions['price_col']

    # 10106 特殊處理：追蹤跨行的產品代碼
    last_valid_product_code = None if rout != '10106' else None
    
    for table in doc.Tables:
        
        for row_index in range(1, table.Rows.Count + 1):
            try:
                product_code_result = get_cell_text_and_color(table, row_index, product_code_col)
                
                # 10106 特殊處理：合併儲存格
                if rout == '10106':
                    if product_code_result and product_code_result[0]:
                        # 有產品代碼，更新記錄
                        product_code_text, is_black = product_code_result
                        last_valid_product_code = product_code_text
                    elif last_valid_product_code:
                        # 沒有產品代碼但有上一個有效代碼，使用它
                        product_code_text = last_valid_product_code
                        is_black = True
                        logging.debug(f'行 {row_index}: 使用上一個產品代碼（10106合併儲存格）')
                    else:
                        # 沒有產品代碼也沒有上一個有效代碼，跳過
                        continue
                else:
                    # 其他路線：維持原有邏輯
                    if product_code_result is None or product_code_result[0] is None:
                        continue
                    product_code_text, is_black = product_code_result
                logging.debug(f'原始產品代碼文本 (行 {row_index}): {repr(product_code_text)}')

                # 處理多行產品代碼：檢查是否有換行符號
                if product_code_text and ('\r' in product_code_text or '\n' in product_code_text or '\x0b' in product_code_text):
                    logging.debug(f'發現多行產品代碼，開始分割處理')
                    # 處理有多個品號的情況
                    product_lines = product_code_text.replace('\r\n', '\r').replace('\n', '\r').replace('\x0b', '\r').split('\r')
                    logging.debug(f'分割後的產品行數: {len(product_lines)}, 內容: {product_lines}')

                    # 簡化邏輯：嘗試共享價格方式
                    price_result = get_cell_text_and_color(table, row_index, price_col)
                    price_text = price_result[0] if price_result and price_result[0] else ""
                    price, unit, unit_price, unit_price_unit, previous_unit_price_unit, case_price = process_price(rout,
                                                                                                                   price_text,
                                                                                                                   previous_unit_price_unit)
                    # 逐一處理每個品號
                    for product_line in product_lines:
                        product_line = product_line.strip()
                        logging.debug(f'處理產品行: {product_line}')
                        if not product_line:
                            continue

                        # 根據路線處理不同格式的品號
                        if rout == '10106':
                            if product_line.isdigit():
                                product_number = product_line
                                product_code = query_product_code(rout, product_number, user_id)
                                # 更新最後有效的產品代碼（用於合併儲存格）
                                if product_code:
                                    last_valid_product_code = product_code
                            else:
                                continue
                        else:
                            original_product_line = product_line.strip()
                            # 統一轉換為大寫，避免大小寫不一致的問題
                            product_code = original_product_line.upper()
                            logging.debug(f'查詢前產品代碼: {product_code}')
                            product_code = query_product_code(rout, product_code, user_id)
                            logging.debug(f'查詢後產品代碼: {product_code}')

                        # 只有當品項代號長度大於等於4時才添加
                        logging.debug(
                            f'產品代碼: {product_code}, 價格: {price}, 單位: {unit}, 單價: {unit_price}, 單價單位: {unit_price_unit}, 箱價: {case_price}')

                        # 檢查產品代碼是否異常長
                        if product_code and len(product_code) > 100:
                            logging.error(f'發現異常長的產品代碼: {product_code[:100]}...')
                            logging.error(f'產品代碼長度: {len(product_code)}')
                            continue  # 跳過這個異常的產品代碼

                        if product_code and len(product_code) >= 4 and (price or unit_price or case_price):
                            entry_data = [
                                product_code,
                                price if price and re.match(r'^[\d\.]+$', price) and (
                                    '箱' in (unit or '') if rout != '10106' else True) else '',
                                unit if price and re.match(r'^[\d\.]+$', price) and (
                                    '箱' in (unit or '') if rout != '10106' else True) else '',
                                unit_price if unit_price_unit and '入' in unit_price_unit else '',
                                unit_price_unit if unit_price_unit and '入' in unit_price_unit else '',
                                case_price,
                                unit if case_price else ''
                            ]

                            # 檢查整個條目是否有異常長的字符串
                            for i, value in enumerate(entry_data):
                                if value and isinstance(value, str) and len(value) > 100:
                                    logging.error(f'發現異常長的數據值 (列{i}): {value[:100]}...')

                            data.append(entry_data)
                            logging.debug(f'添加數據: {data[-1]}')
                else:
                    # 處理單一品號的情況（原有邏輯）
                    if rout == '10106':
                        if product_code_text and product_code_text.isdigit():
                            product_number = product_code_text
                            product_code = query_product_code(rout, product_number, user_id)
                            # 更新最後有效的產品代碼
                            if product_code:
                                last_valid_product_code = product_code
                        else:
                            # 對於10106，非數字的產品代碼仍然跳過
                            continue

                        price_result = get_cell_text_and_color(table, row_index, price_col)
                        price_text = price_result[0] if price_result and price_result[0] else ""
                        price, unit, unit_price, unit_price_unit, previous_unit_price_unit, case_price = process_price(
                            rout, price_text, previous_unit_price_unit)

                        price = price or previous_price
                        unit_price = unit_price or previous_unit_price
                        unit_price_unit = unit_price_unit or previous_unit_price_unit

                        if product_code and unit_price_unit is None and previous_unit_price_unit is None:
                            unit_price = None
                            unit_price_unit = None

                        case_price = case_price or previous_case_price
                        unit = unit or '元/箱'

                        # 只有當品項代號長度大於等於4時才添加
                        if product_code and len(product_code) >= 4 and (price or unit_price or case_price):
                            data.append([
                                product_code,
                                price if price and re.match(r'^\d+$', price) else '',
                                unit if price and re.match(r'^\d+$', price) else '',
                                unit_price,
                                unit_price_unit,
                                case_price,
                                unit
                            ])
                            previous_product_code = product_code
                            previous_price = price
                            previous_unit_price = unit_price
                            previous_case_price = case_price
                            previous_unit_price_unit = unit_price_unit

                    elif rout in ['10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']:
                        product_code = product_code_text
                        if product_code:
                            product_code = product_code.strip()
                            # 統一轉換為大寫，避免大小寫不一致的問題
                            product_code = product_code.upper()
                            original_product_code = product_code
                            product_code = query_product_code(rout, product_code, user_id)

                            # 移除產品描述檢查限制，因為有些店型態的產品代碼本身就包含這些關鍵字
                        else:
                            continue

                        price_result = get_cell_text_and_color(table, row_index, price_col)
                        price_text = price_result[0] if price_result and price_result[0] else ""
                        price, unit, unit_price, unit_price_unit, _, case_price = process_price(rout, price_text)

                        # 只有當品項代號長度大於等於4時才添加
                        if product_code and len(product_code) >= 4 and (
                                price is not None or unit_price is not None or case_price is not None):
                            data.append([
                                product_code,
                                price if price and re.match(r'^[\d\.]+$', price) and '箱' in (unit or '') else '',
                                unit if price and re.match(r'^[\d\.]+$', price) and '箱' in (unit or '') else '',
                                unit_price if unit_price_unit and '入' in unit_price_unit else '',
                                unit_price_unit if unit_price_unit and '入' in unit_price_unit else '',
                                case_price,
                                unit if case_price else ''
                            ])
                            previous_product_code = product_code
                            previous_price = price
                            previous_unit_price = unit_price
                            previous_case_price = case_price

            except Exception as e:
                logging.error(f"處理行 {row_index} 時發生錯誤: {str(e)}")
                continue
        

    return data


def process_price(rout, price_text, previous_unit_price_unit=None):
    """
    處理價格文本，提取各種價格信息
    :param rout: 路線
    :param price_text: 價格文本
    :param previous_unit_price_unit: 前一個單位價格單位
    :return: 處理後的價格信息元組
    """
    price = None
    unit_price = None
    case_price = None
    unit = None
    unit_price_unit = None

    if price_text:
        if rout == '10106':
            box_match = re.search(r'(\d+)\s*元/箱', price_text)
            if box_match:
                price = box_match.group(1)
                unit = '元/箱'

            unit_match = re.search(r'\(([\d.]+)\s*元/(\d+)入\)', price_text)
            if unit_match:
                unit_price = unit_match.group(1)
                unit_price_unit = f'元/{unit_match.group(2)}入'

            case_match = re.search(r'\(([\d.]+)\s*元/箱\)', price_text)
            if case_match:
                case_price = case_match.group(1)

            if previous_unit_price_unit and re.search(r'元/\d+入', previous_unit_price_unit) and not re.search(
                    r'元/\d+入', price_text):
                unit_price_unit = None
                previous_unit_price_unit = None

        elif rout in ['10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']:
            parts = price_text.split()
            for part in parts:
                # 修改：使用能匹配浮點數的正則表達式
                # 原來的: price_match = re.search(r'(\d+)\s*元/([箱4-6入]+)', part)
                price_match = re.search(r'([\d\.]+)\s*元/([箱4-6入]+)', part)
                if price_match:
                    if '箱' in price_match.group(2):
                        price = price_match.group(1)
                        unit = '元/箱'
                    else:
                        unit_price = price_match.group(1)
                        unit_price_unit = f'元/{price_match.group(2)}'

            if len(parts) > 2:
                # 此部分已經使用能匹配浮點數的正則表達式
                case_match = re.search(r'([\d.]+)\s*元/([箱4-6入]+)', parts[-1])
                if case_match:
                    if '箱' in case_match.group(2):
                        case_price = case_match.group(1)
                    else:
                        unit_price = case_match.group(1)
                        unit_price_unit = f'元/{case_match.group(2)}'

        if not price and not unit_price and not case_price:
            price = None
            unit = None

    return price, unit, unit_price, unit_price_unit, previous_unit_price_unit, case_price


def save_to_excel(rout, data, file_path):
    """
    將提取的數據保存到 Excel 文件
    :param rout: 路線
    :param data: 要保存的數據
    :param file_path: Excel 文件保存路徑
    """
    wb = Workbook()
    ws = wb.active

    # 記錄 rout 參數的值以便調試
    logging.info(f"save_to_excel 被調用，rout 參數值: '{rout}', 類型: {type(rout)}")

    # 確保 rout 是字符串且不為空
    if not isinstance(rout, str):
        logging.warning(f"rout 參數不是字符串類型: {type(rout)}, 值: {rout}")
        rout = str(rout) if rout is not None else "unknown"

    # 檢查 rout 是否異常長（可能是產品名稱）
    if len(rout) > 20:
        logging.error(f"rout 參數異常長，可能是產品名稱: '{rout[:50]}...'")
        rout = "unknown"

    # 設置工作表標題
    if rout == '10106':
        ws.title = "大潤發產品價格"
    elif rout == '10212':
        ws.title = "楓康產品價格"
    elif rout == '10247':
        ws.title = "814超市產品價格"
    elif rout == '10228':
        ws.title = "省錢產品價格"
    elif rout == '11201':
        ws.title = "小北百貨產品價格"
    elif rout == '80901':
        ws.title = "PCHOME產品價格"
    elif rout == '80902':
        ws.title = "東森產品價格"
    elif rout == '80904':
        ws.title = "MOMO產品價格"
    elif rout == '80905':
        ws.title = "蝦皮產品價格"

    # 添加表頭
    headers = ["品號", "含稅價格(箱)", "含稅單位(箱)", "含稅價格(4或6入)", "含稅單位(4或6入)", "未稅價格(箱)",
               "未稅單位(箱)"]
    ws.append(headers)

    # 添加數據，並檢查數據長度
    logging.info(f"準備添加 {len(data)} 筆數據到 Excel")
    for i, entry in enumerate(data):
        # 檢查每個單元格的內容長度，避免過長的內容
        safe_entry = []
        for j, cell_value in enumerate(entry):
            if cell_value and isinstance(cell_value, str):
                # 清理字符串中的特殊字符
                cleaned_value = cell_value

                # 移除或替換可能導致問題的字符
                if '\x0b' in cleaned_value:  # 垂直制表符
                    logging.warning(f'發現垂直制表符，將其替換為空格 (行{i + 1}, 列{j + 1}): {cleaned_value[:50]}...')
                    cleaned_value = cleaned_value.replace('\x0b', ' ')

                # 移除其他控制字符
                cleaned_value = ''.join(char for char in cleaned_value if ord(char) >= 32 or char in '\t\n\r')

                if len(cleaned_value) > 255:
                    # Excel 單元格內容限制為 32,767 個字符，但為了安全起見，我們限制為 255
                    logging.warning(f'截斷過長的單元格內容 (行{i + 1}, 列{j + 1}): {cleaned_value[:50]}...')
                    safe_entry.append(cleaned_value[:255])
                elif len(cleaned_value) > 50:
                    # 記錄較長的字符串以便調試
                    logging.debug(f'較長的單元格內容 (行{i + 1}, 列{j + 1}): {cleaned_value[:50]}...')
                    safe_entry.append(cleaned_value)
                else:
                    safe_entry.append(cleaned_value)
            else:
                safe_entry.append(cell_value)

        # 記錄第一筆數據以便調試
        if i == 0:
            logging.info(f"第一筆數據: {safe_entry}")

        try:
            ws.append(safe_entry)
        except Exception as append_error:
            logging.error(f"添加第 {i + 1} 筆數據時發生錯誤: {str(append_error)}")
            logging.error(f"問題數據: {safe_entry}")
            raise

    # 定義列屬性
    column_properties = {
        "品號": {"alignment": "left", "type": "string", "width": 11},
        "含稅價格(箱)": {"alignment": "right", "type": "number", "width": 13},
        "含稅單位(箱)": {"alignment": "left", "type": "string", "width": 13},
        "含稅價格(4或6入)": {"alignment": "right", "type": "number", "width": 17},
        "含稅單位(4或6入)": {"alignment": "left", "type": "string", "width": 17},
        "未稅價格(箱)": {"alignment": "right", "type": "number", "width": 13},
        "未稅單位(箱)": {"alignment": "left", "type": "string", "width": 13}
    }

    # 應用列屬性
    for col_num, column_title in enumerate(headers, 1):
        column_letter = get_column_letter(col_num)
        properties = column_properties.get(column_title, {"alignment": "center", "type": "string", "width": 15})
        alignment = properties["alignment"]
        value_type = properties["type"]
        custom_width = properties["width"]

        ws.column_dimensions[column_letter].width = custom_width

        for cell in ws[column_letter]:
            cell.alignment = Alignment(horizontal=alignment)
            if cell.row != 1:  # 跳過表頭
                try:
                    original_value = cell.value
                    if value_type == "number":
                        try:
                            cell.value = float(cell.value) if cell.value else ""
                        except ValueError:
                            cell.value = ""
                    else:
                        # 檢查是否是異常長的字符串
                        if original_value and isinstance(original_value, str) and len(original_value) > 100:
                            logging.warning(
                                f"發現異常長的單元格值 (行{cell.row}, 列{column_letter}): {original_value[:50]}...")
                            # 截斷過長的字符串
                            cell.value = original_value[:255] if original_value else ""
                        else:
                            cell.value = str(original_value) if original_value else ""
                except Exception as cell_error:
                    logging.error(f"設置單元格值時發生錯誤 (行{cell.row}, 列{column_letter}): {str(cell_error)}")
                    logging.error(f"問題值: {repr(cell.value)}")
                    cell.value = ""  # 設置為空值以避免錯誤

    # 保存 Excel 文件
    try:
        logging.info(f"準備保存 Excel 文件到: {file_path}")
        wb.save(file_path)
        logging.info("Excel 文件保存成功")
    except Exception as save_error:
        logging.error(f"保存 Excel 文件時發生錯誤: {str(save_error)}")
        logging.error(f"工作表標題: '{ws.title}'")
        logging.error(f"工作表行數: {ws.max_row}, 列數: {ws.max_column}")
        raise


@contextmanager
def word_app():
    """
    創建一個 Word 應用程序實例的上下文管理器
    """
    app = None
    try:
        pythoncom.CoInitialize()
        app = win32com.client.gencache.EnsureDispatch("Word.Application")
        app.Visible = False
        yield app
    finally:
        if app:
            try:
                app.Quit()
            except:
                pass
        pythoncom.CoUninitialize()


@contextmanager
def word_doc(app, file_path):
    """
    打開 Word 文件的上下文管理器
    :param app: Word 應用程序實例
    :param file_path: 文件路徑
    """
    doc = None
    try:
        doc = app.Documents.Open(FileName=file_path)
        yield doc
    finally:
        if doc:
            try:
                doc.Close(SaveChanges=False)
            except:
                pass


@validate_access_token_and_params(None)
def select_business_notification_price_download(request, json_data, role, user_id):
    """
    處理業務通知價格下載請求
    :param request: HTTP 請求
    :param json_data: JSON 格式的請求數據
    :param role: 使用者角色
    :param user_id: 使用者 ID
    :return: Excel 文件下載響應或錯誤信息
    """
    if request.method == "POST":
        excel_path = None
        temp_word_path = None

        try:
            pudcno = json_data['pudcno']
            rout = json_data['rout']

            # 驗證 rout 參數
            logging.info(f"從 JSON 數據中提取的 rout: '{rout}', 類型: {type(rout)}")
            if not isinstance(rout, str):
                logging.error(f"rout 參數不是字符串類型: {type(rout)}, 值: {rout}")
                return "路線參數格式錯誤", status.HTTP_400_BAD_REQUEST

            # 檢查 rout 是否是文件名而不是路線代碼
            if len(rout) > 20 or '通知' in rout:
                logging.warning(f"rout 參數可能是文件名而不是路線代碼: '{rout}'")
                # 嘗試從文件名推斷路線代碼
                if '東急屋' in rout:
                    rout = '10247'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '大全聯' in rout:
                    rout = '10106'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '楓康' in rout:
                    rout = '10212'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '814' in rout:
                    rout = '10247'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '省錢' in rout:
                    rout = '10228'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '小北' in rout:
                    rout = '11201'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif 'PCHOME' in rout.upper():
                    rout = '80901'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '東森' in rout:
                    rout = '80902'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif 'MOMO' in rout.upper():
                    rout = '80904'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                elif '蝦皮' in rout:
                    rout = '80905'
                    logging.info(f"從文件名推斷路線代碼為: {rout}")
                else:
                    logging.error(f"無法從文件名推斷路線代碼: '{rout}'")
                    return "無法識別的路線", status.HTTP_400_BAD_REQUEST

            # 構建 SQL 查詢條件
            strCond = ''
            condition = {}
            params = {
                "file_name": "ATTACHMENT_NAME",
            }
            for param, field in params.items():
                if param in json_data and json_data[param]:
                    condition[f'q{param.upper()}'] = json_data[param]
                    strCond += f' AND {field} = :{f"q{param.upper()}"} '

            # 執行 SQL 查詢
            sSTM = DOWNLOAD_BPUDCHT_SQL + strCond + 'UNION ALL' + DOWNLOAD_ATTACHMENT_SQL + strCond
            data = fetch_from_db(sSTM, condition)

            if not data:
                return "找不到資料", status.HTTP_404_NOT_FOUND

            file_name = data[0][0]
            file_url = file_path(file_name, pudcno)

            if not os.path.exists(file_url):
                logging.error(f"找不到文件: {file_url}")
                return "找不到檔案", status.HTTP_404_NOT_FOUND

            # 準備 Excel 文件名稱和路徑
            excel_file_name = os.path.splitext(file_name)[0] + '.xlsx'
            excel_path = file_path(excel_file_name, pudcno)

            # 創建臨時 Word 文件
            temp_word_path = os.path.join(os.path.dirname(file_url), f"{uuid.uuid4()}.docx")
            shutil.copyfile(file_url, temp_word_path)

            # 使用企業級處理器處理 Word 文件
            def extract_data_worker(user_id, pudcno, file_path, file_name, word_app):
                if not word_app:
                    logging.error("Word 應用程序實例為空")
                    return []

                doc = None
                try:
                    logging.info(f"開始處理文件: {file_path}")
                    doc = word_app.Documents.Open(file_path)
                    result = extract_table_data(doc, rout, user_id)
                    logging.info(f"成功提取 {len(result)} 筆數據")
                    return result
                except Exception as e:
                    logging.error(f"提取表格數據時發生錯誤: {str(e)}")
                    import traceback
                    logging.debug(f"錯誤詳情: {traceback.format_exc()}")
                    return []
                finally:
                    if doc:
                        try:
                            doc.Close()
                        except Exception as close_error:
                            logging.warning(f"關閉文檔時發生錯誤: {str(close_error)}")

            # 嘗試使用企業級處理器
            try:
                logging.info("嘗試使用企業級處理器")
                table_data = process_word_document_enterprise(
                    temp_word_path, file_name, pudcno, user_id, None,
                    lambda u, p, f, n, w: extract_data_worker(u, p, f, n, w),
                    priority=3  # 價格下載優先級較高
                )
                logging.info("企業級處理器執行成功")
            except Exception as enterprise_error:
                logging.warning(f"企業級處理器失敗，降級到原始處理器: {str(enterprise_error)}")

                # 降級到原始處理器
                def extract_data_worker_legacy(temp_path, rout, user_id, word_app=None):
                    if not word_app:
                        logging.error("Word 應用程序實例為空（legacy）")
                        return []

                    doc = None
                    try:
                        logging.info(f"使用 legacy 處理器處理文件: {temp_path}")
                        doc = word_app.Documents.Open(temp_path)
                        result = extract_table_data(doc, rout, user_id)
                        logging.info(f"Legacy 處理器成功提取 {len(result)} 筆數據")
                        return result
                    except Exception as e:
                        logging.error(f"Legacy 處理器提取數據時發生錯誤: {str(e)}")
                        return []
                    finally:
                        if doc:
                            try:
                                doc.Close()
                            except Exception as close_error:
                                logging.warning(f"Legacy 處理器關閉文檔時發生錯誤: {str(close_error)}")

                try:
                    table_data = process_word_task(extract_data_worker_legacy, temp_word_path, rout, user_id)
                    logging.info("原始處理器執行成功")
                except Exception as legacy_error:
                    logging.error(f"原始處理器也失敗: {str(legacy_error)}")
                    table_data = []

            # 驗證提取的數據
            if not table_data:
                logging.warning("未提取到任何數據")
            else:
                logging.info(f"成功提取 {len(table_data)} 筆數據")

            # 保存提取的數據到 Excel 文件
            try:
                logging.info(f"準備調用 save_to_excel，rout: '{rout}', 數據筆數: {len(table_data)}")
                save_to_excel(rout, table_data, excel_path)
                logging.info(f"成功保存 Excel 文件: {excel_path}")
            except Exception as excel_error:
                logging.error(f"保存 Excel 文件時發生錯誤: {str(excel_error)}")
                logging.error(f"錯誤發生時的 rout 值: '{rout}', 類型: {type(rout)}")
                raise

            # 刪除臨時 Word 文件
            if temp_word_path and os.path.exists(temp_word_path):
                os.remove(temp_word_path)

            if not os.path.exists(excel_path):
                logging.error(f"找不到 Excel 文件: {excel_path}")
                return "找不到生成的Excel檔案", status.HTTP_404_NOT_FOUND

            # 準備文件下載響應
            response = FileResponse(open(excel_path, 'rb'))
            file_display_name = quote(excel_file_name.encode('utf-8'))
            response["Content-Disposition"] = f"attachment; filename*=UTF-8''{file_display_name}"
            return response, status.HTTP_200_OK

        except Exception as e:
            logging.error(f"select_business_notification_price_download 發生錯誤: {str(e)}")
            return str(e), status.HTTP_500_INTERNAL_SERVER_ERROR

        finally:
            # 確保在任何情況下都刪除臨時文件
            if temp_word_path and os.path.exists(temp_word_path):
                try:
                    os.remove(temp_word_path)
                except Exception as e:
                    logging.error(f"刪除臨時文件時發生錯誤: {str(e)}")


# 以下是一些可能需要的額外輔助函數或錯誤處理機制
def safe_file_operations(func):
    """
    裝飾器：安全地執行文件操作，處理可能的異常
    """

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IOError as e:
            logging.error(f"{func.__name__} 中發生IO錯誤: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"{func.__name__} 中發生意外錯誤: {str(e)}")
            raise

    return wrapper


@safe_file_operations
def create_temp_file(original_file, temp_dir):
    """
    創建臨時文件的安全方法
    :param original_file: 原始文件路徑
    :param temp_dir: 臨時目錄
    :return: 臨時文件路徑
    """
    temp_file = os.path.join(temp_dir, f"temp_{uuid.uuid4().hex}.docx")
    shutil.copy2(original_file, temp_file)
    return temp_file


def cleanup_temp_files(temp_dir, pattern="temp_*.docx"):
    """
    清理臨時文件
    :param temp_dir: 臨時目錄
    :param pattern: 文件匹配模式
    """
    for file in glob.glob(os.path.join(temp_dir, pattern)):
        try:
            os.remove(file)
        except Exception as e:
            logging.warning(f"刪除臨時文件 {file} 失敗: {str(e)}")


def handle_word_com_error(e):
    """
    處理 Word COM 錯誤
    :param e: COM 錯誤異常
    """
    if isinstance(e, pythoncom.com_error):
        # 處理特定的 COM 錯誤代碼
        if e.hresult == -2147352567:  # 0x80020009
            logging.error("Word 文件可能已損壞或無法訪問")
        elif e.hresult == -2147023170:  # 0x800706BE
            logging.error("Word 服務器可能沒有響應")
        else:
            logging.error(f"未知的 COM 錯誤: {str(e)}")
    else:
        logging.error(f"非 COM 錯誤: {str(e)}")
