# -*- coding: utf-8 -*-
"""
Word 錯誤處理器
處理 Word 應用程序的錯誤對話框和異常情況
"""

import win32com.client
import win32api
import win32con
import win32gui
import win32process
import time
import threading
import logging
import psutil
import os

logger = logging.getLogger(__name__)

class WordErrorHandler:
    """處理 Word 錯誤和對話框"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.error_windows = [
            "Microsoft Word",
            "Microsoft Office Word",
            "Word",
            "錯誤",
            "Error",
            "警告",
            "Warning",
            "Microsoft Visual Basic",
            "執行階段錯誤",
            "Run-time error"
        ]
    
    def start_monitoring(self):
        """開始監控錯誤對話框"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_loop,
                daemon=True,
                name="WordErrorMonitor"
            )
            self.monitor_thread.start()
            logger.info("Word 錯誤監控已啟動")
    
    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Word 錯誤監控已停止")
    
    def _monitor_loop(self):
        """監控循環"""
        # 在監控線程中初始化 COM
        import pythoncom
        pythoncom.CoInitialize()
        
        try:
            while self.monitoring:
                try:
                    # 查找並關閉錯誤對話框
                    self._close_error_dialogs()
                    # 檢查並清理僵死的 Word 進程
                    self._cleanup_zombie_processes()
                    time.sleep(2)  # 每2秒檢查一次
                except Exception as e:
                    # 忽略預期的 COM 線程錯誤
                    error_msg = str(e)
                    if "EnumWindows" in error_msg or "應用程式所呼叫了整理給不同執行緒的介面" in error_msg:
                        logger.debug(f"忽略預期的 COM 錯誤: {error_msg}")
                    else:
                        logger.error(f"錯誤監控異常: {error_msg}")
                    time.sleep(5)
        finally:
            # 清理 COM
            pythoncom.CoUninitialize()
    
    def _close_error_dialogs(self):
        """關閉錯誤對話框"""
        windows = []
        try:
            def enum_windows_callback(hwnd, windows):
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_text = win32gui.GetWindowText(hwnd)
                        class_name = win32gui.GetClassName(hwnd)
                        
                        # 檢查是否是錯誤對話框
                        for error_title in self.error_windows:
                            if error_title in window_text:
                                windows.append((hwnd, window_text, class_name))
                except:
                    pass
                return True
            
            try:
                win32gui.EnumWindows(enum_windows_callback, windows)
            except Exception as e:
                # EnumWindows 可能在跨線程調用時失敗，這是正常的
                error_msg = str(e)
                if "應用程式所呼叫了整理給不同執行緒的介面" in error_msg or \
                   "EnumWindows" in error_msg or \
                   "No error message is available" in error_msg:
                    # 這些都是預期的跨線程錯誤，不需要記錄
                    pass
                else:
                    logger.debug(f"EnumWindows 錯誤: {error_msg}")
                return
        except Exception as e:
            logger.debug(f"關閉錯誤對話框時發生錯誤: {str(e)}")
            return
        
        for hwnd, title, class_name in windows:
            try:
                # 嘗試找到並點擊確定、取消或關閉按鈕
                self._click_dialog_button(hwnd, title)
            except Exception as e:
                logger.error(f"關閉對話框失敗 {title}: {str(e)}")
    
    def _click_dialog_button(self, hwnd, title):
        """點擊對話框按鈕"""
        logger.info(f"發現錯誤對話框: {title}")
        
        # 嘗試找到子窗口（按鈕）
        button_texts = ["確定", "OK", "取消", "Cancel", "關閉", "Close", "是", "Yes", "否", "No"]
        
        def enum_child_callback(child_hwnd, param):
            child_text = win32gui.GetWindowText(child_hwnd)
            child_class = win32gui.GetClassName(child_hwnd)
            
            if child_class == "Button":
                for btn_text in button_texts:
                    if btn_text in child_text:
                        # 點擊按鈕
                        win32api.PostMessage(child_hwnd, win32con.BM_CLICK, 0, 0)
                        logger.info(f"已點擊按鈕: {child_text}")
                        return False
            return True
        
        try:
            win32gui.EnumChildWindows(hwnd, enum_child_callback, None)
        except:
            pass
        
        # 如果沒有找到按鈕，嘗試發送 ESC 或關閉窗口
        try:
            win32api.PostMessage(hwnd, win32con.WM_KEYDOWN, win32con.VK_ESCAPE, 0)
            win32api.PostMessage(hwnd, win32con.WM_KEYUP, win32con.VK_ESCAPE, 0)
        except:
            pass
        
        try:
            win32api.PostMessage(hwnd, win32con.WM_CLOSE, 0, 0)
        except:
            pass
    
    def _cleanup_zombie_processes(self):
        """清理僵死的 Word 進程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                if proc.info['name'] and 'WINWORD.EXE' in proc.info['name'].upper():
                    # 檢查進程是否無響應（CPU 使用率為 0 且存在時間超過 5 分鐘）
                    try:
                        proc_info = proc.as_dict(attrs=['pid', 'create_time', 'cpu_percent', 'status'])
                        create_time = proc_info.get('create_time', 0)
                        
                        if time.time() - create_time > 300:  # 5分鐘
                            cpu_percent = proc_info.get('cpu_percent', 0)
                            status = proc_info.get('status', '')
                            
                            # 如果 CPU 使用率很低且可能卡住
                            if cpu_percent == 0 and self._is_process_stuck(proc):
                                # logger.warning(f"發現僵死的 Word 進程 PID: {proc.info['pid']}")
                                proc.terminate()
                                time.sleep(2)
                                if proc.is_running():
                                    proc.kill()
                                logger.info(f"已終止僵死的 Word 進程 PID: {proc.info['pid']}")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
        except Exception as e:
            logger.error(f"清理僵死進程失敗: {str(e)}")
    
    def _is_process_stuck(self, proc):
        """檢查進程是否卡住"""
        try:
            # 檢查進程是否有窗口無響應
            def check_windows(hwnd, param):
                try:
                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                    if pid == proc.pid:
                        # 發送測試消息看是否響應
                        result = win32gui.SendMessageTimeout(
                            hwnd, win32con.WM_NULL, 0, 0,
                            win32con.SMTO_BLOCK | win32con.SMTO_ABORTIFHUNG,
                            5000  # 5秒超時
                        )
                        if result == 0:  # 無響應
                            param.append(True)
                except:
                    pass
                return True
            
            stuck = []
            try:
                win32gui.EnumWindows(check_windows, stuck)
            except Exception as e:
                # 忽略 EnumWindows 錯誤 - 這是預期的跨線程錯誤
                error_msg = str(e)
                if not ("應用程式所呼叫了整理給不同執行緒的介面" in error_msg or \
                        "EnumWindows" in error_msg or \
                        "No error message is available" in error_msg):
                    logger.debug(f"檢查進程狀態時 EnumWindows 錯誤: {error_msg}")
                # 如果無法檢查窗口，使用其他方法判斷
                return self._check_process_by_cpu(proc)
            return len(stuck) > 0
        except:
            return False
    
    def _check_process_by_cpu(self, proc):
        """通過 CPU 使用率檢查進程是否卡住"""
        try:
            # 檢查 CPU 使用率
            cpu_percent = proc.cpu_percent(interval=0.1)
            # 如果 CPU 使用率為 0 且進程存在超過 5 分鐘，可能卡住
            create_time = proc.create_time()
            if cpu_percent == 0 and time.time() - create_time > 300:
                return True
            return False
        except:
            return False

class SafeWordApplication:
    """安全的 Word 應用程序包裝器"""
    
    def __init__(self, timeout=30):
        self.app = None
        self.timeout = timeout
        self.error_handler = WordErrorHandler()
    
    def __enter__(self):
        """進入上下文時創建 Word 應用程序"""
        try:
            # 初始化 COM (必須在每個線程中調用)
            import pythoncom
            pythoncom.CoInitialize()
            
            # 啟動錯誤監控
            self.error_handler.start_monitoring()
            
            # 創建 Word 應用程序
            self.app = win32com.client.DispatchEx("Word.Application")
            self.app.Visible = False
            self.app.DisplayAlerts = 0  # wdAlertsNone
            
            # 設置一些安全選項
            try:
                self.app.Options.DoNotPromptForConvert = True
                self.app.Options.ConfirmConversions = False
                self.app.Options.UpdateLinksAtOpen = False
                self.app.Options.UpdateFieldsAtPrint = False
            except:
                pass
            
            return self.app
        except Exception as e:
            logger.error(f"創建 Word 應用程序失敗: {str(e)}")
            raise
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文時清理"""
        try:
            if self.app:
                # 關閉所有文檔
                for doc in self.app.Documents:
                    try:
                        doc.Close(SaveChanges=False)
                    except:
                        pass
                
                # 退出 Word
                self.app.Quit()
        except:
            pass
        finally:
            # 停止錯誤監控
            self.error_handler.stop_monitoring()
            self.app = None
            
            # 反初始化 COM
            try:
                import pythoncom
                pythoncom.CoUninitialize()
            except:
                pass

def kill_all_word_processes():
    """強制關閉所有 Word 進程"""
    killed_count = 0
    try:
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] and 'WINWORD.EXE' in proc.info['name'].upper():
                try:
                    proc.terminate()
                    proc.wait(timeout=5)
                    killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    try:
                        proc.kill()
                        killed_count += 1
                    except:
                        pass
    except Exception as e:
        logger.error(f"關閉 Word 進程失敗: {str(e)}")
    
    if killed_count > 0:
        logger.info(f"已關閉 {killed_count} 個 Word 進程")
    
    return killed_count

# 全局錯誤處理器實例
global_error_handler = WordErrorHandler()

def start_global_monitoring():
    """啟動全局錯誤監控"""
    global_error_handler.start_monitoring()

def stop_global_monitoring():
    """停止全局錯誤監控"""
    global_error_handler.stop_monitoring()