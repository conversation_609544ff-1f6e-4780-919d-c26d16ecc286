# -*- coding: utf-8 -*-
"""
Word ���ҭ״_�u��
�M���Ω�״_ Normal.dotm �M��L Word COM �������D
"""

import os
import sys
import time
import shutil
import logging
import tempfile
import subprocess
from pathlib import Path

logger = logging.getLogger(__name__)

class WordEnvironmentFixer:
    """Word ���ҭ״_��"""
    
    def __init__(self):
        self.user_templates_path = Path.home() / "AppData" / "Roaming" / "Microsoft" / "Templates"
        self.normal_dotm_path = self.user_templates_path / "Normal.dotm"
        
    def check_word_processes(self):
        """�ˬd�O�_�� Word �i�{�b�B��"""
        try:
            result = subprocess.run(
                ['tasklist', '/FI', 'IMAGENAME eq WINWORD.EXE'],
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            return 'winword.exe' in result.stdout.lower()
        except Exception as e:
            logger.warning(f"�ˬd Word �i�{����: {str(e)}")
            return False
    
    def backup_normal_dotm(self):
        """�ƥ� Normal.dotm ���"""
        if not self.normal_dotm_path.exists():
            logger.info("Normal.dotm ���s�b�A�L�ݳƥ�")
            return True
            
        try:
            timestamp = int(time.time())
            backup_path = self.normal_dotm_path.with_suffix(f'.dotm.backup_{timestamp}')
            shutil.copy2(self.normal_dotm_path, backup_path)
            logger.info(f"�w�ƥ� Normal.dotm ��: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"�ƥ� Normal.dotm ����: {str(e)}")
            return False
    
    def remove_normal_dotm(self):
        """�R�� Normal.dotm ���"""
        if not self.normal_dotm_path.exists():
            logger.info("Normal.dotm ���s�b�A�L�ݧR��")
            return True
            
        try:
            self.normal_dotm_path.unlink()
            logger.info("�w�R�� Normal.dotm�AWord �N���s�Ы�")
            return True
        except Exception as e:
            logger.error(f"�R�� Normal.dotm ����: {str(e)}")
            return False
    
    def create_temp_templates_dir(self):
        """�Ы��{�ɼҪO�ؿ�"""
        try:
            temp_dir = tempfile.mkdtemp(prefix="word_templates_")
            logger.info(f"�Ы��{�ɼҪO�ؿ�: {temp_dir}")
            return temp_dir
        except Exception as e:
            logger.error(f"�Ы��{�ɼҪO�ؿ�����: {str(e)}")
            return None
    
    def fix_registry_settings(self):
        """�״_���U��]�m�]�p�G�ݭn�^"""
        try:
            import winreg
            
            # �ˬd Word �����U��]�m
            key_path = r"SOFTWARE\Microsoft\Office\16.0\Word\Options"
            try:
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ) as key:
                    logger.info("Word ���U��]�m���`")
            except FileNotFoundError:
                logger.info("Word ���U��]�m���s�b�A�o�O���`��")
            except Exception as e:
                logger.warning(f"�ˬd���U��]�m�ɥX��: {str(e)}")
                
        except ImportError:
            logger.warning("�L�k�ɤJ winreg �Ҷ�")
        except Exception as e:
            logger.warning(f"�״_���U��]�m����: {str(e)}")
    
    def fix_environment(self):
        """���槹�㪺���ҭ״_"""
        logger.info("�}�l�״_ Word ����...")
        
        # 1. �ˬd Word �i�{
        if self.check_word_processes():
            logger.warning("�˴��� Word �i�{���b�B��A��ĳ������ Word")
            return False
        
        # 2. �T�O�ҪO�ؿ��s�b
        self.user_templates_path.mkdir(parents=True, exist_ok=True)
        
        # 3. �ƥ��çR�� Normal.dotm
        if self.normal_dotm_path.exists():
            if not self.backup_normal_dotm():
                logger.error("�ƥ����ѡA����״_")
                return False
                
            if not self.remove_normal_dotm():
                logger.error("�R�� Normal.dotm ���ѡA����״_")
                return False
        
        # 4. �Ы��{�ɼҪO�ؿ�
        temp_dir = self.create_temp_templates_dir()
        if not temp_dir:
            logger.warning("�Ы��{�ɼҪO�ؿ����ѡA���~��״_")
        
        # 5. �״_���U��]�m
        self.fix_registry_settings()
        
        # 6. �]�m�����ܶq
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        logger.info("Word ���ҭ״_����")
        return True
    
    def diagnose_issues(self):
        """�E�_ Word ���Ұ��D"""
        issues = []
        
        # �ˬd Normal.dotm
        if self.normal_dotm_path.exists():
            try:
                # ����Ū�����
                with open(self.normal_dotm_path, 'rb') as f:
                    f.read(1024)  # Ū���e 1KB
                logger.info("Normal.dotm ���iŪ��")
            except Exception as e:
                issues.append(f"Normal.dotm ���l�a: {str(e)}")
        
        # �ˬd�ҪO�ؿ��v��
        try:
            test_file = self.user_templates_path / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            logger.info("�ҪO�ؿ��g�J�v�����`")
        except Exception as e:
            issues.append(f"�ҪO�ؿ��v�����D: {str(e)}")
        
        # �ˬd Word �i�{
        if self.check_word_processes():
            issues.append("Word �i�{���b�B��")
        
        return issues

def fix_word_environment():
    """�״_ Word ���Ҫ��D���"""
    fixer = WordEnvironmentFixer()
    
    # �E�_���D
    issues = fixer.diagnose_issues()
    if issues:
        logger.warning("�o�{�H�U���D:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    
    # ����״_
    success = fixer.fix_environment()
    
    if success:
        logger.info("Word ���ҭ״_���\�A�Э��s���դU���ާ@")
    else:
        logger.error("Word ���ҭ״_���ѡA���pô�޳N�䴩")
    
    return success

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    fix_word_environment()
