import queue
import threading
import time
import logging
import pythoncom
import win32com.client
from contextlib import contextmanager
import uuid
import os

# 設置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class WordTask:
    """Word 處理任務"""
    def __init__(self, task_id, func, args, kwargs):
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.result = None
        self.error = None
        self.completed = threading.Event()

class WordQueueProcessor:
    """
    Word 文檔處理佇列
    使用單一執行緒處理所有 Word 操作，避免並發問題
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化佇列處理器"""
        self.task_queue = queue.Queue()
        self.worker_thread = None
        self.running = False
        self.word_app = None
        self.start_worker()
    
    def start_worker(self):
        """啟動工作執行緒"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(target=self._process_tasks, daemon=True)
            self.worker_thread.start()
            logging.info("Word 佇列處理器已啟動")
    
    def stop_worker(self):
        """停止工作執行緒"""
        self.running = False
        # 放入一個空任務以喚醒執行緒
        self.task_queue.put(None)
        if self.worker_thread:
            self.worker_thread.join(timeout=10)
        logging.info("Word 佇列處理器已停止")
    
    def _initialize_word(self):
        """初始化 Word 應用程序"""
        try:
            pythoncom.CoInitialize()
            self.word_app = win32com.client.DispatchEx("Word.Application")
            self.word_app.Visible = False
            self.word_app.DisplayAlerts = 0  # 關鍵：禁用所有警告對話框

            # 防止 Normal.dotm 錯誤的設置
            try:
                self.word_app.Options.DoNotPromptForConvert = True
                self.word_app.Options.ConfirmConversions = False
                self.word_app.Options.UpdateLinksAtOpen = False
                self.word_app.Options.CheckGrammarAsYouType = False
                self.word_app.Options.CheckSpellingAsYouType = False
                self.word_app.Options.AutoRecover = False
                self.word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
            except Exception as e:
                logging.warning(f"設置 Word 選項時出錯: {str(e)}")

            logging.info("Word 應用程序已初始化")
            return True
        except Exception as e:
            logging.error(f"初始化 Word 失敗: {str(e)}")
            return False
    
    def _cleanup_word(self):
        """清理 Word 應用程序"""
        if self.word_app:
            try:
                # 關閉所有文檔
                for doc in self.word_app.Documents:
                    try:
                        doc.Close(SaveChanges=False)
                    except:
                        pass
                self.word_app.Quit()
                self.word_app = None
                logging.info("Word 應用程序已清理")
            except Exception as e:
                logging.error(f"清理 Word 失敗: {str(e)}")
        try:
            pythoncom.CoUninitialize()
        except:
            pass
    
    def _process_tasks(self):
        """處理任務的工作執行緒"""
        # 初始化 Word
        if not self._initialize_word():
            return
        
        try:
            while self.running:
                try:
                    # 獲取任務（超時設置為 60 秒）
                    task = self.task_queue.get(timeout=60)
                    
                    if task is None:
                        break
                    
                    # 執行任務
                    try:
                        logging.info(f"開始處理任務: {task.task_id}")
                        # 將 word_app 傳遞給任務函數
                        task.kwargs['word_app'] = self.word_app
                        task.result = task.func(*task.args, **task.kwargs)
                        logging.info(f"任務完成: {task.task_id}")
                    except Exception as e:
                        logging.error(f"任務失敗 {task.task_id}: {str(e)}")
                        task.error = e
                    finally:
                        task.completed.set()
                        
                except queue.Empty:
                    # 佇列空閒時檢查 Word 狀態
                    if self.word_app:
                        try:
                            # 簡單測試 Word 是否還在運行
                            _ = self.word_app.Name
                        except:
                            # Word 可能已經崩潰，重新初始化
                            logging.warning("Word 應用程序無響應，重新初始化")
                            self._cleanup_word()
                            if not self._initialize_word():
                                break
                except Exception as e:
                    logging.error(f"處理任務時發生錯誤: {str(e)}")
        finally:
            self._cleanup_word()
    
    def submit_task(self, func, *args, **kwargs):
        """
        提交任務到佇列
        :param func: 要執行的函數
        :param args: 位置參數
        :param kwargs: 關鍵字參數
        :return: 任務結果
        """
        task_id = str(uuid.uuid4())
        task = WordTask(task_id, func, args, kwargs)
        
        # 確保工作執行緒正在運行
        if not self.running or not self.worker_thread.is_alive():
            self.start_worker()
        
        # 提交任務
        self.task_queue.put(task)
        logging.info(f"任務已提交: {task_id}")
        
        # 等待任務完成
        task.completed.wait()
        
        # 返回結果或拋出錯誤
        if task.error:
            raise task.error
        return task.result

# 全局佇列處理器實例
word_queue_processor = WordQueueProcessor()

# 便利函數
def process_word_task(func, *args, **kwargs):
    """
    處理 Word 任務的便利函數
    """
    return word_queue_processor.submit_task(func, *args, **kwargs)