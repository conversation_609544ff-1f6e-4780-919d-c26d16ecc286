# -*- coding: utf-8 -*-
"""
COM 安全的 Word 文檔處理器
使用專用線程池處理所有 Word 操作，完全避免 COM 線程模型問題
"""

import os
import time
import logging
import tempfile
import shutil
import uuid
import threading
import concurrent.futures
import win32com.client
import pythoncom
from functools import wraps

logger = logging.getLogger(__name__)

class COMSafeWordProcessor:
    """COM 安全的 Word 處理器 - 使用專用線程執行所有操作"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        # 使用單線程執行器確保所有操作在同一線程中
        self.executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=1,
            thread_name_prefix="WordCOMThread"
        )
        self._word_app = None
        self._app_lock = threading.Lock()
        logger.info("COM 安全 Word 處理器已初始化")
    
    def _ensure_word_app(self):
        """確保 Word 應用程序在當前線程中可用"""
        # 檢查當前線程是否已初始化 COM
        try:
            pythoncom.CoGetInterfaceAndReleaseStream(
                pythoncom.CoMarshalInterThreadInterfaceInStream(
                    pythoncom.IID_IDispatch,
                    None
                ),
                pythoncom.IID_IDispatch
            )
        except:
            # 需要初始化 COM
            pythoncom.CoInitialize()

        if self._word_app is None:
            try:
                # 創建新的 Word 應用程序
                self._word_app = win32com.client.DispatchEx("Word.Application")
                self._word_app.Visible = False
                self._word_app.DisplayAlerts = 0  # wdAlertsNone

                # 設置安全選項，防止 Normal.dotm 問題
                try:
                    self._word_app.Options.DoNotPromptForConvert = True
                    self._word_app.Options.ConfirmConversions = False
                    self._word_app.Options.UpdateLinksAtOpen = False
                    self._word_app.Options.UpdateFieldsAtPrint = False
                    self._word_app.Options.UpdateLinksAtPrint = False
                    self._word_app.Options.UpdateFieldsWithTrackedChangesAtPrint = False
                    self._word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable

                    # 防止 Normal.dotm 問題的關鍵設置
                    self._word_app.Options.CheckGrammarAsYouType = False
                    self._word_app.Options.CheckSpellingAsYouType = False
                    self._word_app.Options.SuggestSpellingCorrections = False
                    self._word_app.Options.AllowReadingMode = False
                    self._word_app.Options.WarnBeforeSavingPrintingSendingMarkup = False

                    # 禁用自動保存和恢復功能
                    self._word_app.Options.AutoRecover = False
                    self._word_app.Options.SaveInterval = 0
                    self._word_app.Options.BackgroundSave = False

                    # 設置臨時模板路徑
                    try:
                        import tempfile
                        temp_dir = tempfile.mkdtemp(prefix="word_temp_")
                        self._word_app.Options.DefaultFilePath(0) = temp_dir  # wdUserTemplatesPath
                        logger.debug(f"設置臨時模板路徑: {temp_dir}")
                    except Exception as template_error:
                        logger.warning(f"設置模板路徑失敗: {str(template_error)}")

                except Exception as options_error:
                    logger.warning(f"設置 Word 選項失敗: {str(options_error)}")

                logger.info("Word 應用程序已創建")
            except Exception as e:
                logger.error(f"創建 Word 應用程序失敗: {str(e)}")
                # 如果是 Normal.dotm 相關錯誤，嘗試修復
                if "Normal.dotm" in str(e):
                    logger.info("檢測到 Normal.dotm 問題，嘗試修復...")
                    self._fix_normal_dotm_and_retry()
                else:
                    raise

        return self._word_app

    def _fix_normal_dotm_and_retry(self):
        """修復 Normal.dotm 問題並重試創建 Word 應用程序"""
        try:
            import shutil

            # 獲取 Word 用戶模板目錄
            user_templates_path = os.path.expanduser("~\\AppData\\Roaming\\Microsoft\\Templates")
            normal_dotm_path = os.path.join(user_templates_path, "Normal.dotm")

            # 如果 Normal.dotm 存在，備份並刪除它
            if os.path.exists(normal_dotm_path):
                try:
                    backup_path = f"{normal_dotm_path}.backup_{int(time.time())}"
                    shutil.copy2(normal_dotm_path, backup_path)
                    os.remove(normal_dotm_path)
                    logger.info(f"已刪除損壞的 Normal.dotm，備份到: {backup_path}")
                except Exception as e:
                    logger.warning(f"處理 Normal.dotm 時出錯: {str(e)}")

            # 重試創建 Word 應用程序
            try:
                self._word_app = win32com.client.DispatchEx("Word.Application")
                self._word_app.Visible = False
                self._word_app.DisplayAlerts = 0
                logger.info("修復 Normal.dotm 後成功創建 Word 應用程序")
            except Exception as retry_error:
                logger.error(f"修復後仍無法創建 Word 應用程序: {str(retry_error)}")
                raise

        except Exception as e:
            logger.error(f"修復 Normal.dotm 失敗: {str(e)}")
            raise

    def _process_in_com_thread(self, func, *args, **kwargs):
        """在 COM 線程中執行函數"""
        def wrapper():
            try:
                # 確保 Word 應用程序可用
                word_app = self._ensure_word_app()
                # 將 word_app 傳遞給函數
                if 'word_app' not in kwargs:
                    kwargs['word_app'] = word_app
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"COM 線程執行失敗: {str(e)}")
                # 如果出錯，重置 Word 應用程序
                self._reset_word_app()
                raise
        
        # 在專用線程中執行
        future = self.executor.submit(wrapper)
        return future.result(timeout=300)  # 5分鐘超時
    
    def _reset_word_app(self):
        """重置 Word 應用程序"""
        if self._word_app:
            try:
                # 關閉所有文檔
                while self._word_app.Documents.Count > 0:
                    try:
                        self._word_app.Documents(1).Close(SaveChanges=False)
                    except:
                        break
                # 退出 Word
                self._word_app.Quit()
            except:
                pass
            finally:
                self._word_app = None
                logger.info("Word 應用程序已重置")
    
    def process_document(self, file_path, file_name, pudcno, user_id, 
                        file_password, modify_func):
        """
        處理文檔 - COM 安全版本
        """
        logger.info(f"開始處理文檔: {file_name}")
        
        def _process(word_app):
            if file_password:
                return self._process_protected_document_internal(
                    word_app, file_path, file_name, pudcno, 
                    user_id, file_password, modify_func
                )
            else:
                return modify_func(
                    user_id, pudcno, file_path, file_name, word_app
                )
        
        try:
            result = self._process_in_com_thread(_process)
            logger.info(f"文檔處理完成: {file_name}")
            return result
        except Exception as e:
            logger.error(f"處理文檔失敗 {file_name}: {str(e)}")
            return None, None
    
    def _process_protected_document_internal(self, word_app, file_path, file_name, 
                                           pudcno, user_id, file_password, modify_func):
        """
        處理受保護的文檔（內部方法）
        """
        temp_dir = tempfile.mkdtemp()
        doc = None
        
        try:
            # 打開受密碼保護的文件
            logger.info(f"打開受保護的文檔: {file_path}")
            try:
                doc = word_app.Documents.Open(
                    FileName=file_path,
                    PasswordDocument=file_password,
                    ReadOnly=False,
                    AddToRecentFiles=False,
                    Visible=False
                )
            except Exception as e:
                logger.error(f"打開文檔失敗: {str(e)}")
                raise
            
            # 檢查並解除保護
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
                logger.info(f"文檔保護類型: {protection_type}")
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                    logger.info("已解除文檔保護")
                except Exception as e:
                    logger.warning(f"解除保護失敗: {str(e)}")
            
            # 保存未保護的版本
            temp_unprotected = os.path.join(temp_dir, f"temp_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close(SaveChanges=False)
            doc = None
            
            # 修改文檔
            logger.info("開始修改文檔內容")
            new_path, new_name = modify_func(
                user_id, pudcno, temp_unprotected, file_name, word_app
            )
            
            if new_path and os.path.exists(new_path):
                # 重新保護文檔
                logger.info("重新保護文檔")
                doc = word_app.Documents.Open(new_path)
                
                if protection_type != -1:
                    try:
                        doc.Protect(Type=protection_type, Password=file_password)
                        logger.info("已重新保護文檔")
                    except Exception as e:
                        logger.warning(f"保護文檔失敗: {str(e)}")
                
                # 保存最終版本
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close(SaveChanges=False)
                doc = None
                
                # 移動到目標位置
                dest_path = os.path.join(os.path.dirname(file_path), new_name)
                shutil.move(final_path, dest_path)
                
                logger.info(f"文檔處理成功: {dest_path}")
                return dest_path, new_name
            else:
                logger.error("修改文檔失敗")
                return None, None
                
        except Exception as e:
            logger.error(f"處理受保護文檔失敗: {str(e)}")
            raise
        finally:
            # 清理
            if doc:
                try:
                    doc.Close(SaveChanges=False)
                except:
                    pass
            
            # 清理臨時文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
    
    def __del__(self):
        """析構函數 - 清理資源"""
        try:
            self._reset_word_app()
            self.executor.shutdown(wait=False)
        except:
            pass

# 全局處理器實例
_com_safe_processor = None

def get_com_safe_processor():
    """獲取 COM 安全的處理器實例"""
    global _com_safe_processor
    if _com_safe_processor is None:
        _com_safe_processor = COMSafeWordProcessor()
    return _com_safe_processor

def process_document_com_safe(file_path, file_name, pudcno, user_id, 
                             file_password, modify_func):
    """
    使用 COM 安全處理器處理文檔
    """
    processor = get_com_safe_processor()
    return processor.process_document(
        file_path, file_name, pudcno, user_id, file_password, modify_func
    )