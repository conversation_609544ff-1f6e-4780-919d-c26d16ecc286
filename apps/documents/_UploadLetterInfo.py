# -*- coding: UTF8 -*-
import logging
import os

import chardet
import pandas as pd
import zipfile
import xml.etree.ElementTree as ET
import pythoncom
import win32com.client
from django.conf import settings
from django.db import connection
from rest_framework import status

from utils.main_utils import remove_folder, remove_file, is_word_protected, can_open_with_password
from utils.token_utils import verify_access_token

DOWNLOAD_PUDCHT_SQL = """
    SELECT ATTACHMENT_NAME
      FROM ( SELECT FILEARTICLE ATTACHMENT_NAME
               FROM PUDCHT )
     WHERE 1 = 1
"""

DOWNLOAD_ATTACHMENT_SQL = """
    SELECT ATTACHMENT_NAME
      FROM PUDCHT_ATTACHMENT
     WHERE 1 = 1
"""

def fetch_from_db(sql, params=None):
    with connection.cursor() as cursor:
        cursor.execute(sql, params or {})
        return cursor.fetchall()

# 取得檔案是否存在
def get_file_existence(file_name):
    strCond = ' AND ATTACHMENT_NAME = :qATTACHMENT_NAME'
    condition = {'qATTACHMENT_NAME': file_name}

    sSTM = DOWNLOAD_PUDCHT_SQL + strCond + ' UNION ' + DOWNLOAD_ATTACHMENT_SQL + strCond
    # print('sSTM', sSTM)
    # print('condition', condition)

    data = fetch_from_db(sSTM, condition)

    return data[0][0] if data else None

def file_path(file, serial_number):
    folder_path = os.path.join(settings.BASE_DIR, 'uploads', '公文作業', serial_number)
    # print('folder_path', folder_path)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    return os.path.join(folder_path, file.name)

# def is_word_protected(docx_path):
#     try:
#         with zipfile.ZipFile(docx_path, 'r') as z:
#             # 嘗試提取wordsettings.xml檔
#             with z.open('word/settings.xml') as f:
#                 tree = ET.parse(f)
#                 root = tree.getroot()
#                 protection_tag = '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}documentProtection'
#                 if root.find(protection_tag) is not None:
#                     return True
#         return False
#     except Exception as e:
#         print(f"Error occurred: {str(e)}")
#         return True  # 這邊可以選擇當有異常發生時的處理方式

# # 檢查Word文件是否可以使用提供的密碼來打開
# def can_open_with_password(doc_path, password):
#     try:
#         pythoncom.CoInitialize()
#         word_app = win32com.client.gencache.EnsureDispatch("Word.Application")
#         doc = word_app.Documents.Open(doc_path, False, False, False, password)
#
#         # 如果你只是想檢查密碼而不進行任何操作，可以立即關閉文件
#         doc.Close()
#         word_app.Quit()
#         return True
#     except Exception as e:
#         print("Error occurred:" + str(e))
#         return False

def parse_txt_file(content):
    lines = content.split('\n')
    parsed_data = []

    for line in lines:
        if line:
            first_value = line[:10].strip()
            second_value = str(int(line[10:13].strip()))  # 去除前面的0
            fourth_value = line[14:].split(':', 1)[-1].strip()

            parsed_data.append((first_value, second_value, fourth_value))

    return parsed_data

# txt檔案上傳
def handle_txt_file(file, content, serial_number):
    # 保存檔案
    path = file_path(file, serial_number)
    # print('file_path', path)
    try:
        with open(path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
    except Exception as e:
        print('檔案上傳失敗', str(e))
        return '檔案上傳失敗', status.HTTP_500_INTERNAL_SERVER_ERROR

    try:
        parsed_data = parse_txt_file(content)
        # print('parsed_data', parsed_data)
        with connection.cursor() as cursor:
            for data in parsed_data:
                insert_query = """
                    INSERT INTO PUDCHT_FILESTATISTICS (PUDCNO, DEALER, REMARKNUMBER, REMARKVALUE)
                    VALUES (%s, %s, %s, %s)
                """
                cursor.execute(insert_query, (serial_number, data[0], data[1], data[2]))

        return 'TXT檔案上傳成功', status.HTTP_200_OK
    except ValueError as e:
        return str(e), status.HTTP_400_BAD_REQUEST

# excel檔案上傳
def handle_excel_file(file, default_value, serial_number):
    # 保存檔案
    path = file_path(file, serial_number)
    # print('file_path', path)
    try:
        with open(path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)
    except Exception as e:
        return '檔案上傳失敗' + str(e), status.HTTP_500_INTERNAL_SERVER_ERROR

    try:
        # 使用dtype參數，確保經銷商代號的列始終被讀取為字符串，保留開頭為0的數據
        df = pd.read_excel(file, engine='openpyxl', dtype={"經銷商代號": str})

        if default_value == "1":
            if "經銷商代號" not in df.columns:
                return 'Excel文件格式錯誤', status.HTTP_400_BAD_REQUEST

            remark_columns = [col for col in df.columns if col.startswith("備註:")]

            if not remark_columns:
                return 'Excel文件中無"備註:"的關鍵字', status.HTTP_400_BAD_REQUEST

            for _, row in df.iterrows():
                if pd.isna(row["經銷商代號"]):
                    return f'Excel文件中存在不完整的資料: {row}', status.HTTP_400_BAD_REQUEST

            with connection.cursor() as cursor:
                delete_query = "DELETE FROM PUDCHT_FILESTATISTICS WHERE PUDCNO = %s"
                cursor.execute(delete_query, [serial_number])

                for _, row in df.iterrows():
                    dealer_code = row["經銷商代號"]
                    for remark_col in remark_columns:
                        remark_number = remark_col.split(':')[1]
                        remark_value = row[remark_col]
                        insert_query = """
                            INSERT INTO PUDCHT_FILESTATISTICS (PUDCNO, DEALER, REMARKNUMBER, REMARKVALUE)
                            VALUES (%s, %s, %s, %s)
                        """
                        # print('insert_query', insert_query, serial_number, dealer_code, remark_number, remark_value)
                        cursor.execute(insert_query, (serial_number, dealer_code, remark_number, remark_value))

            return '檔案上傳成功', status.HTTP_200_OK
        else:
            return '檔案上傳成功', status.HTTP_200_OK

    except Exception as e:
        return '檔案格式不正確或檔案讀取失敗: ' + str(e), status.HTTP_400_BAD_REQUEST

# word檔案上傳
def handle_word_file(file, serial_number, file_password, check_password):
    # 保存檔案
    path = file_path(file, serial_number)
    # print('file_path', path)
    try:
        with open(path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        if is_word_protected(path):
            if file_password == "null":
                # print(type(check_password))
                if check_password == '0':
                    remove_file(f"{path}")
                    return '檔案是受保護的，請輸入密碼，若沒有統計檔請將本文檔案，有保護密碼打勾', status.HTTP_400_BAD_REQUEST
            else:
                if not can_open_with_password(path, file_password):
                    remove_file(f"{path}")
                    return '提供的密碼不正確', status.HTTP_400_BAD_REQUEST

        return '檔案上傳成功', status.HTTP_200_OK

    except ValueError as ve:  # 捕捉到的無效Word檔案異常
        return str(ve), status.HTTP_400_BAD_REQUEST
    except Exception as e:
        logging.error("檔案上傳失敗" + str(e), exc_info=True)
        return '檔案上傳失敗' + str(e), status.HTTP_500_INTERNAL_SERVER_ERROR

# pdf檔案上傳
def handle_pdf_file(file, serial_number):
    # 保存檔案
    path = file_path(file, serial_number)
    # print('file_path', path)
    try:
        with open(path, 'wb+') as destination:
            for chunk in file.chunks():
                destination.write(chunk)

        return '檔案上傳成功', status.HTTP_200_OK
    except Exception as e:
        logging.error("檔案上傳失敗" + str(e), exc_info=True)
        return '檔案上傳失敗' + str(e), status.HTTP_500_INTERNAL_SERVER_ERROR

def select_letter_upload(request):
    if request.method == 'POST':

        is_valid, message, status_code = verify_access_token(request)

        if not is_valid:
            return message, status_code

        file = request.FILES.get('file')
        if not file:
            return '請選擇檔案', status.HTTP_400_BAD_REQUEST

        # 檢查檔案大小是否為0
        if file.size == 0:
            return '檔案大小為0', status.HTTP_400_BAD_REQUEST

        # 檢查defaultValue是否存在
        if 'defaultValue' not in request.POST:
            return 'defaultValue不存在', status.HTTP_400_BAD_REQUEST

        if 'serialNumber' not in request.POST:
            return 'serialNumber不存在', status.HTTP_400_BAD_REQUEST

        default_value = request.POST.get('defaultValue')

        serial_number = request.POST.get('serialNumber')

        file_password = request.POST.get('filePassword')

        check_password = request.POST.get('checkPassword')

        file_extension = os.path.splitext(file.name)[1].lower()

        print('default_value', default_value, 'serial_number', serial_number, 'file_password', file_password, 'check_password', check_password)

        if get_file_existence(file.name):
            return '已存在相同名稱的文件於不同函文編號', status.HTTP_400_BAD_REQUEST

        if default_value == "1":
            if file_extension not in ['.xlsx', '.txt']:
                return '不支援的文件格式，只支援.xlsx, .txt', status.HTTP_400_BAD_REQUEST
            if check_password == '1':
                return '沒有統計檔: 文檔案，有保護密碼，已打勾無法上傳統計檔案', status.HTTP_400_BAD_REQUEST

        if file_extension == '.txt':
            rawdata = file.read()
            result = chardet.detect(rawdata)
            charenc = result['encoding']
            content = rawdata.decode(charenc)
            return handle_txt_file(file, content, serial_number)
        elif file_extension in ['.xlsx']:
            return handle_excel_file(file, default_value, serial_number)
        elif file_extension in ['.docx']:
            return handle_word_file(file, serial_number, file_password, check_password)
        elif file_extension in ['.pdf']:
            return handle_pdf_file(file, serial_number)
        else:
            return '不支援的文件格式，只支援.xlsx, .docx, .pdf', status.HTTP_400_BAD_REQUEST
