# -*- coding: utf-8 -*-
"""
Word processor module
Lazy loading to avoid startup issues
"""

import logging
from .word_processor_singleton import ensure_single_init

# Basic module info
__version__ = '1.0.0'
__author__ = 'HEYSONG'

logger = logging.getLogger(__name__)

# Lazy loading - components will be imported when needed
# This avoids circular imports and startup errors

def get_enterprise_processor():
    """Get enterprise processor instance (lazy loading)"""
    try:
        from .word_enterprise_processor import enterprise_processor
        return enterprise_processor
    except Exception as e:
        logger.error(f"Failed to get enterprise processor: {str(e)}")
        return None

def get_auto_manager():
    """Get auto manager instance (lazy loading)"""
    try:
        from .word_processor_auto_manager import get_auto_manager as _get_manager
        return _get_manager()
    except Exception as e:
        logger.error(f"Failed to get auto manager: {str(e)}")
        return None

def get_cache():
    """Get cache instance (lazy loading)"""
    try:
        from .word_processor_cache import cache
        return cache
    except Exception as e:
        logger.error(f"Failed to get cache: {str(e)}")
        return None

@ensure_single_init
def init_word_processor():
    """Initialize word processor system (only once)"""
    try:
        # Import and initialize components
        processor = get_enterprise_processor()
        manager = get_auto_manager()
        cache = get_cache()
        
        if processor and manager and cache:
            logger.info("Word processor system initialized successfully")
            return True
        else:
            logger.error("Some components failed to initialize")
            return False
    except Exception as e:
        logger.error(f"Failed to initialize word processor: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Export functions instead of direct imports
__all__ = [
    'get_enterprise_processor',
    'get_auto_manager', 
    'get_cache',
    'init_word_processor',
]