# Word 文件並發處理解決方案

## 問題描述
當多個使用者同時下載需要 Word 處理的文件時，系統會發生卡住的情況。這是因為 Word COM 對象不支援多執行緒並發操作。

## 解決方案：佇列機制

### 核心概念
- 使用單一執行緒處理所有 Word 操作
- 所有請求排隊等待處理
- 自動管理 Word 應用程序生命週期

### 新增檔案

#### 1. word_queue_processor.py
```python
# 路徑：/apps/documents/word_queue_processor.py
# 功能：Word 文檔處理佇列，使用單一執行緒處理所有 Word 操作

主要特性：
- 單例模式確保全局只有一個處理器
- 自動啟動工作執行緒
- 任務佇列管理
- Word 應用程序生命週期管理
- 錯誤恢復機制
```

### 修改的檔案

#### 1. _DownloadLetterInfo.py

##### 修改內容：
1. **引入佇列處理器**
```python
from .word_queue_processor import process_word_task
```

2. **移除原有的 word_application() 上下文管理器**
   - 改為使用 DispatchEx 而不是 gencache.EnsureDispatch
   - 設置 DisplayAlerts = 0 避免彈出對話框

3. **新增 unprotect_and_protect_docx_worker 函數**
   - 專門在佇列中執行的工作函數
   - 接收 word_app 參數（由佇列處理器提供）

4. **修改 unprotect_and_protect_docx 函數**
```python
def unprotect_and_protect_docx(file_path, file_name, pudcno, user_id, file_password, modify_func):
    try:
        # 提交任務到佇列
        result = process_word_task(
            unprotect_and_protect_docx_worker,
            file_path, file_name, pudcno, user_id, file_password
        )
        return result
    except Exception as e:
        logging.error(f"處理文件時發生錯誤: {str(e)}")
        return None, None
```

5. **改善錯誤處理**
```python
if (role == '0') and get_file_statistics(user_id, pudcno) and (noModifyFile == 'Y'):
    result = unprotect_and_protect_docx(file_url, file_name, pudcno, user_id, file_password,
                                                     modify_file_content)
    if result and result[0] and result[1]:
        file_url, file_name = result
    else:
        logging.error(f"文件處理失敗")
        return "文件處理失敗", status.HTTP_500_INTERNAL_SERVER_ERROR
```

#### 2. _DownloadBusinessNotificationInfo.py

##### 修改內容：
1. **引入佇列處理器**
```python
from .word_queue_processor import process_word_task
```

2. **新增 unprotect_and_protect_docx_worker 函數**
   - 與 _DownloadLetterInfo.py 相同的處理邏輯

3. **修改 unprotect_and_protect_docx 函數**
   - 使用佇列處理取代直接操作

4. **修改價格下載功能**
```python
# 使用佇列處理 Word 文件
def extract_data_worker(temp_path, rout, user_id, word_app=None):
    if not word_app:
        return []
    doc = word_app.Documents.Open(temp_path)
    try:
        return extract_table_data(doc, rout, user_id)
    finally:
        doc.Close()

table_data = process_word_task(extract_data_worker, temp_word_path, rout, user_id)
```

5. **改善錯誤處理**
   - 與 _DownloadLetterInfo.py 相同

## 技術細節

### WordTask 類
```python
class WordTask:
    def __init__(self, task_id, func, args, kwargs):
        self.task_id = task_id
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.result = None
        self.error = None
        self.completed = threading.Event()
```

### WordQueueProcessor 類
- 單例模式實現
- 任務佇列管理
- Word 應用程序生命週期管理
- 自動錯誤恢復

### 關鍵改進
1. **COM 錯誤處理**
   - 使用 try-except 包裝所有 COM 操作
   - 避免直接訪問可能失敗的屬性

2. **資源管理**
   - 確保文檔在任何情況下都會被關閉
   - 臨時文件自動清理

3. **錯誤恢復**
   - Word 崩潰時自動重新初始化
   - 任務失敗時返回錯誤信息

## 使用方式

### 1. 基本使用
```python
from .word_queue_processor import process_word_task

# 定義工作函數
def my_word_task(file_path, word_app=None):
    if not word_app:
        return None
    doc = word_app.Documents.Open(file_path)
    try:
        # 處理文檔
        return result
    finally:
        doc.Close()

# 提交任務
result = process_word_task(my_word_task, file_path)
```

### 2. 錯誤處理
```python
try:
    result = process_word_task(my_word_task, file_path)
    if result:
        # 處理成功
    else:
        # 處理失敗
except Exception as e:
    # 處理異常
```

## 優點

1. **完全避免並發問題**
   - 所有 Word 操作在單一執行緒中執行
   - 不會有 COM 對象競爭

2. **自動資源管理**
   - Word 應用程序自動初始化和清理
   - 文檔自動關閉

3. **錯誤恢復機制**
   - Word 崩潰時自動重啟
   - 任務失敗不影響其他任務

4. **效能優化**
   - 重複使用同一個 Word 實例
   - 減少啟動和關閉的開銷

5. **易於維護**
   - 集中管理所有 Word 操作
   - 統一的錯誤處理

## 注意事項

1. **任務函數要求**
   - 必須接受 `word_app` 參數
   - 必須處理 `word_app` 為 None 的情況
   - 必須確保文檔被正確關閉

2. **超時處理**
   - 佇列空閒 60 秒後會檢查 Word 狀態
   - 可根據需要調整超時時間

3. **日誌記錄**
   - 所有操作都有詳細日誌
   - 便於問題排查

## 未來改進建議

1. **任務優先級**
   - 實現優先級佇列
   - 緊急任務優先處理

2. **任務取消**
   - 支援取消等待中的任務
   - 中斷正在執行的任務

3. **多實例支援**
   - 根據負載動態調整 Word 實例數量
   - 平衡處理能力和資源消耗

4. **監控和統計**
   - 任務執行時間統計
   - 佇列長度監控
   - 錯誤率統計