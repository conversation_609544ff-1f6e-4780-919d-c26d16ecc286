# -*- coding: utf-8 -*-
"""
Word 處理器緩存管理
提供多種緩存後端支持，自動降級
"""

import json
import os
import time
import pickle
from datetime import datetime, timedelta
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class FileCache:
    """基於文件系統的緩存實現"""
    
    def __init__(self, cache_dir=None):
        if cache_dir is None:
            cache_dir = os.path.join(os.path.dirname(__file__), '.cache')
        self.cache_dir = cache_dir
        Path(self.cache_dir).mkdir(parents=True, exist_ok=True)
    
    def _get_cache_path(self, key):
        """獲取緩存文件路徑"""
        # 清理 key 中的特殊字符
        safe_key = "".join(c if c.isalnum() or c in ('_', '-') else '_' for c in key)
        return os.path.join(self.cache_dir, f"{safe_key}.cache")
    
    def set(self, key, value, timeout=None):
        """設置緩存"""
        try:
            cache_data = {
                'value': value,
                'timestamp': time.time(),
                'timeout': timeout
            }
            cache_path = self._get_cache_path(key)
            with open(cache_path, 'wb') as f:
                pickle.dump(cache_data, f)
            return True
        except Exception as e:
            logger.error(f"設置緩存失敗 {key}: {str(e)}")
            return False
    
    def get(self, key, default=None):
        """獲取緩存"""
        try:
            cache_path = self._get_cache_path(key)
            if not os.path.exists(cache_path):
                return default
            
            with open(cache_path, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 檢查是否過期
            if cache_data.get('timeout'):
                elapsed = time.time() - cache_data['timestamp']
                if elapsed > cache_data['timeout']:
                    os.remove(cache_path)
                    return default
            
            return cache_data['value']
        except Exception as e:
            logger.error(f"獲取緩存失敗 {key}: {str(e)}")
            return default
    
    def delete(self, key):
        """刪除緩存"""
        try:
            cache_path = self._get_cache_path(key)
            if os.path.exists(cache_path):
                os.remove(cache_path)
            return True
        except Exception as e:
            logger.error(f"刪除緩存失敗 {key}: {str(e)}")
            return False
    
    def clear(self):
        """清空所有緩存"""
        try:
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.cache'):
                    os.remove(os.path.join(self.cache_dir, filename))
            return True
        except Exception as e:
            logger.error(f"清空緩存失敗: {str(e)}")
            return False

class MemoryCache:
    """基於內存的緩存實現"""
    
    def __init__(self):
        self._cache = {}
        self._timeouts = {}
    
    def set(self, key, value, timeout=None):
        """設置緩存"""
        self._cache[key] = value
        if timeout:
            self._timeouts[key] = time.time() + timeout
        return True
    
    def get(self, key, default=None):
        """獲取緩存"""
        # 檢查是否過期
        if key in self._timeouts:
            if time.time() > self._timeouts[key]:
                self.delete(key)
                return default
        
        return self._cache.get(key, default)
    
    def delete(self, key):
        """刪除緩存"""
        self._cache.pop(key, None)
        self._timeouts.pop(key, None)
        return True
    
    def clear(self):
        """清空所有緩存"""
        self._cache.clear()
        self._timeouts.clear()
        return True

class SmartCache:
    """智能緩存，自動選擇最佳緩存後端"""
    
    def __init__(self):
        self._backend = None
        self._init_backend()
    
    def _init_backend(self):
        """初始化緩存後端"""
        # 嘗試使用 Django 緩存
        try:
            from django.core.cache import cache as django_cache
            # 測試 Django 緩存是否可用
            django_cache.set('_test_key', 'test', 1)
            if django_cache.get('_test_key') == 'test':
                self._backend = django_cache
                logger.info("使用 Django 緩存後端")
                return
        except Exception as e:
            logger.warning(f"Django 緩存不可用: {str(e)}")
        
        # 降級到文件緩存
        try:
            self._backend = FileCache()
            logger.info("使用文件系統緩存後端")
        except Exception as e:
            logger.warning(f"文件緩存初始化失敗: {str(e)}")
            # 最後降級到內存緩存
            self._backend = MemoryCache()
            logger.info("使用內存緩存後端")
    
    def set(self, key, value, timeout=None):
        """設置緩存"""
        try:
            if hasattr(self._backend, 'set'):
                return self._backend.set(key, value, timeout=timeout)
            return False
        except Exception as e:
            logger.error(f"緩存設置失敗: {str(e)}")
            # 嘗試降級
            if not isinstance(self._backend, MemoryCache):
                self._backend = MemoryCache()
                return self._backend.set(key, value, timeout=timeout)
            return False
    
    def get(self, key, default=None):
        """獲取緩存"""
        try:
            if hasattr(self._backend, 'get'):
                return self._backend.get(key, default=default)
            return default
        except Exception as e:
            logger.error(f"緩存獲取失敗: {str(e)}")
            return default
    
    def delete(self, key):
        """刪除緩存"""
        try:
            if hasattr(self._backend, 'delete'):
                return self._backend.delete(key)
            return False
        except Exception:
            return False
    
    def clear(self):
        """清空緩存"""
        try:
            if hasattr(self._backend, 'clear'):
                return self._backend.clear()
            elif hasattr(self._backend, 'delete_pattern'):
                # Django 緩存的清空方式
                return self._backend.delete_pattern('*')
            return False
        except Exception:
            return False

# 全局緩存實例
cache = SmartCache()

# 輔助函數
def get_cache():
    """獲取緩存實例"""
    return cache

def set_cache(key, value, timeout=None):
    """設置緩存值"""
    return cache.set(key, value, timeout)

def get_cached(key, default=None):
    """獲取緩存值"""
    return cache.get(key, default)

def delete_cache(key):
    """刪除緩存值"""
    return cache.delete(key)

def clear_all_cache():
    """清空所有緩存"""
    return cache.clear()