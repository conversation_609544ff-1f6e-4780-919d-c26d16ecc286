# -*- coding: utf-8 -*-
"""
Word 處理器管理 API
提供 RESTful API 來控制和監控系統
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from .word_processor_cache import cache, get_cached, set_cache, clear_all_cache
import json

from .word_processor_auto_manager import auto_manager, get_processor_health
from .word_processor_config import WORD_PROCESSOR_CONFIG, apply_load_profile

@csrf_exempt
@require_http_methods(["GET"])
def processor_status(request):
    """
    獲取處理器狀態
    GET /api/documents/processor/status
    """
    stats = auto_manager.get_current_stats()
    health = get_processor_health()
    performance = auto_manager.get_performance_summary()
    
    response_data = {
        'status': 'running' if auto_manager.running else 'stopped',
        'health': health,
        'stats': stats,
        'performance': performance,
        'config': {
            'processor_type': WORD_PROCESSOR_CONFIG.get('processor_type', 'enterprise'),
            'monitor_interval': auto_manager.monitor_interval,
            'auto_scale_enabled': auto_manager.auto_scale_enabled,
            'alert_enabled': auto_manager.alert_enabled,
        }
    }
    
    return JsonResponse(response_data)

@csrf_exempt
@require_http_methods(["POST"])
def processor_control(request):
    """
    控制處理器
    POST /api/documents/processor/control
    {
        "action": "start|stop|restart",
        "config": {
            "monitor_interval": 30,
            "auto_scale": true,
            "alerts": true
        }
    }
    """
    try:
        data = json.loads(request.body)
        action = data.get('action')
        config = data.get('config', {})
        
        # 執行動作
        if action == 'start':
            auto_manager.start()
            message = "處理器已啟動"
        elif action == 'stop':
            auto_manager.stop()
            message = "處理器已停止"
        elif action == 'restart':
            auto_manager.stop()
            auto_manager.start()
            message = "處理器已重啟"
        else:
            return JsonResponse({'error': '無效的動作'}, status=400)
        
        # 更新配置
        if config:
            if 'monitor_interval' in config:
                auto_manager.set_monitor_interval(config['monitor_interval'])
            if 'auto_scale' in config:
                auto_manager.enable_auto_scale(config['auto_scale'])
            if 'alerts' in config:
                auto_manager.enable_alerts(config['alerts'])
        
        return JsonResponse({
            'success': True,
            'message': message,
            'status': 'running' if auto_manager.running else 'stopped'
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def processor_scale(request):
    """
    調整處理器規模
    POST /api/documents/processor/scale
    {
        "profile": "peak|normal|low",
        "custom": {
            "pool_size": 20,
            "max_workers": 30
        }
    }
    """
    try:
        data = json.loads(request.body)
        profile = data.get('profile')
        custom = data.get('custom', {})
        
        if profile:
            # 使用預定義配置
            success = auto_manager.force_scale(profile)
            if success:
                message = f"已切換到 {profile} 配置"
            else:
                return JsonResponse({'error': '無效的配置名稱'}, status=400)
        elif custom:
            # 使用自定義配置
            WORD_PROCESSOR_CONFIG['enterprise'].update(custom)
            message = "已應用自定義配置"
        else:
            return JsonResponse({'error': '缺少配置參數'}, status=400)
        
        return JsonResponse({
            'success': True,
            'message': message,
            'current_config': WORD_PROCESSOR_CONFIG['enterprise']
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def processor_alerts(request):
    """
    獲取警報記錄
    GET /api/documents/processor/alerts?limit=50
    """
    limit = int(request.GET.get('limit', 50))
    alerts = get_cached('word_processor_alerts', [])
    
    # 獲取最近的警報
    recent_alerts = alerts[-limit:] if len(alerts) > limit else alerts
    
    return JsonResponse({
        'total': len(alerts),
        'alerts': recent_alerts,
        'health': get_processor_health()
    })

@csrf_exempt
@require_http_methods(["DELETE"])
def processor_clear_cache(request):
    """
    清除處理器緩存
    DELETE /api/documents/processor/cache
    """
    try:
        from .word_enterprise_processor import enterprise_processor
        
        # 清理文件緩存
        if hasattr(enterprise_processor, 'file_cache'):
            enterprise_processor.file_cache.cleanup_old_cache()
        
        # 清除性能統計緩存
        cache.delete('word_processor_stats')
        cache.delete('word_processor_performance')
        cache.delete('word_processor_alerts')
        
        # 或使用清空所有緩存
        # clear_all_cache()
        
        return JsonResponse({
            'success': True,
            'message': '緩存已清除'
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def processor_performance_report(request):
    """
    獲取性能報告
    GET /api/documents/processor/performance
    """
    stats = auto_manager.get_current_stats()
    performance = get_cached('word_processor_performance', {})
    avg_stats = get_cached('word_processor_avg_stats', {})
    
    # 計算運行時間
    from .word_enterprise_processor import enterprise_processor
    uptime = 0
    if hasattr(enterprise_processor, 'start_time'):
        import time
        uptime = time.time() - enterprise_processor.start_time
    
    report = {
        'uptime_seconds': uptime,
        'current': {
            'available_workers': stats.get('processor', {}).get('available_workers', 0),
            'pending_tasks': stats.get('processor', {}).get('pending_tasks', 0),
            'processing_tasks': stats.get('processor', {}).get('processing_tasks', 0),
        },
        'performance': {
            'success_rate': performance.get('success_rate', 0),
            'avg_processing_time': performance.get('avg_time', 0),
            'cache_hit_rate': performance.get('cache_hit_rate', 0),
            'throughput': performance.get('throughput', 0),
        },
        'resources': {
            'avg_cpu': avg_stats.get('avg_cpu', 0),
            'avg_memory_mb': avg_stats.get('avg_memory', 0),
            'current_cpu': stats.get('system', {}).get('cpu_percent', 0),
            'current_memory_mb': stats.get('system', {}).get('memory_mb', 0),
        }
    }
    
    return JsonResponse(report)

# URL 路由配置（添加到 urls.py）
"""
from django.urls import path
from apps.documents import word_processor_api

urlpatterns = [
    # Word 處理器管理 API
    path('api/documents/processor/status', word_processor_api.processor_status),
    path('api/documents/processor/control', word_processor_api.processor_control),
    path('api/documents/processor/scale', word_processor_api.processor_scale),
    path('api/documents/processor/alerts', word_processor_api.processor_alerts),
    path('api/documents/processor/cache', word_processor_api.processor_clear_cache),
    path('api/documents/processor/performance', word_processor_api.processor_performance_report),
]
"""