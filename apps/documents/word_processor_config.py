# -*- coding: utf-8 -*-
"""
Word 處理器配置文件
可根據實際負載情況調整參數
"""

# 基本配置
WORD_PROCESSOR_CONFIG = {
    # 處理器類型：'queue'（原始佇列）, 'pool'（並發池）, 'enterprise'（企業級）
    'processor_type': 'enterprise',
    
    # 企業級處理器配置
    'enterprise': {
        'pool_size': 20,  # Word 實例池大小（建議為預期同時用戶數的 40%）
        'max_workers': 30,  # 最大並發執行緒數
        'cache_enabled': True,  # 是否啟用緩存
        'cache_size_gb': 50,  # 緩存大小（GB）
        'cache_ttl_hours': 48,  # 緩存有效期（小時）
        'task_timeout': 300,  # 任務超時時間（秒）
        'max_retries': 3,  # 最大重試次數
    },
    
    # 並發池處理器配置
    'pool': {
        'max_workers': 3,  # 最大並發 Word 實例數
        'retry_delay': 2,  # 重試延遲（秒）
        'max_retries': 3,  # 最大重試次數
    },
    
    # 原始佇列處理器配置
    'queue': {
        'worker_timeout': 60,  # 工作執行緒超時（秒）
    },
    
    # 監控配置
    'monitoring': {
        'enabled': True,  # 是否啟用監控
        'interval': 60,  # 監控間隔（秒）
        'alert_threshold': {
            'available_workers': 2,  # 可用工作執行緒警告閾值
            'pending_tasks': 50,  # 待處理任務警告閾值
            'error_rate': 0.1,  # 錯誤率警告閾值
        }
    },
    
    # 性能優化配置
    'optimization': {
        'batch_processing': True,  # 是否啟用批量處理
        'batch_size': 10,  # 批量處理大小
        'priority_queue': True,  # 是否使用優先級佇列
        'compression': True,  # 是否壓縮緩存文件
    }
}

# 負載配置（根據不同時段調整）
LOAD_PROFILES = {
    'peak': {  # 高峰期（如工作時間）
        'pool_size': 30,
        'max_workers': 50,
    },
    'normal': {  # 正常期
        'pool_size': 20,
        'max_workers': 30,
    },
    'low': {  # 低峰期（如夜間）
        'pool_size': 10,
        'max_workers': 15,
    }
}

# 文件類型優先級
FILE_PRIORITIES = {
    'business_notification': 5,  # 業務通報
    'letter': 5,  # 公文
    'price_download': 3,  # 價格下載（較高優先級）
    'report': 7,  # 報表（較低優先級）
    'default': 5,  # 默認優先級
}

# 錯誤處理策略
ERROR_STRATEGIES = {
    'retry_with_delay': {
        'delays': [1, 5, 10],  # 重試延遲（秒）
        'max_attempts': 3,
    },
    'fallback_to_queue': True,  # 失敗時降級到佇列處理器
    'cache_failed_tasks': True,  # 緩存失敗的任務以便重試
    'notify_on_failure': True,  # 失敗時通知管理員
}

# 系統資源限制
RESOURCE_LIMITS = {
    'max_memory_usage_gb': 8,  # 最大記憶體使用（GB）
    'max_cpu_percentage': 80,  # 最大 CPU 使用率
    'max_disk_usage_gb': 100,  # 最大磁碟使用（GB）
    'temp_cleanup_interval': 3600,  # 臨時文件清理間隔（秒）
}

# 日誌配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'word_processor.log',
    'max_size_mb': 100,
    'backup_count': 5,
    'performance_logging': True,  # 是否記錄性能指標
}

def get_config(key, default=None):
    """獲取配置值"""
    return WORD_PROCESSOR_CONFIG.get(key, default)

def get_processor_config():
    """獲取當前處理器的配置"""
    processor_type = WORD_PROCESSOR_CONFIG['processor_type']
    return WORD_PROCESSOR_CONFIG.get(processor_type, {})

def update_config(updates):
    """動態更新配置"""
    WORD_PROCESSOR_CONFIG.update(updates)

def apply_load_profile(profile_name):
    """應用負載配置文件"""
    if profile_name in LOAD_PROFILES:
        profile = LOAD_PROFILES[profile_name]
        WORD_PROCESSOR_CONFIG['enterprise'].update(profile)
        return True
    return False