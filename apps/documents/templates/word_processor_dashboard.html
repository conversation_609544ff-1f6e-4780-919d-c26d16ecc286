<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word 處理器監控儀表板</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        .status.healthy { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.critical { background: #f8d7da; color: #721c24; }
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child { border-bottom: none; }
        .metric-label { color: #666; }
        .metric-value { font-weight: 500; }
        .controls {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: opacity 0.2s;
        }
        button:hover { opacity: 0.8; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .alert-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .alert-item {
            padding: 8px;
            margin: 4px 0;
            border-radius: 4px;
            font-size: 14px;
        }
        .alert-item.warning { background: #fff3cd; }
        .alert-item.error { background: #f8d7da; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Word 處理器監控儀表板</h1>
            <p>系統狀態: <span id="system-status" class="status">載入中...</span></p>
        </div>

        <div class="grid">
            <!-- 處理器狀態 -->
            <div class="card">
                <h3>處理器狀態</h3>
                <div class="metric">
                    <span class="metric-label">可用工作執行緒</span>
                    <span class="metric-value" id="available-workers">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">待處理任務</span>
                    <span class="metric-value" id="pending-tasks">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">處理中任務</span>
                    <span class="metric-value" id="processing-tasks">-</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="worker-usage" style="width: 0%"></div>
                </div>
            </div>

            <!-- 性能指標 -->
            <div class="card">
                <h3>性能指標</h3>
                <div class="metric">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value" id="success-rate">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">平均處理時間</span>
                    <span class="metric-value" id="avg-time">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">緩存命中率</span>
                    <span class="metric-value" id="cache-hit-rate">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">吞吐量</span>
                    <span class="metric-value" id="throughput">-</span>
                </div>
            </div>

            <!-- 系統資源 -->
            <div class="card">
                <h3>系統資源</h3>
                <div class="metric">
                    <span class="metric-label">CPU 使用率</span>
                    <span class="metric-value" id="cpu-usage">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">記憶體使用</span>
                    <span class="metric-value" id="memory-usage">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">執行緒數</span>
                    <span class="metric-value" id="thread-count">-</span>
                </div>
            </div>

            <!-- 控制面板 -->
            <div class="card">
                <h3>控制面板</h3>
                <div class="controls">
                    <button class="btn-success" onclick="controlProcessor('start')">啟動</button>
                    <button class="btn-danger" onclick="controlProcessor('stop')">停止</button>
                    <button class="btn-warning" onclick="controlProcessor('restart')">重啟</button>
                </div>
                <div class="controls">
                    <button class="btn-primary" onclick="scaleProcessor('peak')">高峰模式</button>
                    <button class="btn-primary" onclick="scaleProcessor('normal')">正常模式</button>
                    <button class="btn-primary" onclick="scaleProcessor('low')">低峰模式</button>
                </div>
                <div class="controls">
                    <button class="btn-warning" onclick="clearCache()">清除緩存</button>
                </div>
            </div>

            <!-- 警報列表 -->
            <div class="card">
                <h3>最近警報</h3>
                <div class="alert-list" id="alert-list">
                    <p>暫無警報</p>
                </div>
            </div>

            <!-- 配置信息 -->
            <div class="card">
                <h3>當前配置</h3>
                <div class="metric">
                    <span class="metric-label">處理器類型</span>
                    <span class="metric-value" id="processor-type">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">監控間隔</span>
                    <span class="metric-value" id="monitor-interval">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">自動縮放</span>
                    <span class="metric-value" id="auto-scale">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">警報啟用</span>
                    <span class="metric-value" id="alerts-enabled">-</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API 基礎 URL
        const API_BASE = '/api/documents/processor';

        // 更新間隔（秒）
        const UPDATE_INTERVAL = 5;

        // 獲取系統狀態
        async function fetchStatus() {
            try {
                const response = await fetch(`${API_BASE}/status`);
                const data = await response.json();
                updateDashboard(data);
            } catch (error) {
                console.error('獲取狀態失敗:', error);
            }
        }

        // 獲取警報
        async function fetchAlerts() {
            try {
                const response = await fetch(`${API_BASE}/alerts?limit=10`);
                const data = await response.json();
                updateAlerts(data.alerts);
            } catch (error) {
                console.error('獲取警報失敗:', error);
            }
        }

        // 更新儀表板
        function updateDashboard(data) {
            // 系統狀態
            const statusEl = document.getElementById('system-status');
            statusEl.textContent = data.health;
            statusEl.className = `status ${data.health.toLowerCase()}`;

            // 處理器狀態
            const processor = data.stats.processor || {};
            document.getElementById('available-workers').textContent = 
                `${processor.available_workers || 0} / ${processor.total_workers || 0}`;
            document.getElementById('pending-tasks').textContent = processor.pending_tasks || 0;
            document.getElementById('processing-tasks').textContent = processor.processing_tasks || 0;

            // 工作執行緒使用率
            const workerUsage = processor.total_workers > 0 
                ? ((processor.total_workers - processor.available_workers) / processor.total_workers * 100)
                : 0;
            document.getElementById('worker-usage').style.width = `${workerUsage}%`;

            // 性能指標
            const performance = data.stats.performance || {};
            document.getElementById('success-rate').textContent = 
                `${(performance.success_rate || 0).toFixed(1)}%`;
            document.getElementById('avg-time').textContent = 
                `${(performance.avg_time || 0).toFixed(2)}秒`;
            document.getElementById('cache-hit-rate').textContent = 
                `${(performance.cache_hit_rate || 0).toFixed(1)}%`;
            document.getElementById('throughput').textContent = 
                `${(performance.throughput || 0).toFixed(1)} 文件/分`;

            // 系統資源
            const system = data.stats.system || {};
            document.getElementById('cpu-usage').textContent = 
                `${(system.cpu_percent || 0).toFixed(1)}%`;
            document.getElementById('memory-usage').textContent = 
                `${(system.memory_mb || 0).toFixed(1)} MB`;
            document.getElementById('thread-count').textContent = system.threads || 0;

            // 配置信息
            const config = data.config || {};
            document.getElementById('processor-type').textContent = config.processor_type || '-';
            document.getElementById('monitor-interval').textContent = `${config.monitor_interval || 0}秒`;
            document.getElementById('auto-scale').textContent = config.auto_scale_enabled ? '是' : '否';
            document.getElementById('alerts-enabled').textContent = config.alert_enabled ? '是' : '否';
        }

        // 更新警報列表
        function updateAlerts(alerts) {
            const alertList = document.getElementById('alert-list');
            
            if (!alerts || alerts.length === 0) {
                alertList.innerHTML = '<p>暫無警報</p>';
                return;
            }

            alertList.innerHTML = alerts.map(alert => `
                <div class="alert-item ${alert.level.toLowerCase()}">
                    <strong>[${alert.level}]</strong> ${alert.message}
                    <br><small>${new Date(alert.timestamp).toLocaleString()}</small>
                </div>
            `).join('');
        }

        // 控制處理器
        async function controlProcessor(action) {
            try {
                const response = await fetch(`${API_BASE}/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action })
                });
                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    fetchStatus();
                } else {
                    alert('操作失敗: ' + (data.error || '未知錯誤'));
                }
            } catch (error) {
                alert('操作失敗: ' + error.message);
            }
        }

        // 調整規模
        async function scaleProcessor(profile) {
            try {
                const response = await fetch(`${API_BASE}/scale`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ profile })
                });
                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                    fetchStatus();
                } else {
                    alert('操作失敗: ' + (data.error || '未知錯誤'));
                }
            } catch (error) {
                alert('操作失敗: ' + error.message);
            }
        }

        // 清除緩存
        async function clearCache() {
            if (!confirm('確定要清除所有緩存嗎？')) return;
            
            try {
                const response = await fetch(`${API_BASE}/cache`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (data.success) {
                    alert(data.message);
                } else {
                    alert('操作失敗: ' + (data.error || '未知錯誤'));
                }
            } catch (error) {
                alert('操作失敗: ' + error.message);
            }
        }

        // 定期更新
        function startAutoUpdate() {
            fetchStatus();
            fetchAlerts();
            
            setInterval(() => {
                fetchStatus();
                fetchAlerts();
            }, UPDATE_INTERVAL * 1000);
        }

        // 啟動
        startAutoUpdate();
    </script>
</body>
</html>