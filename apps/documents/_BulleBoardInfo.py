# -*- coding: UTF8 -*-
import itertools

import cx_Oracle
import datetime
import calendar

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import get_ce_today, to_minguo, paginate_data, now_datetime
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' Combobox START '''
# combobox_ocv025_經銷商代號與名稱
combobox_dept_dealer_code_name_sql = """
    SELECT 0 ID, RPAD('ALL', 10, ' ') || RPAD('全部', 20, ' ') DEPT
      FROM DUAL
    UNION ALL
    SELECT ROW_NUMBER() OVER (ORDER BY DEPT) ID, DEPT NAME
      FROM (SELECT RPAD(DEPTCODE, 10, ' ') || RPAD(DEPTNAME, 20, ' ') DEPT
              FROM (SELECT DEPT_CODE007 DEPTCODE, DEPT_DESC_C007 DEPTNAME
                      FROM HRF007@B2B
                     WHERE REMOVE_DATE007 IS NULL
                     UNION ALL
                    SELECT DEPTCODE, DEPTNAME
                      FROM DEPT, HRF005@B2B
                     WHERE REPLACE(OWNERID, 'h', '') = NO005
                     ORDER BY DEPTCODE)
             UNION ALL
            SELECT DEALER
              FROM (SELECT RPAD(AGENT_CODE025, 10, ' ') || RPAD(NVL(BRIEF_NAME025, ' '), 20, ' ') DEALER
                      FROM OCV025@B2B
                     WHERE SUB_TYPE025 = '01'
                       AND EOS_CODE025 = 'Y'
                       AND RETIRE_DATE025 IS NULL
                     ORDER BY AGENT_CODE025))
     """

''' Combobox END '''

''' BULLET_BOARD SELECT START '''
# select_使用者資料
def select_bullet_board_data_sql():
    return """
      WITH ALLCODES AS (SELECT DEPT_CODE007 AS CODE, DEPT_DESC_C007 AS NAME
                          FROM HRF007@B2B
                         WHERE REMOVE_DATE007 IS NULL
                         UNION ALL
                        SELECT DEPTCODE, DEPTNAME
                          FROM DEPT, HRF005@B2B
                         WHERE REPLACE(OWNERID, 'h', '') = NO005
                         UNION ALL
                        SELECT AGENT_CODE025 AS CODE, NVL(BRIEF_NAME025, ' ') AS NAME
                          FROM OCV025@B2B
                         WHERE SUB_TYPE025 = '01' AND EOS_CODE025 = 'Y' AND RETIRE_DATE025 IS NULL)

    SELECT M.BULLETINNUM,
           BEGINDATE, ENDDATE,
           TOPIC, CHI_NAME005, CREATEDATE, OWNERDATE,
           CASE
               WHEN D.VENDORCODE = 'ALL'
                   THEN RPAD(D.VENDORCODE, 10, ' ') || '全部'
               ELSE RPAD(C.CODE, 10, ' ') || C.NAME
           END AS VENDORCODE
      FROM BULLETBOARD_M M, BULLETBOARD_D D, HRF005@B2B H, ALLCODES C
     WHERE M.BULLETINNUM = D.BULLETINNUM
       AND REPLACE(M.OWNERID, 'h', '') = H.NO005
       AND D.VENDORCODE = 'ALL'
       AND C.CODE = :qDEPT_OR_DEALER
    UNION ALL
    SELECT M.BULLETINNUM,
           BEGINDATE, ENDDATE,
           TOPIC, CHI_NAME005, CREATEDATE, OWNERDATE,
              CASE
                WHEN D.VENDORCODE = 'ALL'
                     THEN RPAD(D.VENDORCODE, 10, ' ') || '全部'
                ELSE RPAD(C.CODE, 10, ' ') || C.NAME
              END AS VENDORCODE
      FROM BULLETBOARD_M M, BULLETBOARD_D D, HRF005@B2B H, ALLCODES C
     WHERE M.BULLETINNUM = D.BULLETINNUM
       AND REPLACE(M.OWNERID, 'h', '') = H.NO005
       AND D.VENDORCODE = C.CODE
       AND D.VENDORCODE <> 'ALL'
       AND ( D.VENDORCODE = :qDEPT_OR_DEALER2 OR M.OWNERID = :qUSER_ID)
"""
''' BULLET_BOARD SELECT END '''

""" 部門_經銷商 START """
@validate_access_token_and_params(None)
def select_combobox_dept_dealer_code_name(request, json_data, role, user_id):
    context = "select_combobox_dept_dealer_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:
            try:
                cursor.execute(combobox_dept_dealer_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                result = []
                for row in data:
                    result.append({"id": row[0], "name": row[1]})
                return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


""" 部門_經銷商 END """

''' BULLET_BOARD INSERT START '''

def generate_bulletinnum(cursor):
    today = datetime.date.today()
    roc_year = today.year - 1911  # 民國年份
    today_str = roc_year * 10000 + today.month * 100 + today.day

    # 尋找當天已經存在的最大BULLETINNUM
    query_sql = f"SELECT MAX(BULLETINNUM) FROM BULLETBOARD_M WHERE BULLETINNUM LIKE '{today_str}%'"
    cursor.execute(query_sql)
    max_bulletinnum = cursor.fetchone()[0]

    if max_bulletinnum:
        # 如果找到，增量+1
        next_num = int(max_bulletinnum[-5:]) + 1
    else:
        # 如果沒找到，從00001開始
        next_num = 1

    return f"{today_str}{next_num:05}"  # 格式化為00001, 00002, ...


# 映射字典
BULLET_BOARD_NAME_MAPPING = {
    "bulletin_num": "BULLETINNUM",
    "begin_date": "BEGINDATE",
    "end_date": "ENDDATE",
    "topic": "TOPIC",
    "is_expired": "ISEXPIRED",
    "owner_id": "OWNERID",
    "create_date": "CREATEDATE",
    "owner_date": "OWNERDATE",
}

def is_bulletinnum_exists(bulletinnum, cursor):
    """
    Check if the given bulletinnum exists in the BULLETBOARD_M table.
    """
    check_sql = f"SELECT 1 FROM BULLETBOARD_M WHERE BULLETINNUM = '{bulletinnum}'"
    cursor.execute(check_sql)
    return bool(cursor.fetchone())


def is_dept_vendor_exists(bulletinnum, vendor_code, cursor):
    """
    Check if the given is_dept_vendor_exists exists for a bulletinnum in the BULLETBOARD_D table.
    """
    check_sql = f"SELECT 1 FROM BULLETBOARD_D WHERE BULLETINNUM = '{bulletinnum}' AND VENDORCODE = '{vendor_code}'"
    cursor.execute(check_sql)
    return bool(cursor.fetchone())

def insert_raw_sql(context, data, user_id):

    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                # 生成 bulletinnum
                bulletinnum = generate_bulletinnum(cursor)

                # 確保bulletinnum是唯一的
                while is_bulletinnum_exists(bulletinnum, cursor):
                    bulletinnum = generate_bulletinnum(cursor)

                # 插入主檔資料
                bulletinnum_data = {
                    "bulletin_num": bulletinnum,
                    "begin_date": to_minguo(data['begin_date']),
                    "end_date": to_minguo(data['end_date']),
                    "topic": data['topic'],
                    "is_expired": 'N',
                    "owner_id": user_id,
                    "create_date": to_minguo(get_ce_today()),
                    "owner_date": to_minguo(get_ce_today()),
                }

                # print('bulletinnum_data', bulletinnum_data)

                keys = ", ".join(BULLET_BOARD_NAME_MAPPING[key] for key in bulletinnum_data.keys())
                values = ", ".join(
                    f"'{bulletinnum_data[key]}'" if bulletinnum_data[key] is not None else "null" for key in
                    bulletinnum_data.keys())
                sql = f"INSERT INTO BULLETBOARD_M ({keys}) VALUES ({values})"
                # print('sql', sql)
                cursor.execute(sql)

                # 檢查 dealer_code 是否為 None
                dealer_dealer_code = data.get('dept_dealer_code', [])
                if dealer_dealer_code is None:
                    return handle_error(context, "dealer_dealer_code cannot be None", status.HTTP_400_BAD_REQUEST)

                # 插入明細檔資料
                for dealer_dealer_code_str in dealer_dealer_code:
                    if is_dept_vendor_exists(bulletinnum, dealer_dealer_code_str, cursor):
                        return handle_error(context,
                                            f"Vendor '{dealer_dealer_code_str}' for group '{bulletinnum}' already exists.",
                                            status.HTTP_400_BAD_REQUEST)

                    detail_data = {
                        "BULLETINNUM": bulletinnum,
                        "VENDORCODE": dealer_dealer_code_str
                    }

                    detail_keys = ", ".join(detail_data.keys())
                    detail_values = ", ".join(f"'{detail_data[key]}'" for key in detail_data.keys())
                    detail_sql = f"INSERT INTO BULLETBOARD_D ({detail_keys}) VALUES ({detail_values})"
                    # print('detail_sql', detail_sql)
                    cursor.execute(detail_sql)

                # 在所有插入操作都成功後返回成功信息
                return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def insert_bullet_board_method(request, json_data, role, user_id):
    context = "insert_bullet_board_method"

    if request.method == "POST":

        try:
            result, result_status = insert_raw_sql(context, json_data, user_id)
            # print('result', result, result_status)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' BULLET_BOARD INSERT END '''

''' BULLET_BOARD SELECT START '''

def transform_to_bullet_board_frontend_structure(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["BULLETINNUM"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "bulletin_num": group[0]["BULLETINNUM"],
            "begin_date": group[0]["BEGINDATE"],
            "end_date": group[0]["ENDDATE"],
            "topic": group[0]["TOPIC"],
            "user_name": group[0]["CHI_NAME005"],
            "create_date": group[0]["CREATEDATE"],
            "owner_date": group[0]["OWNERDATE"],
            "details": []
        }
        # 插入明細
        details = []
        for item in group:
            details.append(item["VENDORCODE"])

        total_summary["details"] = details
        report.append(total_summary)
    return report


def select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk):
    ranked_sql = """
                SELECT BULLETINNUM, 
                       TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(BEGINDATE, 1, 3)) + 1911) || SUBSTR(BEGINDATE, 4), 'YYYYMMDD'), 'YYYY/MM/DD') BEGINDATE,
                       TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ENDDATE, 1, 3)) + 1911) || SUBSTR(ENDDATE, 4), 'YYYYMMDD'), 'YYYY/MM/DD') ENDDATE,
                       TOPIC, CHI_NAME005, CREATEDATE, OWNERDATE, VENDORCODE,
                       DENSE_RANK() OVER (ORDER BY BULLETINNUM DESC) AS RNK
                  FROM (  """ + select_bullet_board_data_sql() + """  )
                 WHERE 1 = 1
            """

    if len(sql_conditions) == 0:
        sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

    sql_query = sql_query + " ORDER BY BULLETINNUM DESC "

    # print('sql_query', sql_query)
    # print('params', params)

    ranked_sql1 = """
                SELECT BULLETINNUM, BEGINDATE, ENDDATE, TOPIC, CHI_NAME005, CREATEDATE, OWNERDATE, VENDORCODE, RNK
                  FROM (  """ + sql_query + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    # print('ranked_sql1', ranked_sql1)

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + sql_query + """ )
            """

    try:
        with connection.cursor() as cursor:
            # print(ranked_sql1, params)
            cursor.execute(ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["BULLETINNUM"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_bullet_board_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params_with_pagination(None)
def select_bullet_board_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_bullet_board_method"

    if request.method == "POST":
        # 查資料庫尋找使用者代號或部門代號
        with connection.cursor() as cursor:
            sql = """ 
                 SELECT CASE
                           WHEN SYSROLE = '0'
                               THEN USERID
                           WHEN SYSROLE = '1'
                               THEN DEPTCODE
                       END CODE
                  FROM USERS
                 WHERE USERID = :qUSERID
             """
            cursor.execute(sql, {'qUSERID': user_id})
            data = cursor.fetchall()

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
            else:
                col_names = [desc[0] for desc in cursor.description]
                data = [dict(zip(col_names, row)) for row in data]
                dept_dealer_code = data[0]['CODE']
                dept_dealer_code2 = data[0]['CODE']

        # 前十天的日期
        start_date = now_datetime() - datetime.timedelta(days=10)

        # 當月最後一天的日期
        _, last_day = calendar.monthrange(now_datetime().year, now_datetime().month)
        end_date = datetime.datetime(now_datetime().year, now_datetime().month, last_day)

        start_date_minguo = to_minguo(start_date)
        end_date_minguo = to_minguo(end_date)

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 當前日期
        now = datetime.datetime.now()

        params['qDEPT_OR_DEALER'] = dept_dealer_code
        params['qDEPT_OR_DEALER2'] = dept_dealer_code2
        params['qUSER_ID'] = user_id
        params2['qDEPT_OR_DEALER'] = dept_dealer_code
        params2['qDEPT_OR_DEALER2'] = dept_dealer_code2
        params2['qUSER_ID'] = user_id

        # 若沒有提供日期範圍，則使用當日前十天
        # sql_conditions.append("BEGINDATE >= :qEXECUTE_END_DATE")

        # 定義可能的參數列表
        params_mapping = {
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理主旨，如果是空的
        if 'topic' in json_data and json_data['topic'] is not None:
            # 若提供了主題，則使用模糊查詢
            sql_conditions.append("TOPIC LIKE :qTOPIC")
            params['qTOPIC'] = "%" + json_data['topic'] + "%"
            params2['qTOPIC'] = "%" + json_data['topic'] + "%"

        # 不顯示過期公告
        if 'expired' in json_data and json_data['expired'] is not None:
            if json_data['expired'] == 1:
                if 'end_date' in json_data and json_data['end_date'] is not None:
                    #起訖日期使用迄止日期
                    sql_conditions.append("BEGINDATE <= :qEXECUTE_END_DATE")
                    sql_conditions.append("ENDDATE >= :qEXECUTE_END_DATE")
                    params['qEXECUTE_END_DATE'] = to_minguo(json_data['end_date'])
                    params2['qEXECUTE_END_DATE'] = to_minguo(json_data['end_date'])
                else:
                    sql_conditions.append("BEGINDATE <= :qEXECUTE_END_DATE")
                    sql_conditions.append("ENDDATE >= :qEXECUTE_END_DATE")
                    params['qEXECUTE_END_DATE'] = end_date_minguo
                    params2['qEXECUTE_END_DATE'] = end_date_minguo
            else:
                if 'begin_date' in json_data and json_data['begin_date'] is not None:
                    #起訖日期使用起始日期
                    sql_conditions.append("ENDDATE >= :qEXECUTE_START_DATE")
                    params['qEXECUTE_START_DATE'] = to_minguo(json_data['begin_date'])
                    params2['qEXECUTE_START_DATE'] = to_minguo(json_data['begin_date'])
                else:
                    sql_conditions.append("ENDDATE >= :qEXECUTE_START_DATE")
                    params['qEXECUTE_START_DATE'] = start_date_minguo
                    params2['qEXECUTE_START_DATE'] = start_date_minguo

        try:
            result = select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk)
            # print(result)
            return result
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' BULLET_BOARD SELECT END '''

''' BULLET_BOARD UPDATE START '''

def update_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                # 更新主檔資料
                jsonData = {
                    "bulletin_num": data['bulletin_num'],
                    "begin_date": to_minguo(data['begin_date']),
                    "end_date": to_minguo(data['end_date']),
                    "topic": data['topic'],
                    "is_expired": 'N',
                    "owner_id": user_id,
                    "owner_date": to_minguo(get_ce_today()),
                }

                cursor.execute("SELECT 1 FROM BULLETBOARD_M WHERE BULLETINNUM = %s", [jsonData['bulletin_num']])
                if cursor.fetchone():
                    set_clause = ", ".join(
                        f"{BULLET_BOARD_NAME_MAPPING[key]} = '{jsonData[key]}'" for key in jsonData.keys())
                    sql = f"UPDATE BULLETBOARD_M SET {set_clause} WHERE BULLETINNUM = '{jsonData['bulletin_num']}'"
                    # print('sql', sql)
                    cursor.execute(sql)
                else:
                    # return f"Bulletin '{jsonData['bulletin_num']}' does not exist.", status.HTTP_400_BAD_REQUEST
                    return handle_error(context, f"Bulletin '{jsonData['bulletin_num']}' does not exist.", status.HTTP_400_BAD_REQUEST)

                # 先刪除所有與此bulletin_num相關的明細
                cursor.execute("DELETE FROM BULLETBOARD_D WHERE BULLETINNUM = %s", [jsonData['bulletin_num']])

                # 插入新的明細檔資料
                for dealer_str in data['dept_dealer_code']:
                    if dealer_str:  # 只有在有有效的代碼時才插入
                        detail_data = {
                            "BULLETINNUM": data['bulletin_num'],
                            "VENDORCODE": dealer_str
                        }
                        detail_keys = ", ".join(detail_data.keys())
                        detail_values = ", ".join(f"'{detail_data[key]}'" for key in detail_data.keys())
                        detail_sql = f"INSERT INTO BULLETBOARD_D ({detail_keys}) VALUES ({detail_values})"
                        cursor.execute(detail_sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK
    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('bulletin_num')
def update_bullet_board_method(request, json_data, role, user_id):
    context = "update_bullet_board_method"

    if request.method == "POST":
        try:
            result, result_status = update_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' BULLET_BOARD UPDATE END '''

''' BULLET_BOARD DELETE START '''

def delete_with_raw_sql(context, bulletin_nums):
    for bulletin_num in bulletin_nums:

        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # 先刪除所有與此bulletin_num相關的明細
                    cursor.execute("DELETE FROM BULLETBOARD_D WHERE BULLETINNUM = %s", [bulletin_num])

                    # 刪除主檔資料
                    cursor.execute("DELETE FROM BULLETBOARD_M WHERE BULLETINNUM = %s", [bulletin_num])

            return "刪除成功", status.HTTP_200_OK

        except IntegrityError as ie:
            return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('bulletin_num')
def delete_bullet_board_method(request, json_data, role, user_id):
    context = "delete_bullet_board_method"

    if request.method == "POST":

        bulletin_nums = json_data.get("bulletin_num", [])

        # Check if bulletin_nums is a list and is not empty
        if not isinstance(bulletin_nums, list) or not bulletin_nums:
            return handle_error(context, "bulletin_nums must be a list and cannot be empty", status.HTTP_400_BAD_REQUEST)

        try:
            result, result_status = delete_with_raw_sql(context, bulletin_nums)
            return result, result_status
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST

''' BULLET_BOARD DELETE END '''