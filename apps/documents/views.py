# -*- coding: UTF8 -*-
from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.documents._BulleBoardInfo import select_bullet_board_method, select_combobox_dept_dealer_code_name, \
    insert_bullet_board_method, update_bullet_board_method, delete_bullet_board_method
from apps.documents._BusinessNotificationInfo import select_business_notification_method, \
    update_business_notification_method, business_notification_pudcno_method, delete_business_notification_method, \
    delete_business_notification_method2, select_business_notification_method2
from apps.documents._DocumentInfo import select_combobox_kind_code_name, select_combobox_recipient_code_name
from apps.documents._DownloadBusinessNotificationInfo import select_business_notification_download, \
    select_business_notification_file_statistics, select_business_notification_price_download
from apps.documents._DownloadLetterInfo import select_letter_download, select_letter_file_statistics
from apps.documents._KindInfo import select_kind_method, insert_kind_method, update_kind_method, delete_kind_method
from apps.documents._LetterInfo import select_letter_method, letter_pudcno_method, update_letter_method, \
    select_letter_method2, delete_letter_method, delete_letter_method2
from apps.documents._SendEmailBusinessNotificationInfo import business_notification_email_method
from apps.documents._SendEmailLetterInfo import letter_email_method
from apps.documents._UploadBusinessNotificationInfo import select_business_notification_upload
from apps.documents._UploadLetterInfo import select_letter_upload
from apps.documents.models import mainDocument

DOCUMENT_ACTIONS = {
    'combobox_dept_dealer_code_name': {
        'select': select_combobox_dept_dealer_code_name,
    },
    'combobox_kind_code_name': {
        'select': select_combobox_kind_code_name,
    },
    'combobox_recipient_code_name': {
        'select': select_combobox_recipient_code_name,
    },
    'bullet_board': {
        'select': select_bullet_board_method,
        'insert': insert_bullet_board_method,
        'update': update_bullet_board_method,
        'delete': delete_bullet_board_method,
    },
    'business_notification_serial': {
        'insert_update': business_notification_pudcno_method,
    },
    'business_notification_email': {
        'select': business_notification_email_method,
    },
    'business_notification': {
        'select_download': select_business_notification_download,
        'select_upload': select_business_notification_upload,
        'select_file_statistics': select_business_notification_file_statistics,
        'select_file_price_statistics': select_business_notification_price_download,
        'select': select_business_notification_method,
        'select2': select_business_notification_method2,
        'update': update_business_notification_method,
        'delete': delete_business_notification_method,
        'delete2': delete_business_notification_method2,
    },
    'letter_serial': {
        'insert_update': letter_pudcno_method,
    },
    'letter_email': {
        'select': letter_email_method,
    },
    'letter': {
        'select_download': select_letter_download,
        'select_upload': select_letter_upload,
        'select_file_statistics': select_letter_file_statistics,
        'select': select_letter_method,
        'select2': select_letter_method2,
        'update': update_letter_method,
        'delete': delete_letter_method,
        'delete2': delete_letter_method2,
    },
    'kind': {
        'select': select_kind_method,
        'insert': insert_kind_method,
        'update': update_kind_method,
        'delete': delete_kind_method,
    }
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class DocumentViewSet(viewsets.ModelViewSet ):
    queryset = mainDocument.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 下載業務通知
    @action(detail=False, methods=['post'])
    def select_business_notification_download(self, request):
        sql_result, http_status = select_business_notification_download(request)

        # 如果返回的是一个字串
        if isinstance(sql_result, str):
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': sql_result, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False}
            )
        # 如果返回的是其他（例如FileResponse）
        else:
            return sql_result

    # 下載業務通知價格
    @action(detail=False, methods=['post'])
    def select_business_notification_price_download(self, request):
        sql_result, http_status = select_business_notification_price_download(request)

        # 如果返回的是一个字串
        if isinstance(sql_result, str):
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': sql_result, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False}
            )
        # 如果返回的是其他（例如FileResponse）
        else:
            return sql_result

    # 下載公文作業
    @action(detail=False, methods=['post'])
    def select_letter_download(self, request):
        sql_result, http_status = select_letter_download(request)

        # 如果返回的是一个字串
        if isinstance(sql_result, str):
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': sql_result, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False}
            )

        # 如果返回的是其他（例如FileResponse）
        else:
            return sql_result

    # 上傳業務通報
    @action(detail=False, methods=['post'])
    def select_business_notification_upload(self, request):
        return self._handle_action('business_notification', 'select_upload')

    # 上傳公文作業
    @action(detail=False, methods=['post'])
    def select_letter_upload(self, request):
        return self._handle_action('letter', 'select_upload')

    # 查詢業務通報統計資料
    @action(detail=False, methods=['post'])
    def select_business_notification_file_statistics(self, request):
        return self._handle_action('business_notification', 'select_file_statistics')

    # 查詢公文作業統計資料
    @action(detail=False, methods=['post'])
    def select_letter_file_statistics(self, request):
        return self._handle_action('letter', 'select_file_statistics')

    # 查詢類別代號與名稱
    @action(detail=False, methods=['post'])
    def select_combobox_kind_code_name(self, request):
        return self._handle_action('combobox_kind_code_name', 'select')

    # 查詢受文者代號與名稱
    @action(detail=False, methods=['post'])
    def select_combobox_recipient_code_name(self, request):
        return self._handle_action('combobox_recipient_code_name', 'select')

    # 查詢部門_經銷商代號與名稱
    @action(detail=False, methods=['post'])
    def select_combobox_dept_dealer_code_name(self, request):
        return self._handle_action('combobox_dept_dealer_code_name', 'select')

    # 查詢佈告欄
    @action(detail=False, methods=['post'])
    def select_bullet_board(self, request):
        return self._handle_action('bullet_board', 'select')

    # 新增佈告欄
    @action(detail=False, methods=['post'])
    def insert_bullet_board(self, request):
        return self._handle_action('bullet_board', 'insert')

    # 更新佈告欄
    @action(detail=False, methods=['post'])
    def update_bullet_board(self, request):
        return self._handle_action('bullet_board', 'update')

    # 刪除佈告欄
    @action(detail=False, methods=['post'])
    def delete_bullet_board(self, request):
        return self._handle_action('bullet_board', 'delete')

    # 取得業務通報流水號
    @action(detail=False, methods=['post'])
    def business_notification_serial(self, request):
        return self._handle_action('business_notification_serial', 'insert_update')

    # 查詢業務通報寄送電子郵件
    @action(detail=False, methods=['post'])
    def business_notification_email(self, request):
        return self._handle_action('business_notification_email', 'select')

    # 查詢業務通報
    @action(detail=False, methods=['post'])
    def select_business_notification(self, request):
        return self._handle_action('business_notification', 'select')

    # 查詢業務通報2
    @action(detail=False, methods=['post'])
    def select_business_notification2(self, request):
        return self._handle_action('business_notification', 'select2')

    # 更新業務通報
    @action(detail=False, methods=['post'])
    def update_business_notification(self, request):
        return self._handle_action('business_notification', 'update')

    # 刪除業務通報
    @action(detail=False, methods=['post'])
    def delete_business_notification(self, request):
        return self._handle_action('business_notification', 'delete')

    # 取消業務通報
    @action(detail=False, methods=['post'])
    def cancel_business_notification(self, request):
        return self._handle_action('business_notification', 'delete2')

    # 取得公文作業流水號
    @action(detail=False, methods=['post'])
    def letter_serial(self, request):
        return self._handle_action('letter_serial', 'insert_update')

    # 查詢公文作業寄送電子郵件
    @action(detail=False, methods=['post'])
    def letter_email(self, request):
        return self._handle_action('letter_email', 'select')

    # 查詢公文作業
    @action(detail=False, methods=['post'])
    def select_letter(self, request):
        return self._handle_action('letter', 'select')

    # 查詢公文作業2
    @action(detail=False, methods=['post'])
    def select_letter2(self, request):
        return self._handle_action('letter', 'select2')

    # 更新公文作業
    @action(detail=False, methods=['post'])
    def update_letter(self, request):
        return self._handle_action('letter', 'update')

    # 刪除公文作業
    @action(detail=False, methods=['post'])
    def delete_letter(self, request):
        return self._handle_action('letter', 'delete')

    # 取消公文作業
    @action(detail=False, methods=['post'])
    def cancel_letter(self, request):
        return self._handle_action('letter', 'delete2')

    # 查詢類別
    @action(detail=False, methods=['post'])
    def select_kind(self, request):
        return self._handle_action('kind', 'select')

    # 新增類別
    @action(detail=False, methods=['post'])
    def insert_kind(self, request):
        return self._handle_action('kind', 'insert')

    # 修改類別
    @action(detail=False, methods=['post'])
    def update_kind(self, request):
        return self._handle_action('kind', 'update')

    # 刪除類別
    @action(detail=False, methods=['post'])
    def delete_kind(self, request):
        return self._handle_action('kind', 'delete')
