# -*- coding: utf-8 -*-
from django.apps import AppConfig
import logging

logger = logging.getLogger(__name__)


class DocumentsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.documents'
    verbose_name = 'Documents Management'
    
    def ready(self):
        """
        Initialize word processor when Django is ready
        This avoids initialization during imports
        """
        # 檢查是否已經初始化
        from .word_processor_singleton import ProcessorManager
        
        if ProcessorManager.is_initialized():
            logger.info("Word processor already initialized, skipping")
            return
            
        try:
            # Delayed initialization
            from . import init_word_processor
            if init_word_processor():
                ProcessorManager.set_initialized()
                logger.info("Word processor initialized in Django ready()")
        except Exception as e:
            logger.error(f"Failed to initialize word processor in ready(): {str(e)}")
            # Continue without word processor features
            pass
