"""
Word COM 初始化修復模塊
解決 cmd/powershell 閃退問題
"""

import os
import sys
import time
import logging
import pythoncom
import win32com.client
import win32api
import threading
from contextlib import contextmanager

logger = logging.getLogger(__name__)

# 全局 COM 初始化狀態
_com_initialized = threading.local()

def ensure_com_initialized():
    """
    確保當前線程的 COM 已正確初始化
    """
    if not hasattr(_com_initialized, 'initialized') or not _com_initialized.initialized:
        try:
            # 嘗試多線程模式初始化
            pythoncom.CoInitializeEx(pythoncom.COINIT_MULTITHREADED)
            _com_initialized.initialized = True
            _com_initialized.mode = 'multithreaded'
            logger.debug("COM 已初始化 (多線程模式)")
        except Exception as e1:
            try:
                # 回退到單線程模式
                pythoncom.CoInitialize()
                _com_initialized.initialized = True
                _com_initialized.mode = 'singlethreaded'
                logger.debug("COM 已初始化 (單線程模式)")
            except Exception as e2:
                # 可能已經初始化
                _com_initialized.initialized = True
                _com_initialized.mode = 'unknown'
                logger.debug("COM 可能已經初始化")

def ensure_com_uninitialized():
    """
    確保當前線程的 COM 被正確反初始化
    """
    if hasattr(_com_initialized, 'initialized') and _com_initialized.initialized:
        try:
            pythoncom.CoUninitialize()
            _com_initialized.initialized = False
            logger.debug("COM 已反初始化")
        except:
            # 忽略反初始化錯誤
            pass

@contextmanager
def com_context():
    """
    COM 上下文管理器，確保 COM 的正確初始化和清理
    """
    ensure_com_initialized()
    try:
        yield
    finally:
        # 不要自動反初始化，讓線程結束時自動清理
        pass

def create_word_app_safe():
    """
    安全地創建 Word 應用程式實例
    """
    with com_context():
        try:
            # 使用 DispatchEx 創建新實例，避免共享問題
            word_app = win32com.client.DispatchEx("Word.Application")

            # 基本設置
            word_app.Visible = False
            word_app.DisplayAlerts = 0  # wdAlertsNone

            # 安全設置
            try:
                word_app.Options.DoNotPromptForConvert = True
                word_app.Options.ConfirmConversions = False
                word_app.Options.UpdateLinksAtOpen = False
                word_app.Options.UpdateFieldsAtPrint = False
                word_app.Options.UpdateLinksAtPrint = False
                word_app.Options.UpdateFieldsWithTrackedChangesAtPrint = False
                word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable

                # 修復 Normal.dotm 問題的關鍵設置
                word_app.Options.CheckGrammarAsYouType = False
                word_app.Options.CheckSpellingAsYouType = False
                word_app.Options.SuggestSpellingCorrections = False
                word_app.Options.AllowReadingMode = False
                word_app.Options.WarnBeforeSavingPrintingSendingMarkup = False

                # 禁用自動保存和恢復功能
                word_app.Options.AutoRecover = False
                word_app.Options.SaveInterval = 0
                word_app.Options.BackgroundSave = False

                # 設置啟動路徑，避免 Normal.dotm 問題
                try:
                    # 創建臨時目錄作為用戶模板路徑
                    import tempfile
                    temp_dir = tempfile.mkdtemp(prefix="word_temp_")
                    word_app.Options.DefaultFilePath(0) = temp_dir  # wdUserTemplatesPath
                    logger.debug(f"設置臨時模板路徑: {temp_dir}")
                except Exception as template_error:
                    logger.warning(f"設置模板路徑失敗: {str(template_error)}")

                # 禁用加載項以提高穩定性
                for addon in word_app.COMAddIns:
                    try:
                        addon.Connect = False
                    except:
                        pass

            except Exception as e:
                logger.warning(f"設置 Word 選項時出錯: {str(e)}")

            return word_app

        except Exception as e:
            logger.error(f"創建 Word 應用程式失敗: {str(e)}")
            raise

def fix_console_output():
    """
    修復控制台輸出問題，防止 cmd/powershell 閃退
    """
    if sys.platform == 'win32':
        try:
            # 設置控制台代碼頁為 UTF-8
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleCP(65001)
            kernel32.SetConsoleOutputCP(65001)
            
            # 設置 Python 輸出編碼
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
            
        except Exception as e:
            logger.warning(f"設置控制台編碼失敗: {str(e)}")

def fix_com_permissions():
    """
    修復 COM 權限問題
    """
    try:
        # 確保當前進程有適當的權限
        import win32security
        import win32con
        
        # 獲取當前進程令牌
        process_handle = win32api.GetCurrentProcess()
        token_handle = win32security.OpenProcessToken(
            process_handle, 
            win32con.TOKEN_ADJUST_PRIVILEGES | win32con.TOKEN_QUERY
        )
        
        # 啟用 SeDebugPrivilege（可選，用於調試）
        privilege_luid = win32security.LookupPrivilegeValue(None, "SeDebugPrivilege")
        privilege_tuple = [(privilege_luid, win32con.SE_PRIVILEGE_ENABLED)]
        win32security.AdjustTokenPrivileges(token_handle, 0, privilege_tuple)
        
    except Exception as e:
        logger.debug(f"調整進程權限失敗（非關鍵）: {str(e)}")

def fix_normal_dotm_issue():
    """
    修復 Normal.dotm 模板問題
    """
    try:
        import tempfile
        import shutil

        # 獲取 Word 用戶模板目錄
        user_templates_path = os.path.expanduser("~\\AppData\\Roaming\\Microsoft\\Templates")
        normal_dotm_path = os.path.join(user_templates_path, "Normal.dotm")

        # 如果 Normal.dotm 存在且可能損壞，備份並刪除它
        if os.path.exists(normal_dotm_path):
            try:
                # 嘗試備份
                backup_path = f"{normal_dotm_path}.backup_{int(time.time())}"
                shutil.copy2(normal_dotm_path, backup_path)
                logger.info(f"已備份 Normal.dotm 到: {backup_path}")

                # 刪除原文件，讓 Word 重新創建
                os.remove(normal_dotm_path)
                logger.info("已刪除可能損壞的 Normal.dotm，Word 將重新創建")

            except Exception as e:
                logger.warning(f"處理 Normal.dotm 時出錯: {str(e)}")

        # 確保模板目錄存在
        os.makedirs(user_templates_path, exist_ok=True)

    except Exception as e:
        logger.warning(f"修復 Normal.dotm 問題失敗: {str(e)}")

def initialize_word_environment():
    """
    初始化 Word 處理環境
    """
    # 修復控制台輸出
    fix_console_output()

    # 修復 COM 權限
    fix_com_permissions()

    # 修復 Normal.dotm 問題
    fix_normal_dotm_issue()

    # 設置環境變量
    os.environ['PYTHONIOENCODING'] = 'utf-8'

    logger.info("Word 處理環境已初始化")

# 在模塊加載時自動初始化
initialize_word_environment()