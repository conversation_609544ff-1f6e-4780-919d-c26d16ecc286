import itertools
import json
import logging
import os
import re
import shutil
import uuid
import time
import glob  # 添加這行來導入 glob 模塊
from contextlib import contextmanager
import pythoncom
import win32com.client

# 導入 COM 初始化修復
try:
    from .word_com_initializer import ensure_com_initialized, com_context, create_word_app_safe
except ImportError:
    # 如果導入失敗，提供基本的 fallback
    def ensure_com_initialized():
        try:
            pythoncom.CoInitialize()
        except:
            pass
    
    from contextlib import contextmanager
    @contextmanager
    def com_context():
        ensure_com_initialized()
        yield
    
    def create_word_app_safe():
        try:
            word_app = win32com.client.DispatchEx("Word.Application")
            word_app.Visible = False
            word_app.DisplayAlerts = 0  # 關鍵：禁用所有警告對話框

            # 防止 Normal.dotm 錯誤的設置
            try:
                word_app.Options.DoNotPromptForConvert = True
                word_app.Options.ConfirmConversions = False
                word_app.Options.UpdateLinksAtOpen = False
                word_app.Options.CheckGrammarAsYouType = False
                word_app.Options.CheckSpellingAsYouType = False
                word_app.Options.AutoRecover = False
                word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
            except:
                pass

            return word_app
        except:
            return None
from django.conf import settings
from django.db import connection
from rest_framework import status
from django.http import FileResponse
from urllib.parse import quote

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params
from .word_queue_processor import process_word_task
from .word_enterprise_processor import process_word_document_enterprise
from .word_safe_processor import process_document_with_safety
from .word_simple_processor import process_document_simple
from .word_direct_processor import process_document_direct
from .word_thread_safe_processor import process_document_thread_safe
from .word_com_safe_processor import process_document_com_safe

# 設置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def file_path(file_name, serial_number):
    """
    根據文件名和序列號生成文件路徑
    :param file_name: 文件名
    :param serial_number: 序列號
    :return: 完整的文件路徑
    """
    folder_path = os.path.join(settings.BASE_DIR, 'uploads', '公文作業', serial_number)
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
    return os.path.join(folder_path, file_name)


# SQL 查詢語句
DOWNLOAD_PUDCHT_SQL = """
    SELECT ATTACHMENT_NAME, FILEPASSWORD
      FROM ( SELECT FILEARTICLE ATTACHMENT_NAME, FILEPASSWORD
               FROM PUDCHT )
     WHERE 1 = 1
"""

DOWNLOAD_ATTACHMENT_SQL = """
    SELECT ATTACHMENT_NAME, '' FILEPASSWORD
      FROM PUDCHT_ATTACHMENT A
     WHERE 1 = 1
"""


def fetch_from_db(sql, params=None):
    """
    執行 SQL 查詢並返回結果
    :param sql: SQL 查詢語句
    :param params: 查詢參數
    :return: 查詢結果
    """
    with connection.cursor() as cursor:
        cursor.execute(sql, params or {})
        return cursor.fetchall()


def get_user_role(user_id):
    """
    獲取用戶角色
    :param user_id: 用戶 ID
    :return: 用戶角色
    """
    sql = """
        SELECT SYSROLE
        FROM USERS
        WHERE USERID = :qUSERID
    """
    data = fetch_from_db(sql, {'qUSERID': user_id})
    return data[0][0] if data else None


def get_file_statistics(user_id, pudcno):
    """
    獲取文件統計信息
    :param user_id: 用戶 ID
    :param pudcno: PUDCNO
    :return: 文件統計信息字典
    """
    sql = """ 
        SELECT REMARKNUMBER, REMARKVALUE
          FROM PUDCHT_FILESTATISTICS
         WHERE PUDCNO = :qPUDCNO
           AND DEALER = :qUSERID
         ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
    """
    data = fetch_from_db(sql, {'qUSERID': user_id, 'qPUDCNO': pudcno})
    return {row[0]: row[1] for row in data} if data else {}

@contextmanager
def word_application():
    """
    創建 Word 應用程序的上下文管理器，確保自動清理
    """
    app = None
    try:
        pythoncom.CoInitialize()
        # 嘗試使用 DispatchEx 而不是 gencache.EnsureDispatch
        try:
            app = win32com.client.DispatchEx("Word.Application")
        except:
            # 如果 DispatchEx 失敗，嘗試使用 Dispatch
            app = win32com.client.Dispatch("Word.Application")
        app.Visible = False
        # 設置 DisplayAlerts 為 0 以避免對話框
        app.DisplayAlerts = 0

        # 防止 Normal.dotm 錯誤的設置
        try:
            app.Options.DoNotPromptForConvert = True
            app.Options.ConfirmConversions = False
            app.Options.UpdateLinksAtOpen = False
            app.Options.CheckGrammarAsYouType = False
            app.Options.CheckSpellingAsYouType = False
            app.Options.AutoRecover = False
            app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
        except Exception as e:
            logging.warning(f"設置 Word 選項時出錯: {str(e)}")

        yield app
    except Exception as e:
        logging.error(f"Word 應用程序創建失敗: {str(e)}")
        yield None
    finally:
        if app:
            try:
                # 關閉所有文檔
                for doc in app.Documents:
                    try:
                        doc.Close(SaveChanges=False)
                    except:
                        pass
                app.Quit()
            except Exception as e:
                logging.warning(f"關閉 Word 應用程序失敗: {str(e)}")
        try:
            pythoncom.CoUninitialize()
        except:
            pass


def unprotect_and_protect_docx_worker(file_path, file_name, pudcno, user_id, file_password, word_app=None):
    """
    在佇列中執行的文件處理函數
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param pudcno: PUDCNO
    :param user_id: 用戶 ID
    :param file_password: 文件密碼
    :param word_app: Word 應用程序實例（由佇列處理器提供）
    :return: 新的文件路徑和文件名
    """
    if not word_app:
        logging.error("未提供 Word 應用程序實例")
        return None, None

    if file_password is None:
        try:
            new_path, new_name = modify_file_content(user_id, pudcno, file_path, file_name, word_app)
            return new_path, new_name
        except Exception as e:
            logging.error("錯誤: " + str(e))
            import traceback
            print(traceback.format_exc())
            return None, None
    else:
        doc = None
        try:
            # 打開受密碼保護的文件
            doc = word_app.Documents.Open(FileName=file_path, PasswordDocument=file_password)
            
            # 檢查保護類型
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                except Exception as e:
                    logging.warning(f"解除保護時發生錯誤: {str(e)}")

            # 保存解密後的文件
            temp_uuid = str(uuid.uuid4())
            unprotected_path = f"{file_path}.{temp_uuid}.unprotected.docx"
            doc.SaveAs2(FileName=unprotected_path, FileFormat=16)
            doc.Close()
            doc = None

            # 修改文件內容
            new_path, new_name = modify_file_content(user_id, pudcno, unprotected_path, file_name, word_app)

            if new_path and os.path.exists(new_path):
                # 重新保護文件
                doc = word_app.Documents.Open(FileName=new_path)
                
                # 檢查保護類型
                protection_type = -1
                try:
                    protection_type = doc.ProtectionType
                except:
                    pass
                    
                if protection_type == -1:
                    try:
                        doc.Protect(Type=3, Password=file_password)
                    except Exception as e:
                        logging.warning(f"重新保護時發生錯誤: {str(e)}")
                        
                protected_new_path = f"{new_path}.{temp_uuid}.protected.docx"
                doc.SaveAs2(FileName=protected_new_path, FileFormat=16)
                doc.Close()
                doc = None

                # 清理臨時文件
                if os.path.exists(new_path):
                    os.remove(new_path)
                if os.path.exists(unprotected_path):
                    os.remove(unprotected_path)
                os.rename(protected_new_path, new_path)

                return new_path, new_name
            else:
                # 清理臨時文件
                if os.path.exists(unprotected_path):
                    os.remove(unprotected_path)
                return None, None

        except Exception as e:
            logging.error("錯誤: " + str(e))
            import traceback
            print(traceback.format_exc())
            return None, None
        finally:
            # 確保文檔被關閉
            if doc:
                try:
                    doc.Close()
                except:
                    pass

def unprotect_and_protect_docx(file_path, file_name, pudcno, user_id, file_password, modify_func):
    """
    解除文件保護，修改內容，然後重新保護文件
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param pudcno: PUDCNO
    :param user_id: 用戶 ID
    :param file_password: 文件密碼
    :param modify_func: 修改文件的函數
    :return: 新的文件路徑和文件名
    """
    # 使用 COM 安全的處理器，完全避免 COM 線程模型錯誤
    try:
        result = process_document_com_safe(
            file_path, file_name, pudcno, user_id, file_password, modify_func
        )
        if result and result[0]:
            return result
        else:
            logging.error("COM 安全處理器返回空結果")
            return None, None
    except Exception as e:
        logging.error(f"COM 安全處理器失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def modify_file_content(user_id, pudcno, file_path, file_name, word_app):
    """
    修改文件內容
    :param user_id: 用戶 ID
    :param pudcno: PUDCNO
    :param file_path: 文件路徑
    :param file_name: 文件名
    :param word_app: Word 應用程序實例
    :return: 新的文件路徑和文件名
    """
    notes_data = get_file_statistics(user_id, pudcno)
    doc = word_app.Documents.Open(file_path)

    try:
        # 處理段落
        for paragraph in doc.Paragraphs:
            replace_and_format(paragraph, notes_data)

        # 處理表格
        for table in doc.Tables:
            for cell in table.Range.Cells:
                replace_and_format(cell.Range, notes_data)

        # 保存修改後的文件
        base_name, ext = os.path.splitext(file_name)
        new_file_name = f"{user_id}_{base_name}{ext}"
        new_file_path = os.path.join(os.path.dirname(file_path), new_file_name)
        doc.SaveAs(new_file_path)
        doc.Close()

        return new_file_path, new_file_name

    except Exception as e:
        logging.error("錯誤: " + str(e))
        import traceback
        print(traceback.format_exc())
    finally:
        pass


def replace_and_format(paragraph_or_cell, notes_data):
    """
    替換和格式化文本內容
    :param paragraph_or_cell: 段落或單元格對象
    :param notes_data: 註釋數據
    """
    if hasattr(paragraph_or_cell, 'Range'):
        text_content = paragraph_or_cell.Range.Text
        target_range = paragraph_or_cell.Range
    else:
        return

    for note_id, real_data in sorted(notes_data.items(), key=lambda x: len(x[0]), reverse=True):
        for pattern in [
            f'(註{note_id})',
            f'（註{note_id}）',
            f'註{note_id}',
            f'(註 {note_id})',
            f'註{note_id}點',
            f'註{note_id}元',
            f'註{note_id}%',
        ]:
            try:
                while pattern in text_content:
                    logging.info(f"找到模式: {pattern}")
                    if pattern == f'註{note_id}':
                        formatted_text = real_data
                    else:
                        formatted_text = f'({real_data})'
                    format_replacement(target_range, pattern, formatted_text)
                    text_content = target_range.Text
                    target_range = paragraph_or_cell.Range
            except Exception as e:
                print(str(e))
                continue


def format_replacement(paragraph_or_cell_range, pattern, replacement_text):
    """
    執行文本替換和格式化
    :param paragraph_or_cell_range: 段落或單元格範圍
    :param pattern: 要替換的模式
    :param replacement_text: 替換文本
    """
    search_range = paragraph_or_cell_range.Duplicate
    search_range.Find.Text = pattern
    search_range.Find.Execute()
    while search_range.Find.Found:
        search_range.Text = replacement_text
        search_range.Font.Name = "微軟正黑體"
        search_range.Font.Size = 12
        search_range.Font.Bold = True
        search_range.Collapse(Direction=2)
        search_range.Find.Text = pattern
        search_range.Find.Execute()


@validate_access_token_and_params(None)
def select_letter_download(request, json_data, role, user_id):
    """
    處理公文下載請求
    :param request: HTTP 請求
    :param json_data: JSON 格式的請求數據
    :param role: 用戶角色
    :param user_id: 用戶 ID
    :return: 文件下載響應或錯誤信息
    """
    if request.method == "POST":
        try:
            logging.info(f"開始處理公文下載請求: user_id={user_id}, role={role}")
            logging.info(f"請求數據: {json_data}")
            
            pudcno = json_data['pudcno']
            noModifyFile = json_data['noModifyFile']

            strCond = ''
            condition = {}
            params = {
                "file_name": "ATTACHMENT_NAME",
            }
            for param, field in params.items():
                if param in json_data and json_data[param]:
                    condition[f'q{param.upper()}'] = json_data[param]
                    strCond += f' AND {field} = :{f"q{param.upper()}"} '

            sSTM = DOWNLOAD_PUDCHT_SQL + strCond + 'UNION ALL' + DOWNLOAD_ATTACHMENT_SQL + strCond
            logging.info(f"執行 SQL: {sSTM}")
            logging.info(f"SQL 參數: {condition}")
            
            data = fetch_from_db(sSTM, condition)

            if not data:
                logging.warning(f"找不到資料: pudcno={pudcno}")
                return "找不到資料", status.HTTP_404_NOT_FOUND

            file_name = data[0][0]
            file_url = file_path(file_name, pudcno)
            file_password = data[0][1]

            if noModifyFile == 'N':
                file_password = None

            if (role == '0') and get_file_statistics(user_id, pudcno) and (noModifyFile == 'Y'):
                logging.info(f"需要處理文件替換: file_url={file_url}")
                result = unprotect_and_protect_docx(file_url, file_name, pudcno, user_id, file_password,
                                                                 modify_file_content)
                if result and result[0] and result[1]:
                    file_url, file_name = result
                    logging.info(f"文件處理成功: new_file_url={file_url}")
                else:
                    logging.error(f"文件處理失敗")
                    return "文件處理失敗", status.HTTP_500_INTERNAL_SERVER_ERROR

            if not os.path.exists(file_url):
                logging.error(f"找不到文件: {file_url}")
                return "找不到檔案", status.HTTP_404_NOT_FOUND

            response = FileResponse(open(file_url, 'rb'))
            file_display_name = quote(file_name.encode('utf-8'))
            response["Content-Disposition"] = f"attachment; filename*=UTF-8''{file_display_name}"

            return response, status.HTTP_200_OK
            
        except Exception as e:
            logging.error(f"select_letter_download 發生未預期錯誤: {str(e)}")
            import traceback
            traceback.print_exc()
            return "系統錯誤", status.HTTP_500_INTERNAL_SERVER_ERROR


@validate_access_token_and_params('pudcno')
def select_letter_file_statistics(request, json_data, role, user_id):
    """
    獲取公文文件統計信息
    :param request: HTTP 請求
    :param json_data: JSON 格式的請求數據
    :param role: 用戶角色
    :param user_id: 用戶 ID
    :return: 文件統計信息或錯誤信息
    """
    context = "select_letter_file_statistics"

    if request.method == "POST":
        try:
            sql_conditions = []
            params = {}

            params_mapping = {
                'pudcno': 'PUDCNO',
                'user_id': 'DEALER',
            }

            for param, db_field in params_mapping.items():
                if param in json_data and json_data[param]:
                    sql_conditions.append(f" {db_field} = :{param}")
                    params[param] = json_data[param]

            if role == '0':
                sql_conditions.append(f" DEALER = :user_id")
                params['user_id'] = user_id

            sql_query = f""" 
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND {' AND '.join(sql_conditions)} 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            """

            data = fetch_from_db(sql_query, params)

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
            else:
                result = []

                for row in data:
                    result.append({
                        'DEALER': row[0],
                        'REMARKNUMBER': row[1],
                        'REMARKVALUE': row[2]
                    })
                return result, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

# 以下是一些可能需要的額外輔助函數或錯誤處理機制
def safe_file_operations(func):
    """
    裝飾器：安全地執行文件操作，處理可能的異常
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IOError as e:
            logging.error(f"{func.__name__} 中發生IO錯誤: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"{func.__name__} 中發生意外錯誤: {str(e)}")
            raise
    return wrapper

@safe_file_operations
def create_temp_file(original_file, temp_dir):
    """
    創建臨時文件的安全方法
    :param original_file: 原始文件路徑
    :param temp_dir: 臨時目錄
    :return: 臨時文件路徑
    """
    temp_file = os.path.join(temp_dir, f"temp_{uuid.uuid4().hex}.docx")
    shutil.copy2(original_file, temp_file)
    return temp_file

def cleanup_temp_files(temp_dir, pattern="temp_*.docx"):
    """
    清理臨時文件
    :param temp_dir: 臨時目錄
    :param pattern: 文件匹配模式
    """
    for file in glob.glob(os.path.join(temp_dir, pattern)):
        try:
            os.remove(file)
        except Exception as e:
            logging.warning(f"刪除臨時文件 {file} 失敗: {str(e)}")

def handle_word_com_error(e):
    """
    處理 Word COM 錯誤
    :param e: COM 錯誤異常
    """
    if isinstance(e, pythoncom.com_error):
        # 處理特定的 COM 錯誤代碼
        if e.hresult == -2147352567:  # 0x80020009
            logging.error("Word 文件可能已損壞或無法訪問")
        elif e.hresult == -2147023170:  # 0x800706BE
            logging.error("Word 服務器可能沒有響應")
        else:
            logging.error(f"未知的 COM 錯誤: {str(e)}")
    else:
        logging.error(f"非 COM 錯誤: {str(e)}")