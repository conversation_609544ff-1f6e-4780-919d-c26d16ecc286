# -*- coding: utf-8 -*-
"""
Word 處理器自動管理器
自動啟動監控、負載調整和性能優化
"""

import threading
import time
import logging
import json
import os
from datetime import datetime, timedelta
from django.conf import settings
from .word_processor_cache import cache, get_cached, set_cache
import psutil

from .word_processor_config import (
    get_config, apply_load_profile, RESOURCE_LIMITS, WORD_PROCESSOR_CONFIG
)

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoManager:
    """自動管理器單例類"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.running = False
        self.monitor_thread = None
        self.stats_cache = {}
        self.performance_history = []
        self.alert_callbacks = []
        
        # 配置參數
        self.monitor_interval = 30  # 監控間隔（秒）
        self.auto_scale_enabled = True  # 是否啟用自動縮放
        self.alert_enabled = True  # 是否啟用警報
        self.performance_tracking = True  # 是否追蹤性能
        
        # 自動啟動
        self.start()
    
    def start(self):
        """啟動自動管理器"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_loop,
                daemon=True,
                name="WordProcessorMonitor"
            )
            self.monitor_thread.start()
            logger.info("Word 處理器自動管理器已啟動")
    
    def stop(self):
        """停止自動管理器"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("Word 處理器自動管理器已停止")
    
    def _monitor_loop(self):
        """監控循環"""
        while self.running:
            try:
                # 收集統計信息
                stats = self._collect_stats()
                
                # 更新緩存
                self.stats_cache = stats
                set_cache('word_processor_stats', stats, timeout=300)
                
                # 性能追蹤
                if self.performance_tracking:
                    self._track_performance(stats)
                
                # 自動縮放
                if self.auto_scale_enabled:
                    self._auto_scale(stats)
                
                # 檢查警報
                if self.alert_enabled:
                    alerts = self._check_alerts(stats)
                    if alerts:
                        self._handle_alerts(alerts)
                
                # 清理任務
                self._cleanup_tasks()
                
            except Exception as e:
                logger.error(f"監控循環錯誤: {str(e)}")
            
            time.sleep(self.monitor_interval)
    
    def _collect_stats(self):
        """收集系統統計信息"""
        from .word_enterprise_processor import enterprise_processor
        
        stats = {
            'timestamp': datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f'),
            'processor': {},
            'system': {},
            'performance': {},
        }
        
        # 處理器統計
        if hasattr(enterprise_processor, 'worker_pool'):
            pool = enterprise_processor.worker_pool
            stats['processor'] = {
                'available_workers': pool.workers.qsize(),
                'total_workers': pool.pool_size,
                'pending_tasks': getattr(enterprise_processor, 'task_queue', {}).qsize() if hasattr(enterprise_processor.task_queue, 'qsize') else 0,
                'processing_tasks': len(getattr(enterprise_processor, 'processing_tasks', {})),
            }
        
        # 系統資源統計
        try:
            process = psutil.Process()
            stats['system'] = {
                'cpu_percent': process.cpu_percent(interval=0.1),
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'threads': process.num_threads(),
            }
        except:
            pass
        
        # 性能統計
        perf_data = get_cached('word_processor_performance', {})
        stats['performance'] = perf_data
        
        return stats
    
    def _track_performance(self, stats):
        """追蹤性能數據"""
        perf_entry = {
            'timestamp': stats['timestamp'],
            'available_workers': stats['processor'].get('available_workers', 0),
            'pending_tasks': stats['processor'].get('pending_tasks', 0),
            'cpu_percent': stats['system'].get('cpu_percent', 0),
            'memory_mb': stats['system'].get('memory_mb', 0),
        }
        
        self.performance_history.append(perf_entry)
        
        # 保留最近1小時的數據
        cutoff = datetime.now() - timedelta(hours=1)
        self.performance_history = [
            p for p in self.performance_history
            if datetime.strptime(p['timestamp'], '%Y-%m-%dT%H:%M:%S.%f') > cutoff
        ]
        
        # 計算平均值
        if len(self.performance_history) > 10:
            recent = self.performance_history[-10:]
            avg_cpu = sum(p['cpu_percent'] for p in recent) / len(recent)
            avg_memory = sum(p['memory_mb'] for p in recent) / len(recent)
            
            set_cache('word_processor_avg_stats', {
                'avg_cpu': avg_cpu,
                'avg_memory': avg_memory,
            }, timeout=300)
    
    def _auto_scale(self, stats):
        """自動調整系統配置"""
        processor_stats = stats.get('processor', {})
        system_stats = stats.get('system', {})
        
        pending = processor_stats.get('pending_tasks', 0)
        available = processor_stats.get('available_workers', 0)
        cpu = system_stats.get('cpu_percent', 0)
        
        # 判斷負載情況
        current_profile = None
        
        if pending > 50 or (available < 3 and cpu > 70):
            # 高負載
            current_profile = 'peak'
        elif pending < 5 and available > 10 and cpu < 30:
            # 低負載
            current_profile = 'low'
        else:
            # 正常負載
            current_profile = 'normal'
        
        # 應用配置
        if current_profile and apply_load_profile(current_profile):
            logger.info(f"自動切換到 {current_profile} 負載配置")
            set_cache('word_processor_load_profile', current_profile, timeout=3600)
    
    def _check_alerts(self, stats):
        """檢查警報條件"""
        alerts = []
        
        processor = stats.get('processor', {})
        system = stats.get('system', {})
        
        # 工作執行緒不足
        if processor.get('available_workers', 0) < 2:
            alerts.append({
                'level': 'WARNING',
                'type': 'low_workers',
                'message': f"可用工作執行緒不足: {processor.get('available_workers', 0)}",
                'value': processor.get('available_workers', 0),
            })
        
        # 待處理任務過多
        if processor.get('pending_tasks', 0) > 100:
            alerts.append({
                'level': 'ERROR',
                'type': 'high_queue',
                'message': f"待處理任務過多: {processor.get('pending_tasks', 0)}",
                'value': processor.get('pending_tasks', 0),
            })
        
        # CPU使用率過高
        if system.get('cpu_percent', 0) > 80:
            alerts.append({
                'level': 'WARNING',
                'type': 'high_cpu',
                'message': f"CPU使用率過高: {system.get('cpu_percent', 0):.1f}%",
                'value': system.get('cpu_percent', 0),
            })
        
        # 記憶體使用過高
        max_memory_mb = RESOURCE_LIMITS.get('max_memory_usage_gb', 8) * 1024
        if system.get('memory_mb', 0) > max_memory_mb:
            alerts.append({
                'level': 'ERROR',
                'type': 'high_memory',
                'message': f"記憶體使用過高: {system.get('memory_mb', 0):.1f}MB",
                'value': system.get('memory_mb', 0),
            })
        
        return alerts
    
    def _handle_alerts(self, alerts):
        """處理警報"""
        for alert in alerts:
            logger.warning(f"[{alert['level']}] {alert['message']}")
            
            # 執行回調函數
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"警報回調錯誤: {str(e)}")
            
            # 記錄到緩存
            alert_history = get_cached('word_processor_alerts', [])
            alert['timestamp'] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S.%f')
            alert_history.append(alert)
            
            # 保留最近100條警報
            alert_history = alert_history[-100:]
            set_cache('word_processor_alerts', alert_history, timeout=86400)
    
    def _cleanup_tasks(self):
        """清理任務"""
        from .word_enterprise_processor import enterprise_processor
        
        # 清理過期緩存
        if hasattr(enterprise_processor, 'file_cache'):
            try:
                enterprise_processor.file_cache.cleanup_old_cache()
            except:
                pass
        
        # 清理臨時文件
        temp_dir = getattr(enterprise_processor, 'temp_dir', None)
        if temp_dir and os.path.exists(temp_dir):
            try:
                cutoff = time.time() - 3600  # 1小時前
                for filename in os.listdir(temp_dir):
                    filepath = os.path.join(temp_dir, filename)
                    if os.path.getmtime(filepath) < cutoff:
                        os.remove(filepath)
            except:
                pass
    
    # 公開API方法
    
    def set_monitor_interval(self, seconds):
        """設置監控間隔"""
        self.monitor_interval = max(10, min(300, seconds))
        logger.info(f"監控間隔已設置為 {self.monitor_interval} 秒")
    
    def enable_auto_scale(self, enabled=True):
        """啟用/禁用自動縮放"""
        self.auto_scale_enabled = enabled
        logger.info(f"自動縮放已{'啟用' if enabled else '禁用'}")
    
    def enable_alerts(self, enabled=True):
        """啟用/禁用警報"""
        self.alert_enabled = enabled
        logger.info(f"警報已{'啟用' if enabled else '禁用'}")
    
    def add_alert_callback(self, callback):
        """添加警報回調函數"""
        if callable(callback):
            self.alert_callbacks.append(callback)
    
    def get_current_stats(self):
        """獲取當前統計信息"""
        return self.stats_cache
    
    def get_performance_summary(self):
        """獲取性能摘要"""
        if not self.performance_history:
            return {}
        
        recent = self.performance_history[-20:]
        return {
            'avg_cpu': sum(p['cpu_percent'] for p in recent) / len(recent),
            'avg_memory': sum(p['memory_mb'] for p in recent) / len(recent),
            'max_pending': max(p['pending_tasks'] for p in recent),
            'min_workers': min(p['available_workers'] for p in recent),
        }
    
    def force_scale(self, profile):
        """強制切換負載配置"""
        if apply_load_profile(profile):
            logger.info(f"手動切換到 {profile} 負載配置")
            return True
        return False

# 創建全局實例（但不立即初始化）
_auto_manager_instance = None

def get_auto_manager():
    """獲取自動管理器實例"""
    global _auto_manager_instance
    if _auto_manager_instance is None:
        _auto_manager_instance = AutoManager()
    return _auto_manager_instance

# 為了向後兼容，保留 auto_manager 變量
auto_manager = get_auto_manager()

# 便利函數
def configure_auto_manager(
    monitor_interval=None,
    auto_scale=None,
    alerts=None,
    performance_tracking=None
):
    """配置自動管理器"""
    if monitor_interval is not None:
        auto_manager.set_monitor_interval(monitor_interval)
    
    if auto_scale is not None:
        auto_manager.enable_auto_scale(auto_scale)
    
    if alerts is not None:
        auto_manager.enable_alerts(alerts)
    
    if performance_tracking is not None:
        auto_manager.performance_tracking = performance_tracking

def get_processor_health():
    """獲取處理器健康狀態"""
    stats = auto_manager.get_current_stats()
    if not stats:
        return "UNKNOWN"
    
    processor = stats.get('processor', {})
    system = stats.get('system', {})
    
    # 判斷健康狀態
    if (processor.get('available_workers', 0) < 1 or
        processor.get('pending_tasks', 0) > 200 or
        system.get('cpu_percent', 0) > 90):
        return "CRITICAL"
    elif (processor.get('available_workers', 0) < 3 or
          processor.get('pending_tasks', 0) > 100 or
          system.get('cpu_percent', 0) > 70):
        return "WARNING"
    else:
        return "HEALTHY"

def add_custom_alert_handler(handler):
    """添加自定義警報處理器"""
    auto_manager.add_alert_callback(handler)

# 啟動時自動初始化
logger.info("Word 處理器自動管理器已初始化")