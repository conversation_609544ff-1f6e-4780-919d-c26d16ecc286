# -*- coding: UTF8 -*-
import itertools
import json
import logging
import math

import cx_<PERSON>
from django.core.paginator import Paginator

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' Combobox START '''
# combobox_類別代號與名稱
combobox_kind_code_name_sql = """
    SELECT ROW_NUMBER() OVER (ORDER BY KINDCODE) ID,
           RPAD(NVL(KINDCODE, ' '), 10, ' ') ||
           RPAD(NVL(KINDNAME, ' '), 20, ' ') KIND_CODE_NAME
      FROM KIND
     ORDER BY KINDCODE
     """

# combo_box_受文者代號與名稱
combobox_recipient_code_name_sql = """
    SELECT LPAD(ROW_NUMBER() OVER (ORDER BY SUBSTR(DEPT, 1, 10) DESC, SUBSTR(DEPT, 11, 10)), 3, '0') ID, DEPT NAME
      FROM (SELECT RPAD('HSG001', 10, ' ') || RPAD('總公司', 10, ' ') ||
                   RPAD(DEPTCODE, 10, ' ') || RPAD(DEPTNAME, 20, ' ') DEPT
              FROM (SELECT DEPT_CODE007 DEPTCODE, DEPT_DESC_C007 DEPTNAME
                      FROM HRF007@B2B
                     WHERE REMOVE_DATE007 IS NULL
                     UNION ALL
                    SELECT DEPTCODE, DEPTNAME
                      FROM DEPT, HRF005@B2B
                     WHERE REPLACE(OWNERID, 'h', '') = NO005
                     ORDER BY DEPTCODE)
             UNION ALL
            SELECT DEALER
              FROM (SELECT RPAD(NVL('', ' '), 10, ' ') || RPAD(NVL('', ' '), 10, ' ') ||
                           RPAD(AGENT_CODE025, 10, ' ') || RPAD(NVL(BRIEF_NAME025, ' '), 20, ' ') DEALER
                      FROM OCV025@B2B
                     WHERE SUB_TYPE025 = '01'
                       AND EOS_CODE025 = 'Y'
                       AND RETIRE_DATE025 IS NULL
                     ORDER BY AGENT_CODE025))
     ORDER BY SUBSTR(DEPT, 1, 10) DESC, SUBSTR(DEPT, 11, 20)
"""
''' Combobox END '''

@validate_access_token_and_params(None)
def select_combobox_kind_code_name(request, json_data, role, user_id):
    context = "select_combobox_kind_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:

            try:
                cursor.execute(combobox_kind_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return "找不到資料", status.HTTP_404_NOT_FOUND
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_combobox_recipient_code_name(request, json_data, role, user_id):
    context = "select_combobox_recipient_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:

            try:
                cursor.execute(combobox_recipient_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return "找不到資料", status.HTTP_404_NOT_FOUND
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)