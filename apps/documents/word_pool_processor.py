import queue
import threading
import time
import logging
import pythoncom
import win32com.client
from contextlib import contextmanager
import uuid
import os
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import tempfile
import shutil

# 設置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class WordWorker:
    """獨立的 Word 工作執行緒"""
    def __init__(self, worker_id):
        self.worker_id = worker_id
        self.word_app = None
        self.busy = False
        self.lock = threading.Lock()
        
    def initialize(self):
        """初始化 Word 應用程序"""
        try:
            pythoncom.CoInitialize()
            self.word_app = win32com.client.DispatchEx("Word.Application")
            self.word_app.Visible = False
            self.word_app.DisplayAlerts = 0  # 關鍵：禁用所有警告對話框

            # 防止 Normal.dotm 錯誤的設置
            try:
                self.word_app.Options.DoNotPromptForConvert = True
                self.word_app.Options.ConfirmConversions = False
                self.word_app.Options.UpdateLinksAtOpen = False
                self.word_app.Options.CheckGrammarAsYouType = False
                self.word_app.Options.CheckSpellingAsYouType = False
                self.word_app.Options.AutoRecover = False
                self.word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
            except Exception as e:
                logging.warning(f"Worker {self.worker_id}: 設置 Word 選項時出錯: {str(e)}")

            logging.info(f"Worker {self.worker_id}: Word 應用程序已初始化")
            return True
        except Exception as e:
            logging.error(f"Worker {self.worker_id}: 初始化 Word 失敗: {str(e)}")
            return False
    
    def cleanup(self):
        """清理 Word 應用程序"""
        if self.word_app:
            try:
                # 關閉所有文檔
                for doc in self.word_app.Documents:
                    try:
                        doc.Close(SaveChanges=False)
                    except:
                        pass
                self.word_app.Quit()
                self.word_app = None
                logging.info(f"Worker {self.worker_id}: Word 應用程序已清理")
            except Exception as e:
                logging.error(f"Worker {self.worker_id}: 清理 Word 失敗: {str(e)}")
        try:
            pythoncom.CoUninitialize()
        except:
            pass
    
    def execute_task(self, func, *args, **kwargs):
        """執行任務"""
        with self.lock:
            if self.busy:
                return None, Exception("Worker is busy")
            self.busy = True
        
        try:
            # 確保 Word 已初始化
            if not self.word_app:
                if not self.initialize():
                    return None, Exception("Failed to initialize Word")
            
            # 將 word_app 傳遞給任務函數
            kwargs['word_app'] = self.word_app
            result = func(*args, **kwargs)
            return result, None
        except Exception as e:
            logging.error(f"Worker {self.worker_id}: 任務執行失敗: {str(e)}")
            # 如果發生錯誤，重新初始化 Word
            self.cleanup()
            self.initialize()
            return None, e
        finally:
            with self.lock:
                self.busy = False

class WordPoolProcessor:
    """
    Word 文檔處理池
    使用多個 Word 實例並行處理請求
    """
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化處理池"""
        self.max_workers = 3  # 最多同時運行的 Word 實例數
        self.workers = []
        self.worker_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.temp_dir = os.path.join(tempfile.gettempdir(), 'word_processor')
        
        # 創建臨時目錄
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
        
        # 初始化工作執行緒
        for i in range(self.max_workers):
            worker = WordWorker(i)
            self.workers.append(worker)
        
        logging.info(f"Word 處理池已初始化，工作執行緒數: {self.max_workers}")
    
    def get_available_worker(self):
        """獲取可用的工作執行緒"""
        max_attempts = 30  # 最多嘗試 30 次（30 秒）
        for _ in range(max_attempts):
            with self.worker_lock:
                for worker in self.workers:
                    if not worker.busy:
                        return worker
            time.sleep(1)  # 等待 1 秒後重試
        return None
    
    def process_task_with_retry(self, func, *args, **kwargs):
        """帶重試機制的任務處理"""
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                # 獲取可用的工作執行緒
                worker = self.get_available_worker()
                if not worker:
                    raise Exception("No available workers")
                
                # 執行任務
                result, error = worker.execute_task(func, *args, **kwargs)
                if error:
                    raise error
                
                return result
                
            except Exception as e:
                logging.warning(f"任務執行失敗 (嘗試 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    raise
    
    def submit_task(self, func, *args, **kwargs):
        """
        提交任務到處理池
        :param func: 要執行的函數
        :param args: 位置參數
        :param kwargs: 關鍵字參數
        :return: 任務結果
        """
        # 使用執行緒池提交任務
        future = self.executor.submit(self.process_task_with_retry, func, *args, **kwargs)
        try:
            # 等待結果，設置超時時間
            result = future.result(timeout=300)  # 5 分鐘超時
            return result
        except Exception as e:
            logging.error(f"任務執行失敗: {str(e)}")
            raise
    
    def cleanup(self):
        """清理所有資源"""
        # 關閉執行緒池
        self.executor.shutdown(wait=True)
        
        # 清理所有工作執行緒
        for worker in self.workers:
            worker.cleanup()
        
        # 清理臨時目錄
        if os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
            except:
                pass
        
        logging.info("Word 處理池已清理")

# 全局處理池實例
word_pool_processor = WordPoolProcessor()

# 便利函數
def process_word_task_parallel(func, *args, **kwargs):
    """
    並行處理 Word 任務的便利函數
    """
    return word_pool_processor.submit_task(func, *args, **kwargs)

# 批量處理函數
def process_word_tasks_batch(tasks):
    """
    批量處理多個 Word 任務
    :param tasks: [(func, args, kwargs), ...] 格式的任務列表
    :return: 結果列表
    """
    futures = []
    with ThreadPoolExecutor(max_workers=word_pool_processor.max_workers) as executor:
        for func, args, kwargs in tasks:
            future = executor.submit(word_pool_processor.process_task_with_retry, func, *args, **kwargs)
            futures.append(future)
        
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logging.error(f"批量任務執行失敗: {str(e)}")
                results.append(None)
        
        return results