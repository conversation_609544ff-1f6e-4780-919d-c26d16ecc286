"""
Word COM 物件修復工具
解決表格存取錯誤和進程管理問題
"""

import logging
import time
import pythoncom
import win32com.client
from contextlib import contextmanager
import psutil
import os

logger = logging.getLogger(__name__)

def safe_get_cell(table, row, col):
    """
    安全地獲取表格單元格，包含多層錯誤處理
    """
    try:
        # 檢查表格是否有效
        if not table:
            logger.error("表格對象為空")
            return None
            
        # 獲取表格的行數和列數
        try:
            rows_count = table.Rows.Count
            cols_count = table.Columns.Count
        except:
            # 某些表格可能無法直接獲取行列數
            logger.warning("無法獲取表格行列數，嘗試直接存取")
            rows_count = None
            cols_count = None
        
        # 檢查索引是否在範圍內
        if rows_count and cols_count:
            if row < 1 or row > rows_count or col < 1 or col > cols_count:
                logger.error(f"單元格索引超出範圍: ({row}, {col})，表格大小: ({rows_count}, {cols_count})")
                return None
        
        # 嘗試獲取單元格
        try:
            cell = table.Cell(Row=row, Column=col)
            return cell
        except Exception as e:
            # 如果直接訪問失敗，嘗試通過範圍訪問
            if hasattr(e, 'args') and len(e.args) > 1 and e.args[0] == -2147352567:
                logger.warning(f"單元格 ({row}, {col}) 可能不存在或已合併")
                # 嘗試通過遍歷方式查找
                try:
                    for r_idx in range(1, min(row + 1, rows_count + 1) if rows_count else row + 1):
                        for c_idx in range(1, min(col + 1, cols_count + 1) if cols_count else col + 1):
                            try:
                                test_cell = table.Cell(Row=r_idx, Column=c_idx)
                                if r_idx == row and c_idx == col:
                                    return test_cell
                            except:
                                continue
                except:
                    pass
            logger.error(f"無法獲取單元格 ({row}, {col}): {str(e)}")
            return None
            
    except Exception as e:
        logger.error(f"safe_get_cell 發生錯誤: {str(e)}")
        return None

def get_cell_text_and_color_safe(table, row, col):
    """
    安全版本的單元格文本和顏色獲取函數
    """
    try:
        cell = safe_get_cell(table, row, col)
        if not cell:
            return None, False
            
        # 安全地獲取文本
        try:
            text = cell.Range.Text
            # 移除結束符
            if text:
                text = text.strip(chr(7)).strip()
            else:
                text = ""
        except:
            text = ""
            
        # 安全地獲取顏色
        try:
            font = cell.Range.Font
            is_black = (font.Color == win32com.client.constants.wdColorAutomatic or 
                       font.Color == 0 or 
                       font.Color == -16777216)
        except:
            is_black = True
            
        return text, is_black
        
    except Exception as e:
        logger.error(f"get_cell_text_and_color_safe 發生錯誤: {str(e)}")
        return None, False

@contextmanager
def safe_word_app(visible=False):
    """
    安全的 Word 應用程式上下文管理器
    確保 Word 進程被正確清理
    """
    word_app = None
    try:
        # 初始化 COM
        pythoncom.CoInitialize()
        
        # 創建 Word 應用程式
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = visible
        word_app.DisplayAlerts = False
        
        yield word_app
        
    except Exception as e:
        logger.error(f"Word 應用程式錯誤: {str(e)}")
        raise
    finally:
        # 確保關閉 Word
        if word_app:
            try:
                # 關閉所有文檔
                while word_app.Documents.Count > 0:
                    try:
                        word_app.Documents(1).Close(SaveChanges=False)
                    except:
                        pass
                        
                # 退出 Word
                word_app.Quit()
                time.sleep(0.5)
                
                # 釋放 COM 物件
                del word_app
                
            except Exception as e:
                logger.error(f"關閉 Word 時發生錯誤: {str(e)}")
                
        # 反初始化 COM
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def cleanup_word_processes(max_age_seconds=300):
    """
    清理老舊的 Word 進程
    """
    cleaned_count = 0
    current_time = time.time()
    
    for proc in psutil.process_iter(['pid', 'name', 'create_time']):
        try:
            if proc.info['name'].lower() in ['winword.exe', 'word.exe']:
                age = current_time - proc.info['create_time']
                if age > max_age_seconds:
                    logger.info(f"終止老舊 Word 進程 PID: {proc.info['pid']} (年齡: {age:.1f}秒)")
                    proc.terminate()
                    cleaned_count += 1
                    time.sleep(0.5)
                    if proc.is_running():
                        proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
            
    if cleaned_count > 0:
        logger.info(f"共清理了 {cleaned_count} 個 Word 進程")
    
    return cleaned_count

def fix_com_initialization():
    """
    修復 COM 初始化問題，避免影響 cmd/powershell
    """
    try:
        # 確保 COM 已正確初始化
        pythoncom.CoInitialize()
        
        # 設置 COM 安全性
        pythoncom.CoInitializeSecurity(
            None,  # 安全描述符
            None,  # 認證服務
            None,  # 保留
            pythoncom.RPC_C_AUTHN_LEVEL_DEFAULT,  # 認證級別
            pythoncom.RPC_C_IMP_LEVEL_IMPERSONATE,  # 模擬級別
            None,  # 認證信息
            pythoncom.EOAC_NONE,  # 附加功能
            None   # 保留
        )
        
        return True
        
    except Exception as e:
        # 如果已經初始化，這是正常的
        if hasattr(e, 'hresult') and e.hresult == -2147417831:  # RPC_E_TOO_LATE
            return True
        logger.error(f"COM 初始化失敗: {str(e)}")
        return False
    finally:
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def test_word_com_safety():
    """
    測試 Word COM 物件的安全性
    """
    try:
        with safe_word_app() as word:
            # 創建測試文檔
            doc = word.Documents.Add()
            
            # 添加測試表格
            table = doc.Tables.Add(doc.Range(0, 0), 3, 3)
            
            # 測試單元格存取
            for row in range(1, 4):
                for col in range(1, 4):
                    cell = safe_get_cell(table, row, col)
                    if cell:
                        cell.Range.Text = f"R{row}C{col}"
            
            # 測試讀取
            text, is_black = get_cell_text_and_color_safe(table, 2, 2)
            logger.info(f"測試結果: 文本='{text}', 是否黑色={is_black}")
            
            # 關閉文檔
            doc.Close(SaveChanges=False)
            
            return True
            
    except Exception as e:
        logger.error(f"Word COM 測試失敗: {str(e)}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    # 清理老舊進程
    cleanup_word_processes()
    
    # 修復 COM 初始化
    fix_com_initialization()
    
    # 測試 Word COM
    if test_word_com_safety():
        print("Word COM 測試成功")
    else:
        print("Word COM 測試失敗")