# -*- coding: utf-8 -*-
"""
直接的 Word 文檔處理器
在當前線程中直接處理文檔，不使用任何包裝器
"""

import os
import time
import logging
import tempfile
import shutil
import uuid
import win32com.client
import pythoncom

logger = logging.getLogger(__name__)

def process_document_direct(file_path, file_name, pudcno, user_id, 
                           file_password, modify_func):
    """
    直接處理文檔，不使用任何包裝器或錯誤處理器
    """
    # 初始化 COM
    pythoncom.CoInitialize()
    
    word_app = None
    doc = None
    temp_dir = None
    
    try:
        # 創建 Word 應用程序
        word_app = win32com.client.DispatchEx("Word.Application")
        word_app.Visible = False
        word_app.DisplayAlerts = 0  # wdAlertsNone
        
        # 設置選項，防止 Normal.dotm 錯誤
        try:
            word_app.Options.DoNotPromptForConvert = True
            word_app.Options.ConfirmConversions = False
            word_app.Options.UpdateLinksAtOpen = False
            word_app.Options.CheckGrammarAsYouType = False
            word_app.Options.CheckSpellingAsYouType = False
            word_app.Options.AutoRecover = False
            word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
        except Exception as e:
            logging.warning(f"設置 Word 選項時出錯: {str(e)}")
        
        if file_password:
            # 處理受保護的文檔
            temp_dir = tempfile.mkdtemp()
            
            # 打開受密碼保護的文件
            logger.info(f"打開受保護的文檔: {file_path}")
            doc = word_app.Documents.Open(
                FileName=file_path,
                PasswordDocument=file_password,
                ReadOnly=False,
                AddToRecentFiles=False,
                Visible=False
            )
            
            # 檢查並解除保護
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
                logger.info(f"文檔保護類型: {protection_type}")
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                    logger.info("已解除文檔保護")
                except Exception as e:
                    logger.warning(f"解除保護失敗: {str(e)}")
            
            # 保存未保護的版本
            temp_unprotected = os.path.join(temp_dir, f"temp_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close(SaveChanges=False)
            doc = None
            
            # 修改文檔
            logger.info("開始修改文檔內容")
            new_path, new_name = modify_func(
                user_id, pudcno, temp_unprotected, file_name, word_app
            )
            
            if new_path and os.path.exists(new_path):
                # 重新保護文檔
                logger.info("重新保護文檔")
                doc = word_app.Documents.Open(new_path)
                
                if protection_type != -1:
                    try:
                        doc.Protect(Type=protection_type, Password=file_password)
                        logger.info("已重新保護文檔")
                    except Exception as e:
                        logger.warning(f"保護文檔失敗: {str(e)}")
                
                # 保存最終版本
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close(SaveChanges=False)
                doc = None
                
                # 移動到目標位置
                dest_path = os.path.join(os.path.dirname(file_path), new_name)
                shutil.move(final_path, dest_path)
                
                logger.info(f"文檔處理成功: {dest_path}")
                return dest_path, new_name
            else:
                logger.error("修改文檔失敗")
                return None, None
        else:
            # 處理未保護的文檔
            logger.info("處理未保護的文檔")
            result = modify_func(
                user_id, pudcno, file_path, file_name, word_app
            )
            return result
            
    except Exception as e:
        logger.error(f"處理文檔時發生錯誤: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None
        
    finally:
        # 清理
        if doc:
            try:
                doc.Close(SaveChanges=False)
            except:
                pass
        
        if word_app:
            try:
                # 關閉所有文檔
                while word_app.Documents.Count > 0:
                    try:
                        word_app.Documents(1).Close(SaveChanges=False)
                    except:
                        break
                word_app.Quit()
            except:
                pass
        
        # 清理臨時文件
        if temp_dir and os.path.exists(temp_dir):
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
        
        # 反初始化 COM
        try:
            pythoncom.CoUninitialize()
        except:
            pass