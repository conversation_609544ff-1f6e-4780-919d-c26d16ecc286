# -*- coding: utf-8 -*-
"""
簡化的 Word 文檔處理器
直接在當前線程中處理文檔，避免跨線程的 COM 對象問題
"""

import os
import time
import logging
import tempfile
import shutil
import uuid
import win32com.client
import pythoncom
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class SimpleWordProcessor:
    """簡單的 Word 文檔處理器"""
    
    def __init__(self, max_retries=3):
        self.max_retries = max_retries
    
    def process_document(self, file_path, file_name, pudcno, user_id, 
                        file_password, modify_func):
        """
        處理文檔的主函數
        """
        for attempt in range(self.max_retries):
            try:
                logger.info(f"嘗試處理文檔 (第 {attempt + 1} 次): {file_name}")
                
                # 初始化 COM
                pythoncom.CoInitialize()
                
                try:
                    # 創建 Word 應用程序
                    word_app = win32com.client.DispatchEx("Word.Application")
                    word_app.Visible = False
                    word_app.DisplayAlerts = 0  # wdAlertsNone
                    
                    # 設置安全選項
                    try:
                        word_app.Options.DoNotPromptForConvert = True
                        word_app.Options.ConfirmConversions = False
                        word_app.Options.UpdateLinksAtOpen = False
                        word_app.Options.UpdateFieldsAtPrint = False
                    except:
                        pass
                    
                    # 處理文檔
                    if file_password:
                        result = self._process_protected_document(
                            word_app, file_path, file_name, pudcno, 
                            user_id, file_password, modify_func
                        )
                    else:
                        result = modify_func(
                            user_id, pudcno, file_path, file_name, word_app
                        )
                    
                    if result and result[0]:
                        logger.info(f"文檔處理成功: {file_name}")
                        return result
                    
                finally:
                    # 清理 Word 應用程序
                    if word_app:
                        try:
                            # 關閉所有文檔
                            for doc in word_app.Documents:
                                try:
                                    doc.Close(SaveChanges=False)
                                except:
                                    pass
                            word_app.Quit()
                        except:
                            pass
                    
                    # 反初始化 COM
                    pythoncom.CoUninitialize()
                    
            except Exception as e:
                logger.error(f"處理文檔失敗 (第 {attempt + 1} 次): {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(2)  # 短暫等待後重試
        
        logger.error(f"無法處理文檔: {file_name}")
        return None, None
    
    def _process_protected_document(self, word_app, file_path, file_name, pudcno, 
                                   user_id, file_password, modify_func):
        """處理受保護的文檔"""
        temp_dir = tempfile.mkdtemp()
        doc = None
        
        try:
            # 打開受密碼保護的文件
            doc = word_app.Documents.Open(
                FileName=file_path,
                PasswordDocument=file_password,
                ReadOnly=False,
                AddToRecentFiles=False,
                Visible=False
            )
            
            # 檢查並解除保護
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                except Exception as e:
                    logger.warning(f"解除保護失敗: {str(e)}")
            
            # 保存未保護的版本
            temp_unprotected = os.path.join(temp_dir, f"temp_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close(SaveChanges=False)
            doc = None
            
            # 修改文檔
            new_path, new_name = modify_func(
                user_id, pudcno, temp_unprotected, file_name, word_app
            )
            
            if new_path and os.path.exists(new_path):
                # 重新保護文檔
                doc = word_app.Documents.Open(new_path)
                
                if protection_type != -1:
                    try:
                        doc.Protect(Type=protection_type, Password=file_password)
                    except Exception as e:
                        logger.warning(f"保護文檔失敗: {str(e)}")
                
                # 保存最終版本
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close(SaveChanges=False)
                doc = None
                
                # 移動到目標位置
                dest_path = os.path.join(os.path.dirname(file_path), new_name)
                shutil.move(final_path, dest_path)
                
                return dest_path, new_name
            
            return None, None
            
        finally:
            # 清理
            if doc:
                try:
                    doc.Close(SaveChanges=False)
                except:
                    pass
            
            # 清理臨時文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass

# 全局簡單處理器實例
simple_processor = SimpleWordProcessor()

def process_document_simple(file_path, file_name, pudcno, user_id, 
                           file_password, modify_func):
    """
    使用簡單處理器處理文檔
    """
    return simple_processor.process_document(
        file_path, file_name, pudcno, user_id, file_password, modify_func
    )