# -*- coding: utf-8 -*-
"""
單例管理器，防止重複初始化
"""

import threading
import logging

logger = logging.getLogger(__name__)

class SingletonMeta(type):
    """
    線程安全的單例元類
    """
    _instances = {}
    _lock = threading.Lock()
    
    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]

class ProcessorManager:
    """
    處理器管理器，確保只初始化一次
    """
    _initialized = False
    _lock = threading.Lock()
    
    @classmethod
    def is_initialized(cls):
        """檢查是否已初始化"""
        return cls._initialized
    
    @classmethod
    def set_initialized(cls):
        """設置為已初始化"""
        with cls._lock:
            cls._initialized = True
            logger.info("ProcessorManager marked as initialized")
    
    @classmethod
    def reset(cls):
        """重置狀態（僅用於測試）"""
        with cls._lock:
            cls._initialized = False

# 全局初始化標記
_global_init_lock = threading.Lock()
_global_initialized = False

def ensure_single_init(func):
    """
    裝飾器：確保函數只執行一次
    """
    def wrapper(*args, **kwargs):
        global _global_initialized
        
        if _global_initialized:
            logger.info(f"{func.__name__} already initialized, skipping")
            return None
            
        with _global_init_lock:
            if _global_initialized:
                return None
                
            result = func(*args, **kwargs)
            _global_initialized = True
            return result
    
    return wrapper