import asyncio
import concurrent.futures
import hashlib
import json
import logging
import os
import queue
import shutil
import tempfile
import threading
import time
import uuid
from collections import defaultdict
from datetime import datetime, timedelta
from pathlib import Path

import pythoncom
import win32com.client
from .word_processor_cache import cache, set_cache
from .word_processor_settings import get_setting, TEMP_DIR, CACHE_DIR
from .word_error_handler import SafeWordApplication, kill_all_word_processes, start_global_monitoring

# 設置日誌記錄
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class FileCache:
    """文件緩存管理器"""
    def __init__(self, cache_dir=None, max_cache_size_gb=10, cache_ttl_hours=24):
        self.cache_dir = cache_dir or os.path.join(CACHE_DIR, 'word_files')
        self.max_cache_size = max_cache_size_gb * 1024 * 1024 * 1024  # 轉換為字節
        self.cache_ttl = timedelta(hours=cache_ttl_hours)
        
        # 創建緩存目錄
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
    
    def get_cache_key(self, file_path, user_id, pudcno, file_password):
        """生成緩存鍵"""
        key_data = f"{file_path}:{user_id}:{pudcno}:{file_password or ''}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def get_cached_file(self, cache_key):
        """獲取緩存的文件"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.docx")
        meta_file = os.path.join(self.cache_dir, f"{cache_key}.meta")
        
        if os.path.exists(cache_file) and os.path.exists(meta_file):
            # 檢查緩存是否過期
            with open(meta_file, 'r') as f:
                meta = json.load(f)
            
            cached_time = datetime.fromisoformat(meta['cached_time'])
            if datetime.now() - cached_time < self.cache_ttl:
                logging.info(f"從緩存返回文件: {cache_key}")
                return cache_file, meta['file_name']
        
        return None, None
    
    def save_to_cache(self, cache_key, file_path, file_name):
        """保存文件到緩存"""
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.docx")
        meta_file = os.path.join(self.cache_dir, f"{cache_key}.meta")
        
        # 複製文件到緩存
        shutil.copy2(file_path, cache_file)
        
        # 保存元數據
        meta = {
            'file_name': file_name,
            'cached_time': datetime.now().isoformat(),
            'file_size': os.path.getsize(file_path)
        }
        with open(meta_file, 'w') as f:
            json.dump(meta, f)
        
        # 清理舊緩存
        self.cleanup_old_cache()
    
    def cleanup_old_cache(self):
        """清理過期和超過大小限制的緩存"""
        total_size = 0
        files_info = []
        
        # 收集所有緩存文件信息
        for filename in os.listdir(self.cache_dir):
            if filename.endswith('.docx'):
                file_path = os.path.join(self.cache_dir, filename)
                meta_path = os.path.join(self.cache_dir, filename.replace('.docx', '.meta'))
                
                if os.path.exists(meta_path):
                    with open(meta_path, 'r') as f:
                        meta = json.load(f)
                    
                    cached_time = datetime.fromisoformat(meta['cached_time'])
                    file_size = os.path.getsize(file_path)
                    
                    # 檢查是否過期
                    if datetime.now() - cached_time > self.cache_ttl:
                        os.remove(file_path)
                        os.remove(meta_path)
                        continue
                    
                    files_info.append((file_path, meta_path, cached_time, file_size))
                    total_size += file_size
        
        # 如果總大小超過限制，刪除最舊的文件
        if total_size > self.max_cache_size:
            files_info.sort(key=lambda x: x[2])  # 按時間排序
            
            for file_path, meta_path, _, file_size in files_info:
                if total_size <= self.max_cache_size:
                    break
                
                os.remove(file_path)
                os.remove(meta_path)
                total_size -= file_size

class WordWorkerPool:
    """Word 工作執行緒池"""
    def __init__(self, pool_size=5, max_retries=3):  # 減少初始 Worker 數量
        self.pool_size = pool_size
        self.max_retries = max_retries
        self.workers = queue.Queue()
        self.worker_stats = defaultdict(lambda: {'tasks': 0, 'errors': 0})
        self.lock = threading.Lock()
        
        # 初始化工作執行緒
        self._initialize_workers()
    
    def _initialize_workers(self):
        """初始化所有工作執行緒"""
        for i in range(self.pool_size):
            worker_thread = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                daemon=True
            )
            worker_thread.start()
    
    def _worker_loop(self, worker_id):
        """工作執行緒主循環"""
        # 每個工作線程都需要初始化 COM
        try:
            pythoncom.CoInitialize()
        except:
            pythoncom.CoInitializeEx(pythoncom.COINIT_MULTITHREADED)
            
        word_app = None
        
        try:
            # 創建 Word 應用程序實例
            word_app = win32com.client.DispatchEx("Word.Application")
            word_app.Visible = False
            word_app.DisplayAlerts = 0  # wdAlertsNone
            
            # 設置安全選項
            try:
                word_app.Options.DoNotPromptForConvert = True
                word_app.Options.ConfirmConversions = False
                word_app.Options.UpdateLinksAtOpen = False
                word_app.Options.UpdateFieldsAtPrint = False
                word_app.AutomationSecurity = 3  # msoAutomationSecurityForceDisable
            except:
                pass
            
            # 將工作執行緒加入可用池
            self.workers.put((worker_id, word_app))
            logging.info(f"Worker {worker_id} 已初始化")
            
            # 保持執行緒活動
            while True:
                time.sleep(1)
                
        except Exception as e:
            logging.error(f"Worker {worker_id} 初始化失敗: {str(e)}")
        finally:
            if word_app:
                try:
                    word_app.Quit()
                except:
                    pass
            pythoncom.CoUninitialize()
    
    def get_worker(self, timeout=30):
        """獲取可用的工作執行緒"""
        try:
            return self.workers.get(timeout=timeout)
        except queue.Empty:
            raise Exception("No available workers")
    
    def return_worker(self, worker_id, word_app):
        """歸還工作執行緒"""
        self.workers.put((worker_id, word_app))
    
    def update_stats(self, worker_id, success=True):
        """更新工作執行緒統計"""
        with self.lock:
            self.worker_stats[worker_id]['tasks'] += 1
            if not success:
                self.worker_stats[worker_id]['errors'] += 1

class EnterpriseWordProcessor:
    """企業級 Word 處理器"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.init()
        return cls._instance
    
    def init(self):
        """初始化處理器"""
        # 根據預期負載配置參數
        self.worker_pool = WordWorkerPool(pool_size=5)  # 減少初始化時的 Worker 數量
        self.file_cache = FileCache(max_cache_size_gb=50, cache_ttl_hours=48)
        self.task_queue = queue.PriorityQueue()
        self.processing_tasks = {}
        self.task_lock = threading.Lock()
        self.temp_dir = TEMP_DIR
        
        # 啟動任務處理執行緒
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=30)
        
        # 啟動監控執行緒
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        logging.info("企業級 Word 處理器已初始化")
        
        # 註冊性能追蹤
        self._register_performance_tracking()
        
        # 啟動全局錯誤監控
        start_global_monitoring()
    
    def _monitor_loop(self):
        """監控執行緒，定期檢查系統狀態"""
        while True:
            try:
                # 每分鐘記錄一次統計信息
                time.sleep(60)
                
                stats = {
                    'available_workers': self.worker_pool.workers.qsize(),
                    'pending_tasks': self.task_queue.qsize(),
                    'processing_tasks': len(self.processing_tasks)
                }
                
                logging.info(f"系統狀態: {stats}")
                
                # 檢查是否需要擴展工作池
                if stats['available_workers'] < 2 and stats['pending_tasks'] > 10:
                    logging.warning("工作執行緒不足，考慮增加池大小")
                
            except Exception as e:
                logging.error(f"監控執行緒錯誤: {str(e)}")
    
    def process_document(self, file_path, file_name, pudcno, user_id, file_password, 
                        modify_func, priority=5):
        """
        處理文檔
        :param priority: 優先級（1-10，1最高）
        """
        start_time = time.time()
        
        # 檢查緩存
        cache_key = self.file_cache.get_cache_key(file_path, user_id, pudcno, file_password)
        cached_file, cached_name = self.file_cache.get_cached_file(cache_key)
        
        if cached_file:
            # 更新緩存命中統計
            self._update_cache_hit_stats(True)
            return cached_file, cached_name
        
        self._update_cache_hit_stats(False)
        
        # 檢查是否已在處理中（避免重複處理）
        task_key = f"{file_path}:{user_id}:{pudcno}"
        with self.task_lock:
            if task_key in self.processing_tasks:
                # 等待現有任務完成
                future = self.processing_tasks[task_key]
                return future.result()
            
            # 提交新任務
            future = self.executor.submit(
                self._process_document_internal,
                file_path, file_name, pudcno, user_id, file_password, modify_func
            )
            self.processing_tasks[task_key] = future
        
        try:
            result = future.result(timeout=300)  # 5分鐘超時
            
            # 保存到緩存
            if result[0]:
                self.file_cache.save_to_cache(cache_key, result[0], result[1])
            
            # 更新性能統計
            self._update_performance_stats(True, time.time() - start_time)
            
            return result
        finally:
            # 清理處理中的任務
            with self.task_lock:
                self.processing_tasks.pop(task_key, None)
    
    def _process_document_internal(self, file_path, file_name, pudcno, user_id, 
                                  file_password, modify_func):
        """內部文檔處理函數"""
        # 確保當前線程的 COM 初始化
        try:
            pythoncom.CoInitialize()
        except:
            # 如果已經初始化，忽略錯誤
            pass
            
        worker_id = None
        word_app = None
        
        try:
            # 獲取工作執行緒
            worker_id, word_app = self.worker_pool.get_worker()
            
            # 執行文檔處理
            if file_password:
                result = self._process_protected_document(
                    word_app, file_path, file_name, pudcno, user_id, file_password, modify_func
                )
            else:
                result = modify_func(user_id, pudcno, file_path, file_name, word_app)
            
            # 更新統計
            self.worker_pool.update_stats(worker_id, success=True)
            
            return result
            
        except Exception as e:
            logging.error(f"處理文檔失敗: {str(e)}")
            if worker_id is not None:
                self.worker_pool.update_stats(worker_id, success=False)
            raise
        finally:
            # 歸還工作執行緒
            if worker_id is not None and word_app is not None:
                self.worker_pool.return_worker(worker_id, word_app)
    
    def _process_protected_document(self, word_app, file_path, file_name, pudcno, 
                                   user_id, file_password, modify_func):
        """處理受保護的文檔"""
        temp_dir = tempfile.mkdtemp()
        doc = None
        
        try:
            # 打開受密碼保護的文件
            doc = word_app.Documents.Open(FileName=file_path, PasswordDocument=file_password)
            
            # 解除保護
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                except:
                    pass
            
            # 保存解密後的臨時文件
            temp_unprotected = os.path.join(temp_dir, f"unprotected_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close()
            doc = None
            
            # 修改文件內容
            new_path, new_name = modify_func(user_id, pudcno, temp_unprotected, file_name, word_app)
            
            if new_path and os.path.exists(new_path):
                # 重新保護文件
                doc = word_app.Documents.Open(FileName=new_path)
                
                if protection_type != -1:
                    try:
                        doc.Protect(Type=3, Password=file_password)
                    except:
                        pass
                
                # 保存最終文件
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close()
                doc = None
                
                # 移動到最終位置
                final_name = f"{user_id}_{os.path.basename(file_name)}"
                dest_path = os.path.join(os.path.dirname(file_path), final_name)
                shutil.move(final_path, dest_path)
                
                return dest_path, final_name
            
            return None, None
            
        finally:
            if doc:
                try:
                    doc.Close()
                except:
                    pass
            
            # 清理臨時目錄
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    def _register_performance_tracking(self):
        """註冊性能追蹤"""
        self.performance_stats = {
            'total_processed': 0,
            'total_success': 0,
            'total_failed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0,
            'last_reset': time.time(),
        }
    
    def _update_performance_stats(self, success, processing_time):
        """更新性能統計"""
        self.performance_stats['total_processed'] += 1
        if success:
            self.performance_stats['total_success'] += 1
        else:
            self.performance_stats['total_failed'] += 1
        self.performance_stats['total_time'] += processing_time
        
        # 計算並更新緩存
        stats = self._calculate_performance_metrics()
        set_cache('word_processor_performance', stats, timeout=300)
    
    def _update_cache_hit_stats(self, hit):
        """更新緩存命中統計"""
        if hit:
            self.performance_stats['cache_hits'] += 1
        else:
            self.performance_stats['cache_misses'] += 1
    
    def _calculate_performance_metrics(self):
        """計算性能指標"""
        total = self.performance_stats['total_processed']
        if total == 0:
            return {}
        
        elapsed_time = time.time() - self.performance_stats['last_reset']
        
        return {
            'success_rate': (self.performance_stats['total_success'] / total) * 100,
            'avg_time': self.performance_stats['total_time'] / total,
            'cache_hit_rate': (self.performance_stats['cache_hits'] / 
                              (self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'])) * 100
                              if (self.performance_stats['cache_hits'] + self.performance_stats['cache_misses']) > 0 else 0,
            'throughput': (total / elapsed_time) * 60 if elapsed_time > 0 else 0,  # 每分鐘處理數
        }

# 全局實例
enterprise_processor = EnterpriseWordProcessor()

# 自動啟動管理器
try:
    from .word_processor_auto_manager import auto_manager
    logging.info("自動管理器已啟動")
except Exception as e:
    logging.warning(f"無法啟動自動管理器: {str(e)}")

# 便利函數
def process_word_document_enterprise(file_path, file_name, pudcno, user_id, 
                                   file_password, modify_func, priority=5):
    """企業級文檔處理函數"""
    return enterprise_processor.process_document(
        file_path, file_name, pudcno, user_id, file_password, modify_func, priority
    )