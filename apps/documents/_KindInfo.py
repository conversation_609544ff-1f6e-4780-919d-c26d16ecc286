# -*- coding: UTF8 -*-
import json

import cx_Oracle
from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import get_ce_today
from utils.token_utils import validate_access_token_and_params

''' KIND INSERT START '''

# 映射字典
KIND_NAME_MAPPING = {
    "kind_code": "KINDCODE",
    "kind_name": "KINDNAME",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID",
    "owner_date": "OWNERDATE",
}

def is_kind_code_exists(kind_code, cursor):
    """
    Check if the given kind_code exists in the KIND table.
    """
    check_sql = f"SELECT 1 FROM KIND WHERE KINDCODE = '{kind_code}'"
    cursor.execute(check_sql)
    return bool(cursor.fetchone())

def insert_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:
                if is_kind_code_exists(data['kind_code'], cursor):
                    return handle_error(context, f"類別代號 : '{data['kind_code']}' 已經存在", status.HTTP_400_BAD_REQUEST)

                # 插入主檔資料
                kind_data = {
                    "kind_code": data['kind_code'],
                    "kind_name": data['kind_name'],
                    "owner_id": user_id,
                    "owner_date": get_ce_today()
                }

                keys = ", ".join(KIND_NAME_MAPPING[key] for key in kind_data.keys())
                values = ", ".join(
                    f"'{kind_data[key]}'" if kind_data[key] is not None else "null" for key in kind_data.keys())
                sql = f"INSERT INTO KIND ({keys}) VALUES ({values})"
                cursor.execute(sql)

                return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('kind_code')
def insert_kind_method(request, json_data, role, user_id):
    context = "insert_kind_method"

    if request.method == "POST":
        # 使用原生SQL進行數據插入
        try:
            result, result_status = insert_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' KIND INSERT END '''

''' KIND UPDATE START '''

def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "old_kind_code": "KINDCODE",
        "now_kind_code": "KINDCODE",
        "kind_name": "KINDNAME",
        "hub_type": "HUBTYPE",
        "owner_id": "OWNERID"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 生成SET子句部分
                set_parts = []
                for key, value in data.items():
                    if key not in ["kind_code"]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                # 根据old_kind_code更新??
                sql = f"UPDATE KIND SET {set_string} WHERE KINDCODE = '{data['kind_code']}  '"
                print(sql)
                cursor.execute(sql)

                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('kind_code')
def update_kind_method(request, json_data, role, user_id):
    context = "update_kind_method"

    if request.method == "POST":
        try:
            result, result_status = update_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' KIND UPDATE END '''

''' KIND DELETE START '''

def delete_with_raw_sql(context, kind_codes):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for kind_code in kind_codes:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM KIND WHERE KINDCODE = '{kind_code}'"
                    cursor.execute(sql)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('kind_codes')
def delete_kind_method(request, json_data, role, user_id):
    context = "delete_kind_method"

    if request.method == "POST":
        kind_codes = json_data.get("kind_codes", [])

        # Check if kind_codes is a list and is not empty
        if not isinstance(kind_codes, list) or not kind_codes:
            return handle_error(context, "kind_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST)

        try:
            result, result_status = delete_with_raw_sql(context, kind_codes)
            return result, result_status
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST

''' KIND DELETE END '''

''' KIND SELECT START '''

def transform_to_frontend_structure(data, index):
    # 將資料庫的結構轉換為前端所需的結構
    frontend_data = {
        "id": index,
        "kind_code": data.get("KINDCODE"),
        "kind_name": data.get("KINDNAME"),
        "hub_type": data.get("HUBTYPE"),
        "owner_name": data.get("CHI_NAME005"),
        "modify_date": data.get("OWNERDATE"),
    }
    return frontend_data


def select_with_raw_sql(context):
    try:
        with connection.cursor() as cursor:
            sql = """
        	        SELECT KINDCODE, KINDNAME, HUBTYPE, OWNERID, CHI_NAME005, TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
                      FROM KIND, HRF005@B2B
        		     WHERE REPLACE(OWNERID, 'h', '') = NO005(+)
                     ORDER BY KINDCODE
                """

            cursor.execute(sql)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 將從資料庫中獲取的每一行資料轉換為前端需要的格式，並添加索引
            return [transform_to_frontend_structure(dict(zip(columns, row)), idx + 1) for idx, row in
                    enumerate(rows)], status.HTTP_200_OK

    except Exception as e:
        handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_kind_method(request, json_data, role, user_id):
    context = "select_kind_method"

    if request.method == "POST":
        try:
            result, result_status = select_with_raw_sql(context)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' KIND SELECT END '''
