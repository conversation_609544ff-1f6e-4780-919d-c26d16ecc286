# -*- coding: UTF8 -*-
import json
import logging
import os

from django.db import connection
from rest_framework import status

from HEYSONG_ERP_HY_API.celery_config import app
from HEYSONG_ERP_HY_API.settings import BASE_DIR
from utils.email_utils import send_report_email, DELAY_SEND_EMAIL_SECONDS
from utils.email_utils import FROM_EMAIL, FROM_PASSWORD, SMTP_SERVER, SMTP_PORT
from utils.token_utils import validate_access_token_and_params
from utils.email_helpers import get_business_notification_from_email_info

def update_bpudcht(pudcno, readonly):
    try:
        with connection.cursor() as cursor:
            sql = """
                UPDATE BPUDCHT
                   SET READONLY = :qREADONLY, OWNERDATE = SYSDATE
                 WHERE PUDCNO = :qPUDCNO
            """
            params = {'qREADONLY': readonly, 'qPUDCNO': pudcno}
            cursor.execute(sql, params)
            logging.info(f"成功更新 BPUDCHT 表，pudcno: {pudcno}, readonly: {readonly}")
    except Exception as e:
        logging.exception('update_bpudcht 中出現異常:', exc_info=True)

@app.task
def check_and_send_email_task(pudcno):
    try:
        # 直接在任務中調用 get_business_notification_from_email_info
        email_infos = get_business_notification_from_email_info(pudcno)

        # 調用 send_report_email，傳遞 email_infos
        send_report_email.delay(
            pudcno=pudcno,
            email_infos=email_infos,
            from_type='業務通報',
            file_path=os.path.join(BASE_DIR, 'uploads', '業務通報', pudcno),
            from_email=FROM_EMAIL,
            from_password=FROM_PASSWORD,
            smtp_server=SMTP_SERVER,
            smtp_port=SMTP_PORT
        )

        logging.info(f"check_and_send_email_task 任務成功執行，pudcno: {pudcno}")

        with connection.cursor() as cursor:
            sql = """
                SELECT READONLY FROM BPUDCHT WHERE PUDCNO = :qPUDCNO
            """
            params = {'qPUDCNO': pudcno}

            cursor.execute(sql, params)
            result = cursor.fetchone()
            logging.info(f"查詢結果 result: {result}")

            if result:
                logging.info(f"查詢結果 READONLY: {result[0]}")
                if result[0] == '1':
                    update_bpudcht(pudcno, '2')
                else:
                    logging.info(f"READONLY 不是 1，而是 {result[0]}")
            else:
                logging.info("查詢結果為 None")
    except Exception as e:
        logging.exception('check_and_send_email 任務中出現異常:' + str(e), exc_info=True)

# 保存task_id
def save_task_id(task_id, pudcno):
    try:
        with connection.cursor() as cursor:
            sql = """
                UPDATE BPUDCHT
                   SET TASKID = :qTASKID
                 WHERE PUDCNO = :qPUDCNO
            """
            params = {'qTASKID': task_id, 'qPUDCNO': pudcno}

            cursor.execute(sql, params)
    except Exception as e:
        logging.exception('save_task_id 中出現異常:' + str(e), exc_info=True)

@validate_access_token_and_params(None)
def business_notification_email_method(request, json_data, role, user_id):
    context = "business_notification_email_method"

    if request.method == "POST":
        json_data = json.loads(request.body)
        pudcnos = json_data.get("pudcno", [])

        # 檢查 pudcnos 是否為列表且不為空
        if not isinstance(pudcnos, list) or not pudcnos:
            return "pudcnos 必須是一個列表且不能為空", status.HTTP_400_BAD_REQUEST

        for pudcno in pudcnos:
            try:
                # 更新為定稿狀態，但不立即發送郵件
                with connection.cursor() as cursor:
                    # 檢測函文是否已定稿，前端還沒有刷新頁面
                    sql = """
                        SELECT READONLY FROM BPUDCHT WHERE PUDCNO = :qPUDCNO AND READONLY = :qREADONLY
                    """
                    params = {'qPUDCNO': pudcno, 'qREADONLY': '2'}

                    cursor.execute(sql, params)
                    result = cursor.fetchone()
                    # READONLY為2，即已經發送
                    if result is not None and result[0] == '2':
                        logging.info('函文已發送，請重新查詢或重新整理頁面')
                        return '函文已發送，請重新查詢或重新整理頁面', status.HTTP_400_BAD_REQUEST

                    update_bpudcht(pudcno, '2')

                logging.info(f'排程郵件任務，pudcno: {pudcno}')

                # 設定一個延遲任務，延遲時間為DELAY_SEND_EMAIL_SECONDS秒
                result = check_and_send_email_task.apply_async((pudcno,), countdown=DELAY_SEND_EMAIL_SECONDS)

                # 保存task_id
                save_task_id(result, pudcno)

            except Exception as e:
                logging.exception('排程郵件任務時發生異常:', exc_info=True)
                return str(e), status.HTTP_400_BAD_REQUEST

        return '寄件成功', status.HTTP_200_OK
