# -*- coding: utf-8 -*-
"""
安全的 Word 文檔處理器
處理異常情況和錯誤恢復
"""

import os
import time
import logging
import threading
import tempfile
import shutil
import uuid
import win32com.client
import pythoncom
from contextlib import contextmanager

from .word_error_handler import SafeWordApplication, kill_all_word_processes

logger = logging.getLogger(__name__)

class SafeDocumentProcessor:
    """安全的文檔處理器"""
    
    def __init__(self, max_retries=3, timeout=120):
        self.max_retries = max_retries
        self.timeout = timeout
        self.processing_lock = threading.Lock()
    
    def process_document_safely(self, file_path, file_name, pudcno, user_id, 
                               file_password, modify_func):
        """
        安全地處理文檔，包含錯誤恢復機制
        """
        for attempt in range(self.max_retries):
            try:
                logger.info(f"嘗試處理文檔 (第 {attempt + 1} 次): {file_name}")
                
                # 使用安全的 Word 應用程序
                with SafeWordApplication(timeout=self.timeout) as word_app:
                    if not word_app:
                        raise Exception("無法創建 Word 應用程序")
                    
                    # 執行處理
                    result = self._process_with_timeout(
                        word_app, file_path, file_name, pudcno, 
                        user_id, file_password, modify_func
                    )
                    
                    if result and result[0]:
                        logger.info(f"文檔處理成功: {file_name}")
                        return result
                    
            except Exception as e:
                logger.error(f"處理文檔失敗 (第 {attempt + 1} 次): {str(e)}")
                
                # 如果是最後一次嘗試，清理所有 Word 進程
                if attempt == self.max_retries - 1:
                    logger.warning("所有嘗試都失敗，清理 Word 進程")
                    kill_all_word_processes()
                    time.sleep(5)  # 等待進程完全關閉
                else:
                    time.sleep(2)  # 短暫等待後重試
        
        logger.error(f"無法處理文檔: {file_name}")
        return None, None
    
    def _process_with_timeout(self, word_app, file_path, file_name, pudcno, 
                             user_id, file_password, modify_func):
        """
        帶超時的文檔處理
        """
        result = [None, None]
        error = [None]
        
        def process_thread():
            try:
                # 初始化 COM
                pythoncom.CoInitialize()
                
                if file_password:
                    result[0], result[1] = self._process_protected_document(
                        word_app, file_path, file_name, pudcno, 
                        user_id, file_password, modify_func
                    )
                else:
                    result[0], result[1] = modify_func(
                        user_id, pudcno, file_path, file_name, word_app
                    )
            except Exception as e:
                error[0] = e
            finally:
                pythoncom.CoUninitialize()
        
        # 在新線程中執行處理
        thread = threading.Thread(target=process_thread)
        thread.start()
        thread.join(timeout=self.timeout)
        
        if thread.is_alive():
            logger.error(f"處理超時: {file_name}")
            # 注意：無法強制停止線程，但至少可以記錄錯誤
            return None, None
        
        if error[0]:
            raise error[0]
        
        return result[0], result[1]
    
    def _process_protected_document(self, word_app, file_path, file_name, pudcno, 
                                   user_id, file_password, modify_func):
        """
        處理受保護的文檔
        """
        temp_dir = tempfile.mkdtemp()
        doc = None
        
        try:
            # 安全地打開文檔
            doc = self._safe_open_document(
                word_app, file_path, file_password
            )
            
            if not doc:
                raise Exception("無法打開文檔")
            
            # 解除保護
            protection_type = self._get_protection_type(doc)
            if protection_type != -1:
                self._safe_unprotect(doc, file_password)
            
            # 保存未保護的版本
            temp_unprotected = os.path.join(temp_dir, f"temp_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close(SaveChanges=False)
            doc = None
            
            # 修改文檔
            new_path, new_name = modify_func(
                user_id, pudcno, temp_unprotected, file_name, word_app
            )
            
            if new_path and os.path.exists(new_path):
                # 重新保護文檔
                doc = word_app.Documents.Open(new_path)
                
                if protection_type != -1:
                    self._safe_protect(doc, protection_type, file_password)
                
                # 保存最終版本
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close(SaveChanges=False)
                doc = None
                
                # 移動到目標位置
                dest_path = os.path.join(os.path.dirname(file_path), new_name)
                shutil.move(final_path, dest_path)
                
                return dest_path, new_name
            
            return None, None
            
        finally:
            # 清理
            if doc:
                try:
                    doc.Close(SaveChanges=False)
                except:
                    pass
            
            # 清理臨時文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
    
    def _safe_open_document(self, word_app, file_path, password=None):
        """安全地打開文檔"""
        try:
            # 確保在正確的線程中執行
            pythoncom.CoInitialize()
            
            if password:
                doc = word_app.Documents.Open(
                    FileName=file_path,
                    PasswordDocument=password,
                    ReadOnly=False,
                    AddToRecentFiles=False,
                    Revert=False,
                    Visible=False
                )
            else:
                doc = word_app.Documents.Open(
                    FileName=file_path,
                    ReadOnly=False,
                    AddToRecentFiles=False,
                    Visible=False
                )
            return doc
        except Exception as e:
            logger.error(f"打開文檔失敗: {str(e)}")
            return None
        finally:
            try:
                pythoncom.CoUninitialize()
            except:
                pass
    
    def _get_protection_type(self, doc):
        """獲取文檔保護類型"""
        try:
            return doc.ProtectionType
        except:
            return -1
    
    def _safe_unprotect(self, doc, password):
        """安全地解除文檔保護"""
        try:
            doc.Unprotect(Password=password)
            return True
        except Exception as e:
            logger.warning(f"解除保護失敗: {str(e)}")
            return False
    
    def _safe_protect(self, doc, protection_type, password):
        """安全地保護文檔"""
        try:
            doc.Protect(Type=protection_type, Password=password)
            return True
        except Exception as e:
            logger.warning(f"保護文檔失敗: {str(e)}")
            return False

# 全局安全處理器實例
safe_processor = SafeDocumentProcessor()

def process_document_with_safety(file_path, file_name, pudcno, user_id, 
                                file_password, modify_func):
    """
    使用安全處理器處理文檔
    """
    return safe_processor.process_document_safely(
        file_path, file_name, pudcno, user_id, file_password, modify_func
    )