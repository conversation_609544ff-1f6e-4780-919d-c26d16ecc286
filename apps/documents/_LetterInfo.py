# -*- coding: UTF8 -*-
import itertools
import logging
import os
import shutil

import cx_Oracle
import datetime
import calendar

from celery.result import AsyncResult
from django.db import connection, transaction, IntegrityError
from rest_framework import status

from HEYSONG_ERP_HY_API import settings
from utils.error_utils import handle_error
from utils.main_utils import select_get_serial, get_user_permissions, paginate_data, remove_folder
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' LETTER_SQL SELECT START '''
# select_公文作業
select_letter_data_sql = """
      WITH DEALERORDEPT AS
               (SELECT CASE
                           WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                               THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                           ELSE TRIM(SUBSTR(DEPT, 21, 10))
                       END AS DEALERORDEPT,
                       CASE
                           WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                               THEN DEPT
                           ELSE SUBSTR(DEPT, 21, 10) || TRIM(SUBSTR(DEPT, 31, 20))
                       END AS DEPT
                  FROM (SELECT RPAD('HSG001', 10, ' ') || RPAD('總公司', 10, ' ') || RPAD(DEPTCODE, 10, ' ') ||
                               DEPTNAME AS DEPT
                          FROM (SELECT DEPT_CODE007 AS DEPTCODE, DEPT_DESC_C007 AS DEPTNAME
                                  FROM HRF007@B2B
                                 WHERE REMOVE_DATE007 IS NULL
                                 UNION ALL
                                SELECT DEPTCODE, DEPTNAME
                                  FROM DEPT, HRF005@B2B
                                 WHERE REPLACE(OWNERID, 'h', '') = NO005
                                 ORDER BY DEPTCODE)
                         UNION ALL
                        SELECT DEALER
                          FROM (SELECT RPAD(NVL('', ' '), 10, ' ') || RPAD(NVL('', ' '), 10, ' ') ||
                                       RPAD(AGENT_CODE025, 10, ' ') ||
                                       BRIEF_NAME025 DEALER
                                  FROM OCV025@B2B
                                 WHERE SUB_TYPE025 = '01'
                                   AND EOS_CODE025 = 'Y'
                                   AND RETIRE_DATE025 IS NULL
                                 ORDER BY AGENT_CODE025))),
    
           PUDCRECEIVER_DEALER AS ( SELECT PUDCNO, DEPT
                                      FROM ( SELECT PUDCNO, VENDORCODE, DEPTCODE
                                               FROM PUDCHT_PUDCRECEIVER
                                              WHERE 1 = 1 ), DEALERORDEPT
                                     WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
           COPY_DEALER AS ( SELECT PUDCNO, DEPT
                                      FROM ( SELECT PUDCNO, VENDORCODE,  DEPTCODE
                                               FROM PUDCHT_COPY
                                              WHERE 1 = 1 ), DEALERORDEPT
                                     WHERE VENDORCODE || DEPTCODE = DEALERORDEPT)
    -- Main Query
    SELECT B.PUDCNO, B.PUDCNOIN,B.TITLE, B.RUNTITLE, B.SUBJECT,
           TO_CHAR(B.STARDATE, 'YYYY/MM/DD') STARDATE, TO_CHAR(B.ENDDATE, 'YYYY/MM/DD') ENDDATE,
           TO_CHAR(B.VAILDSTARDATE, 'YYYY/MM/DD') VAILDSTARDATE, TO_CHAR(B.VAILDENDDATE, 'YYYY/MM/DD') VAILDENDDATE,
           B.DEPTCODE OWN_DEPTCODE, B.KINDCODE, K.KINDNAME,
           B.OUTIN, B.FILEARTICLE, B.FILEPASSWORD, B.FILESUM,
           B.STARDATERUNTITLE, B.ENDDATERUNTITLE, B.LKINDCODE,
           A.ATTACHMENT_NAME,
           B.OWNERID, H.CHI_NAME005 OWNERNAME,
           READONLY,
           TO_CHAR(B.OWNERDATE, 'YYYY') - 1911 || SUBSTR(TO_CHAR(B.OWNERDATE, 'YYYY/MM/DD-HH24:MI'), 5) OWNERDATE,
           CASE
               WHEN READONLY = '0'
                   THEN '已定稿，又取消'
               WHEN READONLY = '1'
                   THEN '已定稿'
               WHEN READONLY = '2'
                   THEN '已定稿，Email寄送中'
               WHEN READONLY = '9'
                   THEN '已作廢'
               WHEN READONLY = 'S'
                   THEN '已定稿，且Email已寄送'
               WHEN READONLY = 'E'
                   THEN '已傳送 : ' || EMAIL_SENT_ERROR
               ELSE ''
           END READONLY2,
           PD.DEPT PUDCRECEIVER_DEALER_DEPT,
           CD.DEPT COPY_DEALER_DEPT
      FROM PUDCHT B,
           PUDCRECEIVER_DEALER PD,
           COPY_DEALER CD,
           PUDCHT_ATTACHMENT A,
           KIND K,
           HRF005@B2B H
     WHERE B.PUDCNO = PD.PUDCNO(+)
       AND B.PUDCNO = CD.PUDCNO(+)
       AND B.PUDCNO = A.PUDCNO(+)
       AND B.KINDCODE = K.KINDCODE(+)
       AND REPLACE(B.OWNERID, 'h', '') = H.NO005(+)
    """
''' LETTER_SQL SELECT END '''

''' LETTER_SQL2 SELECT START '''
# select_公文查詢
select_letter_data_sql2 = """
    WITH DEALERORDEPT AS
               (SELECT CASE
                           WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                               THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                           ELSE TRIM(SUBSTR(DEPT, 21, 10))
                       END AS DEALERORDEPT,
                       CASE
                           WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                               THEN DEPT
                           ELSE SUBSTR(DEPT, 21, 10) || TRIM(SUBSTR(DEPT, 31, 20))
                       END AS DEPT
                  FROM (SELECT
                          RPAD('HSG001', 10, ' ') || RPAD('總公司', 10, ' ') || RPAD(DEPTCODE, 10, ' ') || DEPTNAME AS DEPT
                          FROM (SELECT DEPT_CODE007 AS DEPTCODE, DEPT_DESC_C007 AS DEPTNAME
                                  FROM HRF007@B2B
                                 WHERE REMOVE_DATE007 IS NULL
                                 UNION ALL
                                SELECT DEPTCODE, DEPTNAME
                                  FROM DEPT, HRF005@B2B
                                 WHERE REPLACE(OWNERID, 'h', '') = NO005
                                 ORDER BY DEPTCODE)
                         UNION ALL
                        SELECT DEALER
                          FROM (SELECT RPAD(NVL('', ' '), 10, ' ') || RPAD(NVL('', ' '), 10, ' ') ||
                                       RPAD(AGENT_CODE025, 10, ' ') ||
                                       BRIEF_NAME025 DEALER
                                  FROM OCV025@B2B
                                 WHERE SUB_TYPE025 = '01'
                                   AND EOS_CODE025 = 'Y'
                                   AND RETIRE_DATE025 IS NULL
                                 ORDER BY AGENT_CODE025))),
    
           PUDCRECEIVER_DEALER AS (SELECT PUDCNO, DEPT
                                     FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                             FROM PUDCHT_PUDCRECEIVER
                                            WHERE 1 = 1), DEALERORDEPT
                                    WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
           COPY_DEALER AS (SELECT PUDCNO, DEPT
                             FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                     FROM PUDCHT_COPY
                                    WHERE 1 = 1), DEALERORDEPT
                            WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
           PUDCRECEIVER_DEALERORDEPT AS (SELECT PUDCNO,
                                                CASE
                                                    WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                        THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                    ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                                END AS DEALER_DEPT
                                           FROM PUDCRECEIVER_DEALER
                                          UNION
                                         SELECT PUDCNO,
                                                CASE
                                                    WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                        THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                    ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                                END AS DEALER_DEPT
                                           FROM COPY_DEALER)
    
    SELECT PUDCNO, TITLE, RUNTITLE, SUBJECT, KINDNAME, FILEARTICLE, FILESUM, ATTACHMENT_NAME, USERID, OWNERID, OWNERNAME,
           TODAY, STARDATERUNTITLE, ENDDATERUNTITLE
      FROM (SELECT B.PUDCNO, B.TITLE, B.RUNTITLE, B.SUBJECT, K.KINDNAME,
                   B.FILEARTICLE, B.FILESUM, A.ATTACHMENT_NAME, B.OWNERID,
                   H.CHI_NAME005 OWNERNAME,
                TO_CHAR(SYSDATE, 'YYYYMMDD') TODAY, 
                   TO_CHAR(B.STARDATERUNTITLE, 'YYYYMMDD') STARDATERUNTITLE, 
                   TO_CHAR(B.ENDDATERUNTITLE, 'YYYYMMDD') ENDDATERUNTITLE                  
              FROM PUDCHT B,
                   PUDCHT_ATTACHMENT A,
                   KIND K,
                   HRF005@B2B H,
                   DUAL
             WHERE B.READONLY IS NOT NULL AND B.PUDCNO = A.PUDCNO(+)
               AND B.KINDCODE = K.KINDCODE(+)
               AND REPLACE(B.OWNERID, 'h', '') = H.NO005(+)),
           (SELECT PUDCNO PUDCNO_1, USERID
              FROM (SELECT USERID, DEPTCODE FROM USERS),
                   (SELECT PUDCNO, SUBSTR(DEALER_DEPT, 7, 10) DEALER_DEPT
                      FROM PUDCRECEIVER_DEALERORDEPT
                     WHERE SUBSTR(DEALER_DEPT, 1, 6) = 'HSG001')
             WHERE DEPTCODE = DEALER_DEPT
             UNION ALL
            SELECT PUDCNO, DEALER_DEPT
              FROM PUDCRECEIVER_DEALERORDEPT
             WHERE SUBSTR(DEALER_DEPT, 1, 6) <> 'HSG001')
     WHERE PUDCNO = PUDCNO_1
"""
''' LETTER_SQL2 SELECT END '''

''' LETTER_PUDCNO START '''

@validate_access_token_and_params(None)
def letter_pudcno_method(request, json_data, role, user_id):
    if request.method == "POST":
        # 取得年份
        year = str(datetime.datetime.now().year - 1911)
        return {"results": select_get_serial('********', year, user_id)}, status.HTTP_200_OK


''' LETTER_INSERT_PUDCNO END '''

''' LETTER INSERT START '''

# 映射字典
LETTER_NAME_MAPPING = {
    "PUDCNO": "pudcno",
    "PUDCNOIN": "pudcno2",
    "TITLE": "title",
    "RUNTITLE": "runTitle",
    "SUBJECT": "subject",
    "STARDATE": "announcement_startDate",
    "ENDDATE": "announcement_endDate",
    "VAILDSTARDATE": "validity_startDate",
    "VAILDENDDATE": "validity_endDate",
    "DEPTCODE": "deptCode",
    "KINDPROPERTY": "kindProperty",
    "KINDCODE": "kindCode",
    "OUTIN": "isOutIn",
    "FILEARTICLE": "article",
    "FILESUM": "fileSum",
    "STARDATERUNTITLE": "marquee_startDate",
    "ENDDATERUNTITLE": "marquee_endDate",
    "LKINDCODE": "productsCategoryCode",
    "DESCR": "descr",
    "READONLY": "readonly",
    "HUBTYPE": "hubType",
    "OWNERID": "ownerId",
    "OWNERDATE": "ownerDate",
    "RESPONID": "responId",
    "RESPONDATE": "responDate",
    "FILEPASSWORD": "filePassword",
    "CHECKPASSWORD": "checkPassword",
}


def update_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                for item in data:
                    letter_data = {
                        "PUDCNOIN": item['pudcno2'],
                        "TITLE": item['title'],
                        "RUNTITLE": item['runTitle'],
                        "SUBJECT": item['subject'],
                        "DEPTCODE": item['deptCode'],
                        "KINDCODE": item['kindCode'],
                        "OUTIN": item['isOutIn'],
                        "FILEARTICLE": item['article'],
                        "FILESUM": item['fileSum'],
                        "LKINDCODE": item['productsCategoryCode'],
                        "OWNERID": user_id,
                        "OWNERDATE": '',
                        "FILEPASSWORD": item['filePassword'],
                        "CHECKPASSWORD": item['checkPassword'],
                    }

                    # Extract data from details
                    for detail in item['details']:
                        for key, value in detail.items():
                            if key in LETTER_NAME_MAPPING.values():
                                db_key = [k for k, v in LETTER_NAME_MAPPING.items() if v == key][0]
                                letter_data[db_key] = value

                    # Handle updating PUDCHT
                    update_segments = []
                    params = []
                    for db_key, json_key in LETTER_NAME_MAPPING.items():
                        if json_key == "pudcno":
                            continue
                        value = letter_data.get(db_key)
                        if value is None or value == 'undefined':
                            segment = f"{db_key} = %s"
                            params.append('')
                        else:
                            segment = f"{db_key} = %s"
                            params.append(value)
                        update_segments.append(segment)

                    update_string = ", ".join(update_segments)
                    bpudcht_sql = f"UPDATE PUDCHT SET {update_string} WHERE PUDCNO = %s"
                    params.append(item['pudcno'])
                    # print('bpudcht_sql', bpudcht_sql, 'params', params)
                    cursor.execute(bpudcht_sql, params)

                    #  Handle updating PUDCHT_PUDCRECEIVER
                    pudcreceiver_sql = "DELETE FROM PUDCHT_PUDCRECEIVER WHERE PUDCNO = %s"

                    cursor.execute(pudcreceiver_sql, [item['pudcno']])

                    # Insert into pudcreceiver table
                    for recipient_item in item['recipient']:
                        recipient_data = {
                            "PUDCNO": item['pudcno'],
                            "VENDORCODE": recipient_item['company_code'] if recipient_item['company_code'] != 'HSG001' else 'HSG001',
                            "DEPTCODE": recipient_item['dept_code'] if recipient_item['company_code'] == 'HSG001' else ''
                        }
                        sql = "INSERT INTO PUDCHT_PUDCRECEIVER (PUDCNO, VENDORCODE, DEPTCODE) VALUES (:PUDCNO, :VENDORCODE, :DEPTCODE)"
                        cursor.execute(sql, recipient_data)

                    #  Handle updating PUDCHT_COPY
                    copy_sql = "DELETE FROM PUDCHT_COPY WHERE PUDCNO = %s"

                    cursor.execute(copy_sql, [item['pudcno']])

                    # Insert into copy table
                    for copy_item in item['copy']:
                        copy_data = {
                            "PUDCNO": item['pudcno'],
                            "VENDORCODE": copy_item['company_code'] if copy_item['company_code'] != 'HSG001' else 'HSG001',
                            "DEPTCODE": copy_item['dept_code'] if copy_item['company_code'] == 'HSG001' else ''
                        }
                        sql = "INSERT INTO PUDCHT_COPY (PUDCNO, VENDORCODE, DEPTCODE) VALUES (:PUDCNO, :VENDORCODE, :DEPTCODE)"
                        cursor.execute(sql, copy_data)

                    #  Handle updating PUDCHT_ATTACHMENT
                    attachment_sql = "DELETE FROM PUDCHT_ATTACHMENT WHERE PUDCNO = %s"

                    cursor.execute(attachment_sql, [item['pudcno']])

                    # Insert into attachment table
                    for attachment_item in item['attachment']:
                        att_data = {
                            "PUDCNO": item['pudcno'],
                            "ATTACHMENT_NAME": attachment_item['att']
                        }
                        sql = "INSERT INTO PUDCHT_ATTACHMENT (PUDCNO, ATTACHMENT_NAME) VALUES (:PUDCNO, :ATTACHMENT_NAME)"
                        cursor.execute(sql, att_data)

                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params(None)
def update_letter_method(request, json_data, role, user_id):
    context = "update_letter_method"

    if request.method == "POST":

        try:
            result, result_status = update_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' LETTER INSERT END '''

''' LETTER DELETE START '''

# def delete_raw_sql(context, data):
#     try:
#         with transaction.atomic():
#             with connection.cursor() as cursor:
#                 pudcno = data["pudcno"]
#                 sql = "UPDATE PUDCHT SET READONLY = :qREADONLY WHERE PUDCNO = :qPUDCNO"
#                 # print('sql', sql, 'pudcno', pudcno)
#                 cursor.execute(sql, {'qREADONLY': '9', 'qPUDCNO': pudcno})
#
#                 return "刪除成功", status.HTTP_200_OK
#
#     except IntegrityError as ie:
#         return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
#     except Exception as e:
#         return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
#
# @validate_access_token_and_params(None)
# def delete_letter_method(request, json_data, role, user_id):
#     context = "delete_letter_method"
#
#     if request.method == "POST":
#
#         # 使用原生SQL進行數據插入
#         try:
#             result, result_status = delete_raw_sql(context, json_data)
#             return result, result_status
#         except Exception as e:
#             return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params(None)
def delete_letter_method(request, json_data, role, user_id):
    context = "delete_letter_method"
    if request.method == "POST":

        pudcno = json_data["pudcno"]

        with transaction.atomic():
            with connection.cursor() as cursor:
                try:
                    # 查詢當前最大序號
                    cursor.execute(
                        " SELECT MAX(DATECODE || LPAD(SERIALNO, 5, '0')) "
                        "   FROM PROGRAMSERIAL "
                        "  WHERE PROGRAMCODE = %s AND DATECODE = %s AND SERIALNO = %s",
                        ['********', pudcno[:3], pudcno[3:]])
                    max_serial_no = cursor.fetchone()[0]

                    if max_serial_no is not None and pudcno == max_serial_no:
                        # 如果要刪除的序號是最大值，則直接刪除相關記錄

                        # 公文主檔
                        cursor.execute("DELETE FROM PUDCHT WHERE PUDCNO = %s", [pudcno])

                        # 公文檔案統計
                        cursor.execute("DELETE FROM PUDCHT_FILESTATISTICS WHERE PUDCNO = %s", [pudcno])

                        # 公文收件人
                        cursor.execute("DELETE FROM PUDCHT_PUDCRECEIVER WHERE PUDCNO = %s", [pudcno])

                        # 公文副本
                        cursor.execute("DELETE FROM PUDCHT_COPY WHERE PUDCNO = %s", [pudcno])

                        # 公文附件
                        cursor.execute("DELETE FROM PUDCHT_ATTACHMENT WHERE PUDCNO = %s", [pudcno])

                        # 更新序號
                        if max_serial_no[3:] == '00001':
                            cursor.execute(
                                " DELETE FROM PROGRAMSERIAL WHERE PROGRAMCODE = %s AND DATECODE = %s AND SERIALNO = %s",
                                ['********', pudcno[:3], pudcno[3:]])
                        elif max_serial_no is not None and str(int(pudcno) - 1) < max_serial_no:
                            # 如果要刪除的序號不是最大值，則更新後續序號
                            cursor.execute(
                                " UPDATE PROGRAMSERIAL "
                                "    SET SERIALNO = SERIALNO - 1 "
                                "  WHERE PROGRAMCODE = %s AND DATECODE = %s AND SERIALNO = %s",
                                ['********', pudcno[:3], pudcno[3:]])

                        folder_path = os.path.join(settings.BASE_DIR, 'uploads', '公文作業', pudcno)
                        remove_folder(folder_path)
                    else:
                        return '您的函文號碼不是目前最大值，無法刪除', status.HTTP_404_NOT_FOUND

                    return '刪除成功', status.HTTP_200_OK
                except Exception as e:
                    return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' LETTER DELETE END '''

''' LETTER DELETE2 START '''

def delete_raw_sql2(context, data):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                pudcno = data["pudcno"]

                # 查詢任務ID
                sql = "SELECT TASKID FROM PUDCHT WHERE PUDCNO = :qPUDCNO"
                cursor.execute(sql, {'qPUDCNO': pudcno})
                result = cursor.fetchone()
                task_id = result[0]

                # 如果任務ID不為空，則取消任務
                # print('task_id', task_id)
                if task_id:
                    AsyncResult(task_id).revoke(terminate=True)

                # 更新為已取消狀態，如 READONLY = '0', OWNERDATE = '', TASKID = ''
                sql = ("UPDATE PUDCHT SET READONLY = :qREADONLY, OWNERDATE = :qOWNERDATE, TASKID = :qTASKID "
                       " WHERE PUDCNO = :qPUDCNO")
                # print('sql', sql, 'pudcno', pudcno)
                cursor.execute(sql, {'qREADONLY': '', 'qOWNERDATE': '', 'qTASKID': '', 'qPUDCNO': pudcno})

                return "取消成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params(None)
def delete_letter_method2(request, json_data, role, user_id):
    context = "delete_letter_method2"

    if request.method == "POST":

        # 使用原生SQL進行數據插入
        try:
            result, result_status = delete_raw_sql2(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' LETTER DELETE2 END '''

''' LETTER SELECT START '''

def transform_to_letter_frontend_structure(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["PUDCNO"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "pudcno": group[0]["PUDCNO"],
            "pudcno2": group[0]["PUDCNOIN"],
            "title": group[0]["TITLE"],
            "runTitle": group[0]["RUNTITLE"],
            "subject": group[0]["SUBJECT"],
            "announcement_startDate": group[0]["STARDATE"],
            "announcement_endDate": group[0]["ENDDATE"],
            "vaildStarDate": group[0]["VAILDSTARDATE"],
            "vaildEndDate": group[0]["VAILDENDDATE"],
            "deptCode": group[0]["OWN_DEPTCODE"],
            "kindCode": group[0]["KINDCODE"],
            "kind_name": group[0]["KINDNAME"],
            "isOutIn": group[0]["OUTIN"],
            "file_article": group[0]["FILEARTICLE"],
            "file_password": group[0]["FILEPASSWORD"],
            "file_sum": group[0]["FILESUM"],
            "startDateRunTitle": group[0]["STARDATERUNTITLE"],
            "endDateRunTitle": group[0]["ENDDATERUNTITLE"],
            "productsCategoryCode": group[0]["LKINDCODE"],
            "owner_name": group[0]["OWNERNAME"],
            "owner_date": group[0]["OWNERDATE"],
            "readonly": group[0]["READONLY"],
            "readonly2": group[0]["READONLY2"],
            "PUDCRECEIVER": [],
            "COPY": [],
            "ATTACHMENTS": [],
        }

        # PUDCHT_PUDCRECEIVER
        seen_receiver_dealer_codes = set()
        receiver_added = False
        for _, group_by_dealer in itertools.groupby(group, lambda x: x["PUDCRECEIVER_DEALER_DEPT"]):
            group_by_dealer = list(group_by_dealer)
            receiver_dealer_dept_code = group_by_dealer[0]["PUDCRECEIVER_DEALER_DEPT"]

            # 檢查是否為None，如果是則跳過
            if receiver_dealer_dept_code is None:
                continue

            if group_by_dealer[0]["PUDCRECEIVER_DEALER_DEPT"] not in seen_receiver_dealer_codes:
                total_summary['PUDCRECEIVER'].append(receiver_dealer_dept_code)
                seen_receiver_dealer_codes.add(group_by_dealer[0]["PUDCRECEIVER_DEALER_DEPT"])
                receiver_added = True

        if not receiver_added:
            total_summary['PUDCRECEIVER'] = []

        # PUDCHT_COPY
        seen_copy_dept_code = set()
        copy_added = False
        for _, group_by_dealer in itertools.groupby(group, lambda x: (x["COPY_DEALER_DEPT"])):
            group_by_dealer = list(group_by_dealer)
            dealer_dept_code = group_by_dealer[0]["COPY_DEALER_DEPT"]

            # 檢查是否為None，如果是則跳過
            if dealer_dept_code is None:
                continue

            if group_by_dealer[0]["COPY_DEALER_DEPT"] not in seen_copy_dept_code:
                total_summary['COPY'].append(dealer_dept_code)
                seen_copy_dept_code.add(group_by_dealer[0]["COPY_DEALER_DEPT"])
                copy_added = True

        if not copy_added:
            total_summary['COPY'] = []

        # PUDCHT_ATTACHMENT
        seen_attachment_names = set()
        attachment_add = False
        article_index = 1  # Initializing index at 1

        # Add file article (if not added before)
        file_article = group[0]["FILEARTICLE"]
        if file_article and file_article not in seen_attachment_names:
            total_summary['ATTACHMENTS'].append({
                "index": article_index,  # Adding index
                "attachment_name": file_article
            })
            seen_attachment_names.add(file_article)
            attachment_add = True
            article_index += 1  # Incrementing index

        # Add attachment names
        for _, group_by_attachment in itertools.groupby(group, lambda x: x["ATTACHMENT_NAME"]):
            group_by_attachment = list(group_by_attachment)
            attachment_name = group_by_attachment[0]["ATTACHMENT_NAME"]

            # 檢查是否為None，如果是則跳過
            if attachment_name is None:
                continue

            if attachment_name and attachment_name not in seen_attachment_names:
                total_summary['ATTACHMENTS'].append({
                    "index": article_index,  # Adding index
                    "attachment_name": attachment_name
                })
                seen_attachment_names.add(attachment_name)
                attachment_add = True
                article_index += 1  # Incrementing index

        if not attachment_add:
            total_summary['ATTACHMENTS'] = []

        report.append(total_summary)

    return report


def select_with_raw_sql(context, strCond, condition, condition2, page_number, page_size, start_rnk):

    if len(strCond) == 0:
        sql_query = f" {' AND '.join(strCond)} "
    else:
        sql_query = f" AND {' AND '.join(strCond)} "

    ranked_sql = """
                SELECT PUDCNO, PUDCNOIN, TITLE, RUNTITLE, SUBJECT, STARDATE, ENDDATE,
                       VAILDSTARDATE, VAILDENDDATE, OWN_DEPTCODE, KINDCODE, KINDNAME,
                       OUTIN, FILEARTICLE, FILEPASSWORD, FILESUM,
                       STARDATERUNTITLE, ENDDATERUNTITLE, LKINDCODE,
                       PUDCRECEIVER_DEALER_DEPT, COPY_DEALER_DEPT, ATTACHMENT_NAME,
                       OWNERID, OWNERNAME, OWNERDATE, READONLY, READONLY2, 
                       DENSE_RANK() OVER (ORDER BY PUDCNO DESC) AS RNK
                  FROM (  """ + select_letter_data_sql + """  )
                 WHERE 1 = 1 """ + sql_query + """
                 ORDER BY PUDCNO DESC, PUDCRECEIVER_DEALER_DEPT, COPY_DEALER_DEPT, ATTACHMENT_NAME
            """

    ranked_sql1 = """
                SELECT PUDCNO, PUDCNOIN, TITLE, RUNTITLE, SUBJECT, STARDATE, ENDDATE,
                       VAILDSTARDATE, VAILDENDDATE, OWN_DEPTCODE, KINDCODE, KINDNAME, 
                       OUTIN, FILEARTICLE, FILEPASSWORD, FILESUM,
                       STARDATERUNTITLE, ENDDATERUNTITLE, LKINDCODE,
                       PUDCRECEIVER_DEALER_DEPT, COPY_DEALER_DEPT, ATTACHMENT_NAME,
                       OWNERID, OWNERNAME, OWNERDATE, READONLY, READONLY2, RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """

    try:
        with connection.cursor() as cursor:
            # print('ranked_sql1', ranked_sql1, 'condition', condition)
            cursor.execute(ranked_sql1, condition)
            data = cursor.fetchall()

            if data is None:
                return "找不到資料", status.HTTP_404_NOT_FOUND

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["PUDCNO"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, condition2)  # Assuming condition works for this query
                total_data = cursor2.fetchall()

                if not total_data or total_data[0][0] is None:
                    return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_letter_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

def add_condition(field, sql_conditions, params, params2, json_data):
    if field in json_data and json_data[field] is not None:
        placeholder = f":{field}"
        sql_conditions.append(f" {field} LIKE {placeholder} ")
        like_value = f"%{json_data[field]}%"
        params[field] = like_value
        params2[field] = like_value
    return sql_conditions, params, params2

@validate_access_token_and_params_with_pagination(None)
def select_letter_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_letter_method"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 查資料庫尋找使用者代號或部門代號
        data = get_user_permissions('G001_1', user_id)

        # 當前日期
        now = datetime.datetime.now()

        dept_dealer_code = data[0]['CODE']
        can_special = data[0]['CAN_SPECIAL']

        # 前十天的日期
        start_date = now - datetime.timedelta(days=10)

        # 當月最後一天的日期
        _, last_day = calendar.monthrange(now.year, now.month)
        end_date = datetime.datetime(now.year, now.month, last_day)

        # 定義可能的參數列表
        params_mapping = {
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 若沒有特殊權限，則只能查詢自己的資料
        if can_special == '0':
            sql_conditions.append(f" OWNERID = :OWNERID ")
            params['OWNERID'] = user_id
            params2['OWNERID'] = user_id

        # 函文, 標題, 主旨
        for field in ['pudcno', 'title', 'subject']:
            strCond, condition, condition2 = add_condition(field, sql_conditions, params, params2, json_data)

        try:
            result = select_with_raw_sql(context, strCond, condition, condition2, page_number, page_size, start_rnk)
            # print(result)
            return result
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' LETTER SELECT EMD '''

''' LETTER SELECT2 START '''

def transform_to_letter_frontend_structure2(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["PUDCNO"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "pudcno": group[0]["PUDCNO"],
            "title": group[0]["TITLE"],
            "kind_name": group[0]["KINDNAME"],
            "file_article": group[0]["FILEARTICLE"],
            "file_sum": group[0]["FILESUM"],
            "owner_name": group[0]["OWNERNAME"],
            "ATTACHMENTS": [],
        }

        # ATTACHMENTS
        seen_attachment_names = set()
        attachment_add = False
        article_index = 1  # Initializing index at 1

        # Add file article (if not added before)
        file_article = group[0]["FILEARTICLE"]
        if file_article and file_article not in seen_attachment_names:
            total_summary['ATTACHMENTS'].append({
                "index": article_index,  # Adding index
                "attachment_name": file_article
            })
            seen_attachment_names.add(file_article)
            attachment_add = True
            article_index += 1  # Incrementing index

        # Add attachment names
        for _, group_by_attachment in itertools.groupby(group, lambda x: x["ATTACHMENT_NAME"]):
            group_by_attachment = list(group_by_attachment)
            attachment_name = group_by_attachment[0]["ATTACHMENT_NAME"]

            # 檢查是否為None，如果是則跳過
            if attachment_name is None:
                continue

            if attachment_name and attachment_name not in seen_attachment_names:
                total_summary['ATTACHMENTS'].append({
                    "index": article_index,  # Adding index
                    "attachment_name": attachment_name
                })
                seen_attachment_names.add(attachment_name)
                attachment_add = True
                article_index += 1  # Incrementing index

        if not attachment_add:
            total_summary['ATTACHMENTS'] = []

        report.append(total_summary)

    return report


def select_with_raw_sql2(context, json_data, strCond, condition, condition2, page_number, page_size, start_rnk):

    if len(strCond) == 0:
        sql_query = f" {' AND '.join(strCond)} "
    else:
        sql_query = f" AND {' AND '.join(strCond)} "

    if 'checkUnread' in json_data and json_data['checkUnread'] is not None and json_data['checkUnread'] == 1:
        sql_base = """
            SELECT PUDCNO, TITLE, RUNTITLE, SUBJECT, KINDNAME, FILEARTICLE, FILESUM, ATTACHMENT_NAME, USERID, OWNERID, OWNERNAME,
                   TODAY, STARDATERUNTITLE, ENDDATERUNTITLE
              FROM ( """ + select_letter_data_sql2 + """  ),
                   (SELECT PARENTID, CHILDID, SOURCESERNO, USERID USERID_1, ACTIONTYPE FROM USERLOG WHERE USERID = :USERID)
             WHERE PUDCNO = SOURCESERNO(+) AND ACTIONTYPE IS NULL      
        """
    else:
        sql_base = select_letter_data_sql2

    ranked_sql = """
                SELECT PUDCNO, TITLE, KINDNAME, FILEARTICLE, FILESUM, ATTACHMENT_NAME, OWNERNAME,
                       DENSE_RANK() OVER (ORDER BY PUDCNO DESC) AS RNK
                  FROM (  """ + sql_base + """  )
                 WHERE 1 = 1 """ + sql_query + """
                 ORDER BY PUDCNO DESC, ATTACHMENT_NAME
            """

    ranked_sql1 = """
                SELECT PUDCNO, TITLE, KINDNAME, FILEARTICLE, FILESUM, ATTACHMENT_NAME, OWNERNAME, RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """

    try:
        with connection.cursor() as cursor:
            # print('ranked_sql1', ranked_sql1, 'condition', condition)
            cursor.execute(ranked_sql1, condition)
            data = cursor.fetchall()

            if data is None:
                return "找不到資料", status.HTTP_404_NOT_FOUND

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["PUDCNO"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, condition2)  # Assuming condition works for this query
                total_data = cursor2.fetchall()

                if not total_data or total_data[0][0] is None:
                    return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]


            report = transform_to_letter_frontend_structure2(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

def add_condition2(field, sql_conditions, params, params2, json_data):
    if field in json_data and json_data[field] is not None:
        placeholder = f":{field}"
        sql_conditions.append(f" {field} LIKE {placeholder} ")
        like_value = f"%{json_data[field]}%"
        params[field] = like_value
        params2[field] = like_value
    return sql_conditions, params, params2

@validate_access_token_and_params_with_pagination(None)
def select_letter_method2(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_letter_method2"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 查資料庫尋找使用者代號或部門代號
        data = get_user_permissions('G002_1', user_id)

        dept_dealer_code = data[0]['CODE']
        can_special = data[0]['CAN_SPECIAL']

        # 當前日期
        now = datetime.datetime.now()

        # 前十天的日期
        start_date = now - datetime.timedelta(days=10)

        # 當月最後一天的日期
        _, last_day = calendar.monthrange(now.year, now.month)
        end_date = datetime.datetime(now.year, now.month, last_day)

        # 定義可能的參數列表
        params_mapping = {
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 若沒有特殊權限，則只能查詢自己的資料
        if can_special == '0':
            sql_conditions.append(f" USERID = :USERID ")
            params['USERID'] = user_id
            params2['USERID'] = user_id

        if 'checkUnread' in json_data and json_data['checkUnread'] is not None and json_data['checkUnread'] == 1:
            params['USERID'] = user_id
            params2['USERID'] = user_id

        if 'marquee' in json_data and json_data['marquee'] is not None and json_data['marquee'] == 'Y':
            sql_conditions.append(f" STARDATERUNTITLE <= :TODAY AND ENDDATERUNTITLE >= :TODAY ")
            params['TODAY'] = now.strftime('%Y%m%d')
            params2['TODAY'] = now.strftime('%Y%m%d')

        # 函文, 標題, 主旨
        for field in ['pudcno', 'title', 'subject']:
            strCond, condition, condition2 = add_condition2(field, sql_conditions, params, params2, json_data)

        try:
            result = select_with_raw_sql2(context, json_data, strCond, condition, condition2, page_number, page_size, start_rnk)
            return result
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' LETTER SELECT2 EMD '''
