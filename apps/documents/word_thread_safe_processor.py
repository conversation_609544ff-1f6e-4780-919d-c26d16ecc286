# -*- coding: utf-8 -*-
"""
線程安全的 Word 文檔處理器
確保所有 Word 操作在同一線程中執行，避免 COM 線程模型錯誤
"""

import os
import time
import logging
import tempfile
import shutil
import uuid
import queue
import threading
import win32com.client
import pythoncom
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class ThreadSafeWordProcessor:
    """線程安全的 Word 處理器 - 使用單一線程處理所有 Word 操作"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.task_queue = queue.Queue()
        self.result_dict = {}
        self.worker_thread = None
        self.running = False
        self.word_app = None
        
        # 啟動工作線程
        self._start_worker()
    
    def _start_worker(self):
        """啟動工作線程"""
        if not self.running:
            self.running = True
            self.worker_thread = threading.Thread(
                target=self._worker_loop,
                daemon=True,
                name="WordProcessorThread"
            )
            self.worker_thread.start()
            logger.info("Word 處理器工作線程已啟動")
    
    def _worker_loop(self):
        """工作線程主循環"""
        # 在工作線程中初始化 COM
        pythoncom.CoInitialize()
        
        try:
            while self.running:
                try:
                    # 從隊列獲取任務（超時 1 秒）
                    task = self.task_queue.get(timeout=1)
                    
                    if task is None:  # 停止信號
                        break
                    
                    task_id, func, args, kwargs = task
                    
                    try:
                        # 確保 Word 應用程序存在
                        if not self._ensure_word_app():
                            raise Exception("無法創建 Word 應用程序")
                        
                        # 執行任務
                        kwargs['word_app'] = self.word_app
                        result = func(*args, **kwargs)
                        
                        # 保存結果
                        self.result_dict[task_id] = {
                            'success': True,
                            'result': result
                        }
                        
                    except Exception as e:
                        logger.error(f"任務執行失敗: {str(e)}")
                        self.result_dict[task_id] = {
                            'success': False,
                            'error': str(e)
                        }
                        # 重置 Word 應用程序
                        self._reset_word_app()
                    
                    self.task_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    logger.error(f"工作線程錯誤: {str(e)}")
                    time.sleep(1)
        
        finally:
            # 清理
            self._cleanup_word_app()
            pythoncom.CoUninitialize()
            logger.info("Word 處理器工作線程已停止")
    
    def _ensure_word_app(self):
        """確保 Word 應用程序可用"""
        if self.word_app is None:
            try:
                self.word_app = win32com.client.DispatchEx("Word.Application")
                self.word_app.Visible = False
                self.word_app.DisplayAlerts = 0  # wdAlertsNone
                
                # 設置選項
                try:
                    self.word_app.Options.DoNotPromptForConvert = True
                    self.word_app.Options.ConfirmConversions = False
                    self.word_app.Options.UpdateLinksAtOpen = False
                    self.word_app.Options.UpdateFieldsAtPrint = False
                except:
                    pass
                
                logger.info("Word 應用程序已創建")
                return True
            except Exception as e:
                logger.error(f"創建 Word 應用程序失敗: {str(e)}")
                self.word_app = None
                return False
        
        # 檢查 Word 應用程序是否仍然有效
        try:
            _ = self.word_app.Name  # 測試訪問
            return True
        except:
            logger.warning("Word 應用程序無響應，重新創建")
            self._reset_word_app()
            return self._ensure_word_app()
    
    def _reset_word_app(self):
        """重置 Word 應用程序"""
        self._cleanup_word_app()
        self.word_app = None
    
    def _cleanup_word_app(self):
        """清理 Word 應用程序"""
        if self.word_app:
            try:
                # 關閉所有文檔
                while self.word_app.Documents.Count > 0:
                    try:
                        self.word_app.Documents(1).Close(SaveChanges=False)
                    except:
                        break
                # 退出 Word
                self.word_app.Quit()
            except:
                pass
            finally:
                self.word_app = None
                logger.info("Word 應用程序已清理")
    
    def process_document(self, file_path, file_name, pudcno, user_id, 
                        file_password, modify_func):
        """
        處理文檔 - 線程安全版本
        """
        task_id = str(uuid.uuid4())
        
        # 定義包裝函數
        def wrapper_func(file_path, file_name, pudcno, user_id, 
                        file_password, modify_func, word_app=None):
            if not word_app:
                raise Exception("未提供 Word 應用程序")
            
            if file_password:
                return self._process_protected_document(
                    word_app, file_path, file_name, pudcno, 
                    user_id, file_password, modify_func
                )
            else:
                return modify_func(
                    user_id, pudcno, file_path, file_name, word_app
                )
        
        # 提交任務
        self.task_queue.put((
            task_id, wrapper_func, 
            (file_path, file_name, pudcno, user_id, file_password, modify_func),
            {}
        ))
        
        # 等待結果（最多 120 秒）
        timeout = 120
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if task_id in self.result_dict:
                result_info = self.result_dict.pop(task_id)
                if result_info['success']:
                    return result_info['result']
                else:
                    raise Exception(result_info['error'])
            time.sleep(0.1)
        
        raise Exception(f"任務超時: {task_id}")
    
    def _process_protected_document(self, word_app, file_path, file_name, pudcno, 
                                   user_id, file_password, modify_func):
        """
        處理受保護的文檔（在工作線程中執行）
        """
        temp_dir = tempfile.mkdtemp()
        doc = None
        
        try:
            # 打開受密碼保護的文件
            logger.info(f"打開受保護的文檔: {file_path}")
            doc = word_app.Documents.Open(
                FileName=file_path,
                PasswordDocument=file_password,
                ReadOnly=False,
                AddToRecentFiles=False,
                Visible=False
            )
            
            # 檢查並解除保護
            protection_type = -1
            try:
                protection_type = doc.ProtectionType
                logger.info(f"文檔保護類型: {protection_type}")
            except:
                pass
            
            if protection_type != -1:
                try:
                    doc.Unprotect(Password=file_password)
                    logger.info("已解除文檔保護")
                except Exception as e:
                    logger.warning(f"解除保護失敗: {str(e)}")
            
            # 保存未保護的版本
            temp_unprotected = os.path.join(temp_dir, f"temp_{uuid.uuid4()}.docx")
            doc.SaveAs2(FileName=temp_unprotected, FileFormat=16)
            doc.Close(SaveChanges=False)
            doc = None
            
            # 修改文檔
            logger.info("開始修改文檔內容")
            new_path, new_name = modify_func(
                user_id, pudcno, temp_unprotected, file_name, word_app
            )
            
            if new_path and os.path.exists(new_path):
                # 重新保護文檔
                logger.info("重新保護文檔")
                doc = word_app.Documents.Open(new_path)
                
                if protection_type != -1:
                    try:
                        doc.Protect(Type=protection_type, Password=file_password)
                        logger.info("已重新保護文檔")
                    except Exception as e:
                        logger.warning(f"保護文檔失敗: {str(e)}")
                
                # 保存最終版本
                final_path = os.path.join(temp_dir, f"final_{uuid.uuid4()}.docx")
                doc.SaveAs2(FileName=final_path, FileFormat=16)
                doc.Close(SaveChanges=False)
                doc = None
                
                # 移動到目標位置
                dest_path = os.path.join(os.path.dirname(file_path), new_name)
                shutil.move(final_path, dest_path)
                
                logger.info(f"文檔處理成功: {dest_path}")
                return dest_path, new_name
            else:
                logger.error("修改文檔失敗")
                return None, None
                
        finally:
            # 清理
            if doc:
                try:
                    doc.Close(SaveChanges=False)
                except:
                    pass
            
            # 清理臨時文件
            if os.path.exists(temp_dir):
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
    
    def stop(self):
        """停止處理器"""
        if self.running:
            self.running = False
            self.task_queue.put(None)  # 停止信號
            if self.worker_thread:
                self.worker_thread.join(timeout=5)
            logger.info("Word 處理器已停止")

# 全局處理器實例
_processor = None

def get_thread_safe_processor():
    """獲取線程安全的處理器實例"""
    global _processor
    if _processor is None:
        _processor = ThreadSafeWordProcessor()
    return _processor

def process_document_thread_safe(file_path, file_name, pudcno, user_id, 
                                file_password, modify_func):
    """
    使用線程安全的處理器處理文檔
    """
    processor = get_thread_safe_processor()
    return processor.process_document(
        file_path, file_name, pudcno, user_id, file_password, modify_func
    )