# -*- coding: UTF8 -*-
import itertools

import cx_<PERSON>

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data
from utils.token_utils import validate_access_token_and_params_with_pagination, \
    get_pagination_params

''' PRE_SHIPMENT_ORDER SELECT START '''
# select_發貨單資料資料
def select_pre_shipment_order_data_sql(sql_query1, sql_query2):

    return """
      WITH M_DATA AS (SELECT A.OCF210_SEQ210, A.DELIVERY_NO210,
                             TO_CHAR(A.DUE_DATE210, 'YYYY/MM/DD') DUE_DATE210, A.AGENT_CODE210, A.AGENT_BRIEF_NAME210, 
                             A.PRDC210, A.PRDC_COMP_NAME210, A.DELC210, <PERSON><PERSON><PERSON><PERSON>_COMP_NAME210,
                             A.TRACK_INVOICE210, NVL(<PERSON><PERSON>AMT210, 0) AMT210, NVL(<PERSON><PERSON>GDISC211, 0) GDISC211,
                             NVL(B.DISC211, 0) DISC211,
                             NVL(NVL(B.DISC211, 0) + NVL(B.GDISC211, 0), 0) DISC211_GDISC211,
                             TO_CHAR(A.EX_DATE210, 'YYYY/MM/DD') EX_DATE210, TO_CHAR(A.DELIVERY_DATE210, 'YYYY/MM/DD') DELIVERY_DATE210, 
                             C.COUNTY_CODE001, D.CITY_ID025,
                             NVL(A.TAX210, 0) TAX210, A.ORDER_MARK_NAME210, A.PLAC_ID210, A.DELV_ID210
                        FROM OCV210@B2B A,
                             (SELECT OCF210_SEQ211, SUM(NVL(GDISC211, 0)) GDISC211, SUM(NVL(DISC211, 0)) DISC211
                                FROM OCF211@B2B
                               GROUP BY OCF210_SEQ211) B,
                             HRF001@B2B C,
                             OCV025@B2B D
                       WHERE A.OCF210_SEQ210 = B.OCF210_SEQ211
                         AND A.PRDC210 = C.COMP_CODE001
                         AND A.AGENT_OCF025_SEQ210 = D.OCF025_SEQ025 """ + sql_query1 + """
                         AND A.DEL_STATUS210 IN ('1', '3') -- 發貨單狀態為'1 OPEN,3 Close'
                       ORDER BY A.DUE_DATE210, A.DELIVERY_NO210),
           D_DATA AS (SELECT A.OCF210_SEQ211, A.OCF211_SEQ211, 
                             TO_CHAR(E.ORDER_DATE200, 'YYYY/MM/DD') ORDER_DATE200, A.PRODUCT_ID211, 
                             E.ORDER_NO200, A.DQTY211, B.UNITC015, A.BQTY211, B.UNITB015,
                             A.PRICE211, A.AMT211, A.DISC211, B.LKIND_ID015, F.PRINTER_SEQ003, B.BRAND_ID015, B.SMELL_ID015,
                             B.KIND_ID015, B.PACK_ID015, B.FULL_DESC015, A.PRODUCT_MARK211, A.GDISC211
                        FROM OCV211@B2B A, OCV015@B2B B, OCF202@B2B C,
                             OCF201@B2B D, OCF200@B2B E, OCF003@B2B F
                       WHERE A.OCF015_SEQ211 = B.OCF015_SEQ015
                         AND A.OCF211_SEQ211 = C.OCF211_SEQ202
                         AND C.OCF201_SEQ202 = D.OCF201_SEQ201
                         AND D.OCF200_SEQ201 = E.OCF200_SEQ200
                         AND B.LKIND_ID_OCF003_SEQ015 = F.OCF003_SEQ003
                         AND E.ORDER_DATE200 BETWEEN TO_DATE(:qBEGIN_DATE, 'YYYYMMDD') AND TO_DATE(:qEND_DATE, 'YYYYMMDD') """ + sql_query2 + """
                       ORDER BY A.PRODUCT_ID211, E.ORDER_NO200)
    """

''' PRE_SHIPMENT_ORDER SELECT END '''

''' PRE_SHIPMENT_ORDER SELECT START '''

def transform_to_pre_shipment_order_frontend_structure(data, start_index=0):
    # 按照發貨單號分組
    grouped_data = itertools.groupby(data, key=lambda x: (x["DELIVERY_NO210"]))

    # 每個分組計算進貨箱數、進貨總金額、進貨總重量
    summary_report = []
    for _, group in grouped_data:
        group_list = sorted(list(group), key=lambda x: (x["DELIVERY_NO210"], x["PRODUCT_ID211"]))  # 根據日期排序明細

        details = []
        for item in group_list:
            details.append({
                "index": start_index + len(details) + 1,
                "delivery_number": item["DELIVERY_NO210"],
                "order_date": group_list[0]["ORDER_DATE200"], # 訂單日期
                # "ocf210_seq211": item["OCF210_SEQ211"],
                # "ocf211_seq211": item["OCF211_SEQ211"],
                "product_id": item["PRODUCT_ID211"],
                "order_no": item["ORDER_NO200"],
                "dqty": item["DQTY211"],
                "unitc": item["UNITC015"],
                "bqty": item["BQTY211"],
                "unitb": item["UNITB015"],
                "price": item["PRICE211"],
                "amt211": item["AMT211"],
                "lkind_id": item["LKIND_ID015"],
                "printer_seq": item["PRINTER_SEQ003"],
                "brand_id": item["BRAND_ID015"],
                "smell_id": item["SMELL_ID015"],
                "kind_id": item["KIND_ID015"],
                "pack_id": item["PACK_ID015"],
                "full_desc": item["FULL_DESC015"],
                "product_mark": item["PRODUCT_MARK211"],
            })

        # 添加到報告列表
        summary_report.append({
            "index": start_index + len(summary_report) + 1,
            "shipment_number": group_list[0]["DELIVERY_NO210"], # 發貨單號
            "scheduled_shipment_date": group_list[0]["DUE_DATE210"], # 預計出貨日期
            "invoice_number": group_list[0]["TRACK_INVOICE210"], # 發票號碼
            "actual_shipment_date": group_list[0]["DELIVERY_DATE210"], # 實際出貨日期
            "distributor_code": group_list[0]["AGENT_CODE210"], # 經銷商代碼
            "distributor_name": group_list[0]["AGENT_BRIEF_NAME210"], # 經銷商名稱
            "update_date": group_list[0]["EX_DATE210"], # 更新日期
            "allowance_total": group_list[0]["DISC211_GDISC211"], # 折讓總額
            "comprehensive_allowance": group_list[0]["GDISC211"], # 綜合折讓
            "tax_exclusive_total_amount": sum(item["AMT211"] for item in group_list), # 稅前總金額
            "sales_tax": group_list[0]["TAX210"], # 營業稅
            "total_receivable_amount": sum(item["AMT211"] for item in group_list) + group_list[0]["TAX210"] - group_list[0]["DISC211_GDISC211"], # 應收總額
            "total_quantity": sum(item["BQTY211"] for item in group_list), # 總數量
            "production_factory_code": group_list[0]["PRDC210"], # 生產工廠代碼
            "production_factory_name": group_list[0]["PRDC_COMP_NAME210"], # 生產工廠名稱
            "delivery_factory_code": group_list[0]["DELC210"], # 出貨工廠代碼
            "delivery_factory_name": group_list[0]["DELC_COMP_NAME210"], # 出貨工廠名稱
            "delivery_status": group_list[0]["DELIVERY_STATUS210"], # 出貨狀態
            "details": details
        })

    return summary_report

def select_with_raw_sql(context, sql_conditions, sql_conditions2, params, params2, page_number, page_size, start_rnk):

    if len(sql_conditions) == 0:
        sql_query1 = f" {' AND '.join(sql_conditions)} "
    else:
        sql_query1 = f" AND {' AND '.join(sql_conditions)} "

    if len(sql_conditions2) == 0:
        sql_query2 = f" {' AND '.join(sql_conditions2)} "
    else:
        sql_query2 = f" AND {' AND '.join(sql_conditions2)} "

    ranked_sql = """
                SELECT DELIVERY_NO210, DUE_DATE210, AGENT_CODE210, AGENT_BRIEF_NAME210, PRDC210, PRDC_COMP_NAME210, DELC210, DELC_COMP_NAME210,
                       TRACK_INVOICE210, AMT210, GDISC211, DISC211_GDISC211, DISC211, EX_DATE210, DELIVERY_DATE210,
                       COUNTY_CODE001, CITY_ID025, TAX210, ORDER_MARK_NAME210, PLAC_ID210, DELV_ID210, OCF210_SEQ211, OCF211_SEQ211,
                       ORDER_DATE200, PRODUCT_ID211, ORDER_NO200, DQTY211, UNITC015, BQTY211, UNITB015, PRICE211, AMT211, LKIND_ID015,
                       PRINTER_SEQ003, BRAND_ID015, SMELL_ID015, KIND_ID015, PACK_ID015, FULL_DESC015, PRODUCT_MARK211, DELIVERY_STATUS210, 
                       DENSE_RANK() OVER (ORDER BY DELIVERY_NO210 DESC) AS RNK
                  FROM (SELECT DELIVERY_NO210, DUE_DATE210, AGENT_CODE210, AGENT_BRIEF_NAME210, PRDC210, PRDC_COMP_NAME210, DELC210, DELC_COMP_NAME210,
                               TRACK_INVOICE210, AMT210, M_DATA.GDISC211,
                               DISC211_GDISC211, M_DATA.DISC211, EX_DATE210, DELIVERY_DATE210, COUNTY_CODE001, CITY_ID025, TAX210,
                               ORDER_MARK_NAME210, PLAC_ID210, DELV_ID210, OCF210_SEQ211, OCF211_SEQ211, ORDER_DATE200, 
                               PRODUCT_ID211, ORDER_NO200, DQTY211,
                               UNITC015, BQTY211, UNITB015, PRICE211, AMT211, LKIND_ID015, PRINTER_SEQ003, BRAND_ID015,
                               SMELL_ID015, KIND_ID015, PACK_ID015, FULL_DESC015, PRODUCT_MARK211,
                               CASE
                                   WHEN TRIM(TRACK_INVOICE210) IS NULL
                                       THEN '未出貨'
                                   ELSE '已出貨'
                               END DELIVERY_STATUS210
                          FROM M_DATA, D_DATA
                         WHERE M_DATA.OCF210_SEQ210 = D_DATA.OCF210_SEQ211
                         ORDER BY M_DATA.DUE_DATE210, M_DATA.DELIVERY_NO210, D_DATA.PRODUCT_ID211, D_DATA.ORDER_NO200)
                 WHERE 1 = 1
            """

    ranked_sql1 = """
                SELECT DELIVERY_NO210, DUE_DATE210, AGENT_CODE210, AGENT_BRIEF_NAME210, PRDC210, PRDC_COMP_NAME210, DELC210, DELC_COMP_NAME210,
                       TRACK_INVOICE210, AMT210, GDISC211, DISC211_GDISC211, DISC211, EX_DATE210, DELIVERY_DATE210,
                       COUNTY_CODE001, CITY_ID025, TAX210, ORDER_MARK_NAME210, PLAC_ID210, DELV_ID210, OCF210_SEQ211, OCF211_SEQ211,
                       ORDER_DATE200, PRODUCT_ID211, ORDER_NO200, DQTY211, UNITC015, BQTY211, UNITB015, PRICE211, AMT211, LKIND_ID015,
                       PRINTER_SEQ003, BRAND_ID015, SMELL_ID015, KIND_ID015, PACK_ID015, FULL_DESC015, PRODUCT_MARK211, DELIVERY_STATUS210, 
                       RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = select_pre_shipment_order_data_sql(sql_query1, sql_query2) + """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """

    try:
        with connection.cursor() as cursor:
            # print('000', select_pre_shipment_order_data_sql(sql_query1, sql_query2) + ranked_sql1)
            cursor.execute(select_pre_shipment_order_data_sql(sql_query1, sql_query2) + ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE210"], x["PRODUCT_ID211"]))]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_pre_shipment_order_frontend_structure(page_data, start_rnk - 1)
            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params_with_pagination(None)
def select_pre_shipment_order_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_pre_shipment_order_method"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        sql_conditions2 = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping  = {
            "dealers_code": "A.AGENT_CODE210",
            "order_number": "A.DELIVERY_NO210",
        }

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append("A.AGENT_CODE210 = :qAGENT_CODE")
            params['qAGENT_CODE'] = user_id
            params2['qAGENT_CODE'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍起
        if 'begin_date' in json_data and json_data['begin_date'] is not None:
            # 本月
            params['qBEGIN_DATE'] = json_data['begin_date']
            params2['qBEGIN_DATE'] = json_data['begin_date']

        # 處理日期範圍迄
        if 'end_date' in json_data and json_data['end_date'] is not None:
            # 本月
            params['qEND_DATE'] = json_data['end_date']
            params2['qEND_DATE'] = json_data['end_date']

        try:
            result, result_status  = select_with_raw_sql(context, sql_conditions, sql_conditions2, params, params2, page_number, page_size, start_rnk)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PRE_SHIPMENT_ORDER SELECT END '''