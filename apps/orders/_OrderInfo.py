import datetime

import cx_<PERSON>

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.holiday_utils import calculate_delivery_date
from utils.main_utils import calculate_date
from utils.token_utils import validate_access_token_and_params, validate_access_token_and_params_with_pagination

''' OrderMaster Start  '''
ocv029_main_order_master_sql = """
    SELECT AGENT_CODE026, SHORT_NAME025, SALE_TYPE026, SALE_TYPE_NAME026, C.DELV_ADDR029
      FROM (SELECT DISTINCT A.OCF025_SEQ025, B.AGENT_CODE026, A.SHORT_NAME025, B.SALE_TYPE026, SALE_TYPE_NAME026
              FROM OCF025@B2B A, OCV026@B2B B
             WHERE A.OCF025_SEQ025 = B.OCF025_SEQ026
               AND NVL(A.RETIRE_DATE025, TRUNC(SYSDATE)) >= TRUNC(SYSDATE)), OCV029@B2B C
     WHERE OCF025_SEQ025 = C.OCF025_SEQ029(+)
    """
''' Product End '''

''' CanOdderProduct START '''
cv035_can_order_products_code_name_sql = """
    SELECT A.PRODUCT_ID035, C.SDESCR015
      FROM OCV035@B2B A, OCF025@B2B B, OCF015@B2B C, OCF003@B2B D, OCF003@B2B E
     WHERE C.PLACE_ID_OCF003_SEQ015 = D.OCF003_SEQ003
       AND A.AGENT_OCF025_SEQ035 = B.OCF025_SEQ025
       AND A.OCF015_SEQ035 = C.OCF015_SEQ015
       AND NVL(B.RETIRE_DATE025, TRUNC(SYSDATE)) >= TRUNC(SYSDATE)
       AND B.SUB_TYPE025 = '01'
       AND NVL(A.DELETE_FLAG035, 'N') = 'N'
       AND NOT EXISTS (SELECT 1
                         FROM OCF036@B2B C --20160519 added by cyf過濾不上傳關貿之經銷商+產品
                        WHERE A.AGENT_OCF025_SEQ035 = C.OCF025_SEQ036
                          AND A.OCF015_SEQ035 = C.OCF015_SEQ036)
       AND A.BEGIN_DATE035 = (SELECT MAX(C.BEGIN_DATE035)
                                FROM OCF035@B2B C
                               WHERE C.AGENT_OCF025_SEQ035 = A.AGENT_OCF025_SEQ035
                                 AND C.OCF015_SEQ035 = A.OCF015_SEQ035
                                 AND NVL(C.DELETE_FLAG035, 'N') = 'N')
       -- 排除沒有設定產品單位                          
       AND EXISTS (SELECT 1 FROM PRODSALESUNIT WHERE PRODCODE = C.PRODUCT_ID015)
       AND C.DEL_TYPE_OCF003_SEQ015 = E.OCF003_SEQ003
"""
''' CanOdderProduct END '''

''' OrderProductSalesUnit START '''
ocv015_order_product_sales_unit_sql = """
    SELECT PRODUCT_ID015, UNITB_NAME015, UNITB015, SALESUNIT, QTYSPUNITUNIT
      FROM (SELECT PRODCODE, SALESUNIT, QTYSPUNITUNIT, SPUNIT, QTYBOARDUNIT FROM PRODSALESUNIT),
           (SELECT PRODUCT_ID015, SDESCR015, DEL_TYPE015, UNITB_NAME015, UNITB015, OUTYM015 FROM OCV015@B2B)
     WHERE PRODCODE = PRODUCT_ID015 AND SPUNIT = UNITB015 AND OUTYM015 IS NULL
"""
''' OrderProductSalesUnit END '''

# 經銷商訂購主檔 START
@validate_access_token_and_params('dealers_code')
def select_erp_hy_ocv029_main_order_master(request, json_data, role, user_id):
    context = "select_erp_hy_ocv029_main_order_master"

    if request.method == "POST":

        today, delivery_date = calculate_delivery_date()

        # SQL 查詢基本結構
        sql_base = ocv029_main_order_master_sql
        sql_conditions = []
        params = {}
        sql_query = ""

        # 預設參數與資料庫欄位的映射
        params_mapping = {
            "dealers_code": "AGENT_CODE026",
        }

        sql_conditions.append(f" AGENT_CODE026 = :dealers_code ")

        if role == '0':
            params['dealers_code'] = user_id
        else:
            params['dealers_code'] = json_data['dealers_code']

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        # 構建完整的 SQL 查詢
        if sql_conditions:
            sql_query = (f" {sql_base} AND {' AND '.join(sql_conditions)} "
                         f" ORDER BY SALE_TYPE026")

        # 連線資料庫
        try:
            with connection.cursor() as cursor:
                # print(sql_query, params)
                cursor.execute(sql_query, params)
                all_data = cursor.fetchall()
                if all_data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    col_names = [desc[0] for desc in cursor.description]

                    # 將每一行資料轉換為字典
                    data_list = [dict(zip(col_names, row)) for row in all_data]
                    data_list.sort(key=lambda x: x['SALE_TYPE026'])
                    # print(data_list)
                    # 使用一個dictionary來保存最終的結果
                    result = generate_main_order_master(data_list, today, delivery_date)
                    # print(result)

                    return {"results": result}, status.HTTP_200_OK

        except cx_Oracle.IntegrityError as e:
            return handle_error(context, str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

def generate_main_order_master(data_list, today, two_days_later):
    # 先對data_list進行排序，根據SALE_TYPE026的值
    data_list = sorted(data_list, key=lambda x: x['SALE_TYPE026'])

    # 使用一個字典來群組資料，使用公司代碼和公司名稱作為key
    grouped_data = {}

    # 遍歷提供的資料列表
    for data in data_list:
        # 根據AGENT_CODE026和SHORT_NAME025組合成key，作為群組的依據
        key = (data["AGENT_CODE026"], data["SHORT_NAME025"])

        # 如果這個key還沒在grouped_data中，那麼初始化一個新的群組結構
        if key not in grouped_data:
            grouped_data[key] = {
                "company_code": data["AGENT_CODE026"],
                "company_name": data["SHORT_NAME025"],
                "order_date": today.strftime("%Y/%m/%d"),
                "delivery_date": two_days_later.strftime("%Y/%m/%d"),
                "products": [],  # 使用列表代替集合
                "addressDetails": []  # 儲存地址資料
            }

        # 從當前的data中提取產品資訊
        product = {"code": data["SALE_TYPE026"], "description": data["SALE_TYPE_NAME026"]}

        # 檢查產品是否已存在，如果不存在則添加到列表中
        if product not in grouped_data[key]["products"]:
            grouped_data[key]["products"].append(product)

        # 檢查當前地址是否已在該公司的地址詳情列表中
        # 如果沒有，則將新地址和一個唯一的ID加到addressDetails列表中
        if data["DELV_ADDR029"] not in [addr['address'] for addr in grouped_data[key]["addressDetails"]]:
            grouped_data[key]["addressDetails"].append(
                {"id": len(grouped_data[key]["addressDetails"]), "address": data["DELV_ADDR029"]})

    # 遍歷grouped_data，將產品的set格式轉換成字典列表格式
    for key, value in grouped_data.items():
        value["products"] = [{"code": product["code"], "description": product["description"]} for product in value["products"]]

    # 返回轉換後的資料列表
    return list(grouped_data.values())

# 經銷商訂購主檔 END

# 經銷商可訂購品項檔 START
@validate_access_token_and_params('dealers_code', 'product_features')  # 確保這個裝飾器能處理多個參數
def select_erp_hy_ocv035_can_order_products_code_name(request, json_data, role, user_id):
    context = "select_erp_hy_ocv035_can_order_products_code_name"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = cv035_can_order_products_code_name_sql
        sql_conditions = []
        params = {}
        sql_query = ""

        # 預設參數與資料庫欄位的映射
        params_mapping = {
            "dealers_code": "AGENT_CODE035",
            "product_features": "E.CODE_ID003",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        # 構建完整的 SQL 查詢
        if sql_conditions:
            sql_query = (f" {sql_base} AND {' AND '.join(sql_conditions)} "
                         f" ORDER BY A.PRODUCT_ID035")

        try:
            with connection.cursor() as cursor:
                # print(sql_query, params)
                cursor.execute(sql_query, params)
                data = cursor.fetchall()

                if not data:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                result = [{"id": row[0], "name": f"{row[0]}  {row[1]}"} for row in data]
                return result, status.HTTP_200_OK

        except cx_Oracle.IntegrityError as e:
            return handle_error(context, str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)
# 經銷商可訂購品項檔 END

# 查詢品項對應的銷售單位 START
def translate_sales_unit(sales_unit):
    unit_name_mapping = {
        '棧板': 'pallet',
        '層': 'layer',
        '箱': 'box'
    }
    return unit_name_mapping.get(sales_unit)

@validate_access_token_and_params('product_place')
def select_order_product_sales_unit_method(request, json_data, role, user_id):
    context = "select_order_product_sales_unit_method"

    if request.method == "POST":
        # SQL 查詢基本結構
        sql_base = ocv015_order_product_sales_unit_sql
        sql_conditions = []
        params = {}
        sql_query = ""

        # 預設參數與資料庫欄位的映射
        params_mapping = {
            "product_place": "DEL_TYPE015",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        # 構建完整的 SQL 查詢
        if sql_conditions:
            sql_query = (f" {sql_base} AND {' AND '.join(sql_conditions)} "
                         f" ORDER BY PRODUCT_ID015, DECODE(SALESUNIT, '棧板', 1, '層', 2, '箱', 3, 4)")

        try:
            with connection.cursor() as cursor:
                # print(sql_query, params)
                cursor.execute(sql_query, params)
                result = {}
                for row in cursor.fetchall():
                    prod_code, base_unit_name, base_unit_code, sales_unit, conversion = row
                    if prod_code not in result:
                        result[prod_code] = {'units': {}, 'baseUnitName': base_unit_name, 'baseUnitCode': base_unit_code}
                    unit_name = translate_sales_unit(sales_unit)
                    result[prod_code]['units'][unit_name] = {
                        'displayName': sales_unit,
                        'conversion': conversion
                    }
                return result, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)
