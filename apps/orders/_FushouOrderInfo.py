# -*- coding: UTF8 -*-
import itertools

import cx_<PERSON>

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import to_minguo, paginate_data, \
    get_previous_month_dates_minguo
from utils.token_utils import validate_access_token_and_params_with_pagination, get_pagination_params, \
    validate_access_token_and_params

''' FushouOrder Start  '''

asfa_main_fushou_order_sql = """
    SELECT ADNAM2, BEORNO, DECODE(BEBATH, 'H', '飲料', '') BEBATH,
           BEDATE, BESTDT, BEENDT, BEVENN, BECSTN, BESHIP, BEPORT,
           BEITEM, BEITNO, BENAME, BECODE, BESPEC, BESQTY, BEAMTE, BESERI, BEUNIT,
           BEPRDT, BEDODT, HCCSTN, HCNAME
      FROM (SELECT M.BEORNO, BEBATH, BEDATE, BESTDT, BEENDT, BEVENN, M<PERSON>ECSTN, BESHIP, BEPORT,
                   BEITEM, BEITNO, BENAME, BECODE, BESPEC, BESQTY, BEAMTE, BESERI, BEUNIT, 
                   TO_CHAR(BEPRDT, 'YYYY/MM/DD HH24:MI:SS')  BEPRDT, 
                   TO_CHAR(BEDODT, 'YYYY/MM/DD HH24:MI:SS')  BEDODT
              FROM PSBE_M@HY_1 M, PSBE_D@HY_1 D
             WHERE M.BEORNO || M.BECSTN = D.BEORNO || D.BECSTN ), ASHC@HY_1, ASAD@HY_1
     WHERE BECSTN = HCCSTN AND BEVENN = ADVENN AND HCVENN = ADVENN
"""

''' FushouOrder End  '''

# 福總訂單 START
@validate_access_token_and_params_with_pagination('dealers_code')
def select_hy1_asfa_main_fushou_detail_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_hy1_asfa_main_fushou_detail"

    if request.method == "POST":

        # 取得前2個月的第一天和最後一天
        start_date_minguo, end_date_minguo = get_previous_month_dates_minguo(2)

        # SQL 查詢基本結構
        sql_base = asfa_main_fushou_order_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 若沒有提供日期範圍，則使用本月份的第一天和最後一天
        sql_conditions.append(f" BEDATE BETWEEN :due_start_date AND :due_end_date ")

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "BEVENN",
            "batch_number": "BEORNO",
        }

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" BEVENN = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        if 'due_start_date' in json_data and json_data['due_start_date'] is not None and \
                'due_end_date' in json_data and json_data['due_end_date'] is not None:
            params['due_start_date'] = to_minguo(json_data['due_start_date'])
            params['due_end_date'] = to_minguo(json_data['due_end_date'])
            params2['due_start_date'] = to_minguo(json_data['due_start_date'])
            params2['due_end_date'] = to_minguo(json_data['due_end_date'])
        else:
            # 若前端沒有提供日期條件，則使用預設日期
            params['due_start_date'] = start_date_minguo
            params['due_end_date'] = end_date_minguo
            params2['due_start_date'] = start_date_minguo
            params2['due_end_date'] = end_date_minguo

        # 構建完整的 SQL 查詢
        sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} "

        ranked_sql = """
                    SELECT ADNAM2, BEORNO, BEBATH,
                           BEDATE, BESTDT, BEENDT, BEVENN, BECSTN, BESHIP, BEPORT,
                           BEITEM, BEITNO, BENAME, BECODE, BESPEC, BESQTY, BEAMTE, BESERI, BEUNIT,
                           BEPRDT, BEDODT, HCCSTN, HCNAME,
                           DENSE_RANK() OVER (ORDER BY BEORNO DESC, BEVENN) AS RNK
                      FROM (  """ + sql_query + """  )
                     WHERE 1 = 1
                     ORDER BY BEDATE DESC, BEORNO DESC, BEVENN DESC, BESERI
                """

        ranked_sql1 = """
                    SELECT ADNAM2, BEORNO, BEBATH,
                           BEDATE, BESTDT, BEENDT, BEVENN, BECSTN, BESHIP, BEPORT,
                           BEITEM, BEITNO, BENAME, BECODE, BESPEC, BESQTY, BEAMTE, BESERI, BEUNIT,
                           BEPRDT, BEDODT, HCCSTN, HCNAME, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size

                """

        ranked_sql2 = """ 
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        try:
            with connection.cursor() as cursor:
                # print(sql_query, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if len(data) == 0:
                    return {"results": []}, status.HTTP_200_OK

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行資料轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]
                # data.sort(reverse=True, key=lambda x: (x['BEDATE'], x['BEORNO'], x['BEVENN'], x['BECSTN']))

                # Step 1: Decorate
                # 假設 BEDATE， BEORNO， BEVENN， BECSTN 是其他欄位且需要降序排序
                decorated = [(item['BEDATE'], item['BEORNO'], item['BEVENN'], item['BESERI'], item['BECSTN'], item) for item in data]

                # Step 2: 排序（Sort）：對這些元組進行排序
                # 對於 BESERI 使用升序，其他欄位使用降序
                decorated.sort(key=lambda x: (x[0], x[1], x[2], -int(x[3]), x[4]) if x[3].isdigit() else ( x[0], x[1], x[2], x[3], x[4]), reverse=True)

                # Step 3: 還原（Undecorate）：將排序後的元素轉換回原始格式
                sorted_data = [item[-1] for item in decorated]

                # 將 BESERI 欄位格式化回原始的字串格式
                for item in sorted_data:
                    item['BESERI'] = '{:04d}'.format(int(item['BESERI']))

                # 依分組資料
                grouped_by_column = [list(group) for _, group in itertools.groupby(sorted_data, key=lambda x: (x['BEORNO'], x['BEVENN']))]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    # if not total_data or total_data[0][0] is None:
                    #     return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_fushou_master_detail(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

        except cx_Oracle.IntegrityError as e:
            return handle_error(context, str(e), status.HTTP_500_INTERNAL_SERVER_ERROR)

# 生成福總訂單報表
def generate_fushou_master_detail(data, start_index=0):
    report = []
    for track_check, group in itertools.groupby(data, lambda x: (x['BEORNO'], x['BEVENN'])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["BEVENN"],  # 經銷商代碼
            "dealer_name": group[0]["ADNAM2"],  # 經銷商名稱
            "batch_number": group[0]["BEORNO"],  # 發單批次
            "issue_date": group[0]["BEDATE"],  # 發單批次
            # "download_link": '000',  # 碼頭別及交貨地點
            "print_date_time": group[0]["BEPRDT"],  # 列印日期
            "download_date_time": group[0]["BEDODT"],  # 下載日期
        }

        details = []
        for master, master_group in itertools.groupby(group, lambda x: x["BECSTN"]):
            master_group = list(master_group)

            detail = {
                "index": len(details) + 1,
                "dealer_code": group[0]["BEVENN"],  # 經銷商代碼
                "dealer_name": group[0]["ADNAM2"],  # 經銷商名稱
                "batch_number": master_group[0]["BEORNO"],  # 發單批次
                "recipient_code": master_group[0]["HCCSTN"],  # 收貨單位編號
                "shipping_type": master_group[0]["BEBATH"],  # 發貨單別
                "issue_date": master_group[0]["BEDATE"],  # 發單日期
                "start_delivery_date": master_group[0]["BESTDT"],  # 限送日期(起)
                "end_delivery_date": master_group[0]["BEENDT"],  # 限送日期(迄)
                "recipient_name": master_group[0]["HCNAME"],  # 收貨單位名稱
                # "RecipientPhone": master_group[0]["FAORNO"],  # 收貨單位電話
                "vendor_name": '黑松股份有限公司',  # 廠商名稱
                "vendor_code": '44334',  # 廠商編號
                "delivery_method": master_group[0]["BESHIP"],  # 運送方式
                "dock_and_delivery_location": master_group[0]["BEPORT"],  # 碼頭別及交貨地點
            }

            product_details = []
            for product, product_group in itertools.groupby(master_group, lambda x: x["BECODE"]):
                product_group = list(product_group)

                product_detail = {
                    "index": len(product_details) + 1,
                    "dealer_code": group[0]["BEVENN"],  # 經銷商代碼
                    "batch_number": master_group[0]["BEORNO"],  # 發單批次
                    "recipient_code": master_group[0]["HCCSTN"],  # 收貨單位編號
                    "item_number": product_group[0]["BEITEM"],  # 項次
                    "product_barcode": product_group[0]["BECODE"],  # 商品條碼
                    "product_name": product_group[0]["BENAME"],  # 商品名稱
                    "specification": product_group[0]["BESPEC"],  # 規格
                    "requested_qty": product_group[0]["BESQTY"],  # 申請量
                    "serial_number": product_group[0]["BESERI"],  # 流水號
                    "measurement_unit": product_group[0]["BEUNIT"],  # 計量單位
                }

                product_details.append(product_detail)

            detail["child_details"] = product_details
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


# 福總訂單 END

''' UPDATE Order START '''
def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "dealers_code": "BEVENN",
        "batch_number": "BEORNO",
    }
    return mapping.get(key)

def update_with_raw_sql(context, data_list):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                for data in data_list:

                    # 構造 WHERE 子句部分
                    where_parts = [
                        f"BEVENN = '{data['dealers_code']}'",
                        f"BEORNO = '{data['batch_number']}'",
                    ]

                    # 處理 dealers_type 數組
                    if 'dealers_type' in data and data['dealers_type']:
                        dealers_type_conditions = [f"BEROUT = '{dealer_type}'" for dealer_type in data['dealers_type']]
                        where_parts.append(f"({' OR '.join(dealers_type_conditions)})")

                    # 檢查BEPRDT是否已有值
                    sql = f"SELECT BEPRDT FROM PSBE_M@HY_1 WHERE {' AND '.join(where_parts)}"
                    cursor.execute(sql)

                    # 如果BEPRDT已有值，則不更新
                    if cursor.fetchone()[0] is not None:
                        continue

                    # 生成SET子句部分，僅更新 print_date_time 字段
                    set_parts = ["BEPRDT = SYSDATE"]

                    # 如果有要更新的字段
                    if set_parts:
                        set_string = ", ".join(set_parts)
                        where_string = " AND ".join(where_parts)

                        # 構造並執行 SQL 更新語句
                        sql = f"UPDATE PSBE_M@HY_1 SET {set_string} WHERE {where_string}"
                        cursor.execute(sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('dealers_code', 'batch_number')
def update_hy1_asfa_main_fushou_detail_method(request, json_data, role, user_id):
    context = "update_hy1_asfa_main_fushou_detail"

    if request.method == "POST":

        try:
            result, result_status = update_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

''' UPDATE Order END '''