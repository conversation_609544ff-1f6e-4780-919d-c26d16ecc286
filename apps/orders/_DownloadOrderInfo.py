import os

import cx_Oracle

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

download_sql = """
    SELECT DISTINCT FAORNO || '_' || FAVENN FILENAME 
      FROM ASFA_WEB@HY_1 
     WHERE 1 = 1
"""

# 福總訂單 下載 START
@validate_access_token_and_params(None)
def select_hy1_asfa_main_fushou_download(request, json_data, role, user_id):
    context = "select_hy1_asfa_main_fushou_download"

    if request.method == "POST":

        strCond = ''
        condition = {}

        # 定義可能的參數列表
        params = {
            "dealers_code": "FAVENN",
            "batch_number": "FAORNO",
        }

        # 循環遍歷參數列表
        for param, field in params.items():
            if param in json_data and json_data[param] is not None:
                condition['q' + param.upper()] = json_data[param]
                strCond += f' AND {field} = :q{param.upper()} '

        # 連線資料庫
        with connection.cursor() as cursor:
            try:
                cursor.execute(download_sql + strCond, condition)
                data = cursor.fetchone()

                if data is None:
                    return "找不到資料", status.HTTP_404_NOT_FOUND
                else:
                    # 假設 data 中有一個名為 file_path 的字段
                    FILE_ROOT_PATH = r'C:\ts\out'
                    file_name = data[0]
                    file_path = os.path.join(FILE_ROOT_PATH, file_name + '.txt')
                    return file_path, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)
