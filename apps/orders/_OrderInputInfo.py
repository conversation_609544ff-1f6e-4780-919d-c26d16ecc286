# -*- coding: UTF8 -*-
import itertools
import math

import cx_<PERSON>
import datetime

from celery.result import AsyncR<PERSON>ult
from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import to_minguo, select_get_serial, fetch_user_dept_code_and_permissions, now_datetime
from utils.token_utils import validate_access_token_and_params_with_pagination, validate_access_token_and_params

select_order_input_data_sql = """
    SELECT M.ORDERNO, ORDERDATE, VENDORCODE, BRIEF_NAME025, ODDATE, DELV, CARCODE, CARQTYBOARD, TOTQTYBOARD, ASDATE, ASID,
           NVL(DEL_TYPE015, PLACECODE) DEL_TYPE015, DEL_TYPE_NAME015,
           READONLY, READONLY_NAME, OWNERID, OWNERDAT<PERSON>, RESPONID, RESPO<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ORDERMEMO, <PERSON><PERSON>N<PERSON>,
           <PERSON><PERSON><PERSON><PERSON>,
           RPAD(PRODCODE, 6, ' ') || SDESCR015 PRODCODE_DESCR,
           QTY, SALESUNIT, 
           CASE
               WHEN SALESUNIT = 'pallet'
                   THEN '板'
               WHEN SALESUNIT = 'layer'
                   THEN '層'
               WHEN SALESUNIT = 'box'
                   THEN '箱'
               ELSE
                   SALESUNIT
           END SALESUNIT_NAME,
           QTYSPUNITUNIT, SPUNIT, UNITB_NAME015, QTYBOARDUNIT, QTYSPUNIT, QTYBOARD, MARK, DESC_C003, SENDMARK, ISCHECK,
           DETAILMEMO
      FROM (SELECT ORDERNO, VENDORCODE,
                   CASE
                       WHEN ORDERDATE IS NULL
                           THEN ''
                       ELSE
                           SUBSTR(ORDERDATE, 1, 3) + 1911 || '/' || SUBSTR(ORDERDATE, 4, 2) || '/' || SUBSTR(ORDERDATE, 6, 2)
                   END ORDERDATE,
                   CASE
                       WHEN ORDERDATE IS NULL
                           THEN ''
                       ELSE SUBSTR(ODDATE, 1, 3) + 1911 || '/' || SUBSTR(ODDATE, 4, 2) || '/' || SUBSTR(ODDATE, 6, 2)
                   END ODDATE,
                   DELV, CARCODE, CARQTYBOARD, TOTQTYBOARD, ASDATE, ASID, PLACECODE, READONLY,
                   CASE
                       WHEN READONLY = '0'
                           THEN '未傳送'
                       WHEN READONLY = '1'
                           THEN '已傳送'
                       WHEN READONLY = '2'
                           THEN '已作廢'
                       WHEN READONLY = '3'
                           THEN '已下載'
                       ELSE
                           '未知'
                   END READONLY_NAME,
                   OWNERID,
                   TO_CHAR(OWNERDATE, 'YYYY/MM/DD HH24:MI:SS') OWNERDATE,
                   RESPONID, RESPONDATE, SENDTIME, ORDERMEMO
              FROM ORDERHT) M,
           (SELECT ORDERNO, ITEMNO, PRODCODE, QTY, SALESUNIT, QTYSPUNITUNIT, SPUNIT, QTYBOARDUNIT, QTYSPUNIT, QTYBOARD,
                   MARK,
                   SENDMARK, ISCHECK, DETAILMEMO
              FROM ORDERDL) D,
           (SELECT CODE_ID003, DESC_C003 FROM OCV003@B2B WHERE CODE_TYPE003 = '12'
            UNION ALL
            SELECT ' ' CODE_ID003, '' DESC_C003 FROM DUAL), OCV015@B2B, OCV025@B2B
     WHERE M.ORDERNO = D.ORDERNO(+) AND VENDORCODE = AGENT_CODE025(+)
       AND PRODCODE = PRODUCT_ID015(+) AND SPUNIT = UNITB015(+)
       AND MARK = CODE_ID003(+)
"""

''' ORDER_INPUT INSERT START '''

@validate_access_token_and_params(None)
def select_order_serial_method(request, json_data, role, user_id):
    if request.method == "POST":
        # 取得年份
        date = str(datetime.datetime.now().year - 1911) + str(datetime.datetime.now().month).zfill(2) + str(datetime.datetime.now().day).zfill(2)
        return {"results": 'C' + select_get_serial('********', date, user_id, json_data)}, status.HTTP_200_OK


''' ORDER_INPUT_ORDERNO END '''

''' ORDER_INPUT INSERT START '''

# 映射字典
ORDER_MASTER_NAME_MAPPING = {
    "ORDERNO": "order_no",
    "ORDERDATE": "order_date",
    "VENDORCODE": "dealer_code",
    "ODDATE": "delivery_date",
    "DELV": "delv",
    "CARCODE": "carCode",
    "CARQTYBOARD": "carQtyBoard",
    "TOTQTYBOARD": "total_order_quantity",
    "ASDATE": "asDate",
    "ASID": "asId",
    "PLACECODE": "product_features",
    "READONLY": "ready_only",
    "HUBTYPE": "hubType",
    "OWNERID": "ownerId",
    "OWNERDATE": "ownerDate",
    "RESPONID": "responId",
    "RESPONDATE": "responDate",
    "SENDTIME": "sendTime",
    "ORDERMEMO": "orderMemo",
}

ORDER_DETAIL_NAME_MAPPING = {
    "ORDERNO": "order_no",
    "ITEMNO": "index",
    "PRODCODE": "item_number",
    "QTY": "order_quantity",
    "SALESUNIT": "sales_unit_displayName",
    # "QTYSPUNITUNIT": "qtySpUnitUnit",
    "SPUNIT": "sales_unit",
    "QTYBOARDUNIT": "sales_unit_conversion",
    # "QTYSPUNIT": "qtySpUnit",
    "QTYBOARD": "actual_quantity",
    "MARK": "sales_category",
    "SENDMARK": "merge_table",
    # "ISCHECK": "isCheck",
    # "HUBTYPE": "hubType",
    "OWNERID": "ownerId",
    "OWNERDATE": "ownerDate",
    # "RESPONID": "responId",
    # "RESPONDATE": "responDate",
    "DETAILMEMO": "note",
}

def update_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                current_date_time = now_datetime().strftime('%Y%m%d %H:%M:%S')

                # 處理主檔
                order_data = {db_key: data[json_key] for db_key, json_key in ORDER_MASTER_NAME_MAPPING.items() if json_key in data}

                if 'order_date' in data:
                    order_data['ORDERDATE'] = to_minguo(data['order_date'])
                if 'delivery_date' in data:
                    order_data['ODDATE'] = to_minguo(data['delivery_date'])

                order_data['OWNERDATE'] = current_date_time

                # 更新 ORDERHT 表
                update_segments = [f"{db_key} = TO_DATE(%s, 'YYYYMMDD HH24:MI:SS')" if db_key == 'OWNERDATE' else f"{db_key} = %s" for db_key in order_data]
                update_values = list(order_data.values())
                order_no = data['order_no']
                update_values.append(order_no)

                update_query = f"UPDATE ORDERHT SET {', '.join(update_segments)} WHERE ORDERNO = %s"
                cursor.execute(update_query, update_values)

                # 處理明細檔
                if 'details' in data and isinstance(data['details'], list):
                    # 先刪除所有舊的明細數據
                    delete_query = f"DELETE FROM ORDERDL WHERE ORDERNO = %s"
                    cursor.execute(delete_query, [order_no])

                    # 插入新的明細數據
                    for detail_item in data['details']:
                        # 組裝明細數據，包括從主檔中獲取的 ORDERNO
                        detail_data = {db_key: detail_item[json_key] for db_key, json_key in ORDER_DETAIL_NAME_MAPPING.items() if json_key in detail_item}
                        detail_data['ORDERNO'] = order_no
                        detail_data['OWNERID'] = user_id
                        detail_data['OWNERDATE'] = current_date_time

                        # 構建 INSERT 語句
                        columns = ', '.join(detail_data.keys())
                        details_placeholders = ', '.join(["TO_DATE(%s, 'YYYYMMDD HH24:MI:SS')" if col == 'OWNERDATE' else "%s" for col in detail_data.keys()])
                        insert_query = f"INSERT INTO ORDERDL ({columns}) VALUES ({details_placeholders})"

                        cursor.execute(insert_query, list(detail_data.values()))

                    # send_message_to_user(user_id, f"訂單 {order_no} 已更新")

                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, str(ie), status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, str(e), status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params(None)
def update_order_input_method(request, json_data, role, user_id):
    context = 'update_order_input_method'

    if request.method == "POST":
        try:
            result, result_status = update_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' ORDER_INPUT INSERT END '''

''' ORDER_INPUT SELECT START '''

def transform_to_order_input_frontend_structure(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["ORDERNO"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "order_no": group[0].get("ORDERNO", "") or "",
            "order_date": group[0].get("ORDERDATE", "") or "",
            "dealer_code": group[0].get("VENDORCODE", "") or "",
            "dealer_name": group[0].get("BRIEF_NAME025", "") or "",
            "delivery_date": group[0].get("ODDATE", "") or "",
            "delv": group[0].get("DELV", "") or "",
            "carCode": group[0].get("CARCODE", "") or "",
            "carQtyBoard": group[0].get("CARQTYBOARD", "") or "",
            "total_order_quantity": group[0].get("TOTQTYBOARD", "") or "",
            "asDate": group[0].get("ASDATE", "") or "",
            "asId": group[0].get("ASID", "") or "",
            "product_features": group[0].get("DEL_TYPE015", "") or "",
            "product_features_name": group[0].get("DEL_TYPE_NAME015", "") or "",
            "ready_only": group[0].get("READONLY", "") or "",
            "ready_only_name": group[0].get("READONLY_NAME", "") or "",
            "ownerId": group[0].get("OWNERID", "") or "",
            "ownerDate": group[0].get("OWNERDATE", "") or "",
            "responId": group[0].get("RESPONID", "") or "",
            "responDate": group[0].get("RESPONDATE", "") or "",
            "sendTime": group[0].get("SENDTIME", "") or "",
            "orderMemo": group[0].get("ORDERMEMO", "") or "",
        }

        details = []
        for master, master_group in itertools.groupby(group, lambda x: x["PRODCODE"]):
            master_group = list(master_group)

            detail = {
                "index": master_group[0].get("ITEMNO", "") or "1",
                "product_code": master_group[0].get("PRODCODE", "") or "",
                "product_code_descr": master_group[0].get("PRODCODE_DESCR", "") or "",
                "order_quantity": master_group[0].get("QTY", "") or "",
                "sales_unit": master_group[0].get("SALESUNIT", "") or "",
                "sales_unit_displayName": master_group[0].get("UNITB_NAME015", "") or "",
                "sales_unit_displayName2": master_group[0].get("SALESUNIT_NAME", "") or "",
                "qtySpUnitUnit": master_group[0].get("QTYSPUNITUNIT", "") or "",
                "sales_unit_conversion": master_group[0].get("SPUNIT", "") or "",
                "sales_unit_conversion_unit": master_group[0].get("QTYBOARDUNIT", "") or "",
                "qtySpUnit": master_group[0].get("QTYSPUNIT", "") or "",
                "actual_quantity": master_group[0].get("QTYBOARD", "") or "",
                "sales_category": master_group[0].get("MARK", "") or "",
                "sales_category_descr": master_group[0].get("DESC_C003", "") or "",
                "merge_table": master_group[0].get("SENDMARK", "") or "",
                "isCheck": master_group[0].get("ISCHECK", "") or "",
                "note": master_group[0].get("DETAILMEMO", "") or "",
            }

            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


def select_with_raw_sql(context, strCond, condition, condition2, page_number, page_size, start_rnk):
    ranked_sql = """
                SELECT ORDERNO, ORDERDATE, VENDORCODE, BRIEF_NAME025, ODDATE, DELV, CARCODE, CARQTYBOARD, TOTQTYBOARD, ASDATE, ASID, 
                       DEL_TYPE015, DEL_TYPE_NAME015,
                       READONLY, READONLY_NAME, OWNERID, OWNERDATE, RESPONID, RESPONDATE, SENDTIME, ORDERMEMO, ITEMNO, 
                       PRODCODE, PRODCODE_DESCR,  QTY, SALESUNIT, SALESUNIT_NAME,
                       QTYSPUNITUNIT, SPUNIT, UNITB_NAME015, QTYBOARDUNIT, QTYSPUNIT, QTYBOARD, 
                       MARK, DESC_C003, SENDMARK, ISCHECK, DETAILMEMO,
                       DENSE_RANK() OVER (ORDER BY ORDERNO DESC) AS RNK
                  FROM (  """ + select_order_input_data_sql + """  )
                 WHERE 1 = 1 """ + strCond + """
                 ORDER BY ORDERNO DESC, LPAD(ITEMNO, 3, '0')
            """

    # print(ranked_sql)
    # print(condition)

    ranked_sql1 = """
                SELECT ORDERNO, ORDERDATE, VENDORCODE, BRIEF_NAME025, ODDATE, DELV, CARCODE, CARQTYBOARD, TOTQTYBOARD, ASDATE, ASID, 
                       DEL_TYPE015, DEL_TYPE_NAME015,
                       READONLY, READONLY_NAME, OWNERID, OWNERDATE, RESPONID, RESPONDATE, SENDTIME, ORDERMEMO, ITEMNO, 
                       PRODCODE, PRODCODE_DESCR, QTY, 
                       SALESUNIT, SALESUNIT_NAME, QTYSPUNITUNIT, SPUNIT, UNITB_NAME015, QTYBOARDUNIT, QTYSPUNIT, QTYBOARD, 
                       MARK, DESC_C003, SENDMARK, ISCHECK, DETAILMEMO, RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """

    try:
        with connection.cursor() as cursor:
            cursor.execute(ranked_sql1, condition)
            data = cursor.fetchall()

            if data is None:
                return handle_error("查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["ORDERNO"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, condition2)  # Assuming condition works for this query
                total_data = cursor2.fetchall()

                if not total_data or total_data[0][0] is None:
                    return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            total_pages = math.ceil(total_rank / page_size)

            if page_number > total_pages:
                page_number = total_pages

            # If you need to flatten your grouped data or do some other processing
            page_data = [item for sublist in grouped_by_column for item in sublist]

            # print('page_data', page_data)

            report = transform_to_order_input_frontend_structure(page_data, start_rnk - 1)

            # print('report', report)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, str(e), status.HTTP_400_BAD_REQUEST)

def add_condition(field, strCond, condition, condition2, json_data):
    if field in json_data and json_data[field] is not None:
        placeholder = f":q{field}"
        strCond += f" AND {field} LIKE {placeholder} "
        like_value = f"%{json_data[field]}%"
        condition[placeholder] = like_value
        condition2[placeholder] = like_value
    return strCond, condition, condition2

@validate_access_token_and_params_with_pagination(None)
def select_order_input_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = 'select_order_input_method'

    if request.method == "POST":

        # 查資料庫尋找使用者代號或部門代號
        data, error = fetch_user_dept_code_and_permissions('B001_6', user_id)
        if error:
            return error, status.HTTP_404_NOT_FOUND
        else:
            dept_dealer_code = data[0]['CODE']
            can_special = data[0]['CAN_SPECIAL']

        # 初始化
        strCond = ''
        condition = {}
        condition2 = {}

        # 將前端的參數名稱映射到查詢中的參數名稱
        params_mapping = {
            "pageIndex": "qpage_number",
            "pageSize": "qpage_size",
        }

        # 提供預設值
        default_values = {
            "pageIndex": start_rnk,  # 預設為第一頁
            "pageSize": end_rnk,  # 您可以根據需要設置其他預設值
        }

        # 檢查是否有缺少的參數
        for frontend_param, query_param in params_mapping.items():
            if frontend_param in default_values:
                condition[query_param] = default_values[frontend_param]
            else:
                raise ValueError(f"Parameter {frontend_param} is missing!")

        # 若沒有提供日期範圍，則使用當日前十天
        strCond += ""

        # 定義可能的參數列表
        params = {
            "dealers_code": "VENDORCODE",
        }

        # 循環遍歷參數列表
        for param, field in params.items():
            if param in json_data and json_data[param] is not None:
                value = json_data[param]
                condition[f'f_{param}'] = value
                condition2[f'f_{param}'] = value
                strCond += f' AND {field} = :f_{param} '

        # 若沒有特殊權限，則只能查詢自己的資料
        if role == '1':
            strCond += f' AND OWNERID = :f_OWNERID '
            condition[f'f_OWNERID'] = user_id
            condition2[f'f_OWNERID'] = user_id
        else:
            strCond += f' AND VENDORCODE = :f_VENDORCODE '
            condition[f'f_VENDORCODE'] = dept_dealer_code
            condition2[f'f_VENDORCODE'] = dept_dealer_code

        try:
            result = select_with_raw_sql(context, strCond, condition, condition2, page_number, page_size, start_rnk)
            # print(result)
            return result
        except Exception as e:
            return handle_error(str(e), status.HTTP_400_BAD_REQUEST)


''' ORDER_INPUT SELECT END '''