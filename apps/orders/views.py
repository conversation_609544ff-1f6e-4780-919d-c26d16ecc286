import codecs
import mimetypes
import os

import chardet as chardet
from django.http import JsonResponse, FileResponse, HttpResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.orders._DownloadOrderInfo import select_hy1_asfa_main_fushou_download
from apps.orders._FushouOrderInfo import select_hy1_asfa_main_fushou_detail_method, update_hy1_asfa_main_fushou_detail_method
from apps.orders._OrderInfo import select_erp_hy_ocv029_main_order_master, \
    select_erp_hy_ocv035_can_order_products_code_name, select_order_product_sales_unit_method
from apps.orders._OrderInputInfo import select_order_serial_method, update_order_input_method, select_order_input_method
from apps.orders._PreShipmentOrderInfo import select_pre_shipment_order_method
from apps.orders._ShipmentOrderInfo import select_shipment_order_method
from apps.orders.models import mainOrder

ORDER_ACTIONS = {
    'fushou_order': {
        'select': select_hy1_asfa_main_fushou_detail_method,
        'update': update_hy1_asfa_main_fushou_detail_method,
    },
    'ocv029_order': {
        'select': select_erp_hy_ocv029_main_order_master,
    },
    'ocv035_order': {
        'select': select_erp_hy_ocv035_can_order_products_code_name,
    },
    'order_product_sales_unit': {
        'select': select_order_product_sales_unit_method,
    },
    'shipment_order': {
        'select': select_shipment_order_method,
    },
    'pre_shipment_order': {
        'select': select_pre_shipment_order_method,
    },
    'order_serial': {
        'insert_update': select_order_serial_method,
    },
    'order_input': {
        'update': update_order_input_method,
        'select': select_order_input_method,
    },

}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class OrderViewSet(viewsets.ModelViewSet):
    queryset = mainOrder.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = ORDER_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 福總訂單下載
    @action(detail=False, methods=['post'])
    def hy1_asfa_main_fushou_download(self, request):
        file_path, http_status = select_hy1_asfa_main_fushou_download(request)

        if http_status == status.HTTP_200_OK:
            try:
                mime_type, encoding = mimetypes.guess_type(file_path)
                file = open(file_path, 'rb')
                response = FileResponse(file, content_type=mime_type or "application/octet-stream")
                response['Content-Disposition'] = f'attachment; filename="{os.path.basename(file_path)}"'
                return response
            except Exception as e:
                print(f"Error when opening the file: {e}")
                return JsonResponse(
                    {'message': '文件打開失敗', 'meta': {'msg': '失敗', 'status': status.HTTP_500_INTERNAL_SERVER_ERROR}},
                    json_dumps_params={'ensure_ascii': False})
        else:
            return JsonResponse(
                {'message': file_path, 'meta': {'msg': '失敗', 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False}
            )

    # 查詢福總訂單
    @action(detail=False, methods=['post'])
    def hy1_asfa_main_fushou_detail(self, request):
        return self._handle_action('fushou_order', 'select')

    # 更新福總訂單
    @action(detail=False, methods=['post'])
    def update_hy1_asfa_main_fushou_detail(self, request):
        return self._handle_action('fushou_order', 'update')

    #
    @action(detail=False, methods=['post'])
    def erp_hy_ocv029_main_order_master(self, request):
        return self._handle_action('ocv029_order', 'select')

    #
    @action(detail=False, methods=['post'])
    def erp_hy_ocv035_can_order_products_code_name(self, request):
        return self._handle_action('ocv035_order', 'select')

    # 查詢產品銷售單位
    @action(detail=False, methods=['post'])
    def select_order_product_sales_unit(self, request):
        return self._handle_action('order_product_sales_unit', 'select')

    # 查詢發貨單
    @action(detail=False, methods=['post'])
    def select_shipment_order(self, request):
        return self._handle_action('shipment_order', 'select')

    # 查詢預發貨單
    @action(detail=False, methods=['post'])
    def select_pre_shipment_order(self, request):
        return self._handle_action('pre_shipment_order', 'select')

    # 取得訂單流水號
    @action(detail=False, methods=['post'])
    def select_order_serial(self, request):
        return self._handle_action('order_serial', 'insert_update')

    # 更新訂單
    @action(detail=False, methods=['post'])
    def update_order_input(self, request):
        return self._handle_action('order_input', 'update')

    # 查詢訂單
    @action(detail=False, methods=['post'])
    def select_order_input(self, request):
        return self._handle_action('order_input', 'select')

