import logging

from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.accounts.accountInfo import select_login, select_logout
from apps.accounts.models import mainAccount


class AccountViewSet(viewsets.ModelViewSet):
    queryset = mainAccount.objects.all()

    @action(detail=False, methods=['post'])
    def login(self, request):
        # sql查詢結果
        sql_result, http_status = select_login(request)

        # print(sql_result, http_status)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        if isinstance(sql_result, str):
            # print('sql_result', {'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False})

        else:
            return sql_result

    @action(detail=False, methods=['post'])
    def logout(self, request):
        # sql查詢結果
        sql_result, http_status = select_logout(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        if isinstance(sql_result, str):
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False})
        else:
            return sql_result
