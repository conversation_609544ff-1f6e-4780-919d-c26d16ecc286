import logging
import uuid
from datetime import datetime, timedelta

import requests
from django.db import connection
from django.http import JsonResponse
from rest_framework import status
from django.views.decorators.csrf import csrf_protect
from rest_framework.decorators import throttle_classes

from HEYSONG_ERP_HY_API import settings
from HEYSONG_ERP_HY_API.settings import strCORS_URL
from apps.throttles import SuperLowRateThrottle
from utils.error_utils import handle_error
from utils.main_utils import get_taiwan_timezone, set_token_access_minutes_expi, set_token_refresh_days_expi

def is_taiwan_ip(ip):
    try:
        response = requests.get(f'http://ip-api.com/json/{ip}')
        logging.info(f'IP API 回應: {response.text}')
        data = response.json()
        # 如果回應成功且國家代碼為台灣，由於公司可能在DMZ區為************所以排除**************
        if data.get('status') == 'success' and data.get('countryCode') == 'TW':
            return True
        elif data.get('status') == 'fail' and data.get('query') == '**************':
            return True
        return False
    except Exception as e:
        logging.error(f'錯誤: {str(e)}')
        return False

# def is_taiwan_ip(ip):
#     try:
#         response = requests.get(f'http://**************:50000/ip?ip={ip}')
#         data = response.json()
#         logging.info(f'IP API 回應狀態碼: {response.status_code}, 回應內容: {data}')
#
#         # 檢查 HTTP 狀態碼
#         if response.status_code == 200:
#             # 獲取國家 ISO 碼並檢查是否為台灣
#             if data.get('country', {}).get('iso_code') == 'TW':
#                 return True
#             else:
#                 return False
#         elif response.status_code == 400 and data.get('query') == '**************':
#             return True
#         else:
#             # 處理其他 HTTP 錯誤
#             logging.error(f'非成功的回應狀態碼: {response.status_code}')
#             return False
#     except Exception as e:
#         logging.error(f'錯誤: {str(e)}')
#         return False

# 定義最大失敗嘗試次數
MAX_LOGIN_ATTEMPTS = 10

# @csrf_protect
@throttle_classes([SuperLowRateThrottle])
def select_login(request):
    context = 'select_login'

    if request.method == "POST":
        username = request.data.get('username')
        password = request.data.get('password')

        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')

        # 檢查 IP 是否來自台灣
        if not settings.DEBUG:
            if not is_taiwan_ip(ip):
                return '只允許來自台灣的 IP 登入', status.HTTP_451_UNAVAILABLE_FOR_LEGAL_REASONS

        # User-Agent
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        sSTM = """
            SELECT USERID, PSWD, USERNAME, SYSROLE, FAILEDLOGINS, LOCKTIME
              FROM USERS 
             WHERE EXPIREDATE > TRUNC(SYSDATE) AND USERID = %s
        """

        try:
            with connection.cursor() as cursor:


                cursor.execute(sSTM, [username])
                row = cursor.fetchone()

                if row is not None:
                    db_username, db_password, name, role, failed_logins, lock_time = row
                    failed_logins = failed_logins if failed_logins is not None else 0

                    # 將含時區的 datetime 轉為無時區的 datetime
                    taiwan_now = datetime.now(get_taiwan_timezone()).replace(tzinfo=None)

                    if lock_time and lock_time > taiwan_now:
                        return '帳號已被鎖定，請30分鐘後再試', status.HTTP_423_LOCKED

                    if db_password != password:
                        failed_logins += 1
                        cursor.execute("""
                            UPDATE USERS SET FAILEDLOGINS = %s
                            WHERE USERID = %s
                        """, [failed_logins, username])

                        # 如果登入失敗次數超過限制,鎖定帳號
                        if failed_logins >= 10:
                            lock_time = datetime.now() + timedelta(minutes=30)
                            cursor.execute("""
                                UPDATE USERS SET LOCKTIME = %s WHERE USERID = %s
                            """, [lock_time, username])

                        return '使用者名稱或密碼無效', status.HTTP_422_UNPROCESSABLE_ENTITY

                    # 重置登入失敗次數
                    sSTM = """
                        UPDATE USERS 
                           SET FAILEDLOGINS = 0, LOCKTIME = NULL
                         WHERE USERID = :username
                    """
                    cursor.execute(sSTM, {'username': username})

                    access_token = uuid.uuid4()
                    refresh_token = uuid.uuid4()

                    access_expi = datetime.now(get_taiwan_timezone()) + timedelta(minutes=set_token_access_minutes_expi())
                    refresh_expi = datetime.now(get_taiwan_timezone()) + timedelta(days=set_token_refresh_days_expi())

                    sSTM = """
                        INSERT INTO WBZE (ZEUSER, ZEACCESSTOKEN, ZEACCESSEXPI, ZEREFRESHTOKEN, ZEREFRESHEXPI, ZEADDRESS)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """

                    cursor.execute(sSTM, [username, str(access_token), access_expi, str(refresh_token), refresh_expi, ip])

                    # 記錄登入日誌
                    sSTM = """
                        INSERT INTO LOGIN_LOGS (USERID, LOGINTIME, IPADDRESS, USERAGENT)
                        VALUES (:username, SYSDATE, :ip_address, :user_agent)
                    """
                    cursor.execute(sSTM, {'username': username, 'ip_address': ip, 'user_agent': user_agent})

                else:
                    # 對未知使用者累計登入失敗次數
                    cursor.execute("""
                        MERGE INTO UNKNOWN_USER_LOGINS u
                        USING (SELECT :ip AS IP_ADDRESS, :username AS USERID FROM DUAL) i
                        ON (u.IP_ADDRESS = i.IP_ADDRESS AND u.USERID = i.USERID)
                        WHEN MATCHED THEN
                            UPDATE SET u.FAILEDLOGINS = u.FAILEDLOGINS + 1
                        WHEN NOT MATCHED THEN
                            INSERT (IP_ADDRESS, USERID, FAILEDLOGINS)
                            VALUES (i.IP_ADDRESS, i.USERID, 1)
                    """, {'ip': ip, 'username': username})

                    cursor.execute("""
                        SELECT FAILEDLOGINS 
                        FROM UNKNOWN_USER_LOGINS 
                        WHERE IP_ADDRESS = :ip AND USERID = :username
                    """, {'ip': ip, 'username': username})
                    failed_logins = cursor.fetchone()[0]

                    # 登錄失敗次數達到閾值,永久封鎖 IP
                    if failed_logins >= MAX_LOGIN_ATTEMPTS:
                        cursor.execute("""
                            INSERT INTO BLOCKED_IPS (USERID, IP_ADDRESS)
                            VALUES (:username, :ip)
                        """, {'username': username, 'ip': ip})
                        return '您的 IP 已被封鎖,請聯繫管理員', status.HTTP_418_IM_A_TEAPOT

                    return '使用者名稱或密碼無效', status.HTTP_422_UNPROCESSABLE_ENTITY

                if access_token is not None:
                    response_data = {'message': {'refresh_token': str(refresh_token), 'userId': username, 'name': name,
                                                 'role': role}}
                    response = JsonResponse(response_data)
                    response.set_cookie('access_token', str(access_token), expires=access_expi, httponly=True,
                                        samesite='Strict')
                    response["Access-Control-Allow-Credentials"] = "true"
                    return response, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

# @csrf_protect
def select_logout(request):
    if request.method == "POST":
        access_token = request.COOKIES.get('access_token')

        sSTM = """
            DELETE FROM WBZE
            WHERE ZEACCESSTOKEN = %s
        """

        with connection.cursor() as cursor:
            cursor.execute(sSTM, [access_token])

        response_data = {'message': '登出成功'}
        response = JsonResponse(response_data)
        response.delete_cookie('access_token')
        response["Access-Control-Allow-Origin"] = strCORS_URL
        response["Access-Control-Allow-Credentials"] = "true"
        return response, status.HTTP_200_OK