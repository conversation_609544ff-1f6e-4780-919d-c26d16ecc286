import itertools

import cx_<PERSON>
import datetime
import calendar

from django.db import connection, IntegrityError, transaction
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import to_minguo, paginate_data, get_previous_months_minguo
from utils.token_utils import validate_access_token_and_params_with_pagination, \
    get_pagination_params, validate_access_token_and_params

''' Invoice Start  '''
psbf_main_invoice_sql = """
    SELECT BFYYMM, BFVENN, ADNAM2, BFROUT, BFTYP1, BFTYP1_NAME, BFTYP2, BFTYP2_NAME,
           BFBSNO, BFIVNM, BFDAT1, 
           BFITNO, BFSQTY, BFAMTE, BFPQTY, BFIQTY, BFRQTY, BFCQTY, BFPRIC, BFIPRC, 
           BFIAMT, BFSAMT, BFPRDT
      FROM (SELECT M.BFYYMM, <PERSON><PERSON>, <PERSON><PERSON>BFRO<PERSON>,
                   BFTYP1,
                   CASE
                       WHEN BFTYP1 = 1
                           THEN '本島'
                       WHEN BFTYP1 = 2
                           THEN '外島'
                       ELSE '例外'
                   END BFTYP1_NAME,
                   BFTYP2,
                   CASE
                       WHEN BFTYP2 = 'H'
                           THEN '飲料'
                       WHEN BFTYP2 = 'C'
                           THEN '全聯'
                       ELSE ''
                   END BFTYP2_NAME,
                   BFBSNO, BFIVNM, BFDAT1, BFITNO,
                   BFSQTY, BFAMTE, BFPQTY, BFIQTY, BFRQTY, BFCQTY, BFPRIC, BFIPRC, BFIAMT, BFSAMT, 
                   TO_CHAR(BFPRDT, 'YYYY/MM/DD HH24:MI:SS') BFPRDT
              FROM PSBF_M@HY_1 M, PSBF_D@HY_1 D
             WHERE M.BFYYMM = D.BFYYMM AND M.BFVENN = D.BFVENN AND M.BFROUT = D.BFROUT), ASAD@HY_1
     WHERE ADVENN = BFVENN
"""

''' Invoice End  '''

# SELECT Invoice START
@validate_access_token_and_params_with_pagination(None)
def select_hy1_psbf_main_invoice_detail(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_hy1_psbf_main_invoice_detail"

    if request.method == "POST":

        # 得到6個月前的月份和年份
        start_month_minguo, end_month_minguo= get_previous_months_minguo(6)

        # 初始化
        sql_base = psbf_main_invoice_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 若沒有提供日期範圍，則使用本月份的第一天和最後一天
        sql_conditions.append(f"BFYYMM BETWEEN :qEXECUTE_START_MONTH AND :qEXECUTE_END_MONTH")

        if 'dealers_type' in json_data:
            values_list = json_data['dealers_type']
            placeholders = ', '.join([f':qBFROUT_{i}' for i in range(len(values_list))])
            sql_conditions.append(f' BFROUT IN ({placeholders}) ')
            for i, value in enumerate(values_list):
                params[f'qBFROUT_{i}'] = value
                params2[f'qBFROUT_{i}'] = value

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "BFVENN",
        }

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" BFVENN = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        if 'start_month' in json_data and json_data['start_month'] is not None and \
                'end_month' in json_data and json_data['end_month'] is not None:
            params['qEXECUTE_START_MONTH'] = to_minguo(json_data['start_month'], year_and_month_only=True)
            params['qEXECUTE_END_MONTH'] = to_minguo(json_data['end_month'], year_and_month_only=True)
            params2['qEXECUTE_START_MONTH'] = to_minguo(json_data['start_month'], year_and_month_only=True)
            params2['qEXECUTE_END_MONTH'] = to_minguo(json_data['end_month'], year_and_month_only=True)
        else:
            # 若前端沒有提供日期條件，則使用預設日期
            params['qEXECUTE_START_MONTH'] = start_month_minguo
            params['qEXECUTE_END_MONTH'] = end_month_minguo
            params2['qEXECUTE_START_MONTH'] = start_month_minguo
            params2['qEXECUTE_END_MONTH'] = end_month_minguo

        # 構建完整的 SQL 查詢
        sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} ORDER BY BFYYMM DESC, BFVENN, BFROUT, BFITNO"

        ranked_sql = """
                    SELECT BFYYMM, BFVENN, ADNAM2, BFROUT, BFTYP1, BFTYP1_NAME, BFTYP2, BFTYP2_NAME, 
                           BFBSNO, BFIVNM, BFDAT1, 
                           BFITNO, BFSQTY, BFAMTE, BFPQTY, BFIQTY, BFRQTY, BFCQTY, BFPRIC, BFIPRC, 
                           BFIAMT, BFSAMT, BFPRDT,
                           DENSE_RANK() OVER (ORDER BY BFYYMM DESC, BFVENN, BFROUT) AS RNK
                      FROM (  """ + sql_query + """  )
                     WHERE 1 = 1
                """

        ranked_sql1 = """
                    SELECT BFYYMM, BFVENN, ADNAM2, BFROUT, BFTYP1, BFTYP1_NAME, BFTYP2, BFTYP2_NAME,
                           BFBSNO, BFIVNM, BFDAT1, 
                           BFITNO, BFSQTY, BFAMTE, BFPQTY, BFIQTY, BFRQTY, BFCQTY, BFPRIC, BFIPRC, 
                           BFIAMT, BFSAMT, BFPRDT, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size
                """

        ranked_sql2 = """ 
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(sql_query, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if len(data) == 0:
                    return {"results": []}, status.HTTP_200_OK

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行資料轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]

                # 依分組資料
                grouped_by_column  = [
                    list(group)
                    for _, group in itertools.groupby(data, key=lambda x: (x['BFYYMM'], x['BFVENN'], x['BFROUT']))
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    # if not total_data or total_data[0][0] is None:
                    #     return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_invoice_master_detail(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

def generate_invoice_master_detail(data, start_index=0):
    report = []
    for track_check, group in itertools.groupby(data, lambda x: (x['BFYYMM'], x['BFVENN'], x['BFROUT'])):
        group = list(group)  # 將分組資料轉換為列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "yyyymm": group[0]["BFYYMM"],  # 年月
            "dealer_code": group[0]["BFVENN"],  # 經銷商代碼
            "dealer_name": group[0]["ADNAM2"],  # 經銷商名稱
            "issued_date": group[0]["BFDAT1"],  # 開立日期
            "issued_function_type": group[0]["BFTYP1"],  # 開立函別
            "issued_function_type_name": group[0]["BFTYP1_NAME"],  # 開立函別名稱
            "invoice_type": group[0]["BFTYP2"],  # 發貨單別
            "invoice_type_name": group[0]["BFTYP2_NAME"],  # 發貨單別名稱
            "total_amount": int(sum(item["BFIAMT"] for item in group)),  # 合計金額
            "subsidy_amount": int(sum(item["BFSAMT"] for item in group)),  # 補助金額
            "print_date_time": group[0]["BFPRDT"],  # 列印日期
        }

        details = []
        for master, master_group in itertools.groupby(group, lambda x: x["BFVENN"]):
            master_group = list(master_group)

            detail = {
                "index": str(len(details) + 1),
                "invoice_file_type": master_group[0]["BFTYP1"],  # 發票檔種類
                "invoice_name": master_group[0]["BFIVNM"],  # 發票抬頭
                "yyyymm": master_group[0]["BFYYMM"],  # 年月
                "invoice_issued_date": master_group[0]["BFDAT1"],  # 發票開立日
                "subsidy_amount": int(sum(item["BFSAMT"] for item in master_group)),  # 補助金額
                "total_qty": len(master_group),  # 明細項目數
                "total_amount": int(sum(item["BFIAMT"] for item in master_group)),  # 正項總計
                "negative_total_amount": int(sum(item["BFIAMT"] for item in master_group if item["BFIAMT"] < 0)),  # 負項總計
            }

            product_details = []
            for product, product_group in itertools.groupby(master_group, lambda x: x["BFITNO"]):
                product_group = list(product_group)

                product_detail = {
                    "index": str(len(product_details) + 1),
                    "yyyymm": product_group[0]["BFYYMM"],  # 年月
                    "dealer_code": group[0]["BFVENN"],  # 經銷商代碼
                    "issued_function_type": group[0]["BFTYP1"],
                    "product_code": product_group[0]["BFITNO"],         # 產品代號
                    "last_month_inventory": product_group[0]["BFPQTY"], # 上月存貨
                    "this_month_incoming_goods": product_group[0]["BFIQTY"], # 本月進貨
                    "this_month_sales": product_group[0]["BFSQTY"], # 本月銷貨
                    "this_month_returns": product_group[0]["BFRQTY"], # 本月退貨
                    "this_month_inventory": product_group[0]["BFCQTY"], # 本月存貨
                    "contract_unit_price": product_group[0]["BFIPRC"], # 合約單價
                    "contract_amount": product_group[0]["BFIAMT"], # 合約金額
                    "invoice_unit_price": product_group[0]["BFPRIC"], # 發票單價
                    "invoice_amount": product_group[0]["BFAMTE"], # 發票金額
                    "subsidy_amount": product_group[0]["BFSAMT"] # 發票金額
                }

                product_details.append(product_detail)

            # 添加總計到child_details
            product_details.append({
                "index": ".",
                "yyyymm": group[0]["BFYYMM"],
                "dealer_code": group[0]["BFVENN"],
                "issued_function_type": group[0]["BFTYP1"],
                "product_code": "總計",
                "last_month_inventory": sum(item["last_month_inventory"] for item in product_details),
                "this_month_incoming_goods": sum(item["this_month_incoming_goods"] for item in product_details),
                "this_month_sales": sum(item["this_month_sales"] for item in product_details),
                "this_month_returns": sum(item["this_month_returns"] for item in product_details),
                "this_month_inventory": sum(item["this_month_inventory"] for item in product_details),
                "contract_unit_price": sum(item["contract_unit_price"] for item in product_details),
                "contract_amount": sum(item["contract_amount"] for item in product_details),
                "invoice_unit_price": ".",
                "invoice_amount": sum(item["invoice_amount"] for item in product_details),
                "subsidy_amount": sum(item["subsidy_amount"] for item in product_details)
            })

            detail["child_details"] = product_details
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report

# SELECT Invoice END

''' UPDATE Invoice START '''
def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "dealers_code": "BFVENN",
        "yyyymm": "BFYYMM",
        "issued_function_type": "BFTYP1",
        "invoice_type": "BFTYP2",
    }
    return mapping.get(key)


def update_with_raw_sql(context, data_list):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                for data in data_list:

                    # 構造 WHERE 子句部分
                    where_parts = [
                        f"BFVENN = '{data['dealers_code']}'",
                        f"BFYYMM = '{data['yyyymm']}'",
                        f"BFTYP1 = '{data['issued_function_type']}'",
                        f"BFTYP2 = '{data['invoice_type']}'"
                    ]

                    # 處理 dealers_type 數組
                    if 'dealers_type' in data and data['dealers_type']:
                        dealers_type_conditions = [f"BFROUT = '{dealer_type}'" for dealer_type in data['dealers_type']]
                        where_parts.append(f"({' OR '.join(dealers_type_conditions)})")

                    # 檢查BFPRDT是否已有值
                    sql = f"SELECT BFPRDT FROM PSBF_M@HY_1 WHERE {' AND '.join(where_parts)}"
                    cursor.execute(sql)

                    # 如果BFPRDT已有值，則不更新
                    if cursor.fetchone()[0] is not None:
                        continue

                    # 生成SET子句部分，僅更新 print_date_time 字段
                    set_parts = ["BFPRDT = SYSDATE"]

                    # 如果有要更新的字段
                    if set_parts:
                        set_string = ", ".join(set_parts)
                        where_string = " AND ".join(where_parts)

                        # 構造並執行 SQL 更新語句
                        sql = f"UPDATE PSBF_M@HY_1 SET {set_string} WHERE {where_string}"
                        cursor.execute(sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('dealers_type', 'yyyymm', 'dealers_type', 'issued_function_type', 'invoice_type')
def update_hy1_psbf_main_invoice_detail(request, json_data, role, user_id):
    context = "update_hy1_psbf_main_invoice_detail"

    if request.method == "POST":

        try:
            result, result_status = update_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

''' UPDATE Invoice END '''