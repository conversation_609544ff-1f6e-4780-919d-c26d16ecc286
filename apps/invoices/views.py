import codecs
import mimetypes
import os

import chardet as chardet
from django.http import JsonResponse, FileResponse, HttpResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.invoices.InvoiceInfo import select_hy1_psbf_main_invoice_detail, update_hy1_psbf_main_invoice_detail
from apps.invoices.models import mainInvoice

INVOICE_ACTIONS = {
    'hy1_psbf_main_invoice': {
        'select': select_hy1_psbf_main_invoice_detail,
        'update': update_hy1_psbf_main_invoice_detail,
    },
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )


class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = mainInvoice.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = INVOICE_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 查詢福總訂單
    @action(detail=False, methods=['post'])
    def hy1_psbf_main_invoice_detail(self, request):
        return self._handle_action('hy1_psbf_main_invoice', 'select')

    # 更新福總訂單
    @action(detail=False, methods=['post'])
    def update_hy1_psbf_main_invoice_detail(self, request):
        return self._handle_action('hy1_psbf_main_invoice', 'update')