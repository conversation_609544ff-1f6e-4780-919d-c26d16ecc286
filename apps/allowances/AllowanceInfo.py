import itertools
import json
import logging
import math

import cx_Oracle
import datetime
import calendar

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data, get_previous_months_minguo, to_minguo
from utils.token_utils import validate_access_token_and_params_with_pagination, \
    get_pagination_params

''' Balance of Sales Allowance Start '''
# 檢查臨時表是否存在,如果不存在則創建
select_temp_table_sql = """
    BEGIN
        EXECUTE IMMEDIATE 'CREATE GLOBAL TEMPORARY TABLE TEMP_SDW503 ON COMMIT PRESERVE ROWS AS
            SELECT * FROM SDW503@B2B WHERE 1 = 0';
        EXCEPTION
            WHEN OTHERS THEN
                IF SQLCODE != -955 THEN -- 如果錯誤不是因為表已存在
                    RAISE;
                END IF;
    END;
"""

# 清空臨時表中的數據，新增數據
insert_temp_table_sql = """
    BEGIN
        EXECUTE IMMEDIATE 'TRUNCATE TABLE TEMP_SDW503';

        INSERT INTO TEMP_SDW503 (AGENT_CODE503, AGENT_NAME503, DISC_GROUP503, DISC_GROUP_NAME503, SEQ503, SEQA503,
                                 CONFIRM_DATE503, DISC_KEY503, DISC_DISCR503, SLIP_NO503, SDF200_SEQ503, A_REST_AMT503,
                                 C_REST_AMT503, B_REST_AMT503, REST_AMT503, DEPT200, DEPT_DESC_C007)
    -- 1. 本期銷貨折讓前期有餘額
        SELECT AGENT_CODE200, AGENT_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, '1', '1', CONFIRM_DATE200, DISC_KEY200,
               DISC_DISCR200, SLIP_NO200, SDF200_SEQ200, A_REST_AMT200, C_REST_AMT200, B_REST_AMT200, REST_AMT200,
               DEPT200, DEPT_DESC_C007
          FROM (SELECT A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025 AGENT_CODE200,
                       A3.BRIEF_NAME025 AGENT_NAME200, A1.OCF025_SEQ200, A4.CODE_ID003 DISC_GROUP200,
                       A4.DESC_C003 DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A5.KEY001 DISC_KEY200,
                       A5.DISCR001 DISC_DISCR200, A1.AMT200 A_REST_AMT200, A1.AMT200 C_REST_AMT200,
                       SUM(A2.WRITE_OFF_AMT201) B_REST_AMT200,
                       NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) REST_AMT200,
                       A1.DEPT200, B.DEPT_DESC_C007
                  FROM SDF200@B2B A1, SDF201@B2B A2, OCF025@B2B A3, OCF003@B2B A4, SDF001@B2B A5, HRF007@B2B B
                 WHERE A1.KEY_TYPE200 = 'A' AND A1.SDF200_SEQ200 = A2.SDF200_SEQ201 (+)
                   AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A1.OCF025_SEQ200 = A3.OCF025_SEQ025
                   AND A1.DISC_GROUP_OCF003_SEQ200 = A4.OCF003_SEQ003 (+) AND A1.SDF001_SEQ200 = A5.SDF001_SEQ001 (+) AND (
                     :V_EORDER = 'A' OR (:V_EORDER != 'A' AND NVL(A3.EOS_CODE025, 'N') = :V_EORDER))
                   AND A1.DEPT200 = B.DEPT_CODE007 (+)
                 GROUP BY A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025, A3.BRIEF_NAME025,
                          A1.OCF025_SEQ200, A4.CODE_ID003, A4.DESC_C003, A1.CONFIRM_DATE200, A5.KEY001, A5.DISCR001,
                          A1.AMT200, A1.DEPT200, B.DEPT_DESC_C007) A
         WHERE REST_AMT200 != 0 AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_DISC_GROUP, A.DISC_GROUP200)) || '''',
                                          '''' || A.DISC_GROUP200 || '''') > 0
           AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_AGENT_CODE, A.AGENT_CODE200)) || '''',
                     '''' || A.AGENT_CODE200 || '''') > 0;--折讓分攤前期有餘額


        INSERT INTO TEMP_SDW503 (AGENT_CODE503, AGENT_NAME503, DISC_GROUP503, DISC_GROUP_NAME503, SEQ503, SEQA503,
                                 CONFIRM_DATE503, DISC_KEY503, DISC_DISCR503, SLIP_NO503, SDF200_SEQ503, A_REST_AMT503,
                                 C_REST_AMT503, B_REST_AMT503, REST_AMT503, DEPT200, DEPT_DESC_C007)
    -- 2. 前期銷貨折讓本期有餘額
        SELECT AGENT_CODE200, AGENT_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, '1', '1', CONFIRM_DATE200, DISC_KEY200,
               DISC_DISCR200, SLIP_NO200, SDF200_SEQ200, A_REST_AMT200, C_REST_AMT200, B_REST_AMT200, (-1) * REST_AMT200,
               DEPT200, DEPT_DESC_C007
          FROM (SELECT A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025 AGENT_CODE200,
                       A3.BRIEF_NAME025 AGENT_NAME200, A1.OCF025_SEQ200, A4.CODE_ID003 DISC_GROUP200,
                       A4.DESC_C003 DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A5.KEY001 DISC_KEY200,
                       A5.DISCR001 DISC_DISCR200, 0 A_REST_AMT200, 0 C_REST_AMT200,
                       NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) B_REST_AMT200,
                       NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) REST_AMT200,
                       A1.DEPT200, B.DEPT_DESC_C007
                  FROM SDF200@B2B A1, SDF201@B2B A2, OCF025@B2B A3, OCF003@B2B A4, SDF001@B2B A5, HRF007@B2B B
                 WHERE A1.KEY_TYPE200 = 'B' AND A1.SDF200_SEQ200 = A2.B_SDF200_SEQ201 (+)
                   AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A1.OCF025_SEQ200 = A3.OCF025_SEQ025
                   AND A1.DISC_GROUP_OCF003_SEQ200 = A4.OCF003_SEQ003 (+) AND A1.SDF001_SEQ200 = A5.SDF001_SEQ001 (+) AND (
                     :V_EORDER = 'A' OR (:V_EORDER != 'A' AND NVL(A3.EOS_CODE025, 'N') = :V_EORDER))
                   AND A1.DEPT200 = B.DEPT_CODE007 (+)
                 GROUP BY A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025, A3.BRIEF_NAME025,
                          A1.OCF025_SEQ200, A4.CODE_ID003, A4.DESC_C003, A1.CONFIRM_DATE200, A5.KEY001, A5.DISCR001,
                          A1.AMT200, A1.DEPT200, B.DEPT_DESC_C007) A
         WHERE REST_AMT200 != 0 AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_DISC_GROUP, A.DISC_GROUP200)) || '''',
                                          '''' || A.DISC_GROUP200 || '''') > 0
           AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_AGENT_CODE, A.AGENT_CODE200)) || '''',
                     '''' || A.AGENT_CODE200 || '''') > 0;--前期銷貨折讓本期有餘額


        INSERT INTO TEMP_SDW503 (AGENT_CODE503, AGENT_NAME503, DISC_GROUP503, DISC_GROUP_NAME503, SEQ503, SEQA503,
                                 CONFIRM_DATE503, DISC_KEY503, DISC_DISCR503, SLIP_NO503, SDF200_SEQ503, A_REST_AMT503,
                                 C_REST_AMT503, B_REST_AMT503, REST_AMT503, DEPT200, DEPT_DESC_C007)
    -- 3. 本期銷貨折讓本期有餘額
        SELECT AGENT_CODE200, AGENT_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, '2', '2', CONFIRM_DATE200, DISC_KEY200,
               DISC_DISCR200 || '(前期未沖)' DISC_DISCR200, SLIP_NO200, SDF200_SEQ200, 0 A_REST_AMT200, C_REST_AMT200,
               B_REST_AMT200, (-1) * B_REST_AMT200 REST_AMT200, DEPT200, DEPT_DESC_C007
          FROM (SELECT A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025 AGENT_CODE200,
                       A3.BRIEF_NAME025 AGENT_NAME200, A1.OCF025_SEQ200, A4.CODE_ID003 DISC_GROUP200,
                       A4.DESC_C003 DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A5.KEY001 DISC_KEY200,
                       A5.DISCR001 DISC_DISCR200, NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) A_REST_AMT200,
                       0 C_REST_AMT200, 0 B_REST_AMT200, 0 REST_AMT200, A1.DEPT200, B.DEPT_DESC_C007
                  FROM SDF200@B2B A1, SDF201@B2B A2, OCF025@B2B A3, OCF003@B2B A4, SDF001@B2B A5, HRF007@B2B B
                 WHERE A1.KEY_TYPE200 = 'A' AND A1.SDF200_SEQ200 = A2.SDF200_SEQ201 (+)
                   AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')
                   AND A1.OCF025_SEQ200 = A3.OCF025_SEQ025
                   AND A1.DISC_GROUP_OCF003_SEQ200 = A4.OCF003_SEQ003 (+) AND A1.SDF001_SEQ200 = A5.SDF001_SEQ001 (+) AND (
                     :V_EORDER = 'A' OR (:V_EORDER != 'A' AND NVL(A3.EOS_CODE025, 'N') = :V_EORDER))
                   AND A1.DEPT200 = B.DEPT_CODE007 (+)
                 GROUP BY A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025, A3.BRIEF_NAME025,
                          A1.OCF025_SEQ200, A4.CODE_ID003, A4.DESC_C003, A1.CONFIRM_DATE200, A5.KEY001, A5.DISCR001,
                          A1.AMT200, A1.DEPT200, B.DEPT_DESC_C007) A
         WHERE A_REST_AMT200 != 0 AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_DISC_GROUP, A.DISC_GROUP200)) || '''',
                                            '''' || A.DISC_GROUP200 || '''') > 0
           AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_AGENT_CODE, A.AGENT_CODE200)) || '''',
                     '''' || A.AGENT_CODE200 || '''') > 0;

        UPDATE TEMP_SDW503 A
           SET (B_REST_AMT503, REST_AMT503)=(SELECT SUM(WRITE_OFF_AMT201), (-1) * SUM(WRITE_OFF_AMT201)
                                               FROM SDF201@B2B B
                                              WHERE A.SDF200_SEQ503 = B.SDF200_SEQ201
                                                AND B.WRITE_OFF_DATE201 (+) BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.99)
         WHERE SEQ503 = '2' AND SEQA503 = '2' AND EXISTS (SELECT 'x'
                                                            FROM SDF201@B2B B
                                                           WHERE A.SDF200_SEQ503 = B.SDF200_SEQ201
                                                             AND B.WRITE_OFF_DATE201 (+) BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.99);

        DELETE FROM TEMP_SDW503 A WHERE SEQ503 = '2' AND SEQA503 = '2' AND B_REST_AMT503 = 0;--本期銷貨折讓
        INSERT INTO TEMP_SDW503 (AGENT_CODE503, AGENT_NAME503, DISC_GROUP503, DISC_GROUP_NAME503, SEQ503, SEQA503,
                                 CONFIRM_DATE503, DISC_KEY503, DISC_DISCR503, SLIP_NO503, SDF200_SEQ503, A_REST_AMT503,
                                 C_REST_AMT503, B_REST_AMT503, REST_AMT503, DEPT200, DEPT_DESC_C007)
        SELECT AGENT_CODE200, AGENT_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, '2', '1', CONFIRM_DATE200, DISC_KEY200,
               DISC_DISCR200, SLIP_NO200, SDF200_SEQ200, A_REST_AMT200, C_REST_AMT200, B_REST_AMT200, REST_AMT200,
               DEPT200, DEPT_DESC_C007
          FROM (SELECT A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025 AGENT_CODE200,
                       A3.BRIEF_NAME025 AGENT_NAME200, A1.OCF025_SEQ200, A4.CODE_ID003 DISC_GROUP200,
                       A4.DESC_C003 DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A5.KEY001 DISC_KEY200,
                       A5.DISCR001 DISC_DISCR200, A1.AMT200 A_REST_AMT200, A1.AMT200 C_REST_AMT200,--                      SUM(a2.write_off_amt201) b_rest_amt200,
                       NVL(SUM(DECODE(SIGN(NVL(A6.CONFIRM_DATE200, TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')) -
                                           TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')), -1, 0, A2.WRITE_OFF_AMT201)),
                           0) B_REST_AMT200,
                       NVL(A1.AMT200, 0) -
                       NVL(SUM(DECODE(SIGN(NVL(A6.CONFIRM_DATE200, TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')) -
                                           TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')), -1, 0,
                                      A2.WRITE_OFF_AMT201)), 0) REST_AMT200,
                       A1.DEPT200, B.DEPT_DESC_C007
                  FROM SDF200@B2B A1, SDF201@B2B A2, OCF025@B2B A3, OCF003@B2B A4, SDF001@B2B A5, SDF200@B2B A6, HRF007@B2B B
                 WHERE A1.KEY_TYPE200 = 'A' AND A1.SDF200_SEQ200 = A2.SDF200_SEQ201 (+)
                   AND A1.CONFIRM_DATE200 BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.999
                   AND A2.WRITE_OFF_DATE201 (+) BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.999
                   AND A1.OCF025_SEQ200 = A3.OCF025_SEQ025 AND A1.DISC_GROUP_OCF003_SEQ200 = A4.OCF003_SEQ003 (+)
                   AND A1.SDF001_SEQ200 = A5.SDF001_SEQ001 (+) AND A2.B_SDF200_SEQ201 = A6.SDF200_SEQ200 (+) AND (
                     :V_EORDER = 'A' OR (:V_EORDER != 'A' AND NVL(A3.EOS_CODE025, 'N') = :V_EORDER))
                   AND A1.DEPT200 = B.DEPT_CODE007 (+)  
                 GROUP BY A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025, A3.BRIEF_NAME025,
                          A1.OCF025_SEQ200, A4.CODE_ID003, A4.DESC_C003, A1.CONFIRM_DATE200, A5.KEY001, A5.DISCR001,
                          A1.AMT200, A1.DEPT200, B.DEPT_DESC_C007) A
         WHERE INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_DISC_GROUP, A.DISC_GROUP200)) || '''',
                     '''' || A.DISC_GROUP200 || '''') > 0
           AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_AGENT_CODE, A.AGENT_CODE200)) || '''',
                     '''' || A.AGENT_CODE200 || '''') > 0;--折讓分攤本期有餘額

        INSERT INTO TEMP_SDW503 (AGENT_CODE503, AGENT_NAME503, DISC_GROUP503, DISC_GROUP_NAME503, SEQ503, SEQA503,
                                 CONFIRM_DATE503, DISC_KEY503, DISC_DISCR503, SLIP_NO503, SDF200_SEQ503, A_REST_AMT503,
                                 C_REST_AMT503, B_REST_AMT503, REST_AMT503, DEPT200, DEPT_DESC_C007)
        SELECT AGENT_CODE200, AGENT_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, '2', '1', CONFIRM_DATE200, DISC_KEY200,
               DISC_DISCR200, SLIP_NO200, SDF200_SEQ200, A_REST_AMT200, C_REST_AMT200, B_REST_AMT200, (-1) * REST_AMT200,
               DEPT200, DEPT_DESC_C007
          FROM (SELECT A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025 AGENT_CODE200,
                       A3.BRIEF_NAME025 AGENT_NAME200, A1.OCF025_SEQ200, A4.CODE_ID003 DISC_GROUP200,
                       A4.DESC_C003 DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A5.KEY001 DISC_KEY200,
                       A5.DISCR001 DISC_DISCR200, 0 A_REST_AMT200, 0 C_REST_AMT200,
                       NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) B_REST_AMT200,
                       NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) REST_AMT200,
                       A1.DEPT200, B.DEPT_DESC_C007
                  FROM SDF200@B2B A1, SDF201@B2B A2, OCF025@B2B A3, OCF003@B2B A4, SDF001@B2B A5, HRF007@B2B B
                 WHERE A1.KEY_TYPE200 = 'B' AND A1.SDF200_SEQ200 = A2.B_SDF200_SEQ201 (+)
                   AND A1.CONFIRM_DATE200 BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.999
                   AND A2.WRITE_OFF_DATE201 (+) <= TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.999
                   AND A1.OCF025_SEQ200 = A3.OCF025_SEQ025 AND A1.DISC_GROUP_OCF003_SEQ200 = A4.OCF003_SEQ003 (+)
                   AND A1.SDF001_SEQ200 = A5.SDF001_SEQ001 (+) AND (:V_EORDER = 'A' OR
                                                                    (:V_EORDER != 'A' AND NVL(A3.EOS_CODE025, 'N') = :V_EORDER))
                   AND A1.DEPT200 = B.DEPT_CODE007 (+)
                 GROUP BY A1.SDF200_SEQ200, A1.SLIP_NO200, A1.KEY_TYPE200, A3.AGENT_CODE025, A3.BRIEF_NAME025,
                          A1.OCF025_SEQ200, A4.CODE_ID003, A4.DESC_C003, A1.CONFIRM_DATE200, A5.KEY001, A5.DISCR001,
                          A1.AMT200, A1.DEPT200, B.DEPT_DESC_C007) A
         WHERE REST_AMT200 != 0 AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_DISC_GROUP, A.DISC_GROUP200)) || '''',
                                          '''' || A.DISC_GROUP200 || '''') > 0
           AND INSTR('''' || CMK001.CMN0119@B2B(NVL(:P_AGENT_CODE, A.AGENT_CODE200)) || '''',
                     '''' || A.AGENT_CODE200 || '''') > 0;
            -- 確保這裡有提交事務
            COMMIT;
        EXCEPTION
            WHEN OTHERS THEN
                -- 如果有錯誤，輸出錯誤信息
                DBMS_OUTPUT.PUT_LINE('錯誤代碼: ' || SQLCODE || ' 錯誤訊息: ' || SQLERRM);
                -- 可以選擇是否回滾
                ROLLBACK;		
    END;
"""
''' Balance of Sales Allowance End '''

''' AllowanceDetailScheduleA Start  '''
sdv200_main_allowance_detail_schedule_a_sql = """
    SELECT A.AGENT_CODE, A.AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200, A.SEQ, A.SEQA, A.CONFIRM_DATE, A.DISC_KEY200,
           A.DISC_DISCR200, A.DEPT200, A.DEPT_DESC_C007, A.USER200, A.CHI_NAME005, A.A_REST_AMT, A.DESCR200, A.SLIP_NO200,
           A.SDF200_SEQ200
      FROM (SELECT A.AGENT_CODE200 AGENT_CODE, A.AGENT_BRIEF_NAME200 AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
                   '1' SEQ, '1' SEQA, TRUNC(E.CONFIRM_DATE200) CONFIRM_DATE, A.DISC_KEY200, A.DISC_DISCR200, A.DEPT200,
                   B.DEPT_DESC_C007, A.USER200, C.CHI_NAME005,
                   DECODE(A.KEY_TYPE200, 'A', NVL(A.AMT200, 0),
                          -1 * (NVL(E.AMT200, 0) - NVL(E.WRITE_OFF_AMT201, 0))) A_REST_AMT,
                   A.DESCR200, A.SLIP_NO200, A.SDF200_SEQ200
              FROM SDV200@B2B A, HRF007@B2B B, HRF005@B2B C, OCF025@B2B D,
                   (SELECT A.SDF200_SEQ200, A.CONFIRM_DATE200, A.AMT200, SUM(WRITE_OFF_AMT201) WRITE_OFF_AMT201
                      FROM (SELECT A1.SDF200_SEQ200, A1.CONFIRM_DATE200, A1.AMT200, 0 WRITE_OFF_AMT201,
                                   NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) PRE_REST_AMT
                              FROM SDV200@B2B A1, SDF201@B2B A2
                             WHERE A1.SDF200_SEQ200 = A2.SDF200_SEQ201 (+) AND A1.KEY_TYPE200 = 'A'
                               AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                               AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                             GROUP BY A1.SDF200_SEQ200, A1.CONFIRM_DATE200, A1.AMT200) A
                     WHERE NVL(PRE_REST_AMT, 0) != 0
                     GROUP BY A.SDF200_SEQ200, A.CONFIRM_DATE200, A.AMT200
                     UNION ALL
                    SELECT SDF200_SEQ200 SDF200_SEQ200, CONFIRM_DATE200, AMT200, WRITE_OFF_AMT201
                      FROM (SELECT A1.SDF200_SEQ200 SDF200_SEQ200, A1.CONFIRM_DATE200 CONFIRM_DATE200, A1.AMT200,
                                   SUM(A2.WRITE_OFF_AMT201) WRITE_OFF_AMT201
                              FROM SDV200@B2B A1, SDF201@B2B A2
                             WHERE A1.SDF200_SEQ200 = A2.B_SDF200_SEQ201 (+) AND A1.KEY_TYPE200 = 'B'
                               AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                               AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                             GROUP BY A1.SDF200_SEQ200, A1.CONFIRM_DATE200, A1.AMT200)
                     WHERE NVL(AMT200, 0) - NVL(WRITE_OFF_AMT201, 0) != 0) E
             WHERE A.OCF025_SEQ200 = D.OCF025_SEQ025 AND A.SDF200_SEQ200 = E.SDF200_SEQ200
               AND A.DEPT200 = B.DEPT_CODE007 (+) AND A.USER200 = C.NO005 (+)
               AND A.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
             UNION ALL
            SELECT A.AGENT_CODE200 AGENT_CODE, A.AGENT_BRIEF_NAME200 AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
                   '2' SEQ, '1' SEQA, TRUNC(E.CONFIRM_DATE200) CONFIRM_DATE, A.DISC_KEY200,
                   A.DISC_DISCR200 || '(上期未沖)' DISC_DISCR200, A.DEPT200, B.DEPT_DESC_C007, A.USER200, C.CHI_NAME005,
                   E.AMT200 A_REST_AMT, A.DESCR200, A.SLIP_NO200, A.SDF200_SEQ200
              FROM SDV200@B2B A, HRF007@B2B B, HRF005@B2B C, OCF025@B2B D,
                   (SELECT A.SDF200_SEQ200, A.CONFIRM_DATE200, A.AMT200, SUM(WRITE_OFF_AMT201) WRITE_OFF_AMT201
                      FROM (SELECT A1.SDF200_SEQ200, A1.CONFIRM_DATE200,
                                   NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) AMT200,
                                   NVL(SUM(A2.WRITE_OFF_AMT201), 0) WRITE_OFF_AMT201, 0 PRE_REST_AMT
                              FROM SDV200@B2B A1, SDF201@B2B A2
                             WHERE A1.SDF200_SEQ200 = A2.SDF200_SEQ201 (+) AND A1.KEY_TYPE200 = 'A'
                               AND A1.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                               AND A2.WRITE_OFF_DATE201 (+) < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
                             GROUP BY A1.SDF200_SEQ200, A1.CONFIRM_DATE200, A1.AMT200) A
                     WHERE NVL(AMT200, 0) != 0
                       AND EXISTS (SELECT 'x'
                                     FROM SDF201@B2B B
                                    WHERE A.SDF200_SEQ200 = B.SDF200_SEQ201
                                      AND B.WRITE_OFF_DATE201 BETWEEN TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD') AND TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99)
                     GROUP BY A.SDF200_SEQ200, A.CONFIRM_DATE200, A.AMT200) E
             WHERE A.OCF025_SEQ200 = D.OCF025_SEQ025 (+) AND A.SDF200_SEQ200 = E.SDF200_SEQ200
               AND A.DEPT200 = B.DEPT_CODE007 (+) AND A.USER200 = C.NO005 (+)
               AND A.CONFIRM_DATE200 < TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD')
             UNION ALL
            SELECT A.AGENT_CODE200 AGENT_CODE, A.AGENT_BRIEF_NAME200 AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
                   '2' SEQ, '1' SEQA, TRUNC(A.CONFIRM_DATE200) CONFIRM_DATE, A.DISC_KEY200, A.DISC_DISCR200, A.DEPT200,
                   B.DEPT_DESC_C007, A.USER200, C.CHI_NAME005,
                   DECODE(A.KEY_TYPE200, 'A', 1, 0) * NVL(A.AMT200, 0) A_REST_AMT, A.DESCR200, A.SLIP_NO200, A.SDF200_SEQ200
              FROM SDV200@B2B A, HRF007@B2B B, HRF005@B2B C, OCF025@B2B D
             WHERE A.KEY_TYPE200 = 'A' AND A.DEPT200 = B.DEPT_CODE007 (+) AND A.USER200 = C.NO005 (+)
               AND A.OCF025_SEQ200 = D.OCF025_SEQ025
               AND A.CONFIRM_DATE200 BETWEEN TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD') AND TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
             UNION ALL
            SELECT A.AGENT_CODE200 AGENT_CODE, A.AGENT_BRIEF_NAME200 AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
                   '2' SEQ, '2' SEQA, TRUNC(A.CONFIRM_DATE200) CONFIRM_DATE, A.DISC_KEY200, A.DISC_DISCR200, A.DEPT200,
                   B.DEPT_DESC_C007, A.USER200, C.CHI_NAME005, DECODE(A.KEY_TYPE200, 'B', -1, 0) * A.REST_AMT200 A_REST_AMT,
                   A.DESCR200 || A.SLIP_NO200 DESCR200, A.SLIP_NO200, A.SDF200_SEQ200
              FROM (SELECT A1.SDF200_SEQ200, A1.AGENT_CODE200, A1.AGENT_BRIEF_NAME200, A1.OCF025_SEQ200, A1.DISC_GROUP200,
                           A1.DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A1.DISC_KEY200, A1.DISC_DISCR200, A1.DEPT200,
                           A1.USER200, A1.KEY_TYPE200, A1.DESCR200, A1.SLIP_NO200, A1.AMT200,
                           NVL(A1.AMT200, 0) - NVL(SUM(A2.WRITE_OFF_AMT201), 0) REST_AMT200
                      FROM SDV200@B2B A1, SDF201@B2B A2
                     WHERE A1.SDF200_SEQ200 = A2.B_SDF200_SEQ201 (+) AND A1.KEY_TYPE200 = 'B'
                       AND A1.CONFIRM_DATE200 BETWEEN TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD') AND TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
                       AND A2.WRITE_OFF_DATE201 (+) <= TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
                     GROUP BY A1.SDF200_SEQ200, A1.AGENT_CODE200, A1.AGENT_BRIEF_NAME200, A1.OCF025_SEQ200,
                              A1.DISC_GROUP200, A1.DISC_GROUP_NAME200, A1.CONFIRM_DATE200, A1.DISC_KEY200, A1.DISC_DISCR200,
                              A1.DEPT200, A1.USER200, A1.KEY_TYPE200, A1.DESCR200, A1.SLIP_NO200, A1.AMT200) A,
                   HRF007@B2B B, HRF005@B2B C, OCF025@B2B D
             WHERE A.KEY_TYPE200 = 'B' AND A.DEPT200 = B.DEPT_CODE007 (+) AND A.USER200 = C.NO005 (+)
               AND A.OCF025_SEQ200 = D.OCF025_SEQ025 AND NVL(A.REST_AMT200, 0) != 0
               AND A.CONFIRM_DATE200 BETWEEN TO_DATE(:P_BEGINDATE, 'YYYY/MM/DD') AND TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
             UNION ALL
            SELECT A.AGENT_CODE200 AGENT_CODE, A.AGENT_BRIEF_NAME200 AGENT_NAME, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
                   '3' SEQ, '1' SEQA, TO_DATE(NULL) CONFIRM_DATE, TO_CHAR(NULL) DISC_KEY200, '本期餘額:' DISC_DISCR200,
                   TO_CHAR(NULL) DEPT200, TO_CHAR(NULL) DEPT_DESC_C007, TO_CHAR(NULL) USER200, TO_CHAR(NULL) CHI_NAME005,
                   SUM(DECODE(A.KEY_TYPE200, 'A', 1, 'B', -1, 0) * NVL(A.AMT200, 0)) A_REST_AMT, TO_CHAR(NULL) DESCR200,
                   TO_CHAR(NULL) SLIP_NO200, 0 SDF200_SEQ200
              FROM SDV200@B2B A, OCF025@B2B D
             WHERE A.OCF025_SEQ200 = D.OCF025_SEQ025 AND A.CONFIRM_DATE200 <= TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
             GROUP BY A.AGENT_CODE200, A.AGENT_BRIEF_NAME200, A.DISC_GROUP200, A.DISC_GROUP_NAME200) A,
           (SELECT A.AGENT_CODE200, A.DISC_GROUP200,
                   SUM(DECODE(A.KEY_TYPE200, 'A', 1, 'B', -1, 0) * NVL(A.AMT200, 0)) A_REST_AMT
              FROM SDV200@B2B A, OCF025@B2B D
             WHERE A.OCF025_SEQ200 = D.OCF025_SEQ025 AND A.CONFIRM_DATE200 <= TO_DATE(:P_ENDDATE, 'YYYY/MM/DD') + 0.99
             GROUP BY A.AGENT_CODE200, A.DISC_GROUP200) B
     WHERE A.AGENT_CODE = B.AGENT_CODE200 AND A.DISC_GROUP200 = B.DISC_GROUP200 AND (
         (:P_PRINT_OPTION = '1' AND B.A_REST_AMT < 0) OR NVL(:P_PRINT_OPTION, '0') = 0)
"""


def sdv200_main_allowance_detail_schedule_a_sql2(seq, cond):
    return f"""
    SELECT SDF200_SEQ201, SLIP_NO200, SEQ, TO_CHAR(CONFIRM_DATE, 'YYYY/MM/DD') CONFIRM_DATE, 
           DISC_KEY200, DISC_DISCR200, INVOICE_DATE220,
           SUM(A_REST_AMT) A_REST_AMT, TRACK_INVOICE220, DESCR200
      FROM (SELECT G.SDF200_SEQ201 SDF200_SEQ201, A.SLIP_NO200 SLIP_NO200, '{seq}' SEQ, A.CONFIRM_DATE200 CONFIRM_DATE,
                   NVL(F.INVOICE_DATE220, A.CONFIRM_DATE200) INVOICE_DATE220, H.CODE_ID003 DISC_KEY200,
                   I.KEY001 DISC_DISCR200,
                   TRUNC(DECODE(A.BTYPE200, '1',
                                DECODE(SIGN(A.CREATE_DATE200 - G.WRITE_OFF_DATE201), 1, G.WRITE_OFF_DATE201,
                                       A.CREATE_DATE200), G.WRITE_OFF_DATE201), 'HH') CREATE_DATE, E.OCF210_SEQ210,
                   NVL(F.TRACK_INVOICE220, A.SLIP_NO200) TRACK_INVOICE220, A.DESCR200, G.WRITE_OFF_AMT201 A_REST_AMT,
                   A.AMT200 AMT200
              FROM SDF200@B2B A, OCF025@B2B D, OCF210@B2B E, OCF220@B2B F, SDF201@B2B G, OCF003@B2B H, SDF001@B2B I
             WHERE G.SDF200_SEQ201 = : SDF200_SEQ1 AND A.KEY_TYPE200 = 'B' AND A.OCF025_SEQ200 = D.OCF025_SEQ025
               AND A.OCF210_SEQ200 = E.OCF210_SEQ210 (+) AND E.OCF220_SEQ210 = F.OCF220_SEQ220 (+)
               AND A.DISC_GROUP_OCF003_SEQ200 = H.OCF003_SEQ003 (+) AND A.SDF001_SEQ200 = I.SDF001_SEQ001 (+) """ + cond + """
     GROUP BY SDF200_SEQ201, SEQ, SLIP_NO200, CONFIRM_DATE, INVOICE_DATE220, TRACK_INVOICE220, DESCR200, CREATE_DATE,
              OCF210_SEQ210, DISC_KEY200, DISC_DISCR200
     ORDER BY CREATE_DATE, OCF210_SEQ210, SLIP_NO200, TRACK_INVOICE220
"""


''' AllowanceDetailScheduleA End  '''

''' AllowanceDetailScheduleB Start  '''
ocv211_214_main_allowance_detail_schedule_b_sql = """
    SELECT E.TRACK_INVOICE220,
           C.PRODUCT_ID211, C.PRODUCT_NAME211,
           C.PRODUCT_MARK211, C.MARK_NAME211,
           SUM(NVL(C.BQTY211, 0)) BQTY211, SUM(NVL(A.DISC_QTY214, 0)) DISC_QTY214,
           G.PROMOTE_ID040, A.DISC_TYPE_ID214,
           A.DISC_TYPE_ID_NAME214, SUM(NVL(A.DISC_AMT214, 0)) DISC_AMT214,
           TO_CHAR (D.DELIVERY_DATE210, 'YYYY/MM/DD') DELIVERY_DATE210, 
           TO_CHAR (E.INVOICE_DATE220, 'YYYY/MM/DD') INVOICE_DATE220,
           A.AGENT_CODE214, A.AGENT_BRIEF_NAME214
      FROM OCV214@B2B A, OCV211@B2B C,
           OCF210@B2B D, OCF220@B2B E,
           OCF041@B2B F, OCF040@B2B G
     WHERE A.DISC_TYPE214 = '1' --產品折讓
       AND A.OCF211_SEQ214 = C.OCF211_SEQ211
       AND C.OCF210_SEQ211 = D.OCF210_SEQ210
       AND D.OCF220_SEQ210 = E.OCF220_SEQ220
       AND A.OCF041_SEQ214 = F.OCF041_SEQ041
       AND F.OCF040_SEQ041 = G.OCF040_SEQ040
       AND NVL(E.DROP_FLAG220, 'N') = 'N'
"""
''' AllowanceDetailScheduleB End  '''

''' ProofOfSingleAllowance Start  '''
sdv250_main_proof_of_single_allowance_sql = """
    SELECT B.AGENT_CODE250, B.AGENT_BRIEF_NAME250,
           TO_CHAR (B.BELG_DATE250, 'YYYY/MM/DD') BELG_DATE250, B.SLIP_NO250, A.ITEM_NAME251, A.AMT251,
           A.TAX251, B.TYPE_NAME250, C.BUTAX_DESC001,
           NVL(A.QTY251, 0) QTY251
      FROM SDF251@B2B A,
           SDV250@B2B B,
           CMF001@B2B C
     WHERE A.SDF250_SEQ251 = B.SDF250_SEQ250
       AND B.BTAX_FLAG250 = C.BUTAX_CODE001
       AND B.CONFIRM_DATE250 IS NOT NULL
       AND B.STATUS250 IN ('2', '3')
       AND B.TYPE250 IN ('1', '2')
"""
''' ProofOfSingleAllowance End  '''

''' SalesAllowanceNotification Start  '''
sdv200_main_sales_allowance_notification_sql = """
    SELECT A.AGENT_CODE200, A.AGENT_BRIEF_NAME200, A.KEY_TYPE200, A.KEY_TYPE_NAME200, A.DISC_GROUP200, A.DISC_GROUP_NAME200,
           A.DISC_KEY200, A.DISC_DISCR200, TO_CHAR(A.CONFIRM_DATE200, 'YYYY/MM/DD') CONFIRM_DATE200, A.SLIP_NO200,
           NVL(A.AMT200, 0) AMT200, A.DESCR200, A.DEPT200, A.USER200, C.CHI_NAME005,
           TO_CHAR(A.CONFIRM_DATE200 + INTERVAL '10' DAY, 'YYYY/MM/DD') END_DATE
      FROM SDV200@B2B A, HRF007@B2B B, HRF005@B2B C
     WHERE A.DEPT200 = B.DEPT_CODE007 (+) AND A.USER200 = C.NO005 (+)
       AND A.OCF210_SEQ200 IS NULL
"""
''' SalesAllowanceNotification End '''

''' SubsidyForStationVending Start '''
asea_main_subsidy_for_station_vending_sql = """
      WITH RANKED_DATA AS
               (SELECT EAYYMM, EAVENN, ADNAM2, EAITNO, TBNAM2, AFAMTE, QTY, SUBAMTE,
                       DENSE_RANK() OVER (ORDER BY EAYYMM, EAVENN) AS RNK
                  FROM (SELECT EAYYMM, EAVENN, ADNAM2, EAITNO, TBNAM2, AFAMTE, QTY, SUBAMTE
                          FROM (SELECT EAYYMM, EAVENN, EAITNO, AFAMTE, QTY,
                                       ROUND(ROUND(QTY / AFSQTY, 1) * AFAMTE, 0) SUBAMTE
                                  FROM (SELECT EAYYMM, EAVENN,
                                               DECODE(SUBSTR(EAITNO, 9, 5), DBITNO || DBBARC, DBITNO2,
                                                      SUBSTR(EAITNO, 9, 4)) EAITNO,
                                               SUM(EAQTY) QTY
                                          FROM ASEA@HY_1,
                                               (SELECT DBITNO, DBITNO2, DBBARC, DBITNO || DBBARC DBIT FROM ASDB@HY_1)
                                         WHERE EAYYMM = :qEAYYMM AND SUBSTR(EAITNO, 9, 5) = DBIT(+)
                                         GROUP BY EAYYMM, EAVENN, EAITNO, DBITNO, DBITNO2, DBBARC),
                                       (SELECT DISTINCT AFITNO, AFSQTY, AFAMTE
                                          FROM SBAF@HY_1
                                         WHERE AFPMNO = :qAFPMNO AND (AFITNO, AFBGDT) IN (SELECT AFITNO, MAX(AFBGDT)
                                                                                          FROM SBAF@HY_1
                                                                                         WHERE AFPMNO = :qAFPMNO
                                                                                           AND AFBGDT <= :qAFBGDT
                                                                                           AND (AFSTDT IS NULL OR AFSTDT > :qAFSTDT)
                                                                                           AND AFSWCH = '1'
                                                                                         GROUP BY AFITNO)
                                           AND (AFSTDT IS NULL OR AFSTDT > :qAFSTDT) AND AFSWCH = '1')
                                 WHERE EAITNO = AFITNO), ASAD@HY_1, IMTB@HY_1
                         WHERE ADVENN = EAVENN AND TBITNO = EAITNO))

    SELECT EAYYMM, EAVENN, ADNAM2, EAITNO, TBNAM2, AFAMTE, QTY, SUBAMTE, RNK
      FROM RANKED_DATA
     WHERE 1 = 1
"""
''' SubsidyForStationVending End '''

""" 銷貨折讓餘額 START """


@validate_access_token_and_params_with_pagination('due_start_date', 'due_end_date')
def select_erp_hy_sdw503_balance_of_sales_allowance(request, json_data, role, user_id, page_size, page_number,
                                                    start_rnk, end_rnk):
    context = 'select_erp_hy_sdw503_balance_of_sales_allowance'

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}
        params3 = {}

        start_end_date = json_data['due_start_date'] + '_' + json_data['due_end_date']

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params3.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
        }

        params['P_BEGINDATE'] = json_data['due_start_date']
        params['P_ENDDATE'] = json_data['due_end_date']
        params2['P_BEGINDATE'] = json_data['due_start_date']
        params2['P_ENDDATE'] = json_data['due_end_date']

        params['P_DISC_GROUP'] = ''
        params2['P_DISC_GROUP'] = ''

        params['V_EORDER'] = 'Y'
        params2['V_EORDER'] = 'Y'

        params['P_AGENT_CODE'] = json_data['dealers_code']
        params2['P_AGENT_CODE'] = json_data['dealers_code']

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f"P_AGENT_CODE = :P_AGENT_CODE")
            params['P_AGENT_CODE'] = user_id
            params2['P_AGENT_CODE'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        try:
            try:
                with connection.cursor() as cursor:
                    # 檢查臨時表是否存在，如果不存在則創建
                    # print('create_temp_table_sql', select_temp_table_sql)
                    cursor.execute(select_temp_table_sql)

                    # 每次查詢之前，清空臨時表中的數據，並插入更新臨時表中的數據
                    # print('insert_temp_table_sql', insert_temp_table_sql)
                    # print('params', params)
                    cursor.execute(insert_temp_table_sql, params)

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

            result = select_with_raw_sdw503_sql(context, start_end_date, params3, page_number, page_size, start_rnk)

            return result

        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def select_with_raw_sdw503_sql(context, start_end_date, params3, page_number, page_size, start_rnk):
    # 查詢TEMP_SDW503表中的數據
    ranked_sql = f"""
        SELECT '{start_end_date}' BEGIN_END_DATE,
               AGENT_CODE, AGENT_NAME, DISC_GROUP, DISC_GROUP_NAME, SEQ, 
               TO_CHAR(CONFIRM_DATE, 'YYYY/MM/DD') CONFIRM_DATE, DISC_KEY, DISC_DISCR, SLIP_NO200,
               SDF200_SEQ200, A_REST_AMT, B_REST_AMT, REST_AMT, TOTAL_REST_AMT, A2_REST_AMT, B2_REST_AMT, 
               AAAA, DEPT200, DEPT_DESC_C007,
               DENSE_RANK() OVER (ORDER BY AGENT_CODE) AS RNK
          FROM (SELECT AGENT_CODE, AGENT_NAME, DISC_GROUP, DISC_GROUP_NAME, SEQ, CONFIRM_DATE, --更新日期
                       DISC_KEY, DISC_DISCR, SLIP_NO200, SDF200_SEQ200, A_REST_AMT, B_REST_AMT, REST_AMT, TOTAL_REST_AMT,
                       DECODE(SEQ, '2', A_REST_AMT, 0) A2_REST_AMT, DECODE(SEQ, '2', B_REST_AMT, 0) B2_REST_AMT,
                       SUM(A_REST_AMT - NVL(B_REST_AMT, 0))
                       OVER (PARTITION BY AGENT_CODE, DISC_GROUP ORDER BY AGENT_CODE, DISC_GROUP, SEQ, CONFIRM_DATE, DISC_KEY, SLIP_NO200) AAAA,
                       DEPT200, DEPT_DESC_C007
                  FROM (SELECT AGENT_CODE, AGENT_NAME, DISC_GROUP, DISC_GROUP_NAME, SEQ, CONFIRM_DATE, --更新日期
                               DISC_KEY, DISC_DISCR, SLIP_NO200, SDF200_SEQ200, A_REST_AMT, B_REST_AMT, REST_AMT,
                               SUM(REST_AMT) OVER (PARTITION BY AGENT_CODE) AS TOTAL_REST_AMT,
                               DEPT200, DEPT_DESC_C007
                          FROM (SELECT AGENT_CODE503 AGENT_CODE, AGENT_NAME503 AGENT_NAME, DISC_GROUP503 DISC_GROUP,
                                       DISC_GROUP_NAME503 DISC_GROUP_NAME, SEQ503 SEQ, CONFIRM_DATE503 CONFIRM_DATE, --更新日期
                                       DISC_KEY503 DISC_KEY, DISC_DISCR503 DISC_DISCR, SLIP_NO503 SLIP_NO200,
                                       SDF200_SEQ503 SDF200_SEQ200, A_REST_AMT503 A_REST_AMT, B_REST_AMT503 B_REST_AMT,
                                       REST_AMT503 REST_AMT, DEPT200, DEPT_DESC_C007
                                  FROM TEMP_SDW503)
                         WHERE A_REST_AMT != 0 OR B_REST_AMT != 0)
                 ORDER BY 1, 2, 3, 4, 5, 6, 7, 8, 9)
    """

    # print('ranked_sql00000000', ranked_sql)

    ranked_sql1 = """
                SELECT BEGIN_END_DATE, AGENT_CODE, AGENT_NAME, DISC_GROUP, DISC_GROUP_NAME, SEQ, CONFIRM_DATE, DISC_KEY, DISC_DISCR, SLIP_NO200,
                       SDF200_SEQ200, A_REST_AMT, B_REST_AMT, REST_AMT, TOTAL_REST_AMT, A2_REST_AMT, B2_REST_AMT, AAAA, DEPT200, DEPT_DESC_C007, RNK
                  FROM (  """ + ranked_sql + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size
            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + ranked_sql + """ )
            """
    try:
        with connection.cursor() as cursor:
            cursor.execute(ranked_sql1, params3)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE"]))]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_balance_of_sales_allowance_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def transform_to_balance_of_sales_allowance_frontend_structure(data, start_index=0):
    report = []
    for agent_code, group in itertools.groupby(data, key=lambda x: (x["AGENT_CODE"])):
        group = list(group)

        total_summery = {
            "index": start_index + len(report) + 1,
            "dealer_code": agent_code,
            "dealer_name": group[0]["AGENT_NAME"],
            "start_end_date": group[0]["BEGIN_END_DATE"],
            "total_rest_amt": group[0]["TOTAL_REST_AMT"],
        }

        details = []
        subtotal_index = 1
        detail_index = 1
        subtotal_seq1 = 0
        subtotal_a_seq2 = 0
        subtotal_b_seq2 = 0
        total_a_rest_amt = 0
        total_b_rest_amt = 0
        prev_disc_group = None
        prev_seq = None
        seq1_exists = False
        seq2_exists = False

        for item in group:
            curr_disc_group = item["DISC_GROUP"]
            curr_seq = item["SEQ"]

            if prev_disc_group is not None and (curr_disc_group != prev_disc_group or curr_seq != prev_seq):
                # 當上一筆與下一筆disc_group或seq不一樣時,添加小計
                if seq1_exists:
                    details.append({
                        "disc_key": "ST",
                        "disc_discr": "小計",
                        "aaaa": subtotal_seq1
                    })
                    subtotal_seq1 = 0  # 將subtotal_seq1重置為0
                    seq1_exists = False
                if seq2_exists:
                    details.append({
                        "disc_key": "ST",
                        "disc_discr": "小計",
                        "a_rest_amt": subtotal_a_seq2,
                        "b_rest_amt": subtotal_b_seq2
                    })
                    subtotal_a_seq2 = 0  # 將subtotal_a_seq2重置為0
                    subtotal_b_seq2 = 0  # 將subtotal_b_seq2重置為0
                    seq2_exists = False
                subtotal_index += 1

            details.append({
                "index": detail_index,
                "disc_group": item["DISC_GROUP"],
                "disc_group_name": item["DISC_GROUP_NAME"],
                "seq": item["SEQ"],
                "confirm_date": item["CONFIRM_DATE"],
                "disc_key": item["DISC_KEY"],
                "disc_discr": item["DISC_DISCR"],
                "slip_no200": item["SLIP_NO200"],
                "a_rest_amt": item["A_REST_AMT"],
                "b_rest_amt": item["B_REST_AMT"],
                "aaaa": item["AAAA"],
                "dept200": item["DEPT200"],
                "dept_desc_c007": item["DEPT_DESC_C007"]
            })
            detail_index += 1

            # item["B_REST_AMT"]
            if item["B_REST_AMT"] is None:
                item["B_REST_AMT"] = 0

            if curr_seq == '1':
                subtotal_seq1 += item["A_REST_AMT"] - item["B_REST_AMT"]
                seq1_exists = True
            elif curr_seq == '2':
                subtotal_a_seq2 += item["A_REST_AMT"]
                subtotal_b_seq2 += item["B_REST_AMT"]
                seq2_exists = True

                total_a_rest_amt += item["A_REST_AMT"]
                total_b_rest_amt += item["B_REST_AMT"]

            prev_disc_group = curr_disc_group
            prev_seq = curr_seq

        # 處理最後一組的小計
        if seq1_exists:
            details.append({
                "disc_key": "ST",
                "disc_discr": "小計",
                "aaaa": subtotal_seq1
            })
        if seq2_exists:
            details.append({
                "disc_key": "ST",
                "disc_discr": "小計",
                "a_rest_amt": subtotal_a_seq2,
                "b_rest_amt": subtotal_b_seq2
            })

        # 添加總計
        details.append({
            "disc_key": "TT",
            "disc_discr": "總計",
            "a_rest_amt": total_a_rest_amt,
            "b_rest_amt": total_b_rest_amt
        })

        total_summery["details"] = details
        report.append(total_summery)

    return report


""" 銷貨折讓餘額 END """

""" 折讓明細A表 START """


@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_sdv200_main_allowance_detail_schedule_a(request, json_data, role, user_id, page_size, page_number,
                                                          start_rnk, end_rnk):
    context = 'select_erp_hy_sdv200_main_allowance_detail_schedule_a'

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}
        params3 = {}

        start_end_date = json_data['due_start_date'] + '_' + json_data['due_end_date']

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "AGENT_CODE",
        }

        params['P_BEGINDATE'] = json_data['due_start_date']
        params['P_ENDDATE'] = json_data['due_end_date']
        params2['P_BEGINDATE'] = json_data['due_start_date']
        params2['P_ENDDATE'] = json_data['due_end_date']
        params3['P_BEGINDATE'] = json_data['due_start_date']
        params3['P_ENDDATE'] = json_data['due_end_date']

        params['P_PRINT_OPTION'] = '0'
        params2['P_PRINT_OPTION'] = '0'
        params3['seq'] = '1'
        params3['seqa'] = '1'
        params3['seq_1'] = '2'
        params3['seqa_1'] = '1'

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f"AGENT_CODE = :P_AGENT_CODE")
            params['P_AGENT_CODE'] = user_id
            params2['P_AGENT_CODE'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        try:

            result = select_with_raw_sdv201_sql(context, sql_conditions, params, params2, params3, start_end_date,
                                                page_number, page_size, start_rnk)
            # print('result', result)

            return result

        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def select_with_raw_sdv201_sql(context, sql_conditions, params, params2, params3, start_end_date, page_number,
                               page_size, start_rnk):
    if len(sql_conditions) == 0:
        sql_query = f" {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" AND {' AND '.join(sql_conditions)} "

    ranked_sql = f"""
        SELECT '{start_end_date}' BEGIN_END_DATE,
               AGENT_CODE, AGENT_NAME, DISC_GROUP200, DISC_GROUP_NAME200, SEQ, SEQA, 
               TO_CHAR(CONFIRM_DATE, 'YYYY/MM/DD') CONFIRM_DATE, DISC_KEY200, DISC_DISCR200,
               DEPT200, DEPT_DESC_C007, USER200, CHI_NAME005, A_REST_AMT, DESCR200, SLIP_NO200, SDF200_SEQ200,
               DENSE_RANK() OVER (ORDER BY AGENT_CODE) AS RNK
          FROM ( {sdv200_main_allowance_detail_schedule_a_sql + sql_query}
                 ORDER BY 1, 3, 5, 7, 8, 16 )
         WHERE 1 = 1
    """

    ranked_sql1 = """
        SELECT BEGIN_END_DATE, AGENT_CODE, AGENT_NAME, DISC_GROUP200, DISC_GROUP_NAME200, SEQ, SEQA, CONFIRM_DATE, DISC_KEY200, DISC_DISCR200,
               DEPT200, DEPT_DESC_C007, USER200, CHI_NAME005, A_REST_AMT, DESCR200, SLIP_NO200, SDF200_SEQ200, RNK
          FROM (  """ + ranked_sql + """  )   
         WHERE RNK BETWEEN :qpage_number AND :qpage_size
    """

    ranked_sql2 = """
        SELECT MAX(RNK) RNK
          FROM ( """ + ranked_sql + """ )
    """

    try:
        with connection.cursor() as cursor:
            # print('ranked_sql1', ranked_sql1)
            # print('params', params)
            cursor.execute(ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE"]))]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = generate_allowance_detail_schedule_a_master_detail(params3, page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def query_additional_data(params3):
    context = 'query_additional_data'
    with connection.cursor() as cursor:
        cond = """
            AND A.SDF200_SEQ200 = G.B_SDF200_SEQ201 AND (
                (:seq = '1' AND :seqa = '1' AND G.WRITE_OFF_DATE201 < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')) OR
                (:seq = '2' AND :seqa = '1' AND
                G.WRITE_OFF_DATE201 BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.99)))
        """

        cond2 = """
            AND A.SDF200_SEQ200 = G.B_SDF200_SEQ201 AND (
                (:seq_1 = '1' AND :seqa_1 = '1' AND G.WRITE_OFF_DATE201 < TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd')) OR
                (:seq_1 = '2' AND :seqa_1 = '1' AND
                G.WRITE_OFF_DATE201 BETWEEN TO_DATE(:P_BEGINDATE, 'yyyy/mm/dd') AND TO_DATE(:P_ENDDATE, 'yyyy/mm/dd') + 0.99)))
        """

        sSTM = """
            SELECT *
              FROM ( """ + sdv200_main_allowance_detail_schedule_a_sql2('1', cond) + """ )
            UNION ALL
            SELECT *
              FROM ( """ + sdv200_main_allowance_detail_schedule_a_sql2('2', cond2) + """ )
        """

        # print('sSTM', sSTM)
        # print('params3', params3)

        cursor.execute(sSTM, params3)
        data = cursor.fetchall()

        if data is None:
            return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

        col_names = [desc[0] for desc in cursor.description]
        additional_data = [dict(zip(col_names, row)) for row in data]

        return additional_data


def generate_allowance_detail_schedule_a_master_detail(params3, data, start_index=0):
    report = []
    for agent_code, group in itertools.groupby(data, key=lambda x: (x["AGENT_CODE"])):
        group = list(group)

        total_summery = {
            "index": start_index + len(report) + 1,
            "dealer_code": agent_code,
            "dealer_name": group[0]["AGENT_NAME"],
            "start_end_date": group[0]["BEGIN_END_DATE"],
        }

        details = []
        for item in group:
            sdf200_seq200 = item["SDF200_SEQ200"]
            params3.update({'SDF200_SEQ1': sdf200_seq200})

            additional_data = query_additional_data(params3)
            customized_additional_data = []
            subtotal = 0
            for child in additional_data:
                if child["SEQ"] == item["SEQ"]:
                    customized_additional_data.append({
                        # "slip_no200": child["SLIP_NO200"],
                        # "seq": child["SEQ"],
                        "confirm_date": child["CONFIRM_DATE"],
                        # "disc_key200": item["DISC_KEY200"],
                        # "disc_discr200": item["DISC_DISCR200"],
                        "a_rest_amt": child["A_REST_AMT"],
                        "track_invoice": child["TRACK_INVOICE220"],
                        "descr200": child["DESCR200"],
                        # "sdf200_seq201": item["SDF200_SEQ201"],
                    })

                    subtotal += child["A_REST_AMT"]

            # 將小計作為一個單獨的字典添加到customized_additional_data中
            if subtotal > 0:
                customized_additional_data.append({
                    "confirm_date": "小計",
                    "a_rest_amt": subtotal
                })

            details.append({
                "index": start_index + len(details) + 1,
                "dealer_code": item["AGENT_CODE"],
                "dealer_name": item["AGENT_NAME"],
                "disc_group": item["DISC_GROUP200"],
                "disc_group_name": item["DISC_GROUP_NAME200"],
                "seq": item["SEQ"],
                "seqa": item["SEQA"],
                "confirm_date": item["CONFIRM_DATE"],
                "disc_key": item["DISC_KEY200"],
                "disc_discr": item["DISC_DISCR200"],
                "dept200": item["DEPT200"],
                "dept_desc_c007": item["DEPT_DESC_C007"],
                "user200": item["USER200"],
                "chi_name005": item["CHI_NAME005"],
                "a_rest_amt": item["A_REST_AMT"],
                "descr200": item["DESCR200"],
                "slip_no200": item["SLIP_NO200"],
                "sdf200_seq200": item["SDF200_SEQ200"],
                "child_details": customized_additional_data
            })

        total_summery["details"] = details
        report.append(total_summery)

    return report


""" 折讓明細B表 START """


@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_ocv211_214_main_allowance_detail_schedule_b(request, json_data, role, user_id, page_size, page_number,
                                                              start_rnk, end_rnk):
    context = 'select_erp_hy_ocv211_214_main_allowance_detail_schedule_b'

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = ocv211_214_main_allowance_detail_schedule_b_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "A.AGENT_CODE214",
            "track_check": "E.TRACK_INVOICE220",
        }

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f"A.AGENT_CODE214 = :qAGENT_CODE214")
            params['qAGENT_CODE214'] = user_id
            params2['qAGENT_CODE214'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        if 'due_start_date' in json_data and json_data['due_start_date'] is not None and \
                'due_end_date' in json_data and json_data['due_end_date'] is not None:
            params['qDUE_START_DATE'] = json_data['due_start_date']
            params['qDUE_END_DATE'] = json_data['due_end_date']
            params2['qDUE_START_DATE'] = json_data['due_start_date']
            params2['qDUE_END_DATE'] = json_data['due_end_date']
            sql_conditions.append(
                f" E.INVOICE_DATE220 BETWEEN TO_DATE(:qDUE_START_DATE, 'YYYYMMDD') AND TO_DATE(:qDUE_END_DATE, 'YYYYMMDD') ")

        sql_query = f""" {sql_base} AND {' AND '.join(sql_conditions)} 
                      GROUP BY E.TRACK_INVOICE220, C.PRODUCT_ID211, 
                               C.PRODUCT_NAME211, C.PRODUCT_MARK211, 
                               C.MARK_NAME211, G.PROMOTE_ID040, 
                               A.DISC_TYPE_ID214, A.DISC_TYPE_ID_NAME214, 
                               D.DELIVERY_DATE210, E.INVOICE_DATE220, 
                               A.AGENT_CODE214, A.AGENT_BRIEF_NAME214 
                    """

        # 增加使用者是否讀取此筆資料
        sql_query = """
            SELECT TRACK_INVOICE220, PRODUCT_ID211, PRODUCT_NAME211, PRODUCT_MARK211, MARK_NAME211, BQTY211, DISC_QTY214,
                   PROMOTE_ID040, DISC_TYPE_ID214, DISC_TYPE_ID_NAME214, DISC_AMT214, DELIVERY_DATE210, INVOICE_DATE220,
                   AGENT_CODE214, AGENT_BRIEF_NAME214, SOURCESERNO, USERID, ACTIONTYPE
              FROM ( """ + sql_query + """ )
              LEFT JOIN (
                  SELECT SOURCESERNO, USERID, ACTIONTYPE
                    FROM USERLOG
                   WHERE PARENTID = 'E0100000' AND CHILDID = 'F001_1_B')
                ON TRACK_INVOICE220 = SOURCESERNO(+) AND AGENT_CODE214 = USERID(+)
        """

        sql_query = sql_query + " ORDER BY AGENT_CODE214, INVOICE_DATE220 DESC, TRACK_INVOICE220 "

        ranked_sql = """
                    SELECT TRACK_INVOICE220, PRODUCT_ID211, PRODUCT_NAME211, PRODUCT_MARK211, MARK_NAME211, BQTY211, DISC_QTY214,
                           PROMOTE_ID040, DISC_TYPE_ID214, DISC_TYPE_ID_NAME214, DISC_AMT214, DELIVERY_DATE210, INVOICE_DATE220,
                           AGENT_CODE214, AGENT_BRIEF_NAME214, SOURCESERNO, USERID, ACTIONTYPE,
                           DENSE_RANK() OVER (ORDER BY AGENT_CODE214, INVOICE_DATE220 DESC, TRACK_INVOICE220) AS RNK
                      FROM (  """ + sql_query + """  )
                     WHERE 1 = 1
                """

        ranked_sql1 = """
                    SELECT TRACK_INVOICE220, PRODUCT_ID211, PRODUCT_NAME211, PRODUCT_MARK211, MARK_NAME211, BQTY211, DISC_QTY214,
                           PROMOTE_ID040, DISC_TYPE_ID214, DISC_TYPE_ID_NAME214, DISC_AMT214, DELIVERY_DATE210, INVOICE_DATE220,
                           AGENT_CODE214, AGENT_BRIEF_NAME214, SOURCESERNO, USERID, ACTIONTYPE, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size
                """

        ranked_sql2 = """ 
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行數據轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]
                # data.sort(reverse=True, key=lambda x: x['TRACK_INVOICE220'])
                # print(data)

                grouped_by_column = [
                    list(group)
                    for _, group in itertools.groupby(data, key=lambda x: (x['AGENT_CODE214'], x['INVOICE_DATE220']))
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data = [item for sublist in grouped_by_column for item in sublist]

                report = generate_allowance_detail_schedule_b_master_detail(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_allowance_detail_schedule_b_master_detail(data, start_index=0):
    report = []
    for track_invoice, group in itertools.groupby(data, lambda x: x["TRACK_INVOICE220"]):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": str(len(report) + 1 + start_index),  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE214"],  # 經銷商代碼
            "dealer_name": group[0]["AGENT_BRIEF_NAME214"],  # 經銷商名稱
            "track_invoice": track_invoice,  # 憑證號碼
            "total_amount": sum(item["DISC_AMT214"] for item in group),  # 金額
            "delivery_date": min(item["DELIVERY_DATE210"] for item in group),  # 實際發貨日期
            "invoice_date": max(item["INVOICE_DATE220"] for item in group),  # 發票日期
            "readStatus": group[0]["ACTIONTYPE"]  # 使用者是否讀取此筆資料
        }

        details = []
        for product, product_group in itertools.groupby(group, lambda x: x["PRODUCT_ID211"]):
            product_group = list(product_group)

            detail = {
                "index": str(len(details) + 1),
                "dealer_code": group[0]["AGENT_CODE214"],  # 經銷商代碼
                "dealer_name": group[0]["AGENT_BRIEF_NAME214"],  # 經銷商名稱
                "invoice_date": product_group[0]["INVOICE_DATE220"],  # 發票日期
                "track_invoice": track_invoice,  # 憑證號碼
                "product_id": product_group[0]["PRODUCT_ID211"],  # 產品代號
                "product_name": product_group[0]["PRODUCT_NAME211"],  # 產品名稱
                "discount_type": product_group[0]["DISC_TYPE_ID214"],  # 產品折讓類別
                "discount_type_name": product_group[0]["DISC_TYPE_ID_NAME214"],  # 產品折讓類別名稱
                "amount": sum(item["DISC_AMT214"] for item in product_group),  # 金額
                "quantity": sum(item["BQTY211"] for item in product_group),  # 數量
            }
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


""" 折讓明細B表 END """

""" 單次折讓證明 START """


@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_sdv250_main_proof_of_single_allowance(request, json_data, role, user_id, page_size, page_number,
                                                        start_rnk, end_rnk):
    context = 'select_erp_hy_sdv250_main_proof_of_single_allowance'

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = sdv250_main_proof_of_single_allowance_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "B.AGENT_CODE250",
            "track_check": "B.SLIP_NO250",
        }

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f"B.AGENT_CODE250 = :qAGENT_CODE250")
            params['qAGENT_CODE250'] = user_id
            params2['qAGENT_CODE250'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        if 'due_start_date' in json_data and json_data['due_start_date'] is not None and \
                'due_end_date' in json_data and json_data['due_end_date'] is not None:
            params['qDUE_START_DATE'] = json_data['due_start_date']
            params['qDUE_END_DATE'] = json_data['due_end_date']
            params2['qDUE_START_DATE'] = json_data['due_start_date']
            params2['qDUE_END_DATE'] = json_data['due_end_date']
            sql_conditions.append(
                f" B.BELG_DATE250 BETWEEN TO_DATE(:qDUE_START_DATE, 'YYYYMMDD') AND TO_DATE(:qDUE_END_DATE, 'YYYYMMDD') ")

        sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} "

        # 增加使用者是否讀取此筆資料
        sql_query = """
            SELECT AGENT_CODE250, AGENT_BRIEF_NAME250, BELG_DATE250, SLIP_NO250, ITEM_NAME251, AMT251, TAX251, TYPE_NAME250,
                   BUTAX_DESC001, QTY251, SOURCESERNO, USERID, ACTIONTYPE
              FROM ( """ + sql_query + """ )
              LEFT JOIN (
                  SELECT SOURCESERNO, USERID, ACTIONTYPE
                    FROM USERLOG
                   WHERE PARENTID = 'E0100000' AND CHILDID = 'F001_1_Single')
                ON SLIP_NO250 = SOURCESERNO(+) AND AGENT_CODE250 = USERID(+)
        """

        sql_query = sql_query + "ORDER BY SLIP_NO250 DESC"

        ranked_sql = """
                    SELECT AGENT_CODE250, AGENT_BRIEF_NAME250, BELG_DATE250, SLIP_NO250, ITEM_NAME251, AMT251, TAX251, TYPE_NAME250,
                            BUTAX_DESC001, QTY251, SOURCESERNO, USERID, ACTIONTYPE,
                           DENSE_RANK() OVER (ORDER BY SLIP_NO250 DESC) AS RNK
                      FROM (  """ + sql_query + """  )
                     WHERE 1 = 1
                """

        ranked_sql1 = """
                    SELECT AGENT_CODE250, AGENT_BRIEF_NAME250, BELG_DATE250, SLIP_NO250, ITEM_NAME251, AMT251, TAX251, TYPE_NAME250,
                            BUTAX_DESC001, QTY251, SOURCESERNO, USERID, ACTIONTYPE, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size        
                """

        ranked_sql2 = """
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行數據轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]
                # data.sort(reverse=True, key=lambda x: x['SLIP_NO250'])

                # 依照支票號碼分組資料
                grouped_by_column = [
                    list(group)
                    for _, group in itertools.groupby(data, key=lambda x: x['SLIP_NO250'])
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data = [item for sublist in grouped_by_column for item in sublist]

                report = generate_proof_of_single_allowance(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_proof_of_single_allowance(data, start_index=0):
    report = []
    for track_invoice, group in itertools.groupby(data, lambda x: x["SLIP_NO250"]):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE250"],  # 經銷商代碼
            "dealer_name": group[0]["AGENT_BRIEF_NAME250"],  # 經銷商名稱
            "track_invoice": track_invoice,  # 憑證號碼
            "total_amount": sum(item["AMT251"] for item in group),  # 金額
            "total_tax": sum(item["TAX251"] for item in group),  # 稅額
            "discount_date": group[0]["BELG_DATE250"],  # 折讓歸屬日期
            "readStatus": group[0]["ACTIONTYPE"],  # 使用者是否讀取此筆資料
            "detail_button": "明細",
        }

        details = []
        for product, product_group in itertools.groupby(group, lambda x: x["ITEM_NAME251"]):
            product_group = list(product_group)

            detail = {
                "index": len(details) + 1,
                "dealer_code": group[0]["AGENT_CODE250"],  # 經銷商代碼
                "dealer_name": group[0]["AGENT_BRIEF_NAME250"],  # 經銷商名稱
                "track_invoice": track_invoice,  # 憑證號碼"
                "product_name": product_group[0]["ITEM_NAME251"],  # 產品名稱
                "type": product_group[0]["TYPE_NAME250"],  # 類別
                "tax_name": product_group[0]["BUTAX_DESC001"],  # 稅別名稱
                "amount": sum(item["AMT251"] for item in product_group),  # 金額
                "tax": sum(item["TAX251"] for item in product_group),  # 金額
                "qty": sum(item["QTY251"] for item in product_group),  # 稅額
            }
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


""" 單次折讓證明 END """

""" 銷貨折讓通知 START """


@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_sdv200_main_sales_allowance_notification(request, json_data, role, user_id, page_size, page_number,
                                                           start_rnk, end_rnk):
    context = 'select_erp_hy_sdv200_main_sales_allowance_notification'

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = sdv200_main_sales_allowance_notification_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "A.AGENT_CODE200",
            "track_check": "A.SLIP_NO200",
        }

        # 當message中role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f"A.AGENT_CODE200 = :qAGENT_CODE200")
            params['qAGENT_CODE200'] = user_id
            params2['qAGENT_CODE200'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]
        # 處理日期範圍
        print(json_data)
        if 'due_start_date' in json_data and json_data['due_start_date'] is not None and \
                'due_end_date' in json_data and json_data['due_end_date'] is not None:
            params['qDUE_START_DATE'] = json_data['due_start_date']
            params['qDUE_END_DATE'] = json_data['due_end_date']
            params2['qDUE_START_DATE'] = json_data['due_start_date']
            params2['qDUE_END_DATE'] = json_data['due_end_date']
            sql_conditions.append(
                f" A.CONFIRM_DATE200 BETWEEN TO_DATE(:qDUE_START_DATE, 'YYYYMMDD') AND TO_DATE(:qDUE_END_DATE, 'YYYYMMDD') + INTERVAL '1' DAY - INTERVAL '1' SECOND ")

        # 構建完整的 SQL 查詢
        sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} "

        # print('sql_query', sql_query)

        # 增加使用者是否讀取此筆資料
        sql_query = """
            SELECT AGENT_CODE200, AGENT_BRIEF_NAME200, KEY_TYPE200, KEY_TYPE_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, DISC_KEY200,
                   DISC_DISCR200, CONFIRM_DATE200, SLIP_NO200, AMT200, DESCR200, DEPT200, USER200, CHI_NAME005, END_DATE,
                   SOURCESERNO, USERID, ACTIONTYPE
              FROM ( """ + sql_query + """ )
              LEFT JOIN (
                  SELECT SOURCESERNO, USERID, ACTIONTYPE
                    FROM USERLOG
                   WHERE PARENTID = 'E0100000' AND CHILDID = 'F001_1_SalesAllowance')
                ON SLIP_NO200 = SOURCESERNO(+) AND AGENT_CODE200 = USERID(+)
        """

        sql_query = sql_query + "ORDER BY AGENT_CODE200, CONFIRM_DATE200, DISC_GROUP200, DISC_KEY200"

        ranked_sql = """
                    SELECT AGENT_CODE200, AGENT_BRIEF_NAME200, KEY_TYPE200, KEY_TYPE_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, DISC_KEY200,
                           DISC_DISCR200, CONFIRM_DATE200, SLIP_NO200, AMT200, DESCR200, DEPT200, USER200, CHI_NAME005, END_DATE,
                           SOURCESERNO, USERID, ACTIONTYPE,
                           DENSE_RANK() OVER (ORDER BY AGENT_CODE200, CONFIRM_DATE200, DISC_GROUP200, DISC_KEY200) AS RNK
                      FROM (  """ + sql_query + """  )
                     WHERE 1 = 1
                     ORDER BY RNK, AGENT_CODE200, CONFIRM_DATE200, SLIP_NO200
                """

        # print('ranked_sql', ranked_sql)

        ranked_sql1 = """
                    SELECT AGENT_CODE200, AGENT_BRIEF_NAME200, KEY_TYPE200, KEY_TYPE_NAME200, DISC_GROUP200, DISC_GROUP_NAME200, DISC_KEY200,
                           DISC_DISCR200, CONFIRM_DATE200, SLIP_NO200, AMT200, DESCR200, DEPT200, USER200, CHI_NAME005, END_DATE, 
                           SOURCESERNO, USERID, ACTIONTYPE, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size
                """

        ranked_sql2 = """
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 依照代理代碼和折扣組別分組並排序資料
                data = [dict(zip(col_names, row)) for row in data]

                sorted_data = sorted(data, key=lambda x: (x["AGENT_CODE200"], x["KEY_TYPE200"], x["DISC_GROUP200"], x["DISC_KEY200"]))

                grouped_by_column = [
                    list(group)
                    for _, group in itertools.groupby(sorted_data, key=lambda x: (
                        x["AGENT_CODE200"], x["CONFIRM_DATE200"], x["DISC_GROUP200"], x["DISC_DISCR200"]))
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data = [item for sublist in grouped_by_column for item in sublist]

                report = generate_sales_allowance_notification(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, "資料庫發生錯誤", status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_sales_allowance_notification(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (
            x["AGENT_CODE200"], x["CONFIRM_DATE200"], x["DISC_GROUP200"], x["DISC_DISCR200"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE200"],  # 經銷商代碼
            "dealer_name": group[0]["AGENT_BRIEF_NAME200"],  # 經銷商名稱
            "subsidy": group[0]["KEY_TYPE200"] + group[0]["KEY_TYPE_NAME200"],  # 補助/分攤單別
            "comprehensive_discount": group[0]["DISC_GROUP200"] + ' ' + group[0]["DISC_GROUP_NAME200"],  # 綜合折讓產品群
            "reason_for_discount": group[0]["DISC_KEY200"] + ' ' + group[0]["DISC_DISCR200"],  # 折讓事由
            "total_amount": sum(item["AMT200"] for item in group),  # 金額
            "update_date": group[0]["CONFIRM_DATE200"],  # 確認日期
            "end_date": group[0]["END_DATE"],  # 確認日期
            "readStatus": group[0]["ACTIONTYPE"],  # 使用者是否讀取此筆資料
            "detail_button": "明細",
        }

        details = []
        for _, product_group in itertools.groupby(group, lambda x: x["SLIP_NO200"]):
            product_group = list(product_group)

            detail = {
                "index": len(details) + 1,
                "dealer_code": group[0]["AGENT_CODE200"],  # 經銷商代碼
                "dealer_name": group[0]["AGENT_BRIEF_NAME200"],  # 經銷商名稱
                "reason_for_discount": product_group[0]["DISC_KEY200"] + ' ' + product_group[0]["DISC_DISCR200"],
                # 折讓事由
                "update_date": product_group[0]["CONFIRM_DATE200"],  # 更新日期
                "odd_number": product_group[0]["SLIP_NO200"],  # 單號
                "amount": sum(item["AMT200"] for item in product_group),  # 金額
                "summary": product_group[0]["DESCR200"],  # 摘要
                "user": (product_group[0]["USER200"] or '') + ' ' + (product_group[0]["CHI_NAME005"] or ''),  # 摘要
            }
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


""" 銷貨折讓通知 END """

""" 營站自販補助 START """


@validate_access_token_and_params_with_pagination(None)
def select_hy1_asea_main_subsidy_for_station_vending(request, json_data, role, user_id, page_size, page_number,
                                                     start_rnk, end_rnk):
    context = 'select_hy1_asea_main_subsidy_for_station_vending'

    if request.method == "POST":

        # 得到6個月前的月份和年份
        start_month_minguo, end_month_minguo = get_previous_months_minguo(6)

        # SQL 查詢基本結構
        sql_base = asea_main_subsidy_for_station_vending_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "EAVENN",
        }

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" EAVENN = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 處理日期範圍
        if 'start_date1' in json_data and json_data['start_date1'] is not None and \
                'start_date2' in json_data and json_data['start_date1'] is not None:
            params['qEAYYMM'] = to_minguo(json_data['start_date1'], year_and_month_only=True)
            params['qAFSTDT'] = to_minguo(json_data['start_date2'], year_and_month_only=True)
            params['qAFBGDT'] = to_minguo(json_data['end_date1'], year_and_month_only=True)
            params2['qEAYYMM'] = to_minguo(json_data['start_date1'], year_and_month_only=True)
            params2['qAFSTDT'] = to_minguo(json_data['start_date2'], year_and_month_only=True)
            params2['qAFBGDT'] = to_minguo(json_data['end_date1'], year_and_month_only=True)

        #
        params['qAFPMNO'] = json_data['dealers_type']
        params2['qAFPMNO'] = json_data['dealers_type']

        # 構建完整的 SQL 查詢
        if len(sql_conditions) == 0:
            sql_query = f" {sql_base} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {sql_base} AND {' AND '.join(sql_conditions)} "

        sql_query = sql_query + "ORDER BY EAVENN, EAYYMM, EAITNO"

        ranked_sql = """
                    SELECT EAYYMM, EAVENN, ADNAM2, EAITNO, TBNAM2, AFAMTE, QTY, SUBAMTE, RNK
                      FROM (  """ + sql_query + """  )
                """

        ranked_sql1 = """
                    SELECT EAYYMM, EAVENN, ADNAM2, EAITNO, TBNAM2, AFAMTE, QTY, SUBAMTE, RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size  
                """

        ranked_sql2 = """
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                data = [dict(zip(col_names, row)) for row in data]

                # 依分組資料
                grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["EAVENN"])]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data = [item for sublist in grouped_by_column for item in sublist]

                report = generate_subsidy_for_station_vending(page_data, start_rnk - 1)

                return {
                    "results": report,
                    "total_pages": total_pages,
                    "current_page": page_number,
                    "page_size": page_size
                }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_subsidy_for_station_vending(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["EAVENN"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["EAVENN"],  # 經銷商代碼
            "dealer_name": group[0]["ADNAM2"],  # 經銷商名稱
            "yyymm": group[0]["EAYYMM"],  # 年月
            "total_qty": sum(item["QTY"] for item in group),  # 數量
            "total_amount": sum(item["SUBAMTE"] for item in group),  # 金額
        }

        details = []
        for _, product_group in itertools.groupby(group, lambda x: x["EAITNO"]):
            product_group = list(product_group)

            detail = {
                "index": len(details) + 1,  # 項次
                "dealer_code": group[0]["EAVENN"],  # 經銷商代碼
                "dealer_name": group[0]["ADNAM2"],  # 經銷商名稱
                "product_code": product_group[0]["EAITNO"],  # 產品代號
                "product_name": product_group[0]["TBNAM2"],  # 產品名稱
                "subsidy_price": product_group[0]["AFAMTE"],  # 補助單價
                "qty": product_group[0]["QTY"],  # 數量
                "subsidy_amount": product_group[0]["SUBAMTE"],  # 補助金額
            }
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report


""" 營站自販補助 END """



