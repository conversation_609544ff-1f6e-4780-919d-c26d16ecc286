from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.allowances.AllowanceInfo import select_erp_hy_ocv211_214_main_allowance_detail_schedule_b, \
    select_erp_hy_sdv250_main_proof_of_single_allowance, select_erp_hy_sdv200_main_sales_allowance_notification, \
    select_hy1_asea_main_subsidy_for_station_vending, select_erp_hy_sdw503_balance_of_sales_allowance, \
    select_erp_hy_sdv200_main_allowance_detail_schedule_a
from apps.allowances.models import mainAllowance

ALLOWANCE_ACTIONS = {
    'erp_hy_sdw503_main_sales_allowance_balance': {
        'select': select_erp_hy_sdw503_balance_of_sales_allowance,
    },
    'erp_hy_sdv200_main_allowance_detail_schedule_a': {
        'select': select_erp_hy_sdv200_main_allowance_detail_schedule_a,
    },
    'erp_hy_ocv211_214_main_allowance_detail_schedule_b': {
        'select': select_erp_hy_ocv211_214_main_allowance_detail_schedule_b,
    },
    'erp_hy_sdv250_main_proof_of_single_allowance': {
        'select': select_erp_hy_sdv250_main_proof_of_single_allowance,
    },
    'erp_hy_sdv200_main_sales_allowance_notification': {
        'select': select_erp_hy_sdv200_main_sales_allowance_notification,
    },
    'hy1_asea_main_subsidy_for_station_vending': {
        'select': select_hy1_asea_main_subsidy_for_station_vending,
    },
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class AllowanceViewSet(viewsets.ModelViewSet ):
    queryset = mainAllowance.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 銷貨折讓餘額
    @action(detail=False, methods=['post'])
    def erp_hy_sdw503_main_sales_allowance_balance(self, request):
        return self._handle_action('erp_hy_sdw503_main_sales_allowance_balance', 'select')

    # 折讓明細A表
    @action(detail=False, methods=['post'])
    def erp_hy_sdv200_main_allowance_detail_schedule_a(self, request):
        return self._handle_action('erp_hy_sdv200_main_allowance_detail_schedule_a', 'select')

    # 折讓明細B表
    @action(detail=False, methods=['post'])
    def erp_hy_ocv211_214_main_allowance_detail_schedule_b(self, request):
        return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')

    # 單次折讓證明
    @action(detail=False, methods=['post'])
    def erp_hy_sdv250_main_proof_of_single_allowance(self, request):
        return self._handle_action('erp_hy_sdv250_main_proof_of_single_allowance', 'select')

    # 銷貨折讓通知
    @action(detail=False, methods=['post'])
    def erp_hy_sdv200_main_sales_allowance_notification(self, request):
        return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')

    # 銷貨折讓通知
    @action(detail=False, methods=['post'])
    def hy1_asea_main_subsidy_for_station_vending(self, request):
        return self._handle_action('hy1_asea_main_subsidy_for_station_vending', 'select')