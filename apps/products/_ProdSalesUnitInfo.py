# -*- coding: UTF8 -*-
import cx_Oracle

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' PRODSALESUNIT INSERT START '''

# 映射字典
PRODSALESUNIT_NAME_MAPPING = {
    "product_codes": "PRODCODE",
    "order_base_numbers": "SALESUNIT",
    "original_order_unit_conversion": "QTYSPUNITUNIT",
    "sales_unit": "SPUNIT",
    "pallet_conversion_quantity": "QTYBOARDUNIT",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID"
}


def insert_with_raw_sql(context, data):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:
                keys = ", ".join(PRODSALESUNIT_NAME_MAPPING[key] for key in data.keys())
                values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

                sql = f"INSERT INTO PRODSALESUNIT ({keys}) VALUES ({values})"
                cursor.execute(sql)

            # 在所有插入操作都成功後返回成功訊息
            return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def insert_prodsalesunit_method(request, json_data, role, user_id):
    context = "insert_prodsalesunit_method"

    if request.method == "POST":
        try:
            result, result_status = insert_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' PRODSALESUNIT INSERT END '''

''' PRODSALESUNIT UPDATE START '''


def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "product_codes": "PRODCODE",
        "order_base_numbers": "SALESUNIT",
        "original_order_unit_conversion": "QTYSPUNITUNIT",
        "sales_unit": "SPUNIT",
        "pallet_conversion_quantity": "QTYBOARDUNIT",
        "hub_type": "HUBTYPE",
        "owner_id": "OWNERID"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 生成SET子句部分
                set_parts = []
                for key, value in data.items():
                    if key not in ["product_codes", "order_base_numbers"]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                sql = f"UPDATE PRODSALESUNIT SET {set_string} WHERE PRODCODE = '{data['product_codes']}' AND SALESUNIT = '{data['order_base_numbers']}'"
                # print(sql)
                cursor.execute(sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('product_codes', 'order_base_numbers')
def update_prodsalesunit_method(request, json_data, role, user_id):
    context = "update_prodsalesunit_method"

    if request.method == "POST":
        try:
            result, result_status = update_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PRODSALESUNIT UPDATE END '''

''' PRODSALESUNIT DELETE START '''


def delete_with_raw_sql(context, combined_data):

    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for data in combined_data:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM PRODSALESUNIT WHERE PRODCODE = '{data['product_code']}' AND SALESUNIT = '{data['order_base_number']}'"
                    cursor.execute(sql)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('product_codes', 'order_base_numbers')
def delete_prodsalesunit_method(request, json_data, role, user_id):
    context = "delete_prodsalesunit_method"

    if request.method == "POST":
        product_codes = json_data.get("product_codes", [])
        order_base_numbers = json_data.get("order_base_numbers", [])

        # Check if dept_codes is a list and is not empty
        if not isinstance(product_codes, list) or not product_codes:
            return handle_error(context, "product_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST)

        if not isinstance(order_base_numbers, list) or not order_base_numbers:
            return handle_error(context, "order_base_numbers must be a non-empty list", status.HTTP_400_BAD_REQUEST)

        try:
            combined_data = [{"product_code": code, "order_base_number": number} for code, number in
                             zip(product_codes, order_base_numbers)]

            result, result_status = delete_with_raw_sql(context, combined_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PRODSALESUNIT DELETE END '''

''' PRODSALESUNIT SELECT START '''


def transform_to_frontend_structure(data, index):
    # 將資料庫的結構轉換為前端所需的結構
    frontend_data = {
        "id": index,
        "product_code": data.get("PRODCODE"),
        "product_name": data.get("SDESCR015"),
        "order_base_number": data.get("SALESUNIT"),
        "original_order_unit_conversion": int(data.get("QTYSPUNITUNIT")),
        "sales_unit": data.get("UNITB_NAME015"),
        "product_conversion_rate": data.get("RATEA015"),
        "product_unit": data.get("UNITA_NAME015"),
        "pallet_conversion_quantity": int(data.get("QTYBOARDUNIT")),
        "owner_name": data.get("CHI_NAME005"),
        "modify_date": data.get("OWNERDATE"),
    }
    return frontend_data


def select_with_raw_sql(context, sql_conditions, params):

    if len(sql_conditions) == 0:
        sql_query = f" {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" AND {' AND '.join(sql_conditions)} "

    try:
        with connection.cursor() as cursor:
            sql = """  
                SELECT PRODCODE, SDESCR015, SALESUNIT, QTYSPUNITUNIT, UNITB_NAME015, UNITB015, SPUNIT, RATEA015, UNITA_NAME015, UNITA015, QTYBOARDUNIT, 
                       HUBTYPE, OWNERID, CHI_NAME005,
                       TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
                  FROM (SELECT PRODCODE, SALESUNIT, QTYSPUNITUNIT, SPUNIT, QTYBOARDUNIT, HUBTYPE, OWNERID, OWNERDATE
                          FROM PRODSALESUNIT),
                       (SELECT PRODUCT_ID015, SDESCR015, UNITA015, UNITA_NAME015, UNITB015, UNITB_NAME015, RATEA015
                          FROM OCV015@B2B), HRF005@B2B
                 WHERE PRODCODE = PRODUCT_ID015 AND SPUNIT = UNITB015
                   AND REPLACE(OWNERID, 'h', '') = NO005 """ + sql_query + """
                 ORDER BY PRODCODE, SALESUNIT
            """

            cursor.execute(sql, params)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 將從資料庫中獲取的每一行資料轉換為前端需要的格式，並添加索引
            return [transform_to_frontend_structure(dict(zip(columns, row)), idx + 1) for idx, row in
                    enumerate(rows)], status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_prodsalesunit_method(request, json_data, role, user_id):
    context = "select_prodsalesunit_method"

    if request.method == "POST":
        # SQL 查詢基本結構
        sql_conditions = []
        params = {}

        # 定義可能的參數列表
        params_mapping = {
            "product_code": "PRODUCT_ID015",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        try:
            result, result_status  = select_with_raw_sql(context, sql_conditions, params)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PRODSALESUNIT SELECT END '''
