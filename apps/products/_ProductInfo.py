import itertools
import logging

import cx_<PERSON>
from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' Combobox START '''
# combobox_ocv015_產品代號與產品名稱
ocv015_combobox_product_code_name_sql = """
    SELECT ROW_NUMBER() OVER (ORDER BY PRODUCT_ID015) ID, 
           RPAD(NVL(PRODUCT_ID015, ' '), 6, ' ') || 
           RPAD(NVL(SDESCR015, ' '), 20, ' ') PRODUCT_CODE_NAME
      FROM OCV015@B2B
     WHERE OUTYM015 IS NULL
     ORDER BY PRODUCT_ID015
     """

# combobox_ocv003_代號與名稱
ocv003_combobox_product_code_name_sql = """
    SELECT ROW_NUMBER() OVER (ORDER BY CODE_ID003) ID, 
           RPAD(CODE_ID003, 10, ' ') || 
           RPAD(NVL(DESC_C003, ' '), 30, ' ') CODE_NAME
      FROM OCV003@B2B
     WHERE CODE_TYPE003 = %s
     ORDER BY CODE_ID003
    """

''' Combobox END '''

''' Product Start  '''
# 產品主檔_ocv015_產品代號與產品名稱
ocv015_main_product_detail_sql = """
    SELECT PRODUCT_CODE, PRODUCT_NAME, BARCODE, BRAND_NAME, CATEGORY_NAME, FLAVOR_NAME, PACKAGE_NAME, TYPE_NAME,
           CHANNEL_NAME, OTHER1, OTHER2, CAPACITY,
           FLAVOR_CODE, PACKAGE_CODE, TYPE_CODE, CHANNEL_CODE, BRAND_CODE, CATEGORY_CODE,
           SALES_UNIT_NAME, PRODUCT_RATE, PRODUCT_UNIT_NAME, INYMD015
      FROM ( WITH FILTER_CODES (CODE_TYPE, CODE_ID003, DESC_C003, OCF003_SEQ003)
                      AS ( SELECT CODE_TYPE003, CODE_ID003, DESC_C003, OCF003_SEQ003
                             FROM OCV003@B2B
                            WHERE CODE_TYPE003 IN ('01', '02', '03', '04', '05', '06') )

           SELECT P.PRODUCT_ID015 PRODUCT_CODE, P.FULL_DESC015 PRODUCT_NAME,
                  P.BARCODE015 BARCODE, BRAND.CODE_ID003 BRAND_CODE, BRAND.DESC_C003 BRAND_NAME,
                  KIND_ID.CODE_ID003 TYPE_CODE, KIND_ID.DESC_C003 TYPE_NAME, SMELL_ID.CODE_ID003 FLAVOR_CODE,
                  SMELL_ID.DESC_C003 FLAVOR_NAME, PACK.CODE_ID003 PACKAGE_CODE, PACK.DESC_C003 PACKAGE_NAME,
                  LKIND.CODE_ID003 CATEGORY_CODE, LKIND.DESC_C003 CATEGORY_NAME, DEL_TYPE.CODE_ID003 CHANNEL_CODE,
                  DEL_TYPE.DESC_C003 CHANNEL_NAME, '函文' OTHER1, '函文' OTHER2, P.LT015 CAPACITY, 
                  P.UNITB_NAME015 SALES_UNIT_NAME, P.RATEA015 PRODUCT_RATE, P.UNITA_NAME015 PRODUCT_UNIT_NAME,
                  TO_CHAR(P.INYMD015, 'YYYY/MM/DD') INYMD015
             FROM OCV015@B2B P
                      LEFT JOIN FILTER_CODES BRAND
                                ON P.BRAND_ID_OCF003_SEQ015 = BRAND.OCF003_SEQ003 AND BRAND.CODE_TYPE = '01'
                      LEFT JOIN FILTER_CODES KIND_ID
                                ON P.KIND_ID_OCF003_SEQ015 = KIND_ID.OCF003_SEQ003 AND KIND_ID.CODE_TYPE = '02'
                      LEFT JOIN FILTER_CODES SMELL_ID
                                ON P.SMELL_ID_OCF003_SEQ015 = SMELL_ID.OCF003_SEQ003 AND SMELL_ID.CODE_TYPE = '03'
                      LEFT JOIN FILTER_CODES PACK ON P.PACK_ID_OCF003_SEQ015 = PACK.OCF003_SEQ003 AND PACK.CODE_TYPE = '04'
                      LEFT JOIN FILTER_CODES LKIND
                                ON P.LKIND_ID_OCF003_SEQ015 = LKIND.OCF003_SEQ003 AND LKIND.CODE_TYPE = '05'
                      LEFT JOIN FILTER_CODES DEL_TYPE
                                ON P.DEL_TYPE_OCF003_SEQ015 = DEL_TYPE.OCF003_SEQ003 AND DEL_TYPE.CODE_TYPE = '06'
            WHERE OUTYM015 IS NULL)
    """
''' Product End '''

''' Set Start '''
# 集合包
ocv003_main_product_set_detail_sql = """
    SELECT CODE_ID003, DESC_C003
      FROM (SELECT A.CODE_ID003, A.DESC_C003
              FROM OCV003@B2B A
             WHERE CODE_TYPE003 = '12'
             UNION
            SELECT ' ' CODE_ID003, '一般' DESC_C003
              FROM DUAL)
"""
''' Set End '''

@validate_access_token_and_params(None)
def select_erp_hy_ocv015_products_code_name(request, json_data, role, user_id):
    context = "select_erp_hy_ocv015_products_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:
            try:
                cursor.execute(ocv015_combobox_product_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_erp_hy_ocv003_products_set_code_name(request, json_data, role, user_id):
    context = "select_erp_hy_ocv003_products_set_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:

            try:
                cursor.execute(ocv003_main_product_set_detail_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('code_type003')
def select_ocv003_combobox_code_name(request, json_data, role, user_id):
    context = "select_ocv003_combobox_code_name"

    if request.method == "POST":

        with connection.cursor() as cursor:

            try:
                cursor.execute(ocv003_combobox_product_code_name_sql, [json_data["code_type003"]])
                data = cursor.fetchall()
                # print('data', data)

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params_with_pagination(None)
def select_ocv015_main_product_detail(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_ocv015_main_product_detail"

    if request.method == "POST":

        # 初始化
        sql_base = ocv015_main_product_detail_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "products_code": "PRODUCT_CODE",
            "products_category": "CATEGORY_CODE",
            "products_brand": "BRAND_CODE",
            "products_type": "TYPE_CODE",
            "products_pack": "PACKAGE_CODE",
            "products_flavor": "FLAVOR_CODE",
            "products_channel": "CHANNEL_CODE",
            "products_package": "PACKAGE_CODE",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 構建完整的 SQL 查詢
        sql_query = f" {sql_base} WHERE 1 = 1 AND {' AND '.join(sql_conditions)} "
        if not sql_conditions:
            sql_query = f" {sql_base} WHERE 1 = 1 {' AND '.join(sql_conditions)} "

        ranked_sql = """
            SELECT PRODUCT_CODE, PRODUCT_NAME, BARCODE, BRAND_NAME, CATEGORY_NAME, FLAVOR_NAME, PACKAGE_NAME, TYPE_NAME,
                   CHANNEL_NAME, OTHER1, OTHER2, CAPACITY, SALES_UNIT_NAME, PRODUCT_RATE, PRODUCT_UNIT_NAME, INYMD015, 
                   DENSE_RANK() OVER (ORDER BY PRODUCT_CODE) AS RNK
              FROM ( """ + sql_query + """ )
        """

        ranked_sql1 = """
            SELECT PRODUCT_CODE, PRODUCT_NAME, BARCODE, BRAND_NAME, CATEGORY_NAME, FLAVOR_NAME, PACKAGE_NAME, TYPE_NAME,
                   CHANNEL_NAME, OTHER1, OTHER2, CAPACITY, SALES_UNIT_NAME, PRODUCT_RATE, PRODUCT_UNIT_NAME, INYMD015, RNK
              FROM ( """ + ranked_sql + """ )   
             WHERE RNK BETWEEN :qpage_number AND :qpage_size
        """

        ranked_sql2 = """ 
            SELECT MAX(RNK) RNK
              FROM ( """ + ranked_sql + """ )  
        """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]
                data = [dict(zip(col_names, row)) for row in data]
                grouped_by_eavenn = [list(group) for _, group in
                                     itertools.groupby(data, lambda x: (x["PRODUCT_CODE"]))]

                with connection.cursor() as cursor2:
                    try:
                        cursor2.execute(ranked_sql2, params2)
                        data = cursor2.fetchall()

                        if not data or data[0][0] is None:
                            return {"results": []}, status.HTTP_200_OK

                        total_rank = data[0][0]

                    except cx_Oracle.IntegrityError as e:
                        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)
                # 將當前頁面的分組資料攤平
                page_data = [item for sublist in grouped_by_eavenn for item in sublist]

                report = generate_main_marketable_products(page_data, start_rnk - 1)

                return {
                    "results": report,
                    "total_pages": total_pages,
                    "current_page": page_number,
                    "page_size": page_size
                }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                logging.error("資料庫發生錯誤", exc_info=True)
                return "資料庫發生錯誤", status.HTTP_500_INTERNAL_SERVER_ERROR

def generate_main_marketable_products(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["PRODUCT_CODE"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "product_code": group[0]["PRODUCT_CODE"],  # 產品代號
            "product_name": group[0]["PRODUCT_NAME"],  # 產品名稱
            "product_type_name": group[0]["TYPE_NAME"],  # 類別
            "product_barcode": group[0]["BARCODE"],  # 條碼
            "product_brand_name": group[0]["BRAND_NAME"],  # 品牌
            "product_category_name": group[0]["CATEGORY_NAME"],  # 大類
            "product_package_name": group[0]["PACKAGE_NAME"],  # 包裝
            "product_channel_name": group[0]["CHANNEL_NAME"],  # 通路
            "product_sales_unit_name": group[0]["SALES_UNIT_NAME"],  # 銷售單位
            "product_rate": group[0]["PRODUCT_RATE"],  # 產品比率
            "product_unit_name": group[0]["PRODUCT_UNIT_NAME"],  # 產品單位
            "product_capacity": group[0]["CAPACITY"],  # 容量
            "product_launch_date": group[0]["INYMD015"], # 上市日期
            "OTHER1": group[0]["OTHER1"],  # 產品明細
            "OTHER2": group[0]["OTHER2"],  # 公文
        }

        report.append(total_summary)

    return report

""" 產品 END """