# -*- coding: UTF8 -*-
import cx_Oracle

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' PROD_CODE_MAP INSERT START '''

# 映射字典
PROD_CODE_MAP_NAME_MAPPING = {
    "customer_store_type": "APROUT",
    "company_product_code": "APITNO",
    "customer_product_code": "APOHIT",
    "company_vendor_code": "APVENN"
}


def insert_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:

                data["company_vendor_code"] = user_id

                keys = ", ".join(PROD_CODE_MAP_NAME_MAPPING[key] for key in data.keys())
                values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

                sql = f"INSERT INTO PROD_CODE_MAP ({keys}) VALUES ({values})"
                # print('sql:', sql)
                cursor.execute(sql)

            # 在所有插入操作都成功後返回成功訊息
            return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def insert_prod_code_map_method(request, json_data, role, user_id):
    context = "insert_prod_code_map_method"

    if request.method == "POST":
        try:
            result, result_status = insert_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' PROD_CODE_MAP INSERT END '''

''' PROD_CODE_MAP UPDATE START '''


def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "customer_store_type": "APROUT",
        "company_product_code": "APITNO",
        "customer_product_code": "APOHIT",
        "company_vendor_code": "APVENN"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                data["company_vendor_code"] = user_id

                # 生成SET子句部分
                set_parts = []
                for key, value in data.items():
                    if key not in [
                            "customer_store_type", "company_vendor_code",
                            "original_customer_store_type", "original_company_product_code", "original_customer_product_code"
                        ]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                sql = f"""
                    UPDATE PROD_CODE_MAP 
                       SET {set_string} 
                     WHERE APROUT = '{data['original_customer_store_type']}'
                       AND APITNO = '{data['original_company_product_code']}'
                       AND APOHIT = '{data['original_customer_product_code']}'
                       AND APVENN = '{user_id}'
                """
                # print('sql:', sql)
                cursor.execute(sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('customer_store_type')
def update_prod_code_map_method(request, json_data, role, user_id):
    context = "update_prod_code_map_method"

    if request.method == "POST":
        try:
            result, result_status = update_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PROD_CODE_MAP UPDATE END '''

''' PROD_CODE_MAP DELETE START '''

def transform_delete_to_db_column(key):
    # 映射字典，用於將前端欄位轉換為資料庫對應的欄位
    mapping = {
        "original_customer_store_type": "APROUT",
        "original_company_product_code": "APITNO",
        "original_customer_product_code": "APOHIT",
        "company_vendor_code": "APVENN"
    }
    return mapping.get(key)

def delete_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            with connection.cursor() as cursor:

                # 生成刪除條件部分
                conditions = []
                params = []

                # 構建刪除條件的SQL語句和對應的參數
                for key, value in data.items():
                    # print('key:', key)
                    # print('value:', value)
                    db_column = transform_delete_to_db_column(key)
                    if db_column and value is not None:
                        conditions.append(f"{db_column} = %s")
                        # print('conditions:', conditions)
                        params.append(value)

                # 將APVENN (即company_vendor_code) 的值設定為user_id
                conditions.append(f"APVENN = %s")
                params.append(user_id)


                # 將條件轉換為SQL的WHERE子句
                where_clause = " AND ".join(conditions)

                # 組裝完整的SQL刪除語句
                sql = f"DELETE FROM PROD_CODE_MAP WHERE {where_clause}"

                # print('sql:', sql)
                # print('params:', params)

                # 執行SQL刪除操作
                cursor.execute(sql, params)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('original_customer_store_type', 'original_company_product_code', 'original_customer_product_code')
def delete_prod_code_map_method(request, json_data, role, user_id):
    context = "delete_prod_code_map_method"

    if request.method == "POST":
        try:
            # 刪除邏輯
            result, result_status = delete_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            # 處理刪除過程中的異常
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PROD_CODE_MAP DELETE END '''

''' PROD_CODE_MAP SELECT START '''


def transform_to_frontend_structure(data, index):
    """
    將單筆資料轉換為前端所需的格式

    Args:
        data: 單筆資料字典
        index: 索引值

    Returns:
        dict: 轉換後的資料
    """
    # 取得資料
    aprout = data.get("APROUT")  # customer_store_type
    apitno = data.get("APITNO")  # company_product_code
    apohit = data.get("APOHIT")  # customer_product_code
    apvenn = data.get("APVENN")  # company_vendor_code

    # 轉換資料結構
    return {
        "id": index,
        "customer_store_type": aprout,
        "details": [{
            "company_product_code": apitno,
            "customer_product_code": apohit,
            "company_vendor_code": apvenn
        }]
    }


def select_with_raw_sql(context, sql_conditions, params):
    if len(sql_conditions) == 0:
        sql_query = f" {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" AND {' AND '.join(sql_conditions)} "

    try:
        with connection.cursor() as cursor:
            sql = """  
                SELECT APROUT, BEDSKC, APITNO, APOHIT, APVENN
                FROM PROD_CODE_MAP, CMBE@HY_1
                WHERE BEKEY1 = '12' AND APROUT = BEKEY2 """ + sql_query + """
                ORDER BY APROUT, APITNO, APVENN
            """
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 處理資料分組
            temp_dict = {}
            result = []

            # 將資料依照 customer_store_type 分組
            for row in rows:
                row_dict = dict(zip(columns, row))
                aprout = row_dict["APROUT"]

                if aprout not in temp_dict:
                    temp_dict[aprout] = {
                        "id": len(temp_dict) + 1,
                        "customer_store_type": aprout,
                        "customer_store_name": row_dict["BEDSKC"],
                        "details": []
                    }

                temp_dict[aprout]["details"].append({
                    "company_product_code": row_dict["APITNO"],
                    "customer_product_code": row_dict["APOHIT"],
                    "company_vendor_code": row_dict["APVENN"]
                })

            # 轉換為列表
            result = list(temp_dict.values())

            return result, status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


@validate_access_token_and_params(None)
def select_prod_code_map_method(request, json_data, role, user_id):
    context = "select_prod_code_map_method"

    if request.method == "POST":
        # SQL 查詢基本結構
        sql_conditions = []
        params = {}

        # 定義可能的參數列表
        params_mapping = {
            "customer_store_type": "APROUT",
            "company_product_code": "APITNO",
            "customer_product_code": "APOHIT",
        }

        if role == '0':
            sql_conditions.append(" APVENN = :venn")
            params["venn"] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        try:
            result, result_status = select_with_raw_sql(context, sql_conditions, params)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' PROD_CODE_MAP SELECT END '''