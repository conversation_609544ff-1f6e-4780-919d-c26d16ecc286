from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.products._ProdCodeMap import select_prod_code_map_method, insert_prod_code_map_method, \
    update_prod_code_map_method, delete_prod_code_map_method
from apps.products._ProductInfo import select_erp_hy_ocv015_products_code_name, select_ocv003_combobox_code_name, \
    select_ocv015_main_product_detail, \
    select_erp_hy_ocv003_products_set_code_name
from apps.products._ProdSalesUnitInfo import select_prodsalesunit_method, insert_prodsalesunit_method, \
    delete_prodsalesunit_method, update_prodsalesunit_method
from apps.products.models import mainProduct

ACTIONS = {
    'prodsalesunit': {
        'select': select_prodsalesunit_method,
        'insert': insert_prodsalesunit_method,
        'update': update_prodsalesunit_method,
        'delete': delete_prodsalesunit_method,
    },
    'prodcodemap': {
        'select': select_prod_code_map_method,
        'insert': insert_prod_code_map_method,
        'update': update_prod_code_map_method,
        'delete': delete_prod_code_map_method,
    },
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print(sql_result, http_status)
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class ProductViewSet(viewsets.ModelViewSet ):
    queryset = mainProduct.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)


    # 下拉式_產品名稱
    @action(detail=False, methods=['post'])
    def combobox_erp_hy_ocv015_products_code_name(self, request):
        # sql查詢結果
        sql_result, http_status = select_erp_hy_ocv015_products_code_name(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        # print(sql_result, http_status)
        return JsonResponse(
            {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
            status=http_status,
            json_dumps_params={'ensure_ascii': False})

    # 下拉式_產品集合包
    @action(detail=False, methods=['post'])
    def combobox_erp_hy_ocv003_products_set_code_name(self, request):
        # sql查詢結果
        sql_result, http_status = select_erp_hy_ocv003_products_set_code_name(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        # print(sql_result, http_status)
        return JsonResponse(
            {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
            status=http_status,
            json_dumps_params={'ensure_ascii': False})

    # 下拉式_大類_品牌_包裝_通路
    @action(detail=False, methods=['post'])
    def combobox_erp_hy_ocv003_combobox_code_name(self, request):
        # sql查詢結果
        sql_result, http_status = select_ocv003_combobox_code_name(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        # print(sql_result, http_status)
        return JsonResponse(
            {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
            status=http_status,
            json_dumps_params={'ensure_ascii': False})

    #
    @action(detail=False, methods=['post'])
    def erp_hy_ocv015_main_product_detail(self, request):
        # sql查詢結果
        sql_result, http_status = select_ocv015_main_product_detail(request)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        # print(sql_result, http_status)
        return JsonResponse(
            {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
            status=http_status,
            json_dumps_params={'ensure_ascii': False})

    # 查詢產品編修
    @action(detail=False, methods=['post'])
    def select_prodsalesunit(self, request):
        return self._handle_action('prodsalesunit', 'select')

    # 新增產品編修
    @action(detail=False, methods=['post'])
    def insert_prodsalesunit(self, request):
        return self._handle_action('prodsalesunit', 'insert')

    # 刪除產品編修
    @action(detail=False, methods=['post'])
    def delete_prodsalesunit(self, request):
        return self._handle_action('prodsalesunit', 'delete')

    # 修改產品編修
    @action(detail=False, methods=['post'])
    def update_prodsalesunit(self, request):
        return self._handle_action('prodsalesunit', 'update')

    # 查詢產品代碼對應表
    @action(detail=False, methods=['post'])
    def select_prod_code_map(self, request):
        return self._handle_action('prodcodemap', 'select')

    # 新增產品代碼對應表
    @action(detail=False, methods=['post'])
    def insert_prod_code_map(self, request):
        return self._handle_action('prodcodemap', 'insert')

    # 修改產品代碼對應表
    @action(detail=False, methods=['post'])
    def update_prod_code_map(self, request):
        return self._handle_action('prodcodemap', 'update')

    # 刪除產品代碼對應表
    @action(detail=False, methods=['post'])
    def delete_prod_code_map(self, request):
        return self._handle_action('prodcodemap', 'delete')