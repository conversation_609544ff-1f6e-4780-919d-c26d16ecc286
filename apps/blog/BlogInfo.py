# -*- coding: UTF8 -*-
import logging

import cx_Oracle
import os
import uuid
import json
from django.db import connection, transaction, IntegrityError
from django.conf import settings
from django.http import JsonResponse, FileResponse, HttpResponseNotFound
from rest_framework import status, viewsets
from rest_framework.decorators import action
from django.utils import timezone
from pytz import timezone as pytz_timezone

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params, verify_access_token
from apps.blog.models import mainBlog

taipei_tz = pytz_timezone('Asia/Taipei')

''' BLOG OPERATIONS START '''

@validate_access_token_and_params(None)
def select_blog_method(request, json_data, role, user_id):
    context = "select_blog_method"
    json_data = request.data
    sql_conditions = []
    params = {}

    params_mapping = {
        "title": "TITLE LIKE :title",
        "author": "AUTHOR = :author",
        "tag": "TAGS LIKE :tag",
        "start_date": "CREATED_AT >= :start_date",
        "end_date": "CREATED_AT <= :end_date"
    }

    for param, db_field in params_mapping.items():
        if param in json_data and json_data[param]:
            sql_conditions.append(db_field)
            if param in ["title", "tag"]:
                params[param] = f"%{json_data[param]}%"
            else:
                params[param] = json_data[param]

    sql_query = f"""  
        SELECT id, title, content, author, created_at, updated_at, tags, images 
        FROM BLOG
        WHERE 1=1 {"AND " + " AND ".join(sql_conditions) if sql_conditions else ""}
        ORDER BY created_at DESC
    """

    result, status_code = execute_raw_sql(context, sql_query, params)
    if status_code == status.HTTP_200_OK:
        transformed_result = [transform_to_frontend_structure(row, idx + 1) for idx, row in enumerate(result)]
        transformed_result = [item for item in transformed_result if item is not None]
        # logging.info(f"transformed_result: {transformed_result}")
        return transformed_result, status_code
    else:
        return [], status_code


def execute_raw_sql(context, sql, params):
    try:
        with connection.cursor() as cursor:
            cursor.execute(sql, params)
            if sql.strip().upper().startswith("SELECT"):
                rows = cursor.fetchall()
                columns = [column[0].upper() for column in cursor.description]
                return [dict(zip(columns, row)) for row in rows], status.HTTP_200_OK
            else:
                return [], status.HTTP_200_OK  # 對於非SELECT操作，返回空列表
    except Exception as e:
        print(f"Error in execute_raw_sql: {str(e)}")  # 添加錯誤日誌
        return [], status.HTTP_500_INTERNAL_SERVER_ERROR  # 錯誤時返回空列表

def transform_to_frontend_structure(data, index):
    try:
        frontend_data = {
            "id": data["ID"],
            "title": data["TITLE"],
            "content": data["CONTENT"].read() if isinstance(data["CONTENT"], cx_Oracle.LOB) else str(data["CONTENT"]),
            "author": data["AUTHOR"],
            "created_at": data["CREATED_AT"].isoformat() if data["CREATED_AT"] else None,
            "updated_at": data["UPDATED_AT"].isoformat() if data["UPDATED_AT"] else None,
            "tags": data["TAGS"] if data["TAGS"] else "",
            "images": []
        }

        if data["IMAGES"]:
            try:
                if isinstance(data["IMAGES"], cx_Oracle.LOB):
                    images_str = data["IMAGES"].read()
                else:
                    images_str = str(data["IMAGES"])

                frontend_data["images"] = json.loads(images_str)
            except json.JSONDecodeError:
                logging.warning(f"Unable to parse IMAGES JSON for blog id {data['ID']}")
                frontend_data["images"] = []
            except Exception as e:
                logging.error(f"Error processing IMAGES for blog id {data['ID']}: {str(e)}")
                frontend_data["images"] = []

        return frontend_data
    except Exception as e:
        logging.error(f"Error in transform_to_frontend_structure: {str(e)}")
        return None

@validate_access_token_and_params(None)
def insert_blog_method(request, json_data, role, user_id):
    context = "create_blog_method"
    data = request.data
    logging.info(f"create_blog_method data: {data}")
    sql = """
        INSERT INTO BLOG (title, content, author, created_at, updated_at, tags, images) 
        VALUES (:title, :content, :author, :created_at, :updated_at, :tags, :images)
    """
    params = {
        'title': data['title'],
        'content': data['content'],
        'author': data['author'],
        'created_at': timezone.now().astimezone(taipei_tz),
        'updated_at': timezone.now().astimezone(taipei_tz),
        'tags': data.get('tags', ''),
        'images': json.dumps(data.get('images', []))
    }
    return execute_raw_sql(context, sql, params)

@validate_access_token_and_params(None)
def update_blog_method(request, json_data, role, user_id):
    context = "update_blog_method"
    data = request.data
    sql = """
        UPDATE BLOG SET 
        title=:title, content=:content, author=:author, 
        updated_at=:updated_at, tags=:tags, images=:images
        WHERE id=:id
    """
    params = {
        'title': data['title'],
        'content': data['content'],
        'author': data['author'],
        'updated_at': timezone.now().astimezone(taipei_tz),
        'tags': data.get('tags', ''),
        'images': json.dumps(data.get('images', [])),
        'id': data['id']
    }
    return execute_raw_sql(context, sql, params)

@validate_access_token_and_params(None)
def delete_blog_method(request, json_data, role, user_id):
    context = "delete_blog_method"
    data = request.data
    sql = "DELETE FROM BLOG WHERE id=:postId"
    params = {'postId': data['postId']}
    return execute_raw_sql(context, sql, params)

@validate_access_token_and_params(None)
def delete_blog_method(request, json_data, role, user_id):
    context = "delete_blog_method"
    data = request.data
    sql = "DELETE FROM BLOG WHERE id=:postId"
    params = {'postId': data['postId']}
    return execute_raw_sql(context, sql, params)


def upload_image_blog_method(request):
    context = "upload_image_method"

    is_valid, message, status_code = verify_access_token(request, method='GET')

    if not is_valid:
        return message, status_code

    image = request.FILES.get('image')  # 修改這裡，使用'image'而不是'file'
    if not image:
        # logging.error("No image uploaded")
        return '請選擇圖片', status.HTTP_400_BAD_REQUEST

    # 檢查檔案大小是否為0
    # print('image.size', image.size)
    if image.size == 0:
        # logging.error("Image size is 0")
        return '圖片大小為0', status.HTTP_400_BAD_REQUEST

    file_name = f"{uuid.uuid4()}.{image.name.split('.')[-1]}"
    image_dir = os.path.join(settings.BASE_DIR, 'image')
    os.makedirs(image_dir, exist_ok=True)
    file_path = os.path.join(image_dir, file_name)

    try:
        with open(file_path, 'wb+') as destination:
            for chunk in image.chunks():
                destination.write(chunk)

        # 使用完整的URL，包括域名
        image_url = request.build_absolute_uri(f"/api/blogs/get_blog_image/{file_name}")

        # 将返回结果包装为列表
        return [{'url': image_url, 'filename': file_name}], status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def get_image_blog_method(request, filename):
    # 使用 GET 方法驗證 access_token，並從 cookies 中獲取 token
    is_valid, message, status_code = verify_access_token(request, method='GET')

    if not is_valid:
        return JsonResponse({'error': message}, status=status_code, json_dumps_params={'ensure_ascii': False})

    if not filename:
        return JsonResponse({"error": "未提供檔案名稱"}, status=status.HTTP_400_BAD_REQUEST)

    image_path = os.path.join(settings.BASE_DIR, 'image', filename)
    # logging.info(f"完整圖片路徑: {image_path}")

    if os.path.exists(image_path):
        # logging.info(f"找到圖片: {image_path}")
        return FileResponse(open(image_path, 'rb')), status.HTTP_200_OK
    else:
        # logging.warning(f"圖片未找到: {image_path}")
        return JsonResponse({"error": "圖片未找到"}, status=status.HTTP_404_NOT_FOUND)


