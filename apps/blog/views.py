# -*- coding: UTF8 -*-
import logging

from django.http import JsonResponse, FileResponse
from django.views import View
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.blog.BlogInfo import select_blog_method, insert_blog_method, update_blog_method, delete_blog_method, \
    upload_image_blog_method, get_image_blog_method
from apps.blog.models import mainBlog

BLOG_ACTIONS = {
    'blog': {
        'select': select_blog_method,
        'insert': insert_blog_method,
        'update': update_blog_method,
        'delete': delete_blog_method,
        'upload_image': upload_image_blog_method,
        'get_image': get_image_blog_method,
    },
}


def handle_response(result, http_status):
    # logging.info(f"處理結果2：{result}，HTTP 狀態：{http_status}")
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'

    # 確保結果是一個列表
    if not isinstance(result, list):
        # logging.error(f"結果不是列表：{result}")
        result = []

    # 過濾掉 None 值
    result = [item for item in result if item is not None]

    # logging.info(f"處理結果：{result}，HTTP 狀態：{http_status}")

    return JsonResponse(
        {'message': result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )

class BlogViewSet(viewsets.ModelViewSet):
    queryset = mainBlog.objects.all()
    # permission_classes = [CustomTokenPermission]

    @action(detail=False, methods=['post'])
    def select_blog(self, request):
        return self._handle_action('blog', 'select', request=request)

    @action(detail=False, methods=['post'])
    def insert_blog(self, request):
        return self._handle_action('blog', 'insert', request=request)

    @action(detail=False, methods=['post'])
    def update_blog(self, request):
        return self._handle_action('blog', 'update', request=request)

    @action(detail=False, methods=['post'])
    def delete_blog(self, request):
        return self._handle_action('blog', 'delete', request=request)

    @action(detail=False, methods=['post'])
    def upload_blog_image(self, request):
        return self._handle_action('blog', 'upload_image', request=request)

    @action(detail=False, methods=['get'], url_path='get_blog_image/(?P<filename>.*)')
    def get_blog_image(self, request, filename=None):
        # logging.info(f"get_blog_image called with request: {request}, filename: {filename}")
        return self._handle_action('blog', 'get_image', request=request, filename=filename)

    def _handle_action(self, resource, action, **kwargs):
        action_result = BLOG_ACTIONS[resource][action](**kwargs)

        # 如果返回的是 JsonResponse 或 FileResponse，直接返回
        if isinstance(action_result, (JsonResponse, FileResponse)):
            return action_result

        # 確保 action_result 是一個可解包的結果
        try:
            result, http_status = action_result
            # logging.info(f"處理結果：{result}，HTTP 狀態：{http_status}")
        except ValueError as e:
            # 在日誌中記錄解包錯誤
            logging.error(f"無法解包結果：{action_result}，錯誤：{e}")
            return JsonResponse({'error': '伺服器錯誤，無法處理請求'},
                                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                                json_dumps_params={'ensure_ascii': False})
        # 如果結果是 FileResponse，直接返回
        if isinstance(result, FileResponse):
            # logging.info(f"返回 FileResponse：{result}")
            return result

        # 返回一般的 JsonResponse 結果
        return handle_response(result, http_status)

