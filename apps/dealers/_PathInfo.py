# -*- coding: gbk -*-
import itertools

import cx_Oracle

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' PATH SELECT START '''


def transform_to_frontend_structure(rows, columns):
    result = []

    grouped_data = {}  # 這個字典將用於暫存資料，鍵是 AGENT_CODE026，值是 details 的資料

    for row in rows:
        data = dict(zip(columns, row))

        dealer_code = data["AGENT_CODE026"]
        dealer_name = data["AGENT_BRIEF_NAME026"]
        sale_type = data["SALE_TYPE026"]
        sale_name = data["SALE_TYPE_NAME026"]
        car_name = data["CARNAME"]
        car_qty_board = data["CARQTYBOARD"]

        if dealer_code not in grouped_data:
            grouped_data[dealer_code] = {
                'dealer_code': dealer_code,
                'dealer_name': dealer_name,
                'details': {}
            }

        grouped_data[dealer_code]['details'][sale_type] = {
            'path_code': sale_type,
            'path_name': sale_name,
            'truck': car_name,
            'pallets': car_qty_board
        }

    # 將組裝好的 grouped_data 轉換成所需的陣列結構
    for key, value in grouped_data.items():
        result.append(value)

    return result


def select_with_raw_sql(context, sql_conditions, params):
    try:
        with connection.cursor() as cursor:

            sql = """
                SELECT AGENT_CODE026, AGENT_BRIEF_NAME026, SALE_TYPE026, SALE_TYPE_NAME026, C.CARCODE,
                       CARNAME, CARQTYBOARD
                  FROM (SELECT VENDORCODE, PLACECODE, CARCODE
                          FROM VENDORPATH) A,
                       (SELECT DISTINCT AGENT_CODE026, AGENT_BRIEF_NAME026, SALE_TYPE026, SALE_TYPE_NAME026 FROM OCV026@B2B) B,
                       (SELECT CARCODE, CARNAME, CARQTYBOARD FROM TRUCK) C
                 WHERE A.VENDORCODE = B.AGENT_CODE026
                   AND A.PLACECODE = B.SALE_TYPE026
                   AND A.CARCODE = C.CARCODE
            """

            if len(sql_conditions) == 0:
                sql_query = f" {sql} {' AND '.join(sql_conditions)} "
            else:
                sql_query = f" {sql} AND {' AND '.join(sql_conditions)} "

            sql_query += """ ORDER BY AGENT_CODE026, SALE_TYPE026 """

            # print(sql_query, params)
            cursor.execute(sql_query, params)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 直接使用 transform_to_frontend_structure 函數來進行轉換
            return transform_to_frontend_structure(rows, columns)

    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_path_method(request, json_data, role, user_id):
    context = "select_path_method"

    if request.method == "POST":

        # 初始化
        sql_conditions = []
        params = {}

        # 定義可能的參數列表
        params_mapping  = {
            "dealers_code": "AGENT_CODE026",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]

        try:
            result = select_with_raw_sql(context, sql_conditions, params)
            return result, status.HTTP_200_OK
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST


''' PATH SELECT END '''

''' PATH SELECT2 START '''

@validate_access_token_and_params_with_pagination(None)
def select_path_method2(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_path_method2"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "AGENT_CODE026",
        }

        # 循環遍歷參數列表
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        ranked_sql = """
            SELECT AGENT_CODE026, AGENT_BRIEF_NAME026, SALE_TYPE026, SALE_TYPE_NAME026, C.CARCODE,
                   CARNAME, CARQTYBOARD,
                   DENSE_RANK() OVER (ORDER BY AGENT_CODE026, SALE_TYPE026) AS RNK
              FROM (SELECT VENDORCODE, PLACECODE, CARCODE
                      FROM VENDORPATH) A,
                   (SELECT DISTINCT AGENT_CODE026, AGENT_BRIEF_NAME026, SALE_TYPE026, SALE_TYPE_NAME026 FROM OCV026@B2B) B,
                   (SELECT CARCODE, CARNAME, CARQTYBOARD FROM TRUCK) C
             WHERE A.VENDORCODE = B.AGENT_CODE026
               AND A.PLACECODE = B.SALE_TYPE026
               AND A.CARCODE = C.CARCODE
        """

        if len(sql_conditions) == 0:
            sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

        ranked_sql1 = """
            SELECT AGENT_CODE026, AGENT_BRIEF_NAME026, SALE_TYPE026, SALE_TYPE_NAME026, CARCODE,
                   CARNAME, CARQTYBOARD, RNK
              FROM ( """ + sql_query + """)   
             WHERE RNK BETWEEN :qpage_number AND :qpage_size
        """

        ranked_sql2 = """ 
            SELECT MAX(RNK) RNK
              FROM ( """ + sql_query + """)  
        """

        with connection.cursor() as cursor:
            try:
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if len(data) == 0:
                    return {"results": []}, status.HTTP_200_OK

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_400_BAD_REQUEST)

                col_names = [desc[0] for desc in cursor.description]
                data = [dict(zip(col_names, row)) for row in data]
                grouped_by_column = [list(group) for _, group in
                                     itertools.groupby(data, lambda x: (x["AGENT_CODE026"]))]

                with connection.cursor() as cursor2:
                    try:
                        cursor2.execute(ranked_sql2, params2)
                        data = cursor2.fetchall()

                        # if not data or data[0][0] is None:
                        #     return {"results": []}, status.HTTP_200_OK

                        total_rank = data[0][0]

                    except cx_Oracle.IntegrityError as e:
                        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_main_path(page_data, start_rnk - 1)

                return {
                    "results": report,
                    "total_pages": total_pages,
                    "current_page": page_number,
                    "page_size": page_size
                }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_main_path(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE026"], x["SALE_TYPE026"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE026"],  # 經銷商代碼
            "dealer_name": group[0]["AGENT_BRIEF_NAME026"],  # 經銷商名稱
            "path_name": group[0]["SALE_TYPE_NAME026"],  # 通路特性
            "car_name": group[0]["CARNAME"],  # 貨車名稱
        }

        report.append(total_summary)

    return report


''' PATH SELECT2 END '''
