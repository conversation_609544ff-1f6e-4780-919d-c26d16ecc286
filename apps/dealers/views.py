from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.dealers._DealerInfo import select_erp_hy_ocv025_dealers_code_name, select_erp_hy_ocv025_main_dealer_info, \
    select_erp_hy_ocv029_main_delivery_location, select_erp_hy_ocv035_main_marketable_products
from apps.dealers._GroupInfo import insert_dealer_group_method, select_dealer_group_method, update_dealer_group_method, \
    delete_dealer_group_method
from apps.dealers._PathInfo import select_path_method, select_path_method2
from apps.dealers._TruckInfo import insert_truck_method, select_truck_method, update_truck_method, delete_truck_method
from apps.dealers.models import mainDealer

ACTIONS = {
    'trucks': {
        'select': select_truck_method,
        'insert': insert_truck_method,
        'update': update_truck_method,
        'delete': delete_truck_method,
    },
    'paths': {
        'select': select_path_method,
        'select2': select_path_method2,
    },
    'dealer_groups': {
        'insert': insert_dealer_group_method,
        'select': select_dealer_group_method,
        'update': update_dealer_group_method,
        'delete': delete_dealer_group_method
    },
}


def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print(sql_result, http_status)
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )


class DealerViewSet(viewsets.ModelViewSet):
    queryset = mainDealer.objects.all()

    def _handle_action(self, resource, action):
        sql_result, http_status = ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 經銷商代號與名稱
    @action(detail=False, methods=['post'])
    def combobox_erp_hy_ocv025_dealers_code_name(self, request):
        sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
        return handle_response(sql_result, http_status)

    # 經銷商資料
    @action(detail=False, methods=['post'])
    def erp_hy_ocv025_main_dealer_info(self, request):
        sql_result, http_status = select_erp_hy_ocv025_main_dealer_info(request)
        return handle_response(sql_result, http_status)

    # 送貨地點
    @action(detail=False, methods=['post'])
    def erp_hy_ocv029_main_delivery_location(self, request):
        sql_result, http_status = select_erp_hy_ocv029_main_delivery_location(request)
        return handle_response(sql_result, http_status)

    # 可訂購產品代號與名稱
    @action(detail=False, methods=['post'])
    def erp_hy_ocv035_main_marketable_products(self, request):
        sql_result, http_status = select_erp_hy_ocv035_main_marketable_products(request)
        return handle_response(sql_result, http_status)

    # 新增貨車資料
    @action(detail=False, methods=['post'])
    def insert_truck(self, request):
        return self._handle_action('trucks', 'insert')

    # 刪除貨車資料
    @action(detail=False, methods=['post'])
    def delete_truck(self, request):
        return self._handle_action('trucks', 'delete')

    # 修改貨車資料
    @action(detail=False, methods=['post'])
    def update_truck(self, request):
        return self._handle_action('trucks', 'update')

    # 查詢貨車資料
    @action(detail=False, methods=['post'])
    def select_truck(self, request):
        return self._handle_action('trucks', 'select')

    # 查詢通路資料
    @action(detail=False, methods=['post'])
    def select_path(self, request):
        return self._handle_action('paths', 'select')

    # 查詢通路資料2
    @action(detail=False, methods=['post'])
    def select_path2(self, request):
        return self._handle_action('paths', 'select2')

    # 新增群組資料
    @action(detail=False, methods=['post'])
    def insert_dealer_group(self, request):
        return self._handle_action('dealer_groups', 'insert')

    # 查詢群組資料
    @action(detail=False, methods=['post'])
    def select_dealer_group(self, request):
        return self._handle_action('dealer_groups', 'select')

    # 刪除群組資料
    @action(detail=False, methods=['post'])
    def delete_dealer_group(self, request):
        return self._handle_action('dealer_groups', 'delete')

    # 修改經銷商資料
    @action(detail=False, methods=['post'])
    def update_dealer_group(self, request):
        return self._handle_action('dealer_groups', 'update')