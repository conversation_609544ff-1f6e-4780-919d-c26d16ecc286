import itertools
import json
import logging
import math

import cx_Oracle
import datetime
import calendar

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' Combobox START '''
# combobox_ocv025_經銷商代號與名稱
def ocv025_combobox_dealer_code_name_sql(sql_conditions):
    return f"""
        SELECT ROW_NUMBER() OVER (ORDER BY A.AGENT_CODE025) ID,
               RPAD(A.AGENT_CODE025, 6, ' ') || RPAD(NVL(A.BRIEF_NAME025, ' '), 16, ' ') LINETEXT
          FROM OCV025@B2B A
         WHERE {' AND '.join(sql_conditions)}
         ORDER BY A.AGENT_CODE025
    """
''' Combobox END '''

''' Dealer START '''

ocv025_main_dealer_info_sql = """
  WITH RANKED_DATA AS (
      SELECT A.AGENT_CODE025, A.FULL_NAME025, A.BRIEF_NAME025, A.SHORT_NAME025, A.REG_ADDR025,
             A.REG_ZIPCODE025,
             A.MAIL_ADDR025, A.MAIL_ZIPCODE025, A.WADDR025, A.WZIPCODE025, A.LEADER025, A.MANAGER025,
             A.NOTEA025, A.TELA025, A.NOTEB025, A.TELB025, A.NOTEC025, A.TELC025, A.TELD025, A.TELE025,
             A.TAXNO025, A.REG_NO025, A.LOC_NAME025, A.CITY_ID025, A.DEFRMDY025,
             TO_CHAR(A.RETIRE_DATE025, 'YYYMMDD') RETIRE_DATE025,
             A.CUST_TYPE_NAME025, A.EOS_CODE025, A.PURE_DAY025, A.EMAIL025, A.CELL_PHONE025,
             A.CELL_PHONE_OWNER025
        FROM OCV025@B2B A
       WHERE SUB_TYPE025 = '01'
         AND EOS_CODE025 = 'Y'
         AND RETIRE_DATE025 IS NULL
       ORDER BY A.AGENT_CODE025)
  """

''' Dealer END '''

''' DeliveryLocation START '''

ocv029_main_delivery_location_sql = """
  WITH RANKED_DATA AS (SELECT A.AGENT_CODE029, B.BRIEF_NAME025, A.DELV_ID029, A.DELV_NAME029, A.DELV_ADDR029
                         FROM OCV029@B2B A, OCF025@B2B B
                        WHERE A.OCF025_SEQ029 = B.OCF025_SEQ025
                          AND NVL(B.RETIRE_DATE025, TRUNC(SYSDATE)) >= TRUNC(SYSDATE)
                        ORDER BY A.AGENT_CODE029)
    """
''' DeliveryLocation END '''


''' MarketableProducts START '''
ocv035_main_marketable_products_sql = """
  WITH RANKED_DATA AS (SELECT A.AGENT_CODE035, B.BRIEF_NAME025, A.PRODUCT_ID035, C.SDESCR015
                         FROM OCV035@B2B A, OCF025@B2B B, OCF015@B2B C
                        WHERE A.AGENT_OCF025_SEQ035 = B.OCF025_SEQ025
                          AND A.OCF015_SEQ035 = C.OCF015_SEQ015
                          AND NVL(B.RETIRE_DATE025, TRUNC(SYSDATE)) >= TRUNC(SYSDATE)
                          AND B.SUB_TYPE025 = '01'
                          AND NVL(A.DELETE_FLAG035, 'N') = 'N'
                          AND NOT EXISTS (SELECT 1
                                            FROM OCF036@B2B C --20160519 added by cyf過濾不上傳關貿之經銷商+產品
                                           WHERE A.AGENT_OCF025_SEQ035 = C.OCF025_SEQ036
                                             AND A.OCF015_SEQ035 = C.OCF015_SEQ036)
                          AND A.BEGIN_DATE035 = (SELECT MAX(C.BEGIN_DATE035)
                                                   FROM OCF035@B2B C
                                                  WHERE C.AGENT_OCF025_SEQ035 = A.AGENT_OCF025_SEQ035
                                                    AND C.OCF015_SEQ035 = A.OCF015_SEQ035
                                                    AND NVL(C.DELETE_FLAG035, 'N') = 'N')
                        ORDER BY A.AGENT_CODE035)
    """
''' MarketableProducts END '''

@validate_access_token_and_params(None)
def select_erp_hy_ocv025_dealers_code_name(request, json_data, role, user_id):
    context = "select_erp_hy_ocv025_dealers_code_name"

    if request.method == "POST":
        with connection.cursor() as cursor:
            try:
                special_company = json_data.get("special_company", None)
                # print(special_company)

                sql_conditions = ["SUB_TYPE025 = '01'", "EOS_CODE025 = 'Y'", "RETIRE_DATE025 IS NULL"]

                if special_company == 'Y':
                    sql_conditions.append("A.AGENT_CODE025 LIKE :agent_code")
                    sSTM = ocv025_combobox_dealer_code_name_sql(sql_conditions)
                    params = {"agent_code": '7%'}
                else:
                    sSTM = ocv025_combobox_dealer_code_name_sql(sql_conditions)
                    params = {}

                # print(sSTM, params)
                cursor.execute(sSTM, params)
                data = cursor.fetchall()
                if not data:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                result = []
                for row in data:
                    result.append({"id": row[0], "name": row[1]})
                return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                print('error', str(e))
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

""" 經銷商主檔 START """

@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_ocv025_main_dealer_info(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_erp_hy_ocv025_main_dealer_info"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = ocv025_main_dealer_info_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "AGENT_CODE025",
        }

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" AGENT_CODE025 = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 構建完整的 SQL 查詢
        ranked_sql = """
            SELECT AGENT_CODE025, FULL_NAME025, BRIEF_NAME025, SHORT_NAME025, REG_ADDR025, REG_ZIPCODE025, MAIL_ADDR025,
                   MAIL_ZIPCODE025, WADDR025, WZIPCODE025, LEADER025, MANAGER025, NOTEA025, TELA025, NOTEB025, TELB025, NOTEC025,
                   TELC025, TELD025, TELE025, TAXNO025, REG_NO025, LOC_NAME025, CITY_ID025, DEFRMDY025,
                   RETIRE_DATE025, CUST_TYPE_NAME025, EOS_CODE025, PURE_DAY025, EMAIL025, CELL_PHONE025,
                   CELL_PHONE_OWNER025
              FROM RANKED_DATA
             WHERE 1 = 1 
        """

        if len(sql_conditions) == 0:
            sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

        ranked_sql0 = """
           SELECT AGENT_CODE025, FULL_NAME025, BRIEF_NAME025, SHORT_NAME025, REG_ADDR025, REG_ZIPCODE025, MAIL_ADDR025,
                  MAIL_ZIPCODE025, WADDR025, WZIPCODE025, LEADER025, MANAGER025, NOTEA025, TELA025, NOTEB025, TELB025, NOTEC025,
                  TELC025, TELD025, TELE025, TAXNO025, REG_NO025, LOC_NAME025, CITY_ID025, DEFRMDY025,
                  RETIRE_DATE025, CUST_TYPE_NAME025, EOS_CODE025, PURE_DAY025, EMAIL025, CELL_PHONE025,
                  CELL_PHONE_OWNER025,
                  DENSE_RANK() OVER (ORDER BY AGENT_CODE025) AS RNK
             FROM ( """ + sql_query + """ )
        """

        ranked_sql1 = """
            SELECT AGENT_CODE025, FULL_NAME025, BRIEF_NAME025, SHORT_NAME025, REG_ADDR025, REG_ZIPCODE025, MAIL_ADDR025,
                   MAIL_ZIPCODE025, WADDR025, WZIPCODE025, LEADER025, MANAGER025, NOTEA025, TELA025, NOTEB025, TELB025, NOTEC025,
                   TELC025, TELD025, TELE025, TAXNO025, REG_NO025, LOC_NAME025, CITY_ID025, DEFRMDY025,
                   RETIRE_DATE025, CUST_TYPE_NAME025, EOS_CODE025, PURE_DAY025, EMAIL025, CELL_PHONE025,
                   CELL_PHONE_OWNER025, RNK
              FROM ( """ + ranked_sql0 + """ )   
             WHERE RNK BETWEEN :qpage_number AND :qpage_size
        """

        ranked_sql2 = """ 
            SELECT MAX(RNK) RNK
              FROM ( """ + ranked_sql0 + """ )  
        """

        with connection.cursor() as cursor:
            try:
                # print(sql_base + ranked_sql1, params)
                cursor.execute(sql_base + ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                data = [dict(zip(col_names, row)) for row in data]
                grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["AGENT_CODE025"])]

                with connection.cursor() as cursor2:
                    try:
                        cursor2.execute(sql_base + ranked_sql2, params2)
                        data = cursor2.fetchall()

                        if data is None:
                            return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                        total_rank = data[0][0]

                    except cx_Oracle.IntegrityError as e:
                        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_main_dealer_info(page_data, start_rnk - 1)

                return {
                           "results": report,
                           "total_pages": total_pages,
                           "current_page": page_number,
                           "page_size": page_size
                       }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


def generate_main_dealer_info(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE025"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE025"],  # 經銷商代碼
            "dealer_name": group[0]["BRIEF_NAME025"],  # 經銷商名稱
            "short_code": group[0]["TELD025"],  # 簡碼
            "phone": group[0]["TELA025"],  # 電話
            "fax": group[0]["TELE025"],  # FAX
            "mobile": group[0]["CELL_PHONE025"],  # 大哥大
            "owner": group[0]["LEADER025"],  # 所有人

            "full_name": group[0]["FULL_NAME025"],  # 全銜
            "abbreviation": group[0]["BRIEF_NAME025"],  # 簡稱
            "business_address": group[0]["REG_ADDR025"],  # 營業地址
            "postal_code": group[0]["MAIL_ZIPCODE025"],  # 郵遞區號
            "mailing_address": group[0]["MAIL_ADDR025"],  # 通訊地址
            "warehouse_address": group[0]["WADDR025"],  # 倉庫地址
            "responsible_person": group[0]["LEADER025"],  # 負責人
            "manager": group[0]["MANAGER025"],  # 經理人
            "phone1": str(group[0]["NOTEA025"]) + ' ' + str(group[0]["TELA025"]) if group[0]["NOTEA025"] and group[0][
                "TELA025"] else None,
            "phone2": str(group[0]["NOTEB025"]) + ' ' + str(group[0]["TELB025"]) if group[0]["NOTEB025"] and group[0][
                "TELB025"] else None,
            "phone3": str(group[0]["NOTEC025"]) + ' ' + str(group[0]["TELC025"]) if group[0]["NOTEC025"] and group[0][
                "TELC025"] else None,
            "phone4": str(group[0]["TELD025"]),  # 電話4
            "fax_number": group[0]["TELE025"],  # 傳真機號碼
            "registration_number": group[0]["REG_NO025"],  # 統一編號
            "city_code": group[0]["CITY_ID025"],  # 縣市代號
            # "tax_id": group[0][""],  # 稅籍編號
            # "channel": group[0][""],  # 通路
            # "stop_revoke": group[0][""],  # 停止註銷
            # "counseling_department": group[0][""],  # 輔導部門
            "electronic_ordering_note": group[0]["EOS_CODE025"],  # 電子訂貨註記
            # "rc": group[0]["AGENT_CODE025"],  # RC
            "email": group[0]["EMAIL025"],  # E-MAIL
            # "supplier_category": group[0]["AGENT_CODE025"],  # 供應商類別
        }

        report.append(total_summary)

    return report


""" 經銷商主檔 END """


""" 送貨地點 START """

@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_ocv029_main_delivery_location(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_erp_hy_ocv029_main_delivery_location"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = ocv029_main_delivery_location_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping  = {
            "dealers_code": "AGENT_CODE029",
        }

        # 當role是0時，將user Id設置為dealers code的查詢條件
        if role == '0':
            sql_conditions.append(f" AGENT_CODE029 = :dealers_code ")
            params['dealers_code'] = user_id
            params2['dealers_code'] = user_id

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        # 構建完整的 SQL 查詢
        ranked_sql = """
            SELECT AGENT_CODE029, BRIEF_NAME025, DELV_ID029, DELV_NAME029, DELV_ADDR029
              FROM RANKED_DATA
             WHERE 1 = 1
        """

        if len(sql_conditions) == 0:
            sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

        ranked_sql0 = """
            SELECT AGENT_CODE029, BRIEF_NAME025, DELV_ID029, DELV_NAME029, DELV_ADDR029,
                   DENSE_RANK() OVER (ORDER BY AGENT_CODE029, DELV_ID029) AS RNK
              FROM ( """ + sql_query + """ )
        """

        ranked_sql1 = """
            SELECT AGENT_CODE029, BRIEF_NAME025, DELV_ID029, DELV_NAME029, DELV_ADDR029, RNK
              FROM ( """ + ranked_sql0 + """)   
             WHERE RNK BETWEEN :qpage_number AND :qpage_size
        """

        ranked_sql2 = """ 
            SELECT MAX(RNK) RNK
              FROM ( """ + ranked_sql0 + """)  
        """

        with connection.cursor() as cursor:
            try:
                cursor.execute(sql_base + ranked_sql1, params)
                data = cursor.fetchall()

                if len(data) == 0:
                    return {"results": []}, status.HTTP_200_OK

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]
                data = [dict(zip(col_names, row)) for row in data]
                grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE029"], x["DELV_ID029"]))]

                with connection.cursor() as cursor2:
                    try:
                        cursor2.execute(sql_base + ranked_sql2, params2)
                        data = cursor2.fetchall()

                        # if not data or data[0][0] is None:
                        #     return {"results": []}, status.HTTP_200_OK

                        total_rank = data[0][0]

                    except cx_Oracle.IntegrityError as e:
                        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_main_delivery_location(page_data, start_rnk - 1)

                return {
                           "results": report,
                           "total_pages": total_pages,
                           "current_page": page_number,
                           "page_size": page_size
                       }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def generate_main_delivery_location(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE029"], x["DELV_ID029"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE029"],  # 經銷商代碼
            "dealer_name": group[0]["BRIEF_NAME025"],  # 經銷商名稱
            "code": group[0]["DELV_ID029"],  # 代碼
            "code_name": group[0]["DELV_NAME029"],  # 代碼名稱
            "address": group[0]["DELV_ADDR029"],  # 地址
        }

        report.append(total_summary)

    return report


""" 送貨地點 END """


""" 可銷售產品 START """

@validate_access_token_and_params_with_pagination(None)
def select_erp_hy_ocv035_main_marketable_products(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_erp_hy_ocv035_main_marketable_products"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_base = ocv035_main_marketable_products_sql
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "dealers_code": "AGENT_CODE035",
            "products_code": "PRODUCT_ID035",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        ranked_sql = """
            SELECT AGENT_CODE035, BRIEF_NAME025, PRODUCT_ID035, SDESCR015
              FROM RANKED_DATA
             WHERE 1 = 1
        """

        if len(sql_conditions) == 0:
            sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
        else:
            sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

        ranked_sql0 = """
            SELECT AGENT_CODE035, BRIEF_NAME025, PRODUCT_ID035, SDESCR015,
                   DENSE_RANK() OVER (ORDER BY AGENT_CODE035, PRODUCT_ID035) AS RNK
              FROM ( """ + sql_query + """ )
        """

        ranked_sql1 = """
            SELECT AGENT_CODE035, BRIEF_NAME025, PRODUCT_ID035, SDESCR015, RNK
              FROM ( """ + ranked_sql0 + """ )   
             WHERE RNK BETWEEN :qpage_number AND :qpage_size
        """

        ranked_sql2 = """ 
            SELECT MAX(RNK) RNK
              FROM ( """ + ranked_sql0 + """ )  
        """

        with connection.cursor() as cursor:
            try:
                cursor.execute(sql_base + ranked_sql1, params)
                data = cursor.fetchall()

                if len(data) == 0:
                    return {"results": []}, status.HTTP_200_OK

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                data = [dict(zip(col_names, row)) for row in data]
                grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE035"]))]

                with connection.cursor() as cursor2:
                    try:
                        cursor2.execute(sql_base + ranked_sql2, params2)
                        data = cursor2.fetchall()

                        # if not data or data[0][0] is None:
                        #     return {"results": []}, status.HTTP_200_OK

                        total_rank = data[0][0]

                    except cx_Oracle.IntegrityError as e:
                        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_main_marketable_products(page_data, start_rnk - 1)

                return {
                           "results": report,
                           "total_pages": total_pages,
                           "current_page": page_number,
                           "page_size": page_size
                       }, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def generate_main_marketable_products(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["AGENT_CODE035"], x["PRODUCT_ID035"])):
        group = list(group)  # 將分组数据转化为列表

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "dealer_code": group[0]["AGENT_CODE035"],  # 經銷商代碼
            "dealer_name": group[0]["BRIEF_NAME025"],  # 經銷商名稱
            "product_code": group[0]["PRODUCT_ID035"],  # 產品代號
            "product_name": group[0]["SDESCR015"],  # 產品名稱
        }

        report.append(total_summary)

    return report


""" 可銷售產品 END """
