# -*- coding: UTF8 -*-
import itertools
import json
import logging
import math

import cx_Oracle
import datetime
import calendar

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' TRUCK INSERT START '''

# 映射字典
TRUCK_NAME_MAPPING = {
    "car_code": "CARCODE",
    "car_name": "CARNAME",
    "car_qty_board": "CARQTYBOARD",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID"
}

def insert_with_raw_sql(context, data):
    try:
        with connection.cursor() as cursor:
            keys = ", ".join(TRUCK_NAME_MAPPING[key] for key in data.keys())
            values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

            sql = f"INSERT INTO TRUCK ({keys}) VALUES ({values})"
            cursor.execute(sql)

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params("car_code")
def insert_truck_method(request, json_data, role, user_id):
    context = "insert_truck_method"

    if request.method == "POST":

        # 檢查資料結構
        required_keys = set(TRUCK_NAME_MAPPING.keys())
        if not required_keys.issubset(json_data.keys()):
            return handle_error(context, "Invalid data structure", status.HTTP_400_BAD_REQUEST)

        try:
            insert_with_raw_sql(context, json_data)
            return "新增成功", status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' TRUCK INSERT END '''

''' TRUCK UPDATE START '''


def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "old_car_code": "CARCODE",
        "now_car_code": "CARCODE",
        "car_name": "CARNAME",
        "car_qty_board": "CARQTYBOARD",
        "hub_type": "HUBTYPE",
        "owner_id": "OWNERID"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data):
    try:
        with connection.cursor() as cursor:
            # 生成SET子句部分
            set_parts = []
            for key, value in data.items():
                if key not in ["car_code"]:
                    db_column = transform_to_db_column(key)
                    if value is None:
                        set_parts.append(f"{db_column} = NULL")
                    else:
                        set_parts.append(f"{db_column} = '{value}'")

            set_string = ", ".join(set_parts)

            sql = f"UPDATE TRUCK SET {set_string} WHERE CARCODE = '{data['car_code']}'"
            print(sql)
            cursor.execute(sql)

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params("car_code")
def update_truck_method(request, json_data, role, user_id):
    context = "update_truck_method"

    if request.method == "POST":

        try:
            update_with_raw_sql(context, json_data)
            return "更新成功", status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' TRUCK UPDATE END '''

''' TRUCK DELETE START '''

def delete_with_raw_sql(context, car_code):
    try:
        with connection.cursor() as cursor:
            sql = f"DELETE FROM TRUCK WHERE CARCODE = '{car_code}'"
            # print(sql)
            cursor.execute(sql)

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params("car_codes")
def delete_truck_method(request, json_data, role, user_id):
    context = "delete_truck_method"

    if request.method == "POST":

        car_codes = json_data.get("car_codes", [])

        # Check if car_codes is a list and is not empty
        if not isinstance(car_codes, list) or not car_codes:
            return "car_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST

        try:
            for car_code in car_codes:
                delete_with_raw_sql(context, car_code)
            return "Data deleted successfully", status.HTTP_200_OK
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST


''' TRUCK DELETE END '''

''' TRUCK SELECT START '''

def transform_to_frontend_structure(data, index):
    # 將資料庫的結構轉換為前端所需的結構
    frontend_data = {
        "id": index,
        "car_code": data.get("CARCODE"),
        "car_name": data.get("CARNAME"),
        "car_qty_board": int(data.get("CARQTYBOARD")),  # 轉換為整數
        "hub_type": data.get("HUBTYPE"),
        "owner_name": data.get("CHI_NAME005"),
        "modify_date": data.get("OWNERDATE"),
    }
    return frontend_data


def select_with_raw_sql(context):
    try:

        with connection.cursor() as cursor:
            sql = """
        	        SELECT CARCODE, CARNAME, CARQTYBOARD, HUBTYPE, OWNERID, CHI_NAME005, TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
                      FROM TRUCK, HRF005@B2B
        		     WHERE REPLACE(OWNERID, 'h', '') = NO005(+)
                     ORDER BY CARCODE
                """

            cursor.execute(sql)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 將從資料庫中獲取的每一行資料轉換為前端需要的格式，並添加索引
            return [transform_to_frontend_structure(dict(zip(columns, row)), idx + 1) for idx, row in enumerate(rows)]

    except Exception as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params(None)
def select_truck_method(request, json_data, role, user_id):
    context = "select_truck_method"

    if request.method == "POST":

        try:
            result = select_with_raw_sql(context)
            return result, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' TRUCK SELECT END '''
