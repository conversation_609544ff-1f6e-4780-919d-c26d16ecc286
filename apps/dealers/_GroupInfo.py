# -*- coding: UTF8 -*-
import itertools
import json
import logging
import math

import cx_<PERSON>
import datetime
import calendar

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import get_ce_today, paginate_data
from utils.token_utils import validate_access_token_and_params, get_pagination_params, \
    validate_access_token_and_params_with_pagination

''' GROUP SELECT START '''
# select_使用者資料
select_dealer_group_data_sql = """
    SELECT M.GROUPCODE, GROUPNAME, CHI_NAME005, TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE,
           RPAD(AGENT_CODE025, 6, ' ') || BRIEF_NAME025 LINETEXT
      FROM ORGGROUP_M M, ORGGROUP_D D, OCV025@B2B, HRF005@B2B
      WHERE M.GROUPCODE = <PERSON>.<PERSON><PERSON><PERSON>CODE
        AND REPLACE(OWNERID, 'h', '') = NO005
        AND VENDORCODE = AGENT_CODE025
        AND SUB_TYPE025 = '01'
        AND EOS_CODE025 = 'Y'
        AND RETIRE_DATE025 IS NULL
      ORDER BY M.GROUPCODE, LINETEXT
"""
''' GROUP SELECT END '''

''' GROUP INSERT START '''

# 映射字典
DEALER_GROUP_NAME_MAPPING = {
    "group_code": "GROUPCODE",
    "group_name": "GROUPNAME",
    "dealer_code": "VENDORCODE",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID",
    "owner_date": "OWNERDATE",
}

def is_group_code_exists(group_code, cursor):
    """
    Check if the given group_code exists in the ORGGROUP_M table.
    """
    check_sql = f"SELECT 1 FROM ORGGROUP_M WHERE GROUPCODE = '{group_code}'"
    cursor.execute(check_sql)
    return bool(cursor.fetchone())

def is_vendor_exists(group_code, vendor_code, cursor):
    """
    Check if the given vendor_code exists for a group_code in the ORGGROUP_D table.
    """
    check_sql = f"SELECT 1 FROM ORGGROUP_D WHERE GROUPCODE = '{group_code}' AND VENDORCODE = '{vendor_code}'"
    cursor.execute(check_sql)
    return bool(cursor.fetchone())

def insert_dealer_group_and_details_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:
                # 檢查群組代碼是否已存在
                if is_group_code_exists(data['group_code'], cursor):
                    return handle_error(context, f"群組代號 : '{data['group_code']}' 已經存在", status.HTTP_400_BAD_REQUEST)

                # 插入主檔資料
                group_data = {
                    "group_code": data['group_code'],
                    "group_name": data['group_name'],
                    "owner_id": user_id,
                    "owner_date": get_ce_today()
                }

                keys = ", ".join(DEALER_GROUP_NAME_MAPPING[key] for key in group_data.keys())
                values = ", ".join(f"'{group_data[key]}'" if group_data[key] is not None else "null" for key in group_data.keys())
                sql = f"INSERT INTO ORGGROUP_M ({keys}) VALUES ({values})"
                cursor.execute(sql)

                # 檢查 dealer_code 是否為 None
                dealer_codes = data.get('dealer_code', [])
                if dealer_codes is None:
                    return handle_error(context, "dealer_code cannot be None", status.HTTP_400_BAD_REQUEST)

                # 插入明細檔資料
                for dealer_str in data['dealer_code']:
                    if dealer_str:  # 只在有有效的代碼時插入
                        # 檢查此群組代碼的供應商是否已存在
                        if is_vendor_exists(data['group_code'], dealer_str, cursor):
                            return handle_error(context, f"Vendor '{dealer_str}' for group '{data['group_code']}' already exists.", status.HTTP_400_BAD_REQUEST)

                        detail_data = {
                            "GROUPCODE": data['group_code'],
                            "VENDORCODE": dealer_str
                        }

                        detail_keys = ", ".join(detail_data.keys())
                        detail_values = ", ".join(f"'{detail_data[key]}'" for key in detail_data.keys())
                        detail_sql = f"INSERT INTO ORGGROUP_D ({detail_keys}) VALUES ({detail_values})"
                        cursor.execute(detail_sql)

            # 在所有插入操作都成功後返回成功訊息
            return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('group_code')
def insert_dealer_group_method(request, json_data, role, user_id):
    context = "insert_dealer_group_method"

    if request.method == "POST":

        try:
            result, result_status = insert_dealer_group_and_details_with_raw_sql(context, json_data, user_id)
            # print('result', result, result_status)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' GROUP INSERT END '''

''' GROUP SELECT START '''

def transform_to_user_frontend_structure(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["GROUPCODE"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "group_code": group[0]["GROUPCODE"],
            "group_name": group[0]["GROUPNAME"],
            "owner_name": group[0]["CHI_NAME005"],
            "owner_date": group[0]["OWNERDATE"],
            "details": []
        }
        # 插入明細
        details = []
        for item in group:
            details.append(item["LINETEXT"])


        total_summary["details"] = details
        report.append(total_summary)
    return report

def select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk):

    ranked_sql = """
                SELECT GROUPCODE, GROUPNAME, CHI_NAME005, OWNERDATE, LINETEXT,
                       DENSE_RANK() OVER (ORDER BY GROUPCODE) AS RNK
                  FROM (  """ + select_dealer_group_data_sql + """  )
                 WHERE 1 = 1
            """

    if len(sql_conditions) == 0:
        sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

    ranked_sql1 = """
                SELECT GROUPCODE, GROUPNAME, CHI_NAME005, OWNERDATE, LINETEXT, RNK
                  FROM (  """ + sql_query + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size
                 
            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + sql_query + """ )  
            """

    try:
        with connection.cursor() as cursor:
            # print(ranked_sql1, params)
            cursor.execute(ranked_sql1, params)
            # print(ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_400_BAD_REQUEST)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["GROUPCODE"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_user_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

@validate_access_token_and_params_with_pagination(None)
def select_dealer_group_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_dealer_group_method"

    if request.method == "POST":
        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            # "group_code": "GROUPCODE",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        try:
            result, result_status = select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' GROUP SELECT END '''

''' GROUP UPDATE START '''

def update_dealer_group_and_details_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:

                # 更新主檔資料
                group_data = {
                    "group_code": data['group_code'],
                    "group_name": data['group_name'],
                    "owner_id": user_id,
                    "owner_date": get_ce_today()
                }

                cursor.execute("SELECT 1 FROM ORGGROUP_M WHERE GROUPCODE = %s", [group_data["group_code"]])
                if cursor.fetchone():
                    set_clause = ", ".join(
                        f"{DEALER_GROUP_NAME_MAPPING[key]} = '{group_data[key]}'" for key in group_data.keys())
                    sql = f"UPDATE ORGGROUP_M SET {set_clause} WHERE GROUPCODE = '{group_data['group_code']}'"
                    cursor.execute(sql)
                else:
                    return handle_error(context, f"GROUPCODE '{group_data['group_code']}' not found in ORGGROUP_M.", status.HTTP_400_BAD_REQUEST)

                # 先刪除所有與此group_code相關的明細
                cursor.execute("DELETE FROM ORGGROUP_D WHERE GROUPCODE = %s", [group_data["group_code"]])

                # 插入新的明細檔資料
                for dealer_str in data['dealer_code']:
                    if dealer_str:  # 只有在有有效的代碼時才插入
                        detail_data = {
                            "GROUPCODE": data['group_code'],
                            "VENDORCODE": dealer_str
                        }
                        detail_keys = ", ".join(detail_data.keys())
                        detail_values = ", ".join(f"'{detail_data[key]}'" for key in detail_data.keys())
                        detail_sql = f"INSERT INTO ORGGROUP_D ({detail_keys}) VALUES ({detail_values})"
                        cursor.execute(detail_sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('group_code')
def update_dealer_group_method(request, json_data, role, user_id):
    context = "update_dealer_group_method"

    if request.method == "POST":
        try:
            result, result_status = update_dealer_group_and_details_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' GROUP UPDATE END '''

''' GROUP DELETE START '''

def delete_with_raw_sql(context, group_codes):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for group_code in group_codes:
                with connection.cursor() as cursor:
                    # 先刪除所有與此group_code相關的明細
                    cursor.execute("DELETE FROM ORGGROUP_D WHERE GROUPCODE = %s", [group_code])

                    # 刪除主檔資料
                    cursor.execute("DELETE FROM ORGGROUP_M WHERE GROUPCODE = %s", [group_code])

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('group_code')
def delete_dealer_group_method(request, json_data, role, user_id):
    context = "delete_dealer_group_method"

    if request.method == "POST":

        group_codes = json_data.get("group_code", [])

        # Check if group_codes is a list and is not empty
        if not isinstance(group_codes, list) or not group_codes:
            return handle_error(context, "group_codes must be a list and cannot be empty", status.HTTP_400_BAD_REQUEST)

        # 使用原生SQL進行數據刪除
        try:
            result, result_status = delete_with_raw_sql(context, group_codes)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' GROUP DELETE END '''

