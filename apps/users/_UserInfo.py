# -*- coding: UTF8 -*-
import itertools

import cx_Oracle

from django.db import connection, IntegrityError, transaction
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import get_ce_today, paginate_data
from utils.token_utils import validate_access_token_and_params, \
    validate_access_token_and_params_with_pagination, get_pagination_params

''' User Combobox START '''
# combobox_使用者代號與名稱
combobox_user_code_name_sql = """
    SELECT ROW_NUMBER() OVER (ORDER BY 
               CASE 
                   WHEN REGEXP_LIKE(USERID, '^[A-Za-z]') THEN 1 
                   ELSE 2 
               END,
               USERID
           ) ID, 
           RPAD(USERID, 10, ' ') || 
           RPAD(USERNAME, 30, ' ') CODE_NAME
      FROM USERS
     ORDER BY 
           CASE 
               WHEN REGEXP_LIKE(USERID, '^[A-Za-z]') THEN 1 
               ELSE 2 
           END,
           USERID
    """
''' User Combobox END '''

''' Dept Combobox START '''
# combobox_部門代號與名稱
combobox_dept_code_name_sql = """
    SELECT ROW_NUMBER() OVER (ORDER BY DEPT_CODE007) ID,
           RPAD(DEPT_CODE007, 10, ' ') ||
           RPAD(DEPT_DESC_C007, 30, ' ') CODE_NAME
      FROM (SELECT DEPT_CODE007, DEPT_DESC_C007
              FROM HRF007@B2B
             WHERE REMOVE_DATE007 IS NULL
             UNION ALL
            SELECT DEPTCODE, DEPTNAME
              FROM DEPT
             ORDER BY DEPT_CODE007)
 """
''' Dept Combobox END '''

''' USER SELECT START '''
# select_使用者資料
select_user_data_sql = """
    SELECT USERID, USERNAME, BAN, NOTICETYPE, STARTDATE, EXPIREDATE, USERSTAT, UPDUSER, UPDTIME,
           DEPTCODE, DEPTNAME, DEPTSESSNO,
           USERLEVEL, SYSROLE, ISPRICE, RECEIVEMAIL, EMAIL, HUBTYPE, OWNERID, CHI_NAME005, OWNERDATE
      FROM (SELECT USERID, USERNAME, BAN, NOTICETYPE,
                   TO_CHAR(STARTDATE, 'YYYY/MM/DD') STARTDATE,
                   TO_CHAR(EXPIREDATE, 'YYYY/MM/DD') EXPIREDATE,
                   USERSTAT, UPDUSER,
                   TO_CHAR(UPDTIME, 'YYYY/MM/DD') UPDTIME,
                   DEPTCODE,
                   DEPTSESSNO, USERLEVEL, SYSROLE, ISPRICE, RECEIVEMAIL, EMAIL, HUBTYPE, OWNERID,
                   TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
              FROM USERS),
           (SELECT NO005, CHI_NAME005 FROM HRF005@B2B),
           (SELECT DEPTCODE CODE, DEPTNAME FROM DEPT UNION ALL SELECT DEPT_CODE007, DEPT_DESC_C007 FROM HRF007@B2B)
     WHERE REPLACE(OWNERID, 'h', '') = NO005(+) AND DEPTCODE = CODE(+)
    """
''' USER SELECT END '''

''' USER SELECT2 START '''
# select_使用者資料
select_user_data_sql2 = """
    SELECT USERID, USERNAME, NOTICETYPE, STARTDATE, EXPIREDATE, USERSTAT, UPDUSER, UPDTIME,
           RECEIVEMAIL, EMAIL
      FROM (SELECT USERID, USERNAME, NOTICETYPE,
                   TO_CHAR(STARTDATE, 'YYYY/MM/DD') STARTDATE,
                   TO_CHAR(EXPIREDATE, 'YYYY/MM/DD') EXPIREDATE,
                   USERSTAT, UPDUSER,
                   TO_CHAR(UPDTIME, 'YYYY/MM/DD') UPDTIME,
                   RECEIVEMAIL, EMAIL, OWNERID
              FROM USERS),
           (SELECT NO005, CHI_NAME005 FROM HRF005@B2B)
     WHERE REPLACE(OWNERID, 'h', '') = NO005(+)
    """
''' USER SELECT2 END '''

@validate_access_token_and_params(None)
def select_combobox_user_code_name(request, json_data, role, user_id):
    context = "select_combobox_user_code_name"

    if request.method == "POST":
        with connection.cursor() as cursor:
            try:
                cursor.execute(combobox_user_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_combobox_dept_code_name(request, json_data, role, user_id):
    context = "select_combobox_dept_code_name"

    if request.method == "POST":
        with connection.cursor() as cursor:

            try:
                cursor.execute(combobox_dept_code_name_sql, ())
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)
                else:
                    result = []
                    for row in data:
                        result.append({"id": row[0], "name": row[1]})
                    return result, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' USER SELECT START '''

def transform_to_user_frontend_structure(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["USERID"])):
        group = list(group)

        # 檢查 DEPTCODE 和 DEPTNAME 是否為 None
        dept_code = group[0]["DEPTCODE"] if group[0]["DEPTCODE"] is not None else ""
        dept_name = group[0]["DEPTNAME"] if group[0]["DEPTNAME"] is not None else ""

        # 結合 DEPTCODE 和 DEPTNAME
        dept_combined = f'{dept_code} {dept_name}'

        total_summary = {
            "index": len(report) + 1 + start_index,
            "user_code": group[0]["USERID"],
            "user_name": group[0]["USERNAME"],
            "gui_number": group[0]["BAN"],
            "notice_type": group[0]["NOTICETYPE"],
            "start_date": group[0]["STARTDATE"],
            "expire_date": group[0]["EXPIREDATE"],
            "user_stat": group[0]["USERSTAT"],
            "upd_user": group[0]["UPDUSER"],
            "upd_time": group[0]["UPDTIME"],
            "dept_code": group[0]["DEPTCODE"],
            "dept_name": dept_combined,
            "dept_sess_no": group[0]["DEPTSESSNO"],
            "user_level": group[0]["USERLEVEL"],
            "sys_role": group[0]["SYSROLE"],
            "is_price": group[0]["ISPRICE"],
            "receive_mail": group[0]["RECEIVEMAIL"],
            "email": group[0]["EMAIL"],
            "hub_type": group[0]["HUBTYPE"],
            "owner_id": group[0]["OWNERID"],
            "owner_name": group[0]["CHI_NAME005"],
            "owner_date": group[0]["OWNERDATE"],
        }
        report.append(total_summary)
    return report

def select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk):

    ranked_sql = """
                SELECT USERID, USERNAME, BAN, NOTICETYPE, STARTDATE, EXPIREDATE, USERSTAT, UPDUSER, UPDTIME, DEPTCODE, DEPTNAME,
                       DEPTSESSNO, USERLEVEL, SYSROLE, ISPRICE, RECEIVEMAIL, EMAIL, HUBTYPE, OWNERID, CHI_NAME005, OWNERDATE, 
                       DENSE_RANK() OVER (ORDER BY 
                           CASE 
                               WHEN REGEXP_LIKE(USERID, '^[A-Za-z]') THEN 1 
                               ELSE 2 
                           END,
                           USERID
                       ) AS RNK
                  FROM (  """ + select_user_data_sql + """  )
                 WHERE 1 = 1
            """

    # print('ranked_sql', ranked_sql)

    if len(sql_conditions) == 0:
        sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

    # print('sql_query', sql_query)

    ranked_sql1 = """
                SELECT USERID, USERNAME, BAN, NOTICETYPE, STARTDATE, EXPIREDATE, USERSTAT, UPDUSER, UPDTIME, DEPTCODE, DEPTNAME,
                       DEPTSESSNO, USERLEVEL, SYSROLE, ISPRICE, RECEIVEMAIL, EMAIL, HUBTYPE, OWNERID, CHI_NAME005, OWNERDATE, RNK
                  FROM (  """ + sql_query + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size
                 
            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + sql_query + """ )  
            """
    try:
        with connection.cursor() as cursor:
            cursor.execute(ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["USERID"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_user_frontend_structure(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params_with_pagination(None)
def select_user_data_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_user_data_method"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping  = {
            "user_code": "USERID",
            "dept_code": "DEPTCODE",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        try:
            result, result_status = select_with_raw_sql(context, sql_conditions, params, params2, page_number, page_size, start_rnk)
            # print(result)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

''' USER SELECT END '''

''' USER SELECT2 START '''


def transform_to_user_frontend_structure2(data, start_index=0):
    report = []

    for _, group in itertools.groupby(data, lambda x: (x["USERID"])):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,
            "user_code": group[0]["USERID"],
            "user_name": group[0]["USERNAME"],
            "notice_type": group[0]["NOTICETYPE"],
            "start_date": group[0]["STARTDATE"],
            "expire_date": group[0]["EXPIREDATE"],
            "upd_user": group[0]["UPDUSER"],
            "upd_time": group[0]["UPDTIME"],
            "receive_mail": group[0]["RECEIVEMAIL"],
            "email": group[0]["EMAIL"],
        }
        report.append(total_summary)
    return report


def select_with_raw_sql2(context, sql_conditions, params, params2, page_number, page_size, start_rnk):
    ranked_sql = """
                SELECT USERID, USERNAME, NOTICETYPE, STARTDATE, EXPIREDATE, UPDUSER, UPDTIME,
                       RECEIVEMAIL, EMAIL,
                       DENSE_RANK() OVER (ORDER BY USERID) AS RNK
                  FROM (  """ + select_user_data_sql2 + """  )
                 WHERE 1 = 1
            """

    if len(sql_conditions) == 0:
        sql_query = f" {ranked_sql} {' AND '.join(sql_conditions)} "
    else:
        sql_query = f" {ranked_sql} AND {' AND '.join(sql_conditions)} "

    ranked_sql1 = """
                SELECT USERID, USERNAME, NOTICETYPE, STARTDATE, EXPIREDATE, UPDUSER, UPDTIME,
                       RECEIVEMAIL, EMAIL, RNK
                  FROM (  """ + sql_query + """  )
                 WHERE RNK BETWEEN :qpage_number AND :qpage_size

            """

    ranked_sql2 = """ 
                SELECT MAX(RNK) RNK
                  FROM ( """ + sql_query + """ )  
            """
    try:
        with connection.cursor() as cursor:
            cursor.execute(ranked_sql1, params)
            data = cursor.fetchall()

            if len(data) == 0:
                return {"results": []}, status.HTTP_200_OK

            if data is None:
                return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            # I'm assuming here that you want to group by some column, change as necessary
            grouped_by_column = [list(group) for _, group in itertools.groupby(data, lambda x: x["USERID"])]

            with connection.cursor() as cursor2:
                cursor2.execute(ranked_sql2, params2)
                total_data = cursor2.fetchall()

                # if not total_data or total_data[0][0] is None:
                #     return {"results": []}, status.HTTP_200_OK

                total_rank = total_data[0][0]

            # 使用分頁函數
            page_number, total_pages = paginate_data(total_rank, page_size, page_number)

            # 將當前頁面的資料攤平
            page_data = [item for sublist in grouped_by_column for item in sublist]

            report = transform_to_user_frontend_structure2(page_data, start_rnk - 1)

            return {
                "results": report,
                "total_pages": total_pages,
                "current_page": page_number,
                "page_size": page_size
            }, status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


@validate_access_token_and_params_with_pagination('user_code')
def select_user_data_method2(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = "select_user_data_method2"

    if request.method == "POST":

        # SQL 查詢基本結構
        sql_conditions = []
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "user_code": "USERID",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        try:
            result, result_status = select_with_raw_sql2(context, sql_conditions, params, params2, page_number,
                                                         page_size, start_rnk)
            # print(result)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' USER SELECT2 END '''

''' USER UPDATE START '''

def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "user_code": "USERID",
        "user_name": "USERNAME",
        "start_date": "STARTDATE",
        "expire_date": "EXPIREDATE",
        "dept_code": "DEPTCODE",
        "gui_number": "BAN",
        "email": "EMAIL",
        "update_user": "UPDUSER",
        "update_date": "UPDTIME",
        "owner_id": "OWNERID",
        "owner_date": "OWNERDATE",
    }
    return mapping.get(key)

def update_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 生成SET子句部分
                set_parts = []

                set_parts.append(f"OWNERDATE = TO_DATE('{get_ce_today()}', 'YYYYMMDD')")
                set_parts.append(f"UPDTIME = TO_DATE('{get_ce_today()}', 'YYYYMMDD')")
                set_parts.append(f"UPDUSER = '{user_id}'")
                set_parts.append(f"OWNERID = '{user_id}'")

                for key, value in data.items():
                    if key not in ["user_code", "owner_date", "update_date", "update_user", "owner_id"]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                sql = f"UPDATE USERS SET {set_string} WHERE USERID = '{data['user_code']}'"
                # print(sql)
                cursor.execute(sql)

                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('user_code')
def update_user_data_method(request, json_data, role, user_id):
    context = "update_user_data_method"

    if request.method == "POST":
        try:
            result, result_status = update_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST


''' USER UPDATE END '''

''' USER INSERT START '''

# 映射字典
USER_NAME_MAPPING = {
    "user_code": "USERID",
    "user_pswd": "PSWD",
    "user_name": "USERNAME",
    "start_date": "STARTDATE",
    "expire_date": "EXPIREDATE",
    "dept_code": "DEPTCODE",
    "email": "EMAIL",
    "user_role": "SYSROLE",

    "gui_number": "BAN",
    "receive_mail": "RECEIVEMAIL",
    "update_user": "UPDUSER",
    "update_date": "UPDTIME",
    "owner_id": "OWNERID",
    "owner_date": "OWNERDATE",
}

def insert_with_raw_sql(context, data, user_id):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:

                # 如果sys_role = 1，則ban = 2065190與pswd = 1234，否則 = 前端ban的與pswd=ban
                if data.get("user_role") == "1":
                    data["gui_number"] = "20651901"
                    data["user_pswd"] = "1234"
                else:
                    data["gui_number"] = data.get("gui_number")
                    data["user_pswd"] = data.get("gui_number")

                # Add backend-defined data
                data['receive_mail'] = '1'
                data['update_date'] = get_ce_today()
                data['update_user'] = user_id #user_id.replace('h', '')
                # 移除用戶代號第一字h
                data['owner_id'] = user_id #user_id.replace('h', '')
                data['owner_date'] = get_ce_today()

                keys = ", ".join(USER_NAME_MAPPING[key] for key in data.keys())
                values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

                sql = f"INSERT INTO USERS ({keys}) VALUES ({values})"
                # print(sql)
                cursor.execute(sql)

                return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('user_code')
def insert_user_data_method(request, json_data, role, user_id):
    context = "insert_user_data_method"

    if request.method == "POST":
        try:
            result, result_status = insert_with_raw_sql(context, json_data, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' USER INSERT END '''

''' USER DELETE START '''

def delete_with_raw_sql(context, user_codes):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for user_code in user_codes:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM USERS WHERE USERID = '{user_code}'"
                    cursor.execute(sql)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('user_code')
def delete_user_data_method(request, json_data, role, user_id):
    context = "delete_user_data_method"

    if request.method == "POST":
        user_codes = json_data.get("user_code", [])

        # Check if user_codes is a list and is not empty
        if not isinstance(user_codes, list) or not user_codes:
            return handle_error(context, "user_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST)

        try:
            result, result_status = delete_with_raw_sql(context, user_codes)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' USER DELETE END '''

''' USER PASSWORD UPDATE START '''
@validate_access_token_and_params('user_code')
def update_user_password_method(request, json_data, role, user_id):
    context = "update_user_password_method"

    if request.method == "POST":
        try:
            user_code = json_data.get("user_code")
            user_old_password = json_data.get("old_password")
            user_new_password = json_data.get("new_password")

            # 檢查舊密碼是否正確
            with connection.cursor() as cursor:
                sql = f"SELECT * FROM USERS WHERE USERID = '{user_code}' AND PSWD = '{user_old_password}'"
                cursor.execute(sql)
                user = cursor.fetchone()

                if user is None:
                    return handle_error(context, "舊密碼錯誤", status.HTTP_400_BAD_REQUEST)

            with connection.cursor() as cursor:
                sql = f"UPDATE USERS SET PSWD = '{user_new_password}' WHERE USERID = '{user_code}'"
                cursor.execute(sql)

            return "更新成功", status.HTTP_200_OK

        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)
''' USER PASSWORD UPDATE END '''