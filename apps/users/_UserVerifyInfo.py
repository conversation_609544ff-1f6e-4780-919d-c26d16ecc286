# -*- coding: UTF8 -*-
import json
import logging
import cx_Oracle

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import verify_access_token, validate_access_token_and_params


def select_verify_user(request):
    if request.method == "POST":

        is_valid, message, status_code = verify_access_token(request)

        if not is_valid:
            return message, status_code
        else:
            return message, status.HTTP_200_OK

@validate_access_token_and_params('user_code')
def select_check_user_code_method(request, json_data, role, user_id):
    context = "select_check_user_code_method"

    if request.method == "POST":

        # 初始化
        strCond = ''
        condition = {}

        # 定義可能的參數列表
        params = {
            "user_code": "USERID",
        }

        # 循環遍歷參數列表
        for param, field in params.items():
            if param in json_data and json_data[param] is not None:
                value = json_data[param]
                condition[f'f_{param}'] = value
                strCond += f' AND {field} = :f_{param} '
        # 連線資料庫
        with connection.cursor() as cursor:
            # 查詢使用者是否存在
            strSelectUserExist = """
                 SELECT USERID, USERNAME
                   FROM USERS
                  WHERE 1 = 1
                   """ + strCond + """
            """

            # print(strSelectUserExist)

            try:
                cursor.execute(strSelectUserExist, condition)
                user = cursor.fetchone()
                # print(user)
                if user is None:
                    return False, status.HTTP_200_OK
                else:
                    return True, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


