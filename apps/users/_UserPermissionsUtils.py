# import json
# import logging
#
# from django.db import connection
# from rest_framework import permissions
# from utils.token_utils import
#
# class CustomUserPermission(permissions.BasePermission):
#
#     def has_permission(self, request, view):
#         # 驗證token
#         is_valid, message, status_code = verify_access_token(request)
#
#         if not is_valid:
#             return False
#
#         # 從URL中提取'resource'和'action'
#         path_parts = request.path.strip('/').split('/')
#
#         # 取URL的前6個字元作為操作
#         action = path_parts[2][:6].upper()
#
#         user_permissions = self.get_user_permissions(request, message.get('userId'))
#
#         # print('user_permissions', user_permissions)
#
#         if user_permissions is None:
#             return False
#
#         return action in user_permissions
#
#     def get_user_permissions(self, request, user_id):
#
#         PERMISSIONS_MAPPING = {
#             'CAN_ADD': 'CREATE',
#             'CAN_MODIFY': 'UPDATE',
#             'CAN_DELETE': 'DELETE',
#             'CAN_QUERY': 'SELECT',
#             'CAN_SPECIAL': 'SPECIAL'
#         }
#
#         # 取得傳入參數
#         json_data = json.loads(request.body)
#
#         program_id = json_data.get('program_id')
#
#         # print('json_data', json_data)
#
#         cursor = connection.cursor()
#         cursor.execute("""
#             SELECT USER_ID, MODULE_ID, CAN_ADD, CAN_MODIFY, CAN_DELETE, CAN_QUERY, CAN_SPECIAL
#               FROM USER_PERMISSIONS
#              WHERE USER_ID = %s
#                AND MODULE_ID = %s
#         """, [user_id, program_id])
#
#         # 欄位名稱
#         column_names = [col[0] for col in cursor.description]
#
#         rows = cursor.fetchall()
#
#         # print('rows', rows)
#
#         # 如果沒有數據，直接返回空 list
#         if not rows:
#             return []
#
#         permissions_row = rows[0]
#
#         # 使用list comprehension對查詢結果進行映射轉換，將資料是1的欄位名稱轉換成對應的權限
#         user_permissions = [PERMISSIONS_MAPPING[col_name] for col_name, value in
#                             zip(column_names[2:], permissions_row[2:]) if value == '1']
#
#         return user_permissions
