# -*- coding: UTF8 -*-
import cx_Oracle
import datetime
import calendar

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' USERFUNGROUP INSERT START '''

# 映射字典
USERFUNGROUP_NAME_MAPPING = {
    "fun_code": "FUNCO<PERSON>",
    "fun_name": "FUNNAME",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID"
}

def insert_with_raw_sql(context, data):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:
                keys = ", ".join(USERFUNGROUP_NAME_MAPPING[key] for key in data.keys())
                values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

                sql = f"INSERT INTO USERFUNGROUP ({keys}) VALUES ({values})"
                cursor.execute(sql)

                return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def insert_userfungroup_method(request, json_data, role, user_id):
    context = "insert_userfungroup_method"

    if request.method == "POST":
        try:
            result, result_status = insert_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' USERFUNGROUP INSERT END '''

''' USERFUNGROUP UPDATE START '''

def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "old_fun_code": "FUNCODE",
        "now_fun_code": "FUNCODE",
        "fun_name": "FUNNAME",
        "hub_type": "HUBTYPE",
        "owner_id": "OWNERID"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 生成SET子句部分
                set_parts = []
                for key, value in data.items():
                    if key not in ["fun_code"]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                sql = f"UPDATE USERFUNGROUP SET {set_string} WHERE FUNCODE = '{data['fun_code']}'"
                # print(sql)
                cursor.execute(sql)

                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('fun_code')
def update_userfungroup_method(request, json_data, role, user_id):
    context = "update_userfungroup_method"

    if request.method == "POST":
        try:
            result, result_status = update_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' USERFUNGROUP UPDATE END '''

''' USERFUNGROUP DELETE START '''


def delete_with_raw_sql(context, fun_codes):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for fun_code in fun_codes:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM USERFUNGROUP WHERE FUNCODE = '{fun_code}'"
                    cursor.execute(sql)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('fun_codes')
def delete_userfungroup_method(request, json_data, role, user_id):
    context = "delete_userfungroup_method"

    if request.method == "POST":
        fun_codes = json_data.get("fun_codes", [])

        # Check if fun_codes is a list and is not empty
        if not isinstance(fun_codes, list) or not fun_codes:
            return handle_error(context, "fun_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST)

        try:
            result, result_status = delete_with_raw_sql(context, fun_codes)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' USERFUNGROUP DELETE END '''

''' USERFUNGROUP SELECT START '''


def transform_to_frontend_structure(data, index):
    # 將資料庫的結構轉換為前端所需的結構
    frontend_data = {
        "id": index,
        "fun_code": data.get("FUNCODE"),
        "fun_name": data.get("FUNNAME"),
        "hub_type": data.get("HUBTYPE"),
        "owner_name": data.get("CHI_NAME005"),
        "modify_date": data.get("OWNERDATE"),
    }
    return frontend_data


def select_with_raw_sql(context):
    try:
        with connection.cursor() as cursor:
            sql = """
        		    SELECT FUNCODE, FUNNAME, HUBTYPE, OWNERID, CHI_NAME005, TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
                      FROM USERFUNGROUP, HRF005@B2B
        			 WHERE REPLACE(OWNERID, 'h', '') = NO005
                     ORDER BY FUNCODE
                """

            cursor.execute(sql)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 將從資料庫中獲取的每一行資料轉換為前端需要的格式，並添加索引
            return [transform_to_frontend_structure(dict(zip(columns, row)), idx + 1) for idx, row in enumerate(rows)], status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_userfungroup_method(request, json_data, role, user_id):
    context = "select_userfungroup_method"

    if request.method == "POST":
        try:
            result, result_status = select_with_raw_sql(context)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' USERFUNGROUP SELECT END '''
