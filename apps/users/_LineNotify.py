# -*- coding: UTF8 -*-
import itertools
import requests
import logging
import math

import cx_Oracle
from django.core.paginator import Paginator

from django.db import connection, IntegrityError, transaction
from django.http import HttpResponseNotAllowed
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import status
from rest_framework.decorators import api_view

from HEYSONG_ERP_HY_API import settings
from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' 查詢使用者是否有綁定LINE NOTIFY START '''
def select_with_raw_sql(context, user_id):
    try:
        with connection.cursor() as cursor:
            sql = f"SELECT * FROM USERS WHERE USERID = :user_id AND LINE_NOTIFY_TOKEN IS NOT NULL"
            condition = {
                "user_id": user_id
            }
            cursor.execute(sql, condition)
            row = cursor.fetchone()
            if row is None:
                return False, status.HTTP_200_OK

            if len(row) == 0:
                return False, status.HTTP_200_OK

            return user_id, status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def select_user_line_notify_method(request, json_data, role, user_id):
    context = "select_line_notify_method"

    if request.method == "POST":
        try:
            result, result_status = select_with_raw_sql(context, user_id)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' 綁定LINE NOTIFY給使用者 START '''
@method_decorator(csrf_exempt, name='dispatch')
def update_user_line_notify_method(request):
    context = "update_line_notify_method"

    try:
        if request.method == "POST":

            code = request.POST.get('code')
            state = request.POST.get('state')

            token_response = exchange_code_for_token(code)

            # print('token_response', token_response.get('access_token'))

            if token_response.get('access_token') is None:
                return "綁定失敗，請重新綁定", status.HTTP_200_OK

            # 將access_token存入資料庫
            with connection.cursor() as cursor:
                # 查詢使用者是否存在
                sql_select = f"SELECT * FROM USERS WHERE USERID = :user_id"
                condition = {
                    "user_id": state
                }
                cursor.execute(sql_select, condition)

                user = cursor.fetchone()

                if user is None:
                    return False, status.HTTP_200_OK

                sql = f"UPDATE USERS SET LINE_NOTIFY_TOKEN = :token WHERE USERID = :user_id"
                condition = {
                    "token": token_response.get('access_token'),
                    "user_id": state
                }
                # print(sql, condition)
                cursor.execute(sql, condition)

                send_message_to_user(state, "黑松供銷鏈綁定成功")

            return "綁定成功，可以關閉此網頁", status.HTTP_200_OK
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)
''' TAIWAN LINE NOTIFY TO USER END '''

def exchange_code_for_token(code):
    token_url = "https://notify-bot.line.me/oauth/token"
    payload = {
        "grant_type": "authorization_code",
        "code": code,
        "redirect_uri": settings.WEB_URL + "/api/users/update_user_line_notify/",
        "client_id": 'h2SHft1xYNcfDoB9qWadRN',
        "client_secret": 'NTISWAeANjsTukyQ7tXHTBDOcQAeG28YIQlSzjN4EHE',
    }
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    response = requests.post(token_url, data=payload, headers=headers)
    return response.json()
''' 綁定台灣LINE NOTIFY給使用者 END '''

''' 解除綁定LINE NOTIFY給使用者 START '''
@validate_access_token_and_params(None)
def delete_user_line_notify_method(request, json_data, role, user_id):
    context = "delete_line_notify_method"

    if request.method == "POST":
        try:

            # 查詢使用者的Line Notify Token
            with connection.cursor() as cursor:
                sql = f"SELECT LINE_NOTIFY_TOKEN FROM USERS WHERE USERID = :user_id"
                condition = {
                    "user_id": user_id
                }
                cursor.execute(sql, condition)
                row = cursor.fetchone()
                if row is None:
                    return False, status.HTTP_200_OK
                line_notify_token = row[0]

            token_url = "https://notify-api.line.me/api/revoke"
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": f"Bearer {line_notify_token}"
            }
            requests.post(token_url, headers=headers)

            with connection.cursor() as cursor:
                sql = f"UPDATE USERS SET LINE_NOTIFY_TOKEN = '' WHERE USERID = :user_id"
                condition = {
                    "user_id": user_id
                }
                cursor.execute(sql, condition)
                return "解除綁定成功", status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)
''' 解除綁定LINE NOTIFY給使用者 END '''

''' 測試LINE NOTIFY是否正常 START '''
@validate_access_token_and_params(None)
def test_line_notify_method(request, json_data, role, user_id):
    context = "test_line_notify_method"

    if request.method == "POST":
        try:
            message_text = "我的使用者代號 : " + user_id + "\n" + "測試LINE NOTIFY正常"
            send_message_to_user(user_id, message_text)
            return "測試成功", status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

''' 使用LINE NOTIFY發送訊息給使用者 START '''
def send_message_to_user(user_id, message):
    # 查詢使用者的Line Notify Token
    with connection.cursor() as cursor:
        sql = f"SELECT LINE_NOTIFY_TOKEN FROM USERS WHERE USERID = :user_id"
        condition = {
            "user_id": user_id
        }
        cursor.execute(sql, condition)
        row = cursor.fetchone()
        if row is None:
            return False, status.HTTP_200_OK
        line_notify_token = row[0]

    token_url = "https://notify-api.line.me/api/notify"
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": f"Bearer {line_notify_token}"
    }
    payload = {
        "message": "\n" + message
    }
    requests.post(token_url, headers=headers, data=payload)
''' 使用LINE NOTIFY發送訊息給使用者 END '''


