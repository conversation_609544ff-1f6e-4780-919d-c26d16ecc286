# -*- coding: UTF8 -*-
import itertools
import json
import logging
import cx_<PERSON>

from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error
from utils.main_utils import paginate_data
from utils.token_utils import validate_access_token_and_params, validate_access_token_and_params_with_pagination, \
    get_pagination_params

''' SELECT USERLOG START '''
select_business_notification_user_log_sql = """
  WITH DEALERORDEPT AS
           (SELECT CASE
                       WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                           THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                       ELSE TRIM(SUBSTR(DEPT, 21, 10))
                   END AS DEALERORDEPT,
                   CASE
                       WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                           THEN DEPT
                       ELSE SUBSTR(DEPT, 21, 10) || TRIM(SUBSTR(DEPT, 31, 20))
                   END AS DEPT
              FROM (SELECT RPAD('HSG001', 10, ' ') || RPAD('總公司', 10, ' ') || RPAD(DEPTCODE, 10, ' ') ||
                           DEPTNAME AS DEPT
                      FROM (SELECT DEPT_CODE007 AS DEPTCODE, DEPT_DESC_C007 AS DEPTNAME
                              FROM HRF007@B2B
                             WHERE REMOVE_DATE007 IS NULL
                             UNION ALL
                            SELECT DEPTCODE, DEPTNAME
                              FROM DEPT, HRF005@B2B
                             WHERE REPLACE(OWNERID, 'h', '') = NO005
                             ORDER BY DEPTCODE)
                     UNION ALL
                    SELECT DEALER
                      FROM (SELECT RPAD(NVL('', ' '), 10, ' ') || RPAD(NVL('', ' '), 10, ' ') ||
                                   RPAD(AGENT_CODE025, 10, ' ') ||
                                   BRIEF_NAME025 DEALER
                              FROM OCV025@B2B
                             WHERE SUB_TYPE025 = '01'
                               AND EOS_CODE025 = 'Y'
                               AND RETIRE_DATE025 IS NULL
                             ORDER BY AGENT_CODE025))),

       PUDCRECEIVER_DEALER AS (SELECT PUDCNO, DEPT
                                 FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                         FROM BPUDCHT_PUDCRECEIVER
                                        WHERE 1 = 1), DEALERORDEPT
                                WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
       COPY_DEALER AS (SELECT PUDCNO, DEPT
                         FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                 FROM BPUDCHT_COPY
                                WHERE 1 = 1), DEALERORDEPT
                        WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
       PUDCRECEIVER_DEALERORDEPT AS (SELECT PUDCNO,
                                            CASE
                                                WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                    THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                            END AS DEALER_DEPT
                                       FROM PUDCRECEIVER_DEALER
                                      UNION
                                     SELECT PUDCNO,
                                            CASE
                                                WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                    THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                            END AS DEALER_DEPT
                                       FROM COPY_DEALER)


SELECT NVL(SOURCESERNO, PUDCNO_1) SOURCESERNO,
       NVL(USERID, USERID_1) USERID,
       NVL(USERNAME, USERNAME_1) USERNAME,
       ACTIONTYPE,
       CASE
           WHEN ACTIONTYPE = 'A'
               THEN '新增'
           WHEN ACTIONTYPE = 'D'
               THEN '已刪除'
           WHEN ACTIONTYPE = 'E'
               THEN '修改'
           WHEN ACTIONTYPE = 'P'
               THEN '列印'
           WHEN ACTIONTYPE = 'T'
               THEN 'E-Mail寄送'
           WHEN ACTIONTYPE = 'R'
               THEN '已讀'
           WHEN ACTIONTYPE IS NULL
               THEN '未讀'
           ELSE '未知'
       END AS ACTIONTYPE_NAME,
       TO_CHAR(OWNERDATE, 'YYYY/MM/DD HH24:MI:SS') OWNERDATE
  FROM (SELECT PARENTID, CHILDID, SOURCESERNO, ACTIONTYPE,
               B.USERID, B.USERNAME, A.OWNERDATE
          FROM USERLOG A, USERS B
         WHERE A.USERID = B.USERID
           AND PARENTID = :parent_id AND CHILDID = :child_id AND SOURCESERNO = :serial_no
         ORDER BY ACTIONTYPE, USERID)
           FULL JOIN
       (SELECT PUDCNO PUDCNO_1,
               NVL(USERID, DEALER_DEPT) USERID_1,
               NVL(USERNAME, '尚未建立使用者') USERNAME_1
          FROM (SELECT USERID, USERNAME, DEPTCODE FROM USERS),
               (SELECT PUDCNO, DEALER_DEPT
                  FROM PUDCRECEIVER_DEALERORDEPT
                 WHERE SUBSTR(DEALER_DEPT, 1, 6) <> 'HSG001')
         WHERE DEPTCODE = SUBSTR(DEALER_DEPT, 1, 6) AND PUDCNO = :serial_no
        UNION ALL
        SELECT PUDCNO PUDCNO_1,
               NVL(USERID, DEALER_DEPT) USERID_1,
               NVL(USERNAME, '尚未建立使用者') USERNAME_1
          FROM (SELECT USERID, USERNAME, DEPTCODE FROM USERS),
               (SELECT PUDCNO, DEALER_DEPT
                  FROM PUDCRECEIVER_DEALERORDEPT
                 WHERE SUBSTR(DEALER_DEPT, 1, 6) <> 'HSG001')
         WHERE USERID = SUBSTR(DEALER_DEPT, 1, 6) AND PUDCNO = :serial_no)
       ON SOURCESERNO = PUDCNO_1 AND USERID = USERID_1
 """
''' SELECT BUSINESS_NOTIFIATION_USERLOG END '''

''' SELECT DOCUMENT_USERLOG START '''
select_document_user_log_sql = """
  WITH DEALERORDEPT AS
           (SELECT CASE
                       WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                           THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                       ELSE TRIM(SUBSTR(DEPT, 21, 10))
                   END AS DEALERORDEPT,
                   CASE
                       WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                           THEN DEPT
                       ELSE SUBSTR(DEPT, 21, 10) || TRIM(SUBSTR(DEPT, 31, 20))
                   END AS DEPT
              FROM (SELECT RPAD('HSG001', 10, ' ') || RPAD('總公司', 10, ' ') || RPAD(DEPTCODE, 10, ' ') ||
                           DEPTNAME AS DEPT
                      FROM (SELECT DEPT_CODE007 AS DEPTCODE, DEPT_DESC_C007 AS DEPTNAME
                              FROM HRF007@B2B
                             WHERE REMOVE_DATE007 IS NULL
                             UNION ALL
                            SELECT DEPTCODE, DEPTNAME
                              FROM DEPT, HRF005@B2B
                             WHERE REPLACE(OWNERID, 'h', '') = NO005
                             ORDER BY DEPTCODE)
                     UNION ALL
                    SELECT DEALER
                      FROM (SELECT RPAD(NVL('', ' '), 10, ' ') || RPAD(NVL('', ' '), 10, ' ') ||
                                   RPAD(AGENT_CODE025, 10, ' ') ||
                                   BRIEF_NAME025 DEALER
                              FROM OCV025@B2B
                             WHERE SUB_TYPE025 = '01'
                               AND EOS_CODE025 = 'Y'
                               AND RETIRE_DATE025 IS NULL
                             ORDER BY AGENT_CODE025))),

       PUDCRECEIVER_DEALER AS (SELECT PUDCNO, DEPT
                                 FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                         FROM PUDCHT_PUDCRECEIVER
                                        WHERE 1 = 1), DEALERORDEPT
                                WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
       COPY_DEALER AS (SELECT PUDCNO, DEPT
                         FROM (SELECT PUDCNO, VENDORCODE, DEPTCODE
                                 FROM PUDCHT_COPY
                                WHERE 1 = 1), DEALERORDEPT
                        WHERE VENDORCODE || DEPTCODE = DEALERORDEPT),
       PUDCRECEIVER_DEALERORDEPT AS (SELECT PUDCNO,
                                            CASE
                                                WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                    THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                            END AS DEALER_DEPT
                                       FROM PUDCRECEIVER_DEALER
                                      UNION
                                     SELECT PUDCNO,
                                            CASE
                                                WHEN TRIM(SUBSTR(DEPT, 1, 10)) = 'HSG001'
                                                    THEN 'HSG001' || TRIM(SUBSTR(DEPT, 18, 10))
                                                ELSE TRIM(SUBSTR(DEPT, 1, 10))
                                            END AS DEALER_DEPT
                                       FROM COPY_DEALER)


SELECT NVL(SOURCESERNO, PUDCNO_1) SOURCESERNO,
       NVL(USERID, USERID_1) USERID,
       NVL(USERNAME, USERNAME_1) USERNAME,
       ACTIONTYPE,
       CASE
           WHEN ACTIONTYPE = 'A'
               THEN '新增'
           WHEN ACTIONTYPE = 'D'
               THEN '已刪除'
           WHEN ACTIONTYPE = 'E'
               THEN '修改'
           WHEN ACTIONTYPE = 'P'
               THEN '列印'
           WHEN ACTIONTYPE = 'T'
               THEN 'E-Mail寄送'
           WHEN ACTIONTYPE = 'R'
               THEN '已讀'
           WHEN ACTIONTYPE IS NULL
               THEN '未讀'
           ELSE '未知'
       END AS ACTIONTYPE_NAME,
       TO_CHAR(OWNERDATE, 'YYYY/MM/DD HH24:MI:SS') OWNERDATE
  FROM (SELECT PARENTID, CHILDID, SOURCESERNO, ACTIONTYPE,
               B.USERID, B.USERNAME, A.OWNERDATE
          FROM USERLOG A, USERS B
         WHERE A.USERID = B.USERID
           AND PARENTID = :parent_id AND CHILDID = :child_id AND SOURCESERNO = :serial_no
         ORDER BY ACTIONTYPE, USERID)
           FULL JOIN
       (SELECT PUDCNO PUDCNO_1,
               NVL(USERID, DEALER_DEPT) USERID_1,
               NVL(USERNAME, '尚未建立使用者') USERNAME_1
          FROM (SELECT USERID, USERNAME, DEPTCODE FROM USERS),
               (SELECT PUDCNO, DEALER_DEPT
                  FROM PUDCRECEIVER_DEALERORDEPT
                 WHERE SUBSTR(DEALER_DEPT, 1, 6) <> 'HSG001')
         WHERE DEPTCODE = SUBSTR(DEALER_DEPT, 1, 6) AND PUDCNO = :serial_no
        UNION ALL
        SELECT PUDCNO PUDCNO_1,
               NVL(USERID, DEALER_DEPT) USERID_1,
               NVL(USERNAME, '尚未建立使用者') USERNAME_1
          FROM (SELECT USERID, USERNAME, DEPTCODE FROM USERS),
               (SELECT PUDCNO, DEALER_DEPT
                  FROM PUDCRECEIVER_DEALERORDEPT
                 WHERE SUBSTR(DEALER_DEPT, 1, 6) <> 'HSG001')
         WHERE USERID = SUBSTR(DEALER_DEPT, 1, 6) AND PUDCNO = :serial_no )
       ON SOURCESERNO = PUDCNO_1 AND USERID = USERID_1
    """
''' SELECT DOCUMENT_USERLOG END '''


# 新增使用者讀取、新增、修改、列印、EMAIL寄送
@validate_access_token_and_params(None)
def insert_action_user_log_method(request, json_data, role, user_id):
    context = 'insert_action_user_log_method'

    if request.method == "POST":

        # parent_id
        parent_id = json_data.get('parent_id')

        # child_id
        child_id = json_data.get('child_id')

        # serial_no (現在是一個陣列)
        serial_nos = json_data.get('serial_no', [])

        # action_type
        action_type = json_data.get('action_type')

        # 檢查使用者的SOURCESERNO是否存在資料
        with connection.cursor() as cursor:
            try:
                results = []
                for serial_no in serial_nos:
                    # 如果資料庫ACTIONTYPE不是空的，則跳過
                    sql = """ 
                        SELECT SOURCESERNO 
                          FROM USERLOG
                         WHERE USERID = :qUSERID AND PARENTID = :qPARENTID AND CHILDID = :qCHILDID AND SOURCESERNO = :qSOURCESERNO 
                           AND ACTIONTYPE IS NOT NULL 
                    """

                    params = {'qUSERID': user_id, 'qPARENTID': parent_id, 'qCHILDID': child_id, 'qSOURCESERNO': serial_no}

                    cursor.execute(sql, params)
                    row = cursor.fetchone()

                    if row is not None:
                        continue

                    sql = """ 
                        SELECT SOURCESERNO 
                          FROM USERLOG
                         WHERE USERID = :qUSERID AND PARENTID = :qPARENTID AND CHILDID = :qCHILDID AND SOURCESERNO = :qSOURCESERNO """
                    params = {'qUSERID': user_id, 'qPARENTID': parent_id, 'qCHILDID': child_id, 'qSOURCESERNO': serial_no}
                    # print(sql, params)
                    cursor.execute(sql, params)
                    row = cursor.fetchone()

                    if row is None:
                        # 如果row不存在，則新增一筆資料到USERLOG，查看如入的ACTIONTYPE是0:READ  A:ADD  E:EDIT  P:PRINT T:EMAIL寄送
                        insert_sql = """ INSERT INTO USERLOG(PARENTID, CHILDID, SOURCESERNO, USERID, ACTIONTYPE, HUBTYPE, OWNERID, OWNERDATE)
                                         VALUES(:qPARENTID, :qCHILDID, :qSOURCESERNO, :qUSERID, :qACTIONTYPE, :HUBTYPE, :qOWNERID, SYSDATE) """

                        params = {'qPARENTID': parent_id, 'qCHILDID': child_id,
                                  'qSOURCESERNO': serial_no, 'qUSERID': user_id, 'qACTIONTYPE': action_type,
                                  'HUBTYPE': '', 'qOWNERID': user_id}

                        cursor.execute(insert_sql, params)
                        results.append(f'新增成功: {serial_no}')
                    else:
                        # 如果row存在，則更新USERLOG的ACTIONTYPE
                        update_sql = """ 
                            UPDATE USERLOG SET ACTIONTYPE = :qACTIONTYPE, OWNERDATE = SYSDATE 
                             WHERE USERID = :qUSERID AND PARENTID = :qPARENTID AND CHILDID = :qCHILDID AND SOURCESERNO = :qSOURCESERNO """

                        params = {'qACTIONTYPE': action_type, 'qUSERID': user_id, 'qPARENTID': parent_id, 'qCHILDID': child_id, 'qSOURCESERNO': serial_no}
                        cursor.execute(update_sql, params)
                        results.append(f'更新成功: {serial_no}')

                return results, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

# 查詢使用者讀取
@validate_access_token_and_params_with_pagination('parent_id', 'child_id', 'serial_no')
def select_action_user_log_method(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk):
    context = 'select_action_user_log_method'

    if request.method == "POST":

        # SQL 查詢基本結構
        params = {}
        params2 = {}

        # 處理分頁參數
        pagination_params = get_pagination_params(context, start_rnk, end_rnk)
        params.update(pagination_params)

        # 定義可能的參數列表
        params_mapping = {
            "parent_id": "PARENTID",
            "child_id": "CHILDID",
            "serial_no": "SOURCESERNO",
        }

        # 自動化處理參數
        for param, db_field in params_mapping.items():
            if param in json_data and json_data[param]:
                # sql_conditions.append(f" {db_field} = :{param}")
                params[param] = json_data[param]
                params2[param] = json_data[param]

        if params.get("parent_id") == "********" and params.get("child_id") == "G002_1":
            select_sql = select_business_notification_user_log_sql
        elif params.get("parent_id") == "********" and params.get("child_id") == "G001_1":
            select_sql = select_document_user_log_sql
        else:
            return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

        ranked_sql = """
                    SELECT SOURCESERNO, ACTIONTYPE, ACTIONTYPE_NAME, USERID, USERNAME, OWNERDATE,
                           DENSE_RANK() OVER (ORDER BY USERID) AS RNK
                      FROM (  """ + select_sql + """  )
                     ORDER BY SOURCESERNO, ACTIONTYPE, USERID
                """

        ranked_sql1 = """
                    SELECT SOURCESERNO, ACTIONTYPE, ACTIONTYPE_NAME, USERID, USERNAME, OWNERDATE, 
                           RNK
                      FROM (  """ + ranked_sql + """  )
                     WHERE RNK BETWEEN :qpage_number AND :qpage_size        
                """

        ranked_sql2 = """
                    SELECT MAX(RNK) RNK
                      FROM ( """ + ranked_sql + """ )
                """

        with connection.cursor() as cursor:
            try:
                # print(ranked_sql1, params)
                cursor.execute(ranked_sql1, params)
                data = cursor.fetchall()

                if data is None:
                    return handle_error(context, "查無資料", status.HTTP_404_NOT_FOUND)

                col_names = [desc[0] for desc in cursor.description]

                # 將每一行數據轉換為字典，並依據支票號碼排序
                data = [dict(zip(col_names, row)) for row in data]
                # data.sort(reverse=True, key=lambda x: x['SLIP_NO250'])

                # 依照支票號碼分組資料
                grouped_by_column = [
                    list(group)
                    for _, group in itertools.groupby(data, key=lambda x: x['USERID'])
                ]

                with connection.cursor() as cursor2:
                    # print(ranked_sql2, params2)
                    cursor2.execute(ranked_sql2, params2)
                    total_data = cursor2.fetchall()

                    if not total_data or total_data[0][0] is None:
                        return {"results": []}, status.HTTP_200_OK

                    total_rank = total_data[0][0]

                # 使用分頁函數
                page_number, total_pages = paginate_data(total_rank, page_size, page_number)

                # 將當前頁面的資料攤平
                page_data =  [item for sublist in grouped_by_column for item in sublist]

                report = generate_select_action_user_log(page_data, start_rnk - 1)

                return {"results": report, "total_pages": total_pages, "current_page": page_number,
                        "page_size": page_size}, status.HTTP_200_OK

            except cx_Oracle.IntegrityError as e:
                return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def generate_select_action_user_log(data, start_index=0):
    report = []
    for track_invoice, group in itertools.groupby(data, lambda x: x["SOURCESERNO"]):
        group = list(group)

        total_summary = {
            "index": len(report) + 1 + start_index,  # 加上起始索引
            "slip_no": track_invoice,
        }

        details = []
        for product, product_group in itertools.groupby(group, lambda x: x["USERID"]):
            product_group = list(product_group)

            detail = {
                "index": len(details) + 1,
                "user_id": product,
                "user_name": product_group[0]["USERNAME"],
                "action_type_name": product_group[0]["ACTIONTYPE_NAME"],
                "owner_date": product_group[0]["OWNERDATE"],
            }
            details.append(detail)

        total_summary["details"] = details
        report.append(total_summary)

    return report