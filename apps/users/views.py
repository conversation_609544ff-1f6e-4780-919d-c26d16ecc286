from django.http import JsonResponse
from rest_framework import viewsets, status
from rest_framework.decorators import action

from apps.users._LineNotify import update_user_line_notify_method, select_user_line_notify_method, \
    delete_user_line_notify_method, test_line_notify_method
from apps.users._UserDeptInfo import select_dept_method, insert_dept_method, update_dept_method, delete_dept_method
from apps.users._UserFunGroupInfo import select_userfungroup_method, insert_userfungroup_method, update_userfungroup_method, \
    delete_userfungroup_method
from apps.users._UserInfo import select_combobox_user_code_name, select_combobox_dept_code_name, \
    select_user_data_method, update_user_data_method, insert_user_data_method, delete_user_data_method, \
    select_user_data_method2, update_user_password_method
from apps.users._UserLog import insert_action_user_log_method, select_action_user_log_method
from apps.users._UserPermissionInfo import select_user_permission_method, update_user_permission_method
from apps.users._UserProgramNameInfo import select_user_main_menu_program_and_permission_method, \
    select_user_tag_program_and_permission_method
from apps.users._UserVerifyInfo import select_verify_user, select_check_user_code_method
from apps.users.models import mainUser

USER_ACTIONS = {
    'user_fun_group': {
        'select': select_userfungroup_method,
        'insert': insert_userfungroup_method,
        'update': update_userfungroup_method,
        'delete': delete_userfungroup_method,
    },
    'user_dept': {
        'select': select_dept_method,
        'insert': insert_dept_method,
        'update': update_dept_method,
        'delete': delete_dept_method,
    },
    'user_permissions': {
        'select': select_user_permission_method,
        'update': update_user_permission_method,
    },
    'user_program_and_permission': {
        'select': select_user_main_menu_program_and_permission_method,
        'select2': select_user_tag_program_and_permission_method,
    },
    'user_info': {
        'select': select_combobox_user_code_name,
        'select2': select_combobox_dept_code_name,
    },
    'user_data': {
        'select': select_user_data_method,
        'select2': select_user_data_method2,
        'update': update_user_data_method,
        'update2': update_user_password_method,
        'insert': insert_user_data_method,
        'delete': delete_user_data_method,
    },
    'user_verify': {
        'select': select_check_user_code_method,
    },
    'user_log': {
        'insert': insert_action_user_log_method,
        'select': select_action_user_log_method,
    },
    'user_line_notify': {
        'select': select_user_line_notify_method,
        'select2': test_line_notify_method,
        'update': update_user_line_notify_method,
        'delete': delete_user_line_notify_method,
    },
}

def handle_response(sql_result, http_status):
    if http_status == status.HTTP_200_OK:
        data = '成功'
    else:
        data = '失敗'
    # print('sql_result',{'message': sql_result, 'meta': {'msg': data, 'status': http_status}})
    return JsonResponse(
        {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
        status=http_status,
        json_dumps_params={'ensure_ascii': False}
    )


class UserViewSet(viewsets.ModelViewSet ):
    queryset = mainUser.objects.all()
    # permission_classes = [CustomTokenPermission]

    def _handle_action(self, resource, action):
        sql_result, http_status = USER_ACTIONS[resource][action](self.request)
        return handle_response(sql_result, http_status)

    # 驗證使用者
    @action(detail=False, methods=['post'])
    def select_check_user(self, request):
        # sql查詢結果
        sql_result, http_status = select_verify_user(request)

        # print(sql_result, http_status)

        if http_status == status.HTTP_200_OK:
            data = '成功'
        else:
            data = '失敗'

        if isinstance(sql_result, (str, dict)):
            return JsonResponse(
                {'message': sql_result, 'meta': {'msg': data, 'status': http_status}},
                status=http_status,
                json_dumps_params={'ensure_ascii': False})
        else:
            return sql_result

    # 查詢部門資料
    @action(detail=False, methods=['post'])
    def select_user_dept(self, request):
        return self._handle_action('user_dept', 'select')
    
    # 新增部門資料
    @action(detail=False, methods=['post'])
    def insert_user_dept(self, request):
        return self._handle_action('user_dept', 'insert')
    
    # 修改部門資料
    @action(detail=False, methods=['post'])
    def update_user_dept(self, request):
        return self._handle_action('user_dept', 'update')
    
    # 刪除部門資料
    @action(detail=False, methods=['post'])
    def delete_user_dept(self, request):
        return self._handle_action('user_dept', 'delete')

    # 查詢使用者群組
    @action(detail=False, methods=['post'])
    def select_user_fun_group(self, request):
        return self._handle_action('user_fun_group', 'select')

    # 刪除使用者群組
    @action(detail=False, methods=['post'])
    def delete_user_fun_group(self, request):
        return self._handle_action('user_fun_group', 'delete')

    # 新增使用者群組
    @action(detail=False, methods=['post'])
    def insert_user_fun_group(self, request):
        return self._handle_action('user_fun_group', 'insert')

    # 修改使用者群組
    @action(detail=False, methods=['post'])
    def update_user_fun_group(self, request):
        return self._handle_action('user_fun_group', 'update')

    # 查詢使用者程式權限
    @action(detail=False, methods=['post'])
    def select_user_permissions(self, request):
        return self._handle_action('user_permissions', 'select')

    # 更新使用者程式權限
    @action(detail=False, methods=['post'])
    def update_user_permissions(self, request):
        return self._handle_action('user_permissions', 'update')

    # 查詢使用者主菜單程式與權限
    @action(detail=False, methods=['post'])
    def select_user_main_menu_program_and_permission(self, request):
        return self._handle_action('user_program_and_permission', 'select')

    # 查詢使用者標籤程式與權限
    @action(detail=False, methods=['post'])
    def select_user_tag_program_and_permission(self, request):
        return self._handle_action('user_program_and_permission', 'select2')

    # 查詢使用者代號與名稱
    @action(detail=False, methods=['post'])
    def select_combobox_user_code_name(self, request):
        return self._handle_action('user_info', 'select')

    # 查詢部門代號與名稱
    @action(detail=False, methods=['post'])
    def select_combobox_dept_code_name(self, request):
        return self._handle_action('user_info', 'select2')

    # 查詢使用者資料
    @action(detail=False, methods=['post'])
    def select_user_data(self, request):
        return self._handle_action('user_data', 'select')

    # 查詢使用者資料2
    @action(detail=False, methods=['post'])
    def select_user_data2(self, request):
        return self._handle_action('user_data', 'select2')

    # 更新使用者資料
    @action(detail=False, methods=['post'])
    def update_user_data(self, request):
        return self._handle_action('user_data', 'update')

    # 更新使用者密碼
    @action(detail=False, methods=['post'])
    def update_user_password(self, request):
        return self._handle_action('user_data', 'update2')

    # 新增使用者資料
    @action(detail=False, methods=['post'])
    def insert_user_data(self, request):
        return self._handle_action('user_data', 'insert')

    # 刪除使用者資料
    @action(detail=False, methods=['post'])
    def delete_user_data(self, request):
        return self._handle_action('user_data', 'delete')

    # 驗證使用者代號
    @action(detail=False, methods=['post'])
    def select_check_user_code(self, request):
        return self._handle_action('user_verify', 'select')

    # 新增使用者操作紀錄
    @action(detail=False, methods=['post'])
    def insert_user_log(self, request):
        return self._handle_action('user_log', 'insert')

    # 查詢使用者操作紀錄
    @action(detail=False, methods=['post'])
    def select_user_log(self, request):
        return self._handle_action('user_log', 'select')

    # 查詢使用者是否有綁定LINE NOTIFY
    @action(detail=False, methods=['post'])
    def select_user_line_notify(self, request):
        return self._handle_action('user_line_notify', 'select')

    # 測試LINE NOTIFY
    @action(detail=False, methods=['post'])
    def test_user_line_notify(self, request):
        return self._handle_action('user_line_notify', 'select2')

    # 綁定LINE NOTIFY給使用者
    @action(detail=False, methods=['post', 'get'])
    def update_user_line_notify(self, request):
        return self._handle_action('user_line_notify', 'update')

    # 解除綁定LINE NOTIFY給使用者
    @action(detail=False, methods=['post'])
    def delete_user_line_notify(self, request):
        return self._handle_action('user_line_notify', 'delete')

