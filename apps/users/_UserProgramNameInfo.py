# -*- coding: UTF8 -*-
import json
import logging
import cx_<PERSON>

from django.db import connection
from rest_framework import status

from utils.token_utils import validate_access_token_and_params

''' 查詢使用者主菜單程式與權限 SQL START '''
main_menu_sql = """
    SELECT M.MODULE_ID, M.<PERSON>_NAME, M.HRE<PERSON>, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = '10000000'
       AND UP.CAN_QUERY <> '0' 
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者主菜單程式與權限SQL END '''

''' 查詢使用者子菜單程式與權限 SQL START '''
sub_menu_sql = """
    SELECT M.MODULE_ID, <PERSON><PERSON>_NAME, M.HREF, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = %s
       AND UP.CAN_QUERY <> '0' 
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者子菜單程式與權限 SQL END '''

''' 查詢使用者子標籤權限 SQL START '''
sub_tab_sql = """
    SELECT M.MODULE_ID, M.MODULE_NAME, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = %s
       AND UP.CAN_QUERY <> '0' 
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者子標籤權限 SQL END '''

def ensure_not_none(permission):
    return permission if permission is not None else '0'


# 查詢使用者主菜單程式與權限方法 START
@validate_access_token_and_params(None)
def select_user_main_menu_program_and_permission_method(request, json_data, role, user_id):
    try:

        main_menus = fetch_main_menus(user_id)
        # print('main_menus', user_id, main_menus)
        message = process_main_menus(user_id, main_menus)

        return message, status.HTTP_200_OK

    except ValueError:
        return "Invalid JSON", status.HTTP_400_BAD_REQUEST
    except Exception as e:
        logging.error("Unexpected error: %s", str(e))
        return "An error occurred in the database", status.HTTP_500_INTERNAL_SERVER_ERROR


def fetch_main_menus(user_id):
    with connection.cursor() as cursor:
        cursor.execute(main_menu_sql, [user_id])  # Your main menu SQL query here
        return cursor.fetchall()


def process_main_menus(user_id, main_menus):
    message = []
    for menu_data in main_menus:
        menu_details = extract_menu_details(user_id, menu_data)
        message.append(menu_details)
    return message


def extract_menu_details(user_id, menu_data):
    menu_id, menu_name, menu_href, menu_can_add, menu_can_modify, menu_can_delete, menu_can_query, menu_can_special = menu_data
    children = fetch_children(user_id, menu_id)

    menu_details = {
        "id": menu_id,
        "name": menu_name,
        "href": menu_href,
        "details": {"children": children}
    }
    return menu_details


def fetch_children(user_id, menu_id):
    with connection.cursor() as cursor:
        cursor.execute(sub_menu_sql, [user_id, menu_id])  # Your children SQL query here
        children_data = []
        for child_data in cursor.fetchall():
            child_details = extract_child_details(user_id, child_data)
            children_data.append(child_details)
    return children_data


def extract_child_details(user_id, child_data):
    sub_menu_id, sub_menu_name, sub_menu_href, sub_can_add, sub_can_modify, sub_can_delete, sub_can_query, sub_can_special = child_data

    child_details = {
        "id": sub_menu_id,
        "name": sub_menu_name,
        "href": sub_menu_href,
    }
    return child_details
# 查詢使用者主菜單程式與權限方法 END

# 查詢使用者標籤程式與權限方法 START
@validate_access_token_and_params(None)
def select_user_tag_program_and_permission_method(request, json_data, role, user_id):

    if request.method == "POST":
        try:
            program_id = json_data.get("program_id", None)

            # if not user_id:
            #     return 'program_id is required for select', status.HTTP_400_BAD_REQUEST

            main_menus = fetch_subtabs(user_id, program_id)
            # print('main_menus', user_id, main_menus)
            message = main_menus

            return message, status.HTTP_200_OK

        except ValueError:
            return "Invalid JSON", status.HTTP_400_BAD_REQUEST
        except Exception as e:
            logging.error("Unexpected error: %s", str(e))
            return "An error occurred in the database", status.HTTP_500_INTERNAL_SERVER_ERROR

def fetch_subtabs(user_id, sub_menu_id):
    with connection.cursor() as cursor:
        cursor.execute(sub_tab_sql, [user_id, sub_menu_id])
        subtabs_data = []
        for sub_tab_data in cursor.fetchall():
            subtab_details = extract_subtab_details(sub_tab_data)
            subtabs_data.append(subtab_details)
    return subtabs_data


def extract_subtab_details(sub_tab_data):
    sub_tab_id, sub_tab_name, sub_tab_can_add, sub_tab_can_modify, sub_tab_can_delete, sub_tab_can_query, sub_tab_can_special = sub_tab_data
    # print('sub_tab_id', sub_tab_id, sub_tab_name, sub_tab_can_add, sub_tab_can_modify, sub_tab_can_delete, sub_tab_can_query, sub_tab_can_special)
    subtab_details = {
        "id": sub_tab_id,
        "name": sub_tab_name,
        "permissions": {
            "can_add": ensure_not_none(sub_tab_can_add),
            "can_modify": ensure_not_none(sub_tab_can_modify),
            "can_delete": ensure_not_none(sub_tab_can_delete),
            "can_query": ensure_not_none(sub_tab_can_query),
            "can_special": ensure_not_none(sub_tab_can_special)
        }
    }
    return subtab_details
# 查詢使用者程式權限方法 END

