# -*- coding: UTF8 -*-
import cx_Oracle

from django.db import connection, transaction, IntegrityError
from rest_framework import status

from utils.error_utils import handle_error
from utils.token_utils import validate_access_token_and_params

''' DEPT INSERT START '''

# 映射字典
DEPT_NAME_MAPPING = {
    "dept_code": "DEPTCODE",
    "dept_name": "DEPTNAME",
    "hub_type": "HUBTYPE",
    "owner_id": "OWNERID"
}

def insert_with_raw_sql(context, data):
    try:
        with transaction.atomic():  # 確保整個過程在一個事務中執行
            with connection.cursor() as cursor:
                keys = ", ".join(DEPT_NAME_MAPPING[key] for key in data.keys())
                values = ", ".join(f"'{value}'" if value is not None else "null" for value in data.values())

                sql = f"INSERT INTO DEPT ({keys}) VALUES ({values})"
                # print(sql)
                cursor.execute(sql)

            # 在所有插入操作都成功後返回成功訊息
            return "新增成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 特別處理 Oracle 資料庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params(None)
def insert_dept_method(request, json_data, role, user_id):
    context = "insert_dept_method"

    if request.method == "POST":

        # 使用原生SQL進行數據插入
        try:
            result, result_status = insert_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)

''' DEPT INSERT END '''

''' DEPT UPDATE START '''

def transform_to_db_column(key):
    # 映射字典
    mapping = {
        "old_dept_code": "DEPTCODE",
        "now_dept_code": "DEPTCODE",
        "dept_name": "DEPTNAME",
        "hub_type": "HUBTYPE",
        "owner_id": "OWNERID"
    }
    return mapping.get(key)


def update_with_raw_sql(context, data):
    try:
        with transaction.atomic():
            with connection.cursor() as cursor:
                # 生成SET子句部分
                set_parts = []
                for key, value in data.items():
                    if key not in ["dept_code"]:
                        db_column = transform_to_db_column(key)
                        if value is None:
                            set_parts.append(f"{db_column} = NULL")
                        else:
                            set_parts.append(f"{db_column} = '{value}'")

                set_string = ", ".join(set_parts)

                sql = f"UPDATE DEPT SET {set_string} WHERE DEPTCODE = '{data['dept_code']}'"
                # print(sql)
                cursor.execute(sql)

                # 在所有更新操作都成功後返回成功信息
                return "更新成功", status.HTTP_200_OK

    except IntegrityError as ie:
        return handle_error(context, ie, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('dept_code')
def update_dept_method(request, json_data, role, user_id):
    context = "update_dept_method"

    if request.method == "POST":

        try:
            result, result_status = update_with_raw_sql(context, json_data)
            return result, result_status
        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST


''' TRUCK DEPT END '''

''' DEPT DELETE START '''


def delete_with_raw_sql(context, dept_codes):
    try:
        with transaction.atomic():  # 確保所有操作在一個事務中
            for dept_code in dept_codes:
                with connection.cursor() as cursor:
                    sql = f"DELETE FROM DEPT WHERE DEPTCODE = '{dept_code}'"
                    cursor.execute(sql)

        return "刪除成功", status.HTTP_200_OK

    except cx_Oracle.IntegrityError as e:
        # 處理 Oracle 數據庫完整性錯誤
        return handle_error(context, e, status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        # 處理其他類型的異常
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

@validate_access_token_and_params('dept_codes')
def delete_dept_method(request, json_data, role, user_id):
    context = "delete_dept_method"

    if request.method == "POST":
        dept_codes = json_data.get("dept_codes", [])

        # Check if dept_codes is a list and is not empty
        if not isinstance(dept_codes, list) or not dept_codes:
            return "dept_codes must be a non-empty list", status.HTTP_400_BAD_REQUEST

        try:
            result, result_status = delete_with_raw_sql(context, dept_codes)
            return result, result_status
        except Exception as e:
            return handle_error(context, e, status.HTTP_400_BAD_REQUEST)


''' DEPT DELETE END '''

''' DEPT SELECT START '''


def transform_to_frontend_structure(data, index):
    # 將資料庫的結構轉換為前端所需的結構
    frontend_data = {
        "id": index,
        "dept_code": data.get("DEPTCODE"),
        "dept_name": data.get("DEPTNAME"),
        "hub_type": data.get("HUBTYPE"),
        "owner_name": data.get("DEPT_SOURCE"),
        "modify_date": data.get("OWNERDATE"),
    }
    return frontend_data


def select_with_raw_sql(context):
    try:
        with connection.cursor() as cursor:
            sql = """
                    SELECT DEPT_CODE007 DEPTCODE, DEPT_DESC_C007 DEPTNAME, 'ERP' DEPT_SOURCE, TO_CHAR( BUILD_DATE007, 'YYYY/MM/DD') OWNERDATE
                      FROM HRF007@B2B
                     WHERE REMOVE_DATE007 IS NULL
        			UNION ALL 
        			SELECT DEPTCODE, DEPTNAME, CHI_NAME005 DEPT_SOURCE, TO_CHAR(OWNERDATE, 'YYYY/MM/DD') OWNERDATE
        			  FROM DEPT, HRF005@B2B
        	     	 WHERE REPLACE(OWNERID, 'h', '') = NO005
                     ORDER BY DEPTCODE
                """
            cursor.execute(sql)
            rows = cursor.fetchall()
            columns = [column[0].upper() for column in cursor.description]

            # 將從資料庫中獲取的每一行資料轉換為前端需要的格式，並添加索引
            return [transform_to_frontend_structure(dict(zip(columns, row)), idx + 1) for idx, row in enumerate(rows)]
    except Exception as e:
        return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


@validate_access_token_and_params(None)
def select_dept_method(request, json_data, role, user_id):
    context = "select_dept_method"

    if request.method == "POST":
        try:
            result = select_with_raw_sql(context)
            return result, status.HTTP_200_OK
        except Exception as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)


''' DEPT SELECT END '''
