import json
import logging

from django.db import connection
from rest_framework import permissions
from utils.token_utils import verify_access_token

class CustomTokenPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        # 驗證token
        is_valid, message, status_code = verify_access_token(request)

        if not is_valid:
            return False
        return True