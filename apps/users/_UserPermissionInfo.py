import json
import logging
import cx_<PERSON>

from django.db import connection
from rest_framework import status

''' 查詢使用者主菜單權限 SQL START '''
main_menu_sql = """
    SELECT M.MODULE_ID, M.MODULE_NAME, M.HREF, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = '10000000'
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者主菜單權限 SQL END '''

''' 查詢使用者子菜單權限 SQL START '''
sub_menu_sql = """
    SELECT M.MODULE_ID, M.MODULE_NAME, M<PERSON>HREF, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = %s
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者子菜單權限 SQL END '''

''' 查詢使用者子標籤權限 SQL START '''
sub_tab_sql = """
    SELECT M.MODULE_ID, M.MODULE_NAME, UP.CAN_ADD, UP.CAN_MODIFY, UP.CAN_DELETE, UP.CAN_QUERY, UP.CAN_SPECIAL
      FROM MODULE_HIERARCHY MH, MODULES M, USER_PERMISSIONS UP
     WHERE MH.CHILD_ID = M.MODULE_ID
       AND M.MODULE_ID = UP.MODULE_ID(+)
       AND UP.USER_ID(+) = %s
       AND MH.PARENT_ID = %s
     ORDER BY M.MODULE_ID
"""
''' 查詢使用者子標籤權限 SQL END '''

''' 查詢使用者程式權限 SQL START '''
select_user_permission_sql = """
    SELECT COUNT(*) FROM USER_PERMISSIONS WHERE USER_ID = %s AND MODULE_ID = %s
"""
''' 查詢使用者程式權限 SQL END '''

''' 新增使用者程式權限 SQL START '''
insert_user_permission_sql = """
    INSERT INTO USER_PERMISSIONS (USER_ID, MODULE_ID, CAN_ADD, CAN_MODIFY, CAN_DELETE, CAN_QUERY, CAN_SPECIAL)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
"""
''' 新增使用者程式權限 SQL END '''

''' 更新使用者程式權限 SQL START '''
update_user_permission_sql = """
        UPDATE USER_PERMISSIONS 
           SET CAN_ADD = %s,
               CAN_MODIFY = %s,
               CAN_DELETE = %s,
               CAN_QUERY = %s,
               CAN_SPECIAL = %s
         WHERE USER_ID = %s AND MODULE_ID = %s
    """
''' 更新使用者程式權限 SQL END '''

def ensure_not_none(permission):
    return permission if permission is not None else '0'

# 查詢使用者程式權限方法 START
def select_user_permission_method(request):
    try:
        data = json.loads(request.body)
        user_id = data.get("user_id")

        if not user_id:
            return 'user_id is required for select', status.HTTP_400_BAD_REQUEST

        main_menus = fetch_main_menus(user_id)
        message = process_main_menus(user_id, main_menus)

        return message, status.HTTP_200_OK

    except ValueError:
        return "Invalid JSON", status.HTTP_400_BAD_REQUEST
    except Exception as e:
        logging.error("Unexpected error: %s", str(e))
        return "An error occurred in the database", status.HTTP_500_INTERNAL_SERVER_ERROR


def fetch_main_menus(user_id):
    with connection.cursor() as cursor:
        cursor.execute(main_menu_sql, [user_id])  # Your main menu SQL query here
        return cursor.fetchall()


def process_main_menus(user_id, main_menus):
    message = []
    for menu_data in main_menus:
        menu_details = extract_menu_details(user_id, menu_data)
        message.append(menu_details)
    return message


def extract_menu_details(user_id, menu_data):
    menu_id, menu_name, menu_href, menu_can_add, menu_can_modify, menu_can_delete, menu_can_query, menu_can_special = menu_data
    children = fetch_children(user_id, menu_id)

    menu_details = {
        "id": menu_id,
        "name": menu_name,
        "href": menu_href,
        "permissions": {
            "can_add": ensure_not_none(menu_can_add),
            "can_modify": ensure_not_none(menu_can_modify),
            "can_delete": ensure_not_none(menu_can_delete),
            "can_query": ensure_not_none(menu_can_query),
            "can_special": ensure_not_none(menu_can_special)
        },
        "details": {"children": children}
    }
    return menu_details


def fetch_children(user_id, menu_id):
    with connection.cursor() as cursor:
        cursor.execute(sub_menu_sql, [user_id, menu_id])  # Your children SQL query here
        children_data = []
        for child_data in cursor.fetchall():
            child_details = extract_child_details(user_id, child_data)
            children_data.append(child_details)
    return children_data


def extract_child_details(user_id, child_data):
    sub_menu_id, sub_menu_name, sub_menu_href, sub_can_add, sub_can_modify, sub_can_delete, sub_can_query, sub_can_special = child_data
    subtabs = fetch_subtabs(user_id, sub_menu_id)

    child_details = {
        "id": sub_menu_id,
        "name": sub_menu_name,
        "href": sub_menu_href,
        "permissions": {
            "can_add": ensure_not_none(sub_can_add),
            "can_modify": ensure_not_none(sub_can_modify),
            "can_delete": ensure_not_none(sub_can_delete),
            "can_query": ensure_not_none(sub_can_query),
            "can_special": ensure_not_none(sub_can_special)
        },
        "subtabs": subtabs
    }
    return child_details


def fetch_subtabs(user_id, sub_menu_id):
    with connection.cursor() as cursor:
        cursor.execute(sub_tab_sql, [user_id, sub_menu_id])
        subtabs_data = []
        for sub_tab_data in cursor.fetchall():
            subtab_details = extract_subtab_details(sub_tab_data)
            subtabs_data.append(subtab_details)
    return subtabs_data


def extract_subtab_details(sub_tab_data):
    sub_tab_id, sub_tab_name, sub_tab_can_add, sub_tab_can_modify, sub_tab_can_delete, sub_tab_can_query, sub_tab_can_special = sub_tab_data
    subtab_details = {
        "id": sub_tab_id,
        "name": sub_tab_name,
        "permissions": {
            "can_add": ensure_not_none(sub_tab_can_add),
            "can_modify": ensure_not_none(sub_tab_can_modify),
            "can_delete": ensure_not_none(sub_tab_can_delete),
            "can_query": ensure_not_none(sub_tab_can_query),
            "can_special": ensure_not_none(sub_tab_can_special)
        }
    }
    return subtab_details
# 查詢使用者程式權限方法 END

# 更新使用者程式權限方法 START
def update_user_permission_method(request):
    if request.method == "POST":
        try:
            json_data = json.loads(request.body)

            if not json_data.get("user_id"):
                return "user_id is required for update", status.HTTP_400_BAD_REQUEST

            if not json_data.get("data"):
                return "user_id is required for data", status.HTTP_400_BAD_REQUEST

            user_id = json_data["user_id"]
            modules = json_data['data']

            print(user_id)
            print(modules)

            with connection.cursor() as cursor:
                for module in modules:
                    update_or_insert_permission(cursor, user_id, module)

                    # 更新子模組權限
                    for child in module.get('details', {}).get('children', []):
                        update_or_insert_permission(cursor, user_id, child)

                        # 更新子模組的標籤權限
                        for subtab in child.get('subtabs', []):
                            update_or_insert_permission(cursor, user_id, subtab)

            return '成功', status.HTTP_200_OK

        except Exception as e:
            return str(e), status.HTTP_400_BAD_REQUEST

def update_or_insert_permission(cursor, user_id, item):
    cursor.execute(select_user_permission_sql, [user_id, item['id']])
    exists = cursor.fetchone()[0]

    if exists > 0:
        # 更新權限
        cursor.execute(update_user_permission_sql, [
            item['permissions']['can_add'],
            item['permissions']['can_modify'],
            item['permissions']['can_delete'],
            item['permissions']['can_query'],
            item['permissions']['can_special'],
            user_id,
            item['id']
        ])
    else:
        # 插入新的權限
        cursor.execute(insert_user_permission_sql, [
            user_id,
            item['id'],
            item['permissions']['can_add'],
            item['permissions']['can_modify'],
            item['permissions']['can_delete'],
            item['permissions']['can_query'],
            item['permissions']['can_special']
        ])
# 更新使用者程式權限方法 END