@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo Fixing Word Environment Issues...
echo.

REM Set UTF-8 encoding
set PYTHONIOENCODING=utf-8

REM Check if Normal.dotm exists and is causing issues
set "TEMPLATES_PATH=%USERPROFILE%\AppData\Roaming\Microsoft\Templates"
set "NORMAL_DOTM=%TEMPLATES_PATH%\Normal.dotm"

if exist "%NORMAL_DOTM%" (
    echo Found Normal.dotm at: %NORMAL_DOTM%
    echo Creating backup...
    
    REM Create backup with timestamp
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
    set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"
    
    copy "%NORMAL_DOTM%" "%NORMAL_DOTM%.backup_%timestamp%" >nul 2>&1
    if !errorlevel! equ 0 (
        echo Backup created: Normal.dotm.backup_%timestamp%
        
        REM Delete the original Normal.dotm
        del "%NORMAL_DOTM%" >nul 2>&1
        if !errorlevel! equ 0 (
            echo Successfully deleted Normal.dotm - Word will recreate it
        ) else (
            echo Warning: Could not delete Normal.dotm - it may be in use
        )
    ) else (
        echo Warning: Could not create backup of Normal.dotm
    )
) else (
    echo Normal.dotm not found - this is normal after first fix
)

echo.
echo Checking Word processes...
tasklist /FI "IMAGENAME eq WINWORD.EXE" 2>nul | find /I "winword.exe" >nul
if %errorlevel% equ 0 (
    echo Warning: Word is currently running. Please close Word and run this script again.
    echo.
    pause
    exit /b 1
) else (
    echo No Word processes found - good!
)

echo.
echo Creating temporary Word templates directory...
set "TEMP_TEMPLATES=%TEMP%\WordTemplates_%timestamp%"
mkdir "%TEMP_TEMPLATES%" >nul 2>&1

echo.
echo Environment fixes applied successfully!
echo.
echo Next steps:
echo 1. Restart your application
echo 2. Try the download operation again
echo 3. If issues persist, contact IT support
echo.
pause
