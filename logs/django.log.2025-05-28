2025-05-28 08:45:17,187 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-28 08:45:38,003 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-28 08:46:05,288 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-28 08:46:05,289 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-28 08:46:07,826 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-05-28 08:46:07,965 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-05-28 08:46:07,966 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2025-05-28 08:46:09,367 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-28 08:46:09,368 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-28 08:46:09,413 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2025-05-28 08:46:09,414 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-05-28 08:46:09,414 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2025-05-28 08:46:09,495 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-05-28 08:46:14,147 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-05-28 08:46:14,267 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-05-28 08:46:14,403 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-28 08:46:14,470 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-05-28 08:46:14,471 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-05-28 08:46:14,474 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-05-28 08:46:14,474 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-05-28 08:46:14,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2923
2025-05-28 08:46:14,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-05-28 08:46:15,482 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-05-28 08:46:15,508 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3775
2025-05-28 08:46:19,145 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2025-05-28 08:46:19,196 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2025-05-28 08:46:19,372 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 22308
2025-05-28 08:46:26,826 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2025-05-28 08:46:27,028 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 22308
2025-05-28 08:46:27,829 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 22308
2025-05-28 08:46:33,880 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 0
2025-05-28 08:46:36,107 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 181417
2025-05-28 08:47:19,546 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-28 08:47:23,300 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 181417
2025-05-28 08:50:50,864 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-28 08:50:54,697 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-28 08:50:54,698 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-28 08:51:00,286 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 22308
2025-05-28 08:51:01,039 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_shipment_order/ HTTP/1.1" 200 0
2025-05-28 08:51:02,488 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 134158
2025-05-28 08:51:07,930 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 134081
2025-05-28 08:51:53,695 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-28 08:51:57,410 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 135566
