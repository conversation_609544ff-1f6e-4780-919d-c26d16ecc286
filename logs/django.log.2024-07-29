2024-07-29 00:01:09,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:01:37,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:04:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:04:37,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:07:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:07:37,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:10:09,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:10:37,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:13:09,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:13:37,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:16:09,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:16:37,176 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:19:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:19:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:22:09,427 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:22:37,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:25:09,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:25:37,186 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:28:09,471 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:28:37,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:31:09,446 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:31:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:34:09,662 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:34:37,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:37:09,424 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:37:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:40:09,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:40:37,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:43:09,464 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:43:37,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:46:09,425 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:46:37,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:49:09,429 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:49:37,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:52:09,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:52:37,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:55:09,505 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:55:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:58:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 00:58:37,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:01:09,437 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:01:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:04:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:04:37,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:07:09,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:07:37,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:10:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:10:37,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:13:09,438 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:13:37,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:16:09,442 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:16:37,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:19:09,429 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:19:37,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:22:09,426 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:22:37,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:25:09,435 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:25:37,147 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-29 01:25:37,147 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-29 01:28:09,445 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:31:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 01:34:09,442 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-29 01:34:09,443 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-29 07:33:01,750 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 07:33:01,750 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 07:33:01,934 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-29 07:33:01,934 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-29 07:33:01,990 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-29 07:33:01,991 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-29 07:34:48,138 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 07:34:48,139 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 07:34:48,325 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-29 07:34:48,325 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-29 07:34:48,328 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 52018)

2024-07-29 07:34:48,382 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-29 07:34:48,383 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-29 07:34:48,385 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 52019)

2024-07-29 08:26:05,650 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:26:15,053 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-29 08:26:15,931 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-29 08:26:16,088 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 08:26:16,198 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 08:26:16,275 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 08:26:16,275 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 08:26:16,276 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 08:26:16,504 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-29 08:26:16,740 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6167
2024-07-29 08:26:16,773 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 36310
2024-07-29 08:26:16,850 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 15597
2024-07-29 08:26:20,841 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 08:26:20,905 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-29 08:26:20,905 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-29 08:26:21,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 08:26:21,146 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 08:26:24,073 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50736
2024-07-29 08:26:27,283 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-29 08:26:27,284 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-29 08:26:27,423 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 08:26:27,478 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 08:26:28,765 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-29 08:26:33,045 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:33,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:33,326 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:33,424 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:33,577 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:33,934 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:34,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:34,152 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:34,443 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:34,583 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,066 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,205 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,294 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,384 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,403 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,420 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,446 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,468 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,496 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,569 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,592 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,608 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,630 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,634 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:35,683 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:26:36,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7038
2024-07-29 08:27:48,331 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:28:05,489 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:28:15,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:15,270 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:15,492 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:15,543 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:15,610 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:15,953 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:16,038 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:16,232 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:16,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:16,799 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,449 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,593 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,689 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,699 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,784 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,823 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,858 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:17,903 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,066 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,112 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,183 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,190 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,203 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,207 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,250 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,286 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:28:18,587 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7031
2024-07-29 08:30:03,518 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:30:10,845 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,863 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,895 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,914 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,934 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:10,983 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,168 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,195 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,342 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,528 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:11,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:12,221 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:12,374 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:13,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:13,835 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,044 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,266 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,299 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,361 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,375 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,379 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,390 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,424 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,448 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:30:14,771 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7029
2024-07-29 08:30:55,986 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:31:03,670 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:03,797 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,156 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,223 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,280 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,551 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,648 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:04,819 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:05,347 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:05,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:05,844 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,089 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,247 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,421 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,487 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,617 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,650 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,689 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,729 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,841 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,862 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,917 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,932 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,938 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,952 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:06,990 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:07,021 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:07,374 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7052
2024-07-29 08:31:47,091 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 08:31:53,478 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,498 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,505 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,507 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,510 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,533 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,536 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,548 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,561 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,568 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,592 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,600 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,616 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,619 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,645 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,697 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,738 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:53,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:54,042 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:54,289 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:55,124 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:55,269 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:55,395 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:55,460 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:55,777 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:56,079 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:31:57,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7063
2024-07-29 08:38:55,914 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41761
2024-07-29 08:38:58,135 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 08:38:58,189 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 08:39:02,651 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:03,103 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:06,610 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:06,620 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:06,627 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:06,637 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:06,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:07,108 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:07,514 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6328
2024-07-29 08:39:39,797 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 08:39:39,862 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 08:39:43,574 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-29 08:39:47,938 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7323
2024-07-29 09:13:25,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 09:13:25,968 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 09:15:32,827 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-29 09:15:32,951 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:15:33,114 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 09:15:33,323 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-29 09:15:33,392 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 09:15:33,581 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 09:15:33,592 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 09:15:33,704 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 36310
2024-07-29 09:15:33,711 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 15597
2024-07-29 09:18:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:21:34,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:24:34,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:27:34,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:30:34,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:33:34,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:36:34,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:39:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:42:34,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:45:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:45:57,527 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 09:45:57,527 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 09:45:57,670 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 09:45:57,730 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 09:48:34,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:51:34,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:54:34,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 09:57:34,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:00:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:03:34,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:06:34,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:09:34,654 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:12:34,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:15:34,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:16:03,277 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 10:16:03,328 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 10:17:25,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41326
2024-07-29 10:17:33,856 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 779
2024-07-29 10:17:37,648 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 10:17:37,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 10:17:51,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 10:17:52,018 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 10:18:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:21:34,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:24:34,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:27:34,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-29 10:27:54,167 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-29 10:27:54,367 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 10:27:54,536 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 10:28:02,113 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-29 10:28:02,232 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-29 10:28:02,378 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 10:28:02,476 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 10:28:02,525 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 10:28:02,525 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 10:28:02,526 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 10:28:02,801 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-29 10:28:02,976 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6167
2024-07-29 10:28:03,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 10:28:03,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 606
2024-07-29 10:28:04,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 10:28:04,071 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-29 10:28:04,071 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-29 10:28:04,235 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 10:28:04,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 10:28:12,660 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 716
2024-07-29 10:28:33,747 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-29 10:28:33,748 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-29 10:28:33,867 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 10:28:33,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 10:28:36,288 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-29 10:28:36,441 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 163990
2024-07-29 10:28:40,946 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-29 10:28:41,125 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 10:28:41,134 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 10:28:50,830 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 825
2024-07-29 10:28:51,747 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-07-29 10:28:51,812 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 10:28:51,888 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 254
2024-07-29 10:28:55,299 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2024-07-29 10:29:09,627 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 40201
2024-07-29 11:10:38,781 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-29 11:10:38,835 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 11:10:42,138 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 825
2024-07-29 11:10:43,622 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-29 11:10:43,809 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-29 11:10:43,944 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 11:10:43,967 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-29 11:10:44,028 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-29 11:10:44,079 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-29 11:10:44,132 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-29 11:10:44,185 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-29 11:10:44,259 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50625
2024-07-29 11:10:51,420 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-29 11:10:51,670 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49194
2024-07-29 11:10:53,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 11:10:53,415 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 11:10:55,025 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 58422
2024-07-29 11:11:03,703 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 11:11:03,802 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 11:11:03,811 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 11:11:06,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51937
2024-07-29 11:46:48,804 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 11:46:48,804 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 11:46:48,978 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 11:46:49,035 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-29 11:46:55,935 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 11:46:56,159 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 11:47:06,896 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-29 11:47:06,896 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-29 11:47:15,742 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-29 11:47:15,894 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 11:47:16,095 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 11:47:16,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 11:47:16,810 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 11:47:17,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 11:50:07,185 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 11:50:18,258 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-29 11:50:19,444 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 11:50:19,593 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 11:50:19,819 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 11:50:19,986 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 11:50:20,536 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 11:50:20,853 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 11:53:20,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 11:56:20,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 11:59:20,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:02:20,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:05:20,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:08:27,558 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:15:47,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:17:20,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:20:20,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:22:34,298 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 12:23:20,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:26:20,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:29:20,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:32:20,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:35:20,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:38:20,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:41:20,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:44:20,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:47:20,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:50:20,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:52:44,041 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 12:53:20,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:56:20,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 12:59:20,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:02:20,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:05:20,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:08:20,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:11:20,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:14:20,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:17:20,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:19:17,289 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:19:34,917 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:19:35,044 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:19:35,222 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 13:19:35,223 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 13:19:35,223 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 13:19:35,223 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 13:19:35,477 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:19:35,642 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:19:36,139 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:19:36,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:22:36,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:25:36,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:27:26,534 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:28:09,807 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:28:09,946 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:28:10,177 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:28:10,344 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:28:11,134 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 13:28:11,266 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:30:07,659 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 13:31:10,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:34:10,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:35:59,639 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:36:08,800 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:36:08,924 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:36:09,162 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:36:09,320 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:36:09,865 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 13:36:10,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:36:34,680 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 13:37:07,806 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 49554
2024-07-29 13:37:27,081 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:37:32,599 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:37:32,730 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:37:33,066 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:37:33,095 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:37:33,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:37:33,898 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:40:05,146 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:40:08,459 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:40:08,616 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:40:08,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:40:08,987 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:40:09,312 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:40:09,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:43:09,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:43:17,740 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:43:25,652 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:43:25,797 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:43:26,024 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:43:26,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:43:26,556 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:43:27,040 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:44:29,090 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:44:39,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:44:39,362 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:44:39,584 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:44:39,738 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:44:40,063 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:44:40,624 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:46:40,252 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:47:39,380 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:48:35,625 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 52620
2024-07-29 13:50:39,348 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-29 13:50:39,516 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:51:16,359 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:51:22,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:51:22,409 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 13:51:22,552 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:51:22,819 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:51:22,979 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:51:22,979 error_utils.handle_error  13 ERROR   => select_letter_method2 - Unexpected error: ORA-00904: "ENDDATERUNTITLE": invalid identifier
2024-07-29 13:51:23,106 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 257, in select_letter2
    return self._handle_action('letter', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 98, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 90, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'DatabaseError' is not JSON serializable
2024-07-29 13:51:23,116 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter2/ HTTP/1.1" 500 137083
2024-07-29 13:51:23,356 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:52:29,949 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:52:35,587 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:52:35,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:52:35,963 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:52:36,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:52:36,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:52:36,743 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 13:54:39,029 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 13:54:39,272 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:54:41,492 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-29 13:54:41,694 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:54:49,325 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:54:53,463 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:54:59,290 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:55:02,553 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:55:05,825 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:55:07,936 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 13:55:36,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:57:44,895 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 13:57:51,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 13:57:51,248 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 13:57:51,447 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 13:57:51,550 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 13:57:51,782 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 13:57:51,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 13:57:52,112 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:00:22,987 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 14:00:52,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:01:19,460 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 14:01:25,822 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:01:25,951 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:01:26,151 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:01:26,219 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:01:26,484 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:01:26,724 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 14:01:26,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:01:28,060 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:01:30,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 70
2024-07-29 14:01:30,850 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 70
2024-07-29 14:02:49,881 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 14:02:57,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:02:57,297 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:02:57,529 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:02:57,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 70
2024-07-29 14:02:57,854 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 70
2024-07-29 14:02:58,071 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 14:02:58,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:04:36,454 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 14:04:54,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:04:54,335 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:04:54,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:04:54,710 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:04:55,085 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 14:04:55,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:05:51,435 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-29 14:05:57,614 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:05:57,778 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:05:58,124 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:05:58,180 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:05:58,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:05:58,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 14:07:48,365 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:07:48,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:07:48,825 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:07:48,997 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:07:49,349 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 26445
2024-07-29 14:07:49,457 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 32374
2024-07-29 14:10:49,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:13:49,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:16:49,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:19:49,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:22:49,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:25:25,248 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-29 14:25:25,378 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 14:25:25,569 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-29 14:25:31,541 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-29 14:25:31,654 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-29 14:25:31,799 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:25:32,056 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:25:32,214 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:25:32,569 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:25:32,579 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:25:33,654 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:25:33,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:25:41,784 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:26:47,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:29:30,727 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:29:30,874 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:29:31,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:29:31,071 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:29:31,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:29:31,525 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:29:31,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:29:32,512 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:31:30,173 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:32:18,364 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:32:19,035 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:32:24,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:32:24,342 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:32:24,475 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:32:24,542 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:32:24,803 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:32:24,925 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:32:25,101 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:33:42,879 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:34:13,489 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:35:24,253 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:35:38,504 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:35:48,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:35:59,988 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/insert_bullet_board/ HTTP/1.1" 200 0
2024-07-29 14:36:00,130 basehttp.log_message 161 INFO    => "POST /api/documents/insert_bullet_board/ HTTP/1.1" 200 69
2024-07-29 14:36:04,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7074
2024-07-29 14:36:06,029 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:38:09,077 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_bullet_board/ HTTP/1.1" 200 0
2024-07-29 14:38:09,228 basehttp.log_message 161 INFO    => "POST /api/documents/delete_bullet_board/ HTTP/1.1" 200 69
2024-07-29 14:38:10,476 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:38:14,733 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:38:22,665 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:38:24,264 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:41:25,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:42:14,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:42:19,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:42:34,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:42:37,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:43:05,836 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:43:19,289 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:43:41,465 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:44:25,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:45:41,166 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:45:46,970 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:45:47,120 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:45:47,347 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:45:47,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:45:47,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:45:47,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:45:47,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:45:49,861 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:45:58,666 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:45:58,914 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:45:59,063 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:45:59,103 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:45:59,402 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:45:59,571 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:45:59,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:46:22,722 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:46:45,247 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:48:50,360 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:49:00,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:49:00,220 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:49:00,432 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:49:00,478 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:49:00,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:49:00,813 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:49:00,878 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:49:04,731 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:49:18,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:49:18,201 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:49:18,340 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:49:18,434 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:49:18,686 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:49:18,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:49:18,929 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:49:20,089 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:49:26,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:51:19,665 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:51:19,926 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:51:20,200 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:51:20,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:51:20,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6844
2024-07-29 14:51:20,734 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:51:20,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:51:30,367 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:51:41,954 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_bullet_board/ HTTP/1.1" 200 0
2024-07-29 14:51:42,107 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-29 14:51:44,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:52:18,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:52:30,645 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:52:30,795 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:52:30,922 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:52:31,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:52:31,263 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:52:31,432 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:52:31,465 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:52:36,923 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:52:44,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:52:44,351 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:52:44,497 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:52:44,618 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:52:44,829 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:52:44,987 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:52:45,023 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:52:50,240 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:52:54,770 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:52:54,992 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:52:55,153 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:52:55,243 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:52:55,534 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:52:55,646 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:52:55,651 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:53:07,112 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:53:11,976 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:53:12,112 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:53:12,277 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:53:12,346 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:53:12,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:53:12,810 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:53:12,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:53:17,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:53:46,799 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:53:50,233 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:53:56,429 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:53:56,725 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:53:56,966 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:53:57,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:53:57,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:53:57,519 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:53:57,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:54:38,551 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:54:43,126 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:56:34,896 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:56:44,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:56:50,734 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 14:56:50,859 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 14:56:51,086 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 14:56:51,128 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 14:56:51,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:56:51,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 14:56:51,520 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 14:57:02,018 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:57:08,784 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 14:57:10,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 14:59:51,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:01:12,953 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:01:14,986 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:02:51,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:05:28,143 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:05:28,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:05:32,964 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:05:33,150 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:05:33,367 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:05:33,436 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:05:33,733 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:05:33,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:05:33,927 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:05:35,343 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:08:34,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:08:37,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:08:41,412 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:08:41,692 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:08:41,838 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:08:41,913 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:08:42,201 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:08:42,301 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:08:42,483 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:08:52,108 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:08:58,257 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:08:58,384 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:08:58,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:08:58,661 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:08:58,862 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:08:58,989 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:08:59,022 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:09:00,158 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:09:38,518 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:09:52,351 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:10:30,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:10:53,831 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:11:20,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:11:56,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:11:58,381 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:12:16,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:13:27,867 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:14:59,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:17:54,101 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:17:58,402 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:20:58,432 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:23:58,413 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:26:58,217 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:26:58,998 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:27:06,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:27:06,365 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:27:06,528 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 15:27:06,529 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 15:27:06,531 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 15:27:06,664 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:27:06,736 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:27:06,739 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 15:27:07,036 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:27:07,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:27:07,283 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:27:09,355 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:28:17,114 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:30:07,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:31:56,036 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:33:07,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:33:44,634 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:34:22,727 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:36:07,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:38:06,319 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:38:12,555 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:38:12,769 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:38:12,967 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:38:13,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:38:13,311 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:38:13,480 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:38:13,517 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:38:14,682 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:38:53,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:39:02,829 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:39:03,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:39:03,206 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:39:03,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:39:03,561 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:39:03,709 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:39:03,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:39:05,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:41:12,015 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:41:15,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:41:15,880 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:41:16,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:41:16,070 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:41:16,335 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:41:16,526 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:41:16,573 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:41:17,721 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:44:12,540 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:44:15,806 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:44:20,425 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:44:20,644 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:44:20,778 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:44:20,846 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:44:21,124 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:44:21,306 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:44:21,330 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:44:22,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:44:27,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:44:27,212 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:44:27,367 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:44:27,436 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:44:27,721 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:44:27,878 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:44:27,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:44:41,552 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:45:42,189 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:47:28,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:48:19,155 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:50:28,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:52:18,360 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:53:28,497 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-29 15:53:28,606 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:54:48,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:54:48,108 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 15:54:48,250 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 15:54:48,449 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 15:54:48,521 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 15:54:48,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:54:48,954 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 15:54:49,020 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 15:54:56,244 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-29 15:54:56,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:56:53,162 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:57:05,808 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-29 15:57:05,878 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-29 15:57:05,929 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-29 15:57:05,929 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-29 15:57:06,094 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 15:57:06,145 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 15:57:06,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 15:57:07,795 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-29 15:57:09,558 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 15:57:12,265 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-29 15:57:12,422 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 15:57:12,428 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 15:57:12,529 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 15:57:12,630 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 15:57:32,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-29 15:57:32,684 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-29 15:57:32,812 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-29 15:57:32,861 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-29 15:57:35,264 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 15:57:35,294 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 15:57:35,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 15:57:35,410 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 15:57:37,789 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 15:57:37,829 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 15:57:37,833 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 15:57:37,936 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 15:57:47,545 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 15:57:48,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 15:57:48,867 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:58:20,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 15:58:24,534 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:00:48,202 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:00:52,245 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:00:52,424 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 16:00:52,686 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 16:00:52,921 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 16:00:52,945 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 16:00:56,878 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:00:57,111 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 16:00:57,299 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 16:00:57,365 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 16:00:57,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 16:00:57,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 16:00:57,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 16:00:58,903 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:03:07,009 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:03:57,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:06:58,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:07:13,155 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:08:36,524 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 16:08:36,608 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-29 16:08:36,959 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:08:38,311 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_truck/ HTTP/1.1" 200 0
2024-07-29 16:08:38,482 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-29 16:08:39,230 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:08:39,793 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-29 16:08:39,963 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:08:52,587 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:09:02,394 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:09:11,251 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:09:34,985 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:09:42,407 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-29 16:09:42,624 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:09:42,637 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:09:42,753 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:09:53,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 16:09:54,422 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:09:57,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:10:14,120 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:11:11,800 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:11:42,383 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:12:16,782 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:12:41,503 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:12:58,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:13:45,655 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:15:22,114 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:15:58,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:18:58,423 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:21:58,135 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:24:57,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:26:01,723 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:26:01,829 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:26:01,893 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:26:04,340 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 16:26:06,197 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 16:26:06,223 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:26:06,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:26:06,381 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:26:49,184 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-29 16:26:49,369 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:26:49,760 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:26:57,793 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:27:58,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:29:32,080 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:30:57,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:31:05,514 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:31:05,726 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 16:31:05,854 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 16:31:05,924 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 16:31:06,340 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:31:06,397 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 16:31:06,453 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 16:31:07,556 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:31:15,920 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-29 16:31:16,113 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:31:16,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:31:16,237 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:31:18,740 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 16:31:20,843 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 16:31:20,891 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:31:20,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:31:21,036 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:31:52,649 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:31:53,616 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:33:14,788 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:34:06,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:35:24,833 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:35:24,946 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:35:24,959 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:35:28,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 16:35:30,594 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 16:35:30,648 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:35:30,665 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:35:30,746 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:35:53,034 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:35:54,353 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:37:06,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:37:40,050 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:37:43,739 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:37:45,364 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:39:14,061 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:39:14,835 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:39:46,930 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-29 16:39:47,115 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:39:48,683 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:39:48,795 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:39:48,869 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:39:51,351 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 16:39:53,554 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 16:39:53,584 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:39:53,648 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:39:53,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:40:05,691 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:40:24,036 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:40:25,861 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:40:48,669 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:42:13,605 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:42:55,132 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:43:06,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:45:18,341 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:46:06,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:46:38,820 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:46:39,838 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:49:06,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:50:46,771 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:50:46,782 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:50:46,886 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:50:49,255 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 16:50:51,837 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 16:50:51,858 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 16:50:51,932 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 16:50:51,990 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 16:52:00,498 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 16:52:01,632 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 16:52:05,689 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:52:23,191 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 16:52:23,425 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 16:52:25,091 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 16:55:05,737 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:58:06,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 16:58:53,173 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:00:21,114 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:01:06,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:01:45,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:01:47,694 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 17:02:28,530 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:03:10,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:03:10,817 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 17:03:10,976 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:03:11,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 17:03:11,129 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 17:03:16,813 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:03:16,954 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 17:03:17,089 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 17:03:17,165 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 17:03:17,436 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:03:17,634 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 17:03:17,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 17:03:18,740 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:04:14,263 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:06:16,983 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:09:13,151 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:09:16,989 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:09:33,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:12:16,982 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:13:55,099 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:14:11,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:15:16,960 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:15:27,324 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:17:18,287 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:18:17,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:21:17,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:24:17,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:25:40,780 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:25:41,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:26:02,251 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:26:08,695 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 17:26:09,152 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 17:26:10,451 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 17:27:17,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:29:34,265 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 17:29:34,561 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:29:35,993 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:29:49,267 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:29:53,915 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:29:58,826 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:30:15,536 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:30:16,969 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:30:24,983 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 17:30:26,263 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 17:31:38,558 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 17:31:39,896 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 17:33:16,973 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:33:43,951 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 17:33:44,008 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 17:36:16,992 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:39:16,987 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:42:16,983 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:45:16,987 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:48:16,984 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:51:16,982 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:54:16,939 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-29 17:54:17,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 17:57:16,996 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:00:16,990 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:03:16,996 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:06:16,998 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:09:16,986 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:12:17,014 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:15:16,976 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:18:16,983 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:21:16,985 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:21:20,836 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 18:21:20,836 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 18:21:20,980 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 18:21:21,040 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 18:23:57,698 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-29 18:23:57,920 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:24:16,972 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-29 18:27:14,289 log.log_response 230 WARNING => Unauthorized: /api/documents/select_bullet_board/
2024-07-29 18:27:14,289 basehttp.log_message 161 WARNING => "POST /api/documents/select_bullet_board/ HTTP/1.1" 401 79
2024-07-29 18:27:24,809 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-29 18:27:24,944 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-29 18:27:25,085 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 18:27:25,167 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 18:27:25,219 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 18:27:25,220 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 18:27:25,489 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:27:25,560 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 18:27:25,829 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 18:27:25,873 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 18:27:28,378 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-29 18:27:28,439 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-29 18:27:28,440 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-29 18:27:28,441 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-29 18:27:28,592 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:27:28,656 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:27:28,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:27:36,953 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 18:27:37,026 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-29 18:27:37,406 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 18:27:38,602 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-29 18:27:38,801 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:28:42,050 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 18:28:42,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:28:44,987 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:31:04,073 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:39:15,654 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:39:17,057 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:40:12,229 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 18:40:13,983 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:45:14,078 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:45:42,666 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 18:45:44,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:46:38,201 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:47:25,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:47:29,773 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:47:53,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:48:00,880 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:52:27,098 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:52:38,246 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 18:52:39,900 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:52:58,059 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:53:33,304 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:53:33,311 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:53:33,422 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:53:35,888 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-29 18:53:37,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 18:53:41,021 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-29 18:53:41,186 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:53:41,188 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:53:41,308 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:53:41,378 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 18:54:17,165 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:54:17,198 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 18:54:17,269 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:54:17,334 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:54:20,359 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 18:54:20,383 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:54:20,412 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:54:20,499 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:54:39,129 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-07-29 18:54:39,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 18:54:39,269 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 18:54:39,369 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 18:54:39,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-29 18:54:39,502 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-29 18:55:27,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-29 18:55:33,782 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-29 18:55:33,964 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-29 18:55:36,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-29 18:55:42,673 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 18:55:49,152 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 18:56:03,697 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:56:07,148 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:56:12,079 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:56:29,572 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/insert_bullet_board/ HTTP/1.1" 200 0
2024-07-29 18:56:29,709 basehttp.log_message 161 INFO    => "POST /api/documents/insert_bullet_board/ HTTP/1.1" 200 69
2024-07-29 18:56:35,394 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7073
2024-07-29 18:56:38,007 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:56:43,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:57:17,038 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 18:57:45,367 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_bullet_board/ HTTP/1.1" 200 0
2024-07-29 18:57:45,504 basehttp.log_message 161 INFO    => "POST /api/documents/delete_bullet_board/ HTTP/1.1" 200 69
2024-07-29 18:57:46,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 18:58:23,493 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 20:29:07,412 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 20:29:07,412 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 20:29:07,720 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 20:29:07,836 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 21:43:15,690 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-29 21:43:15,720 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 21:43:15,723 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 21:43:15,908 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 21:46:09,551 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 21:46:09,641 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-29 21:46:10,015 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 21:46:12,131 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-29 21:46:12,308 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 22:01:38,664 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 22:01:40,608 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 22:01:51,485 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 22:14:14,522 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 22:16:19,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 22:29:32,186 log.log_response 230 WARNING => Unauthorized: /api/dealers/select_dealer_group/
2024-07-29 22:29:32,186 basehttp.log_message 161 WARNING => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 401 79
2024-07-29 22:29:44,077 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-29 22:29:44,213 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-29 22:29:44,289 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 22:29:44,431 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-29 22:29:44,511 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-29 22:29:44,588 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-29 22:29:44,589 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-29 22:29:44,590 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-29 22:29:44,871 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-29 22:29:45,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 22:29:45,235 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-29 22:29:45,269 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-29 22:29:50,350 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-29 22:29:50,417 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-29 22:29:50,418 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-29 22:29:50,418 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-29 22:29:50,526 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-29 22:29:50,630 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-29 22:29:50,686 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-29 22:29:53,267 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-29 22:29:53,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6843
2024-07-29 22:29:54,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-29 22:31:01,772 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-29 22:31:01,973 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 22:31:03,466 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-29 22:31:03,866 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-29 22:31:05,263 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-29 22:36:16,526 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
