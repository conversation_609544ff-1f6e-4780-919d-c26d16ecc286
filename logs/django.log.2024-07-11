2024-07-11 08:39:53,813 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 08:39:53,814 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 08:39:53,982 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-11 08:39:53,983 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-11 08:39:54,069 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-11 08:39:54,070 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-11 08:43:06,151 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-11 08:43:06,371 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-11 08:43:06,511 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 08:43:06,591 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 08:43:06,592 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-11 08:43:06,644 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-11 08:43:06,644 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-11 08:43:06,947 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4642
2024-07-11 08:43:07,002 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 08:43:07,161 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 08:43:07,302 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 08:46:59,352 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-11 08:46:59,419 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-11 08:46:59,606 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 08:47:06,303 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-11 08:47:06,519 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 08:47:40,404 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 0
2024-07-11 08:47:40,656 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 08:52:01,962 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 08:52:07,089 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 08:52:07,311 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-11 08:52:56,048 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 3057210
2024-07-11 09:09:07,728 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 09:12:22,965 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:12:49,251 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-11 09:13:03,357 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 802131
2024-07-11 09:13:08,206 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:13:16,930 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 09:13:27,141 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:14:23,697 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:14:32,445 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:18:13,435 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:18:19,986 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:18:22,644 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-11 09:18:23,068 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 41888
2024-07-11 09:19:29,176 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:19:33,737 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:19:50,182 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:19:53,293 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:21:59,779 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:22:03,288 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:22:27,249 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:22:32,166 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:23:27,636 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:23:31,304 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:24:51,141 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:24:54,293 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 09:25:36,305 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 09:27:56,255 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:27:58,992 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 09:28:05,182 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 09:28:24,748 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 09:29:20,687 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:29:23,825 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 09:39:06,924 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:39:10,583 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1376, in select_erp_hy_sdv200_main_sales_allowance_notification
    FROM ( """ + sql_query + """ )
UnboundLocalError: local variable 'sql_query' referenced before assignment
2024-07-11 09:39:10,589 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 122381
2024-07-11 09:39:34,264 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:39:37,662 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "A"."CONFIRM_DATE200": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1417, in select_erp_hy_sdv200_main_sales_allowance_notification
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "A"."CONFIRM_DATE200": invalid identifier
2024-07-11 09:39:37,672 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 202876
2024-07-11 09:39:54,251 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:39:57,277 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "A"."AGENT_CODE200": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1417, in select_erp_hy_sdv200_main_sales_allowance_notification
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "A"."AGENT_CODE200": invalid identifier
2024-07-11 09:39:57,288 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 202840
2024-07-11 09:40:55,620 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:41:03,505 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1429, in select_erp_hy_sdv200_main_sales_allowance_notification
    sorted_data = sorted(data, key=lambda x: (x["AGENT_CODE200"], x["KEY_TYPE200"], x["DISC_GROUP200"], x["DISC_KEY200"]))
TypeError: '<' not supported between instances of 'NoneType' and 'str'
2024-07-11 09:41:03,510 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 142013
2024-07-11 09:43:16,279 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:43:19,169 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 09:43:19,232 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00907: missing right parenthesis

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1420, in select_erp_hy_sdv200_main_sales_allowance_notification
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00907: missing right parenthesis
2024-07-11 09:43:19,240 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 208194
2024-07-11 09:44:57,102 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:45:00,504 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1420, in select_erp_hy_sdv200_main_sales_allowance_notification
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-11 09:45:00,518 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 205879
2024-07-11 09:45:55,524 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:46:01,353 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1420, in select_erp_hy_sdv200_main_sales_allowance_notification
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-11 09:46:01,365 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 205879
2024-07-11 09:46:35,503 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:46:40,099 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 09:52:21,886 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:52:25,935 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 53684
2024-07-11 09:52:26,139 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-11 09:52:33,139 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 45048
2024-07-11 09:54:19,484 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:54:24,261 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 09:54:51,982 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:54:55,791 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 09:57:28,557 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:57:33,157 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 09:58:44,092 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 09:58:48,809 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-07-11 10:02:53,363 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:11:54,343 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 10:11:59,284 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:12:05,300 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:12:51,163 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 10:13:12,614 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:13:32,967 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 10:13:36,383 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 10:13:37,572 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:14:20,035 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 10:14:23,189 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 49026
2024-07-11 10:15:14,589 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 10:19:02,133 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 40372
2024-07-11 10:20:18,520 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 10:20:18,660 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-11 10:20:18,662 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-11 10:20:18,662 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-11 10:20:18,827 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:20:18,960 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:20:18,991 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:20:19,462 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-11 10:20:19,673 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 10:20:27,990 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 10:20:28,167 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:20:28,177 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:20:28,283 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:20:29,793 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:20:29,830 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:20:29,835 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:20:31,088 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-11 10:20:31,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4642
2024-07-11 10:20:54,633 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-11 10:20:54,819 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 10:29:33,107 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-11 10:29:33,208 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 10:29:35,340 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-11 10:29:36,786 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:29:36,791 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:29:36,907 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:29:38,427 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-11 10:29:48,917 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 231658
2024-07-11 10:30:33,580 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:30:33,641 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:30:33,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:30:34,469 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-11 10:30:36,326 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147817
2024-07-11 10:30:47,556 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-07-11 10:30:47,846 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 423
2024-07-11 10:30:49,757 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 527
2024-07-11 10:30:54,729 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 527
2024-07-11 10:30:57,038 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1005
2024-07-11 10:31:00,862 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1005
2024-07-11 10:31:02,685 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 414
2024-07-11 10:31:04,531 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 414
2024-07-11 10:31:13,591 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1555
2024-07-11 10:32:27,186 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:32:27,254 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:32:27,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:32:28,182 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:32:28,279 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:32:30,376 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 10:32:30,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 10:32:31,694 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:32:31,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:32:31,809 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:32:43,918 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 231658
2024-07-11 10:33:24,624 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 286
2024-07-11 10:43:47,620 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 10:43:47,816 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 10:48:00,907 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 557
2024-07-11 10:48:10,478 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-11 10:48:10,704 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-11 10:48:10,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:48:10,940 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:48:11,018 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:48:22,234 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 557
2024-07-11 10:50:34,351 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 10:53:28,549 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 557
2024-07-11 10:53:31,141 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-11 10:53:31,161 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 10:53:31,173 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 10:53:31,279 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 10:56:29,726 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 231658
2024-07-11 11:00:01,446 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 231658
2024-07-11 11:02:35,613 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240174
2024-07-11 11:03:42,777 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_kind/ HTTP/1.1" 200 0
2024-07-11 11:03:42,979 basehttp.log_message 161 INFO    => "POST /api/documents/select_kind/ HTTP/1.1" 200 2785
2024-07-11 11:03:45,204 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:03:45,206 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:03:45,313 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:04:13,179 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240174
2024-07-11 11:06:55,656 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240174
2024-07-11 11:07:17,269 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 11:07:17,380 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:07:17,525 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 11:07:17,595 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 11:07:17,647 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-11 11:07:17,648 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-11 11:07:17,730 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 11:07:17,777 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 11:07:17,975 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:07:17,982 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:07:18,103 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:07:18,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 11:07:18,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 11:07:30,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240174
2024-07-11 11:08:09,299 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 4485
2024-07-11 11:08:16,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-11 11:08:16,171 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:08:16,179 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:08:16,270 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:09:08,596 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 1747412
2024-07-11 11:10:17,531 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:11:11,189 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240354
2024-07-11 11:12:53,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 4485
2024-07-11 11:13:17,529 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:13:20,312 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 4485
2024-07-11 11:13:49,476 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-07-11 11:13:49,477 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-11 11:13:49,593 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-11 11:13:49,636 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-11 11:14:20,293 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-11 11:14:20,297 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:14:20,354 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:14:20,427 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:15:22,099 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240354
2024-07-11 11:16:18,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:18:51,789 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240354
2024-07-11 11:19:08,781 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_letter/ HTTP/1.1" 200 0
2024-07-11 11:19:08,929 basehttp.log_message 161 INFO    => "POST /api/documents/delete_letter/ HTTP/1.1" 200 69
2024-07-11 11:19:17,527 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:19:19,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 241000
2024-07-11 11:19:23,493 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2024-07-11 11:19:23,657 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:19:23,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-11 11:19:23,672 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:19:23,786 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:19:23,796 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-11 11:22:17,576 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:23:41,005 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_upload/ HTTP/1.1" 200 0
2024-07-11 11:23:41,162 log.log_response 230 WARNING => Bad Request: /api/documents/select_letter_upload/
2024-07-11 11:23:41,162 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_upload/ HTTP/1.1" 400 108
2024-07-11 11:24:00,752 main_utils.remove_file 426 INFO    => Deleted file: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\下市函文-PKL300立頓鮮漾奶綠AAA240711h16515P.docx
2024-07-11 11:24:00,752 log.log_response 230 WARNING => Bad Request: /api/documents/select_letter_upload/
2024-07-11 11:24:00,752 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_upload/ HTTP/1.1" 400 159
2024-07-11 11:24:31,703 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_upload/ HTTP/1.1" 200 75
2024-07-11 11:25:17,351 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_letter/ HTTP/1.1" 200 0
2024-07-11 11:25:17,520 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2024-07-11 11:25:17,544 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:25:31,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:25:36,822 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-11 11:25:37,080 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:25:49,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:28:17,552 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:28:45,192 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:28:45,251 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:28:45,332 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:28:46,381 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-11 11:28:46,563 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:28:57,903 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:31:17,536 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:33:28,659 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:33:41,424 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:33:47,093 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:33:59,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:34:17,566 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:36:20,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:36:23,890 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:36:35,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:37:16,010 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:37:17,530 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:37:23,517 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 11:37:23,579 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 11:37:27,389 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:37:27,593 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 11:37:27,785 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 11:37:27,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 11:37:28,057 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:37:28,072 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:37:28,185 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:37:28,269 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 11:37:28,338 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 11:37:29,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:37:29,192 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 59114)

2024-07-11 11:37:42,221 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:37:47,469 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:38:03,533 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:38:54,099 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:39:05,160 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:40:27,549 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:40:38,623 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:40:38,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:40:38,700 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:40:40,931 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:40:51,560 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:41:23,711 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:41:23,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:41:23,827 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:41:27,218 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:41:37,898 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:41:41,686 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 11:41:52,073 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 11:42:01,451 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 11:42:01,460 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 11:42:01,577 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 11:42:08,440 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 11:42:19,711 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 11:43:28,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:46:28,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:49:28,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:51:37,998 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 11:52:28,850 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:55:28,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:58:28,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 11:59:56,065 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 12:00:06,857 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:00:07,007 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 12:00:07,211 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 12:00:07,349 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 12:00:07,485 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 12:00:07,505 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 12:00:07,617 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 12:00:07,712 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 12:00:07,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 12:03:07,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:06:07,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:09:07,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:12:07,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:15:07,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:18:07,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:21:07,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:24:07,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:27:07,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:30:07,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:33:07,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-11 12:36:07,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:39:07,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:42:07,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:45:07,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:48:07,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:51:07,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:54:07,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 12:57:07,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:00:07,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:00:33,923 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 13:00:33,923 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 13:00:34,082 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 13:00:34,141 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 13:00:35,366 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-11 13:00:49,035 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:00:55,070 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-11 13:00:55,095 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-11 13:00:55,097 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-11 13:00:55,283 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 13:00:55,314 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 13:00:55,432 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 13:00:57,622 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:01:09,660 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:01:12,752 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:01:25,496 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:01:39,268 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:01:52,525 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:02:56,441 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:03:07,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:06:07,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:09:07,007 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 13:09:07,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:11:11,515 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:11:11,699 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 13:11:11,825 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 13:11:11,900 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-11 13:11:11,902 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-11 13:11:11,902 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-11 13:11:12,265 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 13:11:12,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4642
2024-07-11 13:11:12,412 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 13:11:12,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 13:12:07,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:15:07,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:16:36,852 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 13:16:57,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:17:01,637 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:17:13,219 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:17:23,545 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:17:35,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:17:51,385 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:18:02,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:18:07,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:18:44,781 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:18:56,373 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:21:07,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:21:31,281 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 13:22:03,533 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:22:15,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:22:20,182 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:22:30,885 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:24:07,013 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:27:07,007 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:30:07,006 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:33:07,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:34:18,458 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 13:34:45,986 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 13:34:45,988 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 13:34:53,817 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-11 13:34:54,033 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:35:06,357 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:35:15,836 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-11 13:35:15,965 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:35:26,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:36:07,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:38:17,139 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 13:38:39,505 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-11 13:38:50,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240549
2024-07-11 13:39:01,403 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-11 13:39:07,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:39:12,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:39:59,055 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240568
2024-07-11 13:42:07,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:45:07,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:48:07,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:51:07,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:54:07,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 13:57:07,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:00:07,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:03:07,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:06:07,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:09:07,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:12:07,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:15:07,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:18:07,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:21:07,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:24:07,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:24:49,965 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 14:24:50,018 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-11 14:27:07,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:28:44,860 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240588
2024-07-11 14:30:01,452 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 14:30:01,470 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:30:01,562 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:30:01,563 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 14:30:01,751 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 14:30:01,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:30:01,855 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:30:04,629 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-11 14:30:06,291 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149914
2024-07-11 14:30:06,984 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:30:36,874 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 14:30:36,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:30:37,005 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:30:42,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149914
2024-07-11 14:30:47,338 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-07-11 14:30:47,557 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 423
2024-07-11 14:30:58,199 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 423
2024-07-11 14:31:02,246 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 14:31:02,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:31:02,356 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:31:14,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240588
2024-07-11 14:31:18,012 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 286
2024-07-11 14:31:20,026 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:31:20,034 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:31:22,051 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 14:31:22,058 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 14:31:22,172 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 14:31:28,294 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149914
2024-07-11 14:31:42,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 423
2024-07-11 14:33:07,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:35:37,283 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 14:35:44,208 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149914
2024-07-11 14:35:46,822 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 423
2024-07-11 14:36:07,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:37:05,139 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 14:37:16,162 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149914
2024-07-11 14:37:18,300 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 414
2024-07-11 14:37:21,668 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1354
2024-07-11 14:37:25,539 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 869
2024-07-11 14:37:31,035 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 527
2024-07-11 14:37:45,774 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 414
2024-07-11 14:37:49,117 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1555
2024-07-11 14:39:06,999 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:42:07,009 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:45:07,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:48:07,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:51:07,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:54:07,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 14:57:07,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:00:07,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:03:07,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:06:07,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:09:07,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:12:07,026 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 15:12:07,177 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:15:07,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:18:07,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:21:07,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:24:07,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:27:07,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:30:07,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:33:07,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:36:07,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:39:07,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:42:07,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:45:07,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:48:07,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:51:07,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:54:07,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 15:57:07,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:00:07,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:03:07,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:06:07,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:09:07,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:12:07,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:15:07,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:18:07,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:21:07,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:24:07,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-11 16:27:07,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:30:07,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:33:07,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:35:21,857 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 16:36:08,780 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:37:10,302 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-11 16:39:07,343 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:42:07,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:45:07,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:47:10,238 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 16:47:10,239 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 16:47:10,388 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 16:47:10,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 16:47:13,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:47:13,201 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 16:47:13,281 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 16:47:13,281 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-11 16:47:13,308 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-11 16:47:13,344 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 16:47:13,423 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-11 16:47:13,423 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-11 16:47:13,423 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-11 16:47:13,603 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-11 16:47:13,700 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-11 16:47:13,756 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-11 16:47:13,770 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-11 16:47:13,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-11 16:47:13,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-11 16:50:14,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:53:14,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:56:14,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 16:59:14,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:02:16,272 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:05:14,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:08:14,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:11:14,206 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:14:13,175 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 17:14:13,294 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:17:14,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:20:14,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:23:14,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:23:56,099 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 17:23:56,155 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 17:26:14,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:29:14,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:32:14,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:35:14,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:38:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:41:14,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:44:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:47:14,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:50:14,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:53:14,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:56:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 17:59:14,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:02:14,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:05:14,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:08:14,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:11:14,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:14:14,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:17:14,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:20:14,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:23:14,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:26:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:29:14,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:32:14,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:35:14,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:38:14,176 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:41:14,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:44:14,190 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:47:14,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:50:14,188 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:53:14,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:56:14,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 18:59:14,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:02:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:05:14,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:08:14,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:11:14,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:14:14,040 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 19:14:14,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:17:14,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:20:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:23:14,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:26:14,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:29:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:32:14,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:35:14,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:38:14,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:41:14,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:44:14,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:47:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:50:14,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:53:14,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:54:36,968 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 19:54:36,968 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-11 19:54:37,108 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-11 19:54:37,166 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-11 19:56:14,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 19:59:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:02:14,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:05:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:08:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:11:14,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:14:14,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-11 20:17:14,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:20:14,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:23:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:26:14,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:29:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:32:14,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:35:14,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:38:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:41:14,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:44:14,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:47:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:50:14,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:53:14,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:56:14,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 20:59:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:02:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:05:14,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:08:14,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:11:14,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:14:14,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:17:14,017 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 21:17:14,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:20:14,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:23:14,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:26:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:29:14,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:32:14,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:35:14,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:38:14,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:41:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:44:14,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:47:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:50:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:53:14,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:56:14,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 21:59:14,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:02:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:05:14,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:08:14,260 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:11:14,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:14:14,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:17:14,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:20:14,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:23:14,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:26:14,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:29:14,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:32:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:35:14,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:38:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:41:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:44:14,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:47:14,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:50:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:53:14,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:56:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 22:59:14,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:02:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:05:14,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:08:14,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:11:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:14:14,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:17:14,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:20:14,006 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-11 23:20:14,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:23:14,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:26:14,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:29:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:32:14,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:35:14,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:38:14,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:41:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:44:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:47:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:50:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:53:14,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:56:14,320 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-11 23:59:14,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
