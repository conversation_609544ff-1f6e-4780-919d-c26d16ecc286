2025-01-10 15:06:40,028 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-01-10 15:06:40,171 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-01-10 15:06:40,264 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-01-10 15:06:40,421 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-01-10 15:06:40,506 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-01-10 15:06:40,583 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-01-10 15:06:40,583 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-01-10 15:06:40,583 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-01-10 15:06:40,904 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3292
2025-01-10 15:06:40,993 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-01-10 15:06:41,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-01-10 15:06:41,580 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-01-10 15:06:50,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2025-01-10 15:06:50,331 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-01-10 15:06:50,332 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2025-01-10 15:06:50,332 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-01-10 15:06:50,514 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:06:50,563 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:06:50,571 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:06:50,867 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-01-10 15:06:51,061 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:06:51,107 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:06:51,122 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:06:52,713 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:06:52,774 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:06:52,781 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:06:55,772 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2025-01-10 15:06:56,332 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 2283
2025-01-10 15:10:51,748 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2025-01-10 15:10:51,810 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2025-01-10 15:10:52,019 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19345
2025-01-10 15:12:30,177 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2025-01-10 15:12:30,406 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19345
2025-01-10 15:12:35,354 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:12:35,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:12:35,511 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:22,862 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2025-01-10 15:13:23,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3292
2025-01-10 15:13:23,648 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:23,696 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:23,724 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:41,656 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:41,670 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:41,776 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:42,599 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:42,608 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:42,710 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:43,311 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:43,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:43,426 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:43,806 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:43,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:43,932 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:44,228 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:44,346 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:44,410 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:44,675 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:44,802 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:44,838 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:45,162 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:45,165 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:45,290 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:45,696 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:45,715 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:45,826 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:46,291 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:46,321 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:46,416 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:46,768 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:46,895 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:46,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:47,229 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:47,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:47,379 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:47,580 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:47,581 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:47,683 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:48,028 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:48,036 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:48,152 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:48,471 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:48,490 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:48,599 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:13:48,940 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:13:48,941 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:13:49,037 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:14:04,717 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 15:14:04,752 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 15:14:04,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 15:55:05,952 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2025-01-10 15:55:06,017 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-01-10 16:16:23,484 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 225848
2025-01-10 16:21:04,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 225848
2025-01-10 16:21:07,153 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2025-01-10 16:21:07,154 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2025-01-10 16:21:07,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:21:07,309 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:21:07,402 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:21:07,420 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2025-01-10 16:21:07,555 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:22:09,398 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-01-10 16:22:09,589 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:22:09,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:22:09,722 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:22:10,531 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2025-01-10 16:22:10,651 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:22:10,721 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:22:10,763 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:22:10,777 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:22:10,955 log.log_response 230 ERROR   => Internal Server Error: /api/documents/business_notification_serial/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.IntegrityError: ORA-00001: unique constraint (B2B.BPUDCHT_KEY) violated

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 207, in business_notification_serial
    return self._handle_action('business_notification_serial', 'insert_update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 97, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 268, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 207, in business_notification_pudcno_method
    return {"results": select_get_serial('********', year, user_id)}, status.HTTP_200_OK
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\main_utils.py", line 312, in select_get_serial
    cursor.execute(insert_bpudcht_sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.IntegrityError: ORA-00001: unique constraint (B2B.BPUDCHT_KEY) violated
2025-01-10 16:22:10,959 basehttp.log_message 161 ERROR   => "POST /api/documents/business_notification_serial/ HTTP/1.1" 500 165447
2025-01-10 16:22:24,245 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2025-01-10 16:22:26,478 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 142179
2025-01-10 16:22:36,340 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 142179
2025-01-10 16:22:39,846 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:22:39,889 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:22:39,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:22:56,816 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 224308
2025-01-10 16:23:00,245 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:23:00,286 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:23:00,299 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:23:00,408 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:23:00,473 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2025-01-10 16:23:18,774 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 216748
2025-01-10 16:25:20,985 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-01-10 16:25:21,036 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2025-01-10 16:25:40,255 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 216748
2025-01-10 16:25:46,663 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:25:46,690 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:25:46,761 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:25:46,773 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:27:58,120 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:27:58,168 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:27:58,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:27:58,226 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:28:12,582 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:28:12,636 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:28:12,641 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:28:12,742 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:30:24,463 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2025-01-10 16:30:25,083 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2025-01-10 16:30:26,169 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2025-01-10 16:30:46,410 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 216748
2025-01-10 16:36:37,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:36:37,069 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:36:37,090 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:36:37,103 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:40:04,866 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2025-01-10 16:40:22,065 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2025-01-10 16:40:34,919 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2025-01-10 16:40:55,485 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 217375
2025-01-10 16:40:58,146 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2025-01-10 16:40:58,147 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-01-10 16:40:58,253 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: h16613
2025-01-10 16:40:58,254 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-01-10 16:40:58,257 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 0 條記錄
2025-01-10 16:40:58,257 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2025-01-10 16:40:58,321 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2025-01-10 16:41:09,063 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1339
2025-01-10 16:41:16,337 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 301
2025-01-10 16:41:28,023 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 301
2025-01-10 16:43:55,716 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 217294
2025-01-10 16:44:19,206 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41795
2025-01-10 16:44:21,640 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41795
2025-01-10 16:44:27,889 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41795
2025-01-10 16:45:27,013 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:45:27,016 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:45:27,032 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:45:27,141 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:45:36,233 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:45:36,366 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:45:36,404 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:45:36,468 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:45:41,013 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:45:41,052 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:45:41,061 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:45:41,171 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:45:42,752 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:45:42,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:45:42,871 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:45:42,912 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:45:46,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41795
2025-01-10 16:45:48,001 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: h16613
2025-01-10 16:45:48,001 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-01-10 16:45:48,004 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 0 條記錄
2025-01-10 16:45:48,004 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2025-01-10 16:45:48,076 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2025-01-10 16:46:16,753 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41795
2025-01-10 16:46:18,650 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 429
2025-01-10 16:46:20,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:46:20,065 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:46:20,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:46:20,196 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:46:41,097 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: h16613
2025-01-10 16:46:41,097 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-01-10 16:46:41,101 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 0 條記錄
2025-01-10 16:46:41,101 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2025-01-10 16:46:41,154 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-01-10 16:48:30,909 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2025-01-10 16:48:31,036 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: h16613
2025-01-10 16:48:31,040 _DownloadLetterInfo.select_letter_download 308 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11402003\11400015-114年黑松指定產品SC專業通路商推廣活動函-內文.docx
2025-01-10 16:48:31,040 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2025-01-10 16:48:31,040 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2025-01-10 16:48:36,455 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:48:36,498 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:48:36,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:48:36,636 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:48:49,581 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 429
2025-01-10 16:48:53,015 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:48:53,067 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:48:53,072 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:48:53,170 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:49:05,765 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 429
2025-01-10 16:49:08,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:49:08,469 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:49:08,484 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:49:08,615 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:49:34,960 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: h16613
2025-01-10 16:49:34,960 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-01-10 16:49:34,963 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 0 條記錄
2025-01-10 16:49:34,963 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2025-01-10 16:49:35,024 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-01-10 16:49:36,309 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: h16613
2025-01-10 16:49:36,311 _DownloadLetterInfo.select_letter_download 308 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11402003\11400015-附件-114年黑松指定產品SC專業通路商推廣辦法.docx
2025-01-10 16:49:36,312 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2025-01-10 16:49:36,312 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2025-01-10 16:49:41,555 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: h16613
2025-01-10 16:49:41,558 _DownloadLetterInfo.select_letter_download 308 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11402003\11400015-114年黑松指定產品SC專業通路商推廣活動函-內文.docx
2025-01-10 16:49:41,558 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2025-01-10 16:49:41,558 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2025-01-10 16:50:21,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:50:21,910 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:50:21,972 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:50:22,000 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:50:22,719 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:50:22,773 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:50:22,798 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:50:22,911 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:52:27,600 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 16:52:27,601 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42666
2025-01-10 16:52:27,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 16:52:27,744 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 16:52:39,467 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_letter/ HTTP/1.1" 200 0
2025-01-10 16:52:39,600 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2025-01-10 16:52:41,155 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 41927
2025-01-10 17:10:17,729 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-01-10 17:10:17,730 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-01-10 17:10:17,901 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2025-01-10 17:10:18,007 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-01-10 17:10:19,715 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2025-01-10 17:10:34,926 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 216209
2025-01-10 17:11:41,999 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2025-01-10 17:11:42,000 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-01-10 17:11:42,025 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-01-10 17:11:42,172 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-01-10 17:11:42,254 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 17:11:42,313 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 17:11:42,332 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 17:11:42,695 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2025-01-10 17:11:42,706 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-01-10 17:11:42,815 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 17:11:45,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2025-01-10 17:11:45,100 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-01-10 17:11:45,347 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3292
2025-01-10 17:11:48,717 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2025-01-10 17:11:48,923 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30677
2025-01-10 17:11:54,751 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30677
2025-01-10 17:12:16,723 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2025-01-10 17:12:16,797 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2025-01-10 17:12:16,964 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2025-01-10 17:12:17,044 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2025-01-10 17:12:17,095 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2025-01-10 17:12:17,155 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2025-01-10 17:12:17,223 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2025-01-10 17:12:17,276 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2025-01-10 17:12:17,384 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 53081
2025-01-10 17:12:18,557 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2025-01-10 17:12:18,869 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49171
