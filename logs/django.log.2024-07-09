2024-07-09 00:01:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:02:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:04:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:05:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:08:00,032 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 00:08:00,089 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:08:00,197 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:10:59,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:11:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:13:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:14:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:16:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:17:00,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:19:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:20:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:22:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:23:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:26:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:28:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:29:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:31:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:32:00,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:34:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:35:00,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:37:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:38:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:40:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:41:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:43:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:44:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:46:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:47:00,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:49:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:50:00,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:52:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:53:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:55:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:56:00,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 00:58:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 00:59:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:02:00,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:02:00,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:04:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:05:00,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:07:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:08:00,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:10:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:11:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:13:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:14:00,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:16:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:17:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:19:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:20:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:22:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:23:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:25:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:26:00,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:28:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:29:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:31:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:32:00,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:34:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:35:00,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:37:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:38:00,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:40:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:41:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:43:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:44:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:46:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:47:00,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:50:00,037 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:50:00,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:52:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:53:00,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:55:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:56:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 01:58:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 01:59:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:01:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:02:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:05:00,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:05:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:07:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:08:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:11:00,018 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 02:11:00,078 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:11:00,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:13:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:14:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:16:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:17:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:19:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:20:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:22:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:23:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:26:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:28:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:29:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:31:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:32:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:34:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:35:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:38:00,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:38:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:41:00,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:41:00,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:43:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:44:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:46:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:47:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:49:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:50:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:52:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:53:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:55:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:56:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 02:58:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 02:59:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:02:00,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:02:00,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:04:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:05:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:07:59,972 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:08:00,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:10:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:11:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:14:00,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:14:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:16:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:17:00,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:19:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:20:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:22:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:23:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:25:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:26:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:28:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:29:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:31:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:32:00,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:34:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:35:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:37:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:38:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:40:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:41:00,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-09 03:43:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:44:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:46:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:47:00,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:50:00,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:50:00,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:52:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:53:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:55:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:56:00,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 03:58:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 03:59:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:01:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:02:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:04:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:05:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:07:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:08:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:10:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:11:00,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:14:00,017 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 04:14:00,072 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:14:00,135 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:16:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:17:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:19:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:20:00,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:22:59,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:23:00,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:25:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:26:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:28:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:29:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:31:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:32:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:34:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:35:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:37:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:38:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:40:59,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:41:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:43:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:44:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:46:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:47:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:49:59,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:50:00,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:52:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:53:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:55:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:56:00,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 04:59:00,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 04:59:00,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:01:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:02:00,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:04:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:05:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:07:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:08:00,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:10:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:11:00,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:13:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:14:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:16:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:17:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:19:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:20:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:22:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:23:00,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:26:00,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:28:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:29:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:31:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:32:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:34:59,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:35:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:37:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:38:00,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:40:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:41:00,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:43:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:44:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:46:59,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:47:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:49:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:50:00,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:52:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:53:00,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:55:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:56:00,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 05:58:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 05:59:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:01:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:02:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:05:00,014 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:05:00,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:07:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:08:00,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:10:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:11:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:13:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:14:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:17:00,011 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 06:17:00,061 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:17:00,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:19:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:20:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:22:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:23:00,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:26:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:28:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:29:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:31:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:32:00,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:34:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:35:00,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:37:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:38:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:41:00,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:41:00,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:43:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:44:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:47:00,017 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:47:00,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:49:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:50:00,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:52:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:53:00,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:56:00,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:56:00,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 06:59:00,006 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 06:59:00,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:01:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:02:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:04:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:05:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:07:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:08:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:10:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:11:00,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:14:00,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:14:00,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:16:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:17:00,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:19:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:20:00,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:22:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:23:00,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:25:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:26:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:28:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:29:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:32:00,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:32:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-09 07:34:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:35:00,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:37:59,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:38:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:40:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:41:00,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:41:07,449 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 07:41:07,450 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 07:41:07,526 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:41:07,532 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:41:07,593 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 07:41:07,646 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 07:44:00,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:44:00,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:46:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:47:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:49:59,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:50:00,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:52:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:53:00,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:55:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:56:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 07:59:00,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 07:59:00,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:01:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:02:00,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:05:00,033 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:05:00,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:08:00,018 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:08:00,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:10:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:11:00,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:14:00,056 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:14:00,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:16:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:17:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:20:00,010 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 08:20:00,064 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:20:00,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:22:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:23:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:23:23,303 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:23:23,312 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:23:23,373 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 08:23:23,430 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 08:26:00,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:26:00,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:28:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:29:00,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:31:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:32:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:34:35,040 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:34:35,182 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,251 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 08:34:35,372 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,449 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 08:34:35,449 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 08:34:35,449 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 08:34:35,576 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,578 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,579 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,580 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 08:34:35,636 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 08:34:35,655 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 08:34:35,691 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:34:35,856 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 08:34:36,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12038
2024-07-09 08:34:36,144 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-09 08:37:36,312 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:37:36,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:40:35,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:40:36,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:43:36,023 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:43:36,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:46:35,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:46:36,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:49:35,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:49:36,408 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:52:36,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:52:36,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:55:35,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:55:36,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 08:58:35,996 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 08:58:36,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:01:35,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:01:36,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:04:35,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:04:36,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:07:35,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:07:36,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:10:35,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:10:36,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:13:36,010 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:13:36,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:16:35,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:16:36,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:19:35,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:19:36,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:22:35,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:22:36,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:25:35,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:25:36,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:28:35,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:28:36,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:31:36,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:31:36,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:34:35,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:34:36,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:37:35,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:37:36,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:40:35,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:40:36,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:43:35,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:43:36,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:46:35,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:46:36,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:49:36,014 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:49:36,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:52:35,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:52:36,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:55:35,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:55:36,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 09:58:35,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 09:58:36,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:01:35,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:01:36,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:04:36,029 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:04:36,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:07:35,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:07:36,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:08:32,365 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 10:08:32,367 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 10:08:32,435 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:08:32,440 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:08:32,508 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 10:08:32,566 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 10:10:36,006 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:10:36,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:13:36,031 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:13:36,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:16:36,143 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:16:36,209 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:19:36,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:19:36,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:22:36,115 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 10:22:36,165 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:22:36,229 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:25:35,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:25:36,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:28:35,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:28:36,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:31:35,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:31:36,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:34:35,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:34:36,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:37:36,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:37:36,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:40:35,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:40:36,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:43:35,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:43:36,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:46:35,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:46:36,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:49:35,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:49:36,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:52:35,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:52:36,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:55:36,022 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:55:36,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:57:31,061 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:31,064 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:31,133 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 10:57:31,185 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 10:57:37,149 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,209 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 10:57:37,265 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,348 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 10:57:37,419 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,495 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 10:57:37,495 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 10:57:37,495 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 10:57:37,565 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,569 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,599 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,609 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 10:57:37,640 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 10:57:37,676 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 10:57:37,727 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:37,850 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 10:57:37,932 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-09 10:57:37,953 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12038
2024-07-09 10:57:42,445 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:42,518 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 10:57:42,582 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 10:57:42,716 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:00:21,471 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,545 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:00:21,650 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,729 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 11:00:21,787 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,789 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,790 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,791 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:21,859 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 11:00:21,955 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-09 11:00:22,016 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:22,137 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-09 11:00:22,154 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:00:22,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12038
2024-07-09 11:00:34,813 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:34,950 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:00:35,812 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-07-09 11:00:35,871 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:36,066 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:36,133 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:36,249 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:36,919 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:37,030 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:37,079 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:37,123 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:37,193 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:37,246 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:37,263 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:37,300 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:37,343 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:00:37,495 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 70
2024-07-09 11:00:38,068 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:38,184 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:00:43,951 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:44,025 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-09 11:00:44,092 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:44,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:00:48,716 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-09 11:00:48,775 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:00:50,508 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 6735
2024-07-09 11:01:10,584 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 0
2024-07-09 11:01:10,641 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:01:17,608 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 13374
2024-07-09 11:01:44,010 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 0
2024-07-09 11:01:44,061 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:01:44,264 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 607
2024-07-09 11:01:53,010 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 0
2024-07-09 11:01:53,060 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:01:53,294 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 70
2024-07-09 11:01:53,348 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:01:53,498 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 70
2024-07-09 11:03:37,961 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:03:38,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:06:37,959 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:06:38,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:09:37,964 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:09:38,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:12:38,159 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:12:38,234 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:15:37,970 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:15:38,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:18:38,096 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:18:38,209 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:21:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:21:37,334 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:24:37,284 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:24:37,358 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-09 11:27:37,263 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:27:37,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:30:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:30:37,336 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:33:37,270 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:33:37,348 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:36:37,267 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:36:37,358 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:39:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:39:37,353 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:42:37,266 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:42:37,356 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:45:37,267 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:45:37,342 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:48:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:48:37,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:49:58,024 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:49:58,028 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:49:58,091 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-09 11:49:58,143 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 11:50:50,031 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:50:50,116 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-09 11:50:50,175 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:50:50,343 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:50:51,705 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-09 11:50:51,764 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:50:55,965 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 416942
2024-07-09 11:51:37,278 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:51:37,367 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:51:37,881 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:51:37,945 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-09 11:51:38,830 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:51:38,892 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-09 11:51:38,951 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:51:39,070 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 11:51:40,299 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-09 11:51:40,358 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:51:40,641 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1073
2024-07-09 11:52:06,791 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:06,858 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-09 11:52:06,921 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-09 11:52:06,948 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 11:52:06,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:06,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:07,108 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 11:52:07,118 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 11:52:07,527 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:07,606 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-09 11:52:07,677 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:07,678 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:07,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 11:52:07,793 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 11:52:10,146 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:10,147 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:10,262 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 11:52:10,266 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 11:52:10,808 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:10,810 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:10,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 11:52:10,972 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 11:52:11,448 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:11,531 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 11:52:12,370 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:12,371 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:12,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 11:52:12,536 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 11:52:21,622 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-09 11:52:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:52:21,931 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 11:54:37,267 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:54:37,349 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:57:37,961 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 11:57:38,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 11:59:15,096 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 12:00:38,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:00:38,708 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:03:37,959 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:03:38,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:06:37,966 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:06:38,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:09:37,969 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:09:38,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:12:37,966 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:12:38,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:15:37,960 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:15:38,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:18:37,972 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:18:38,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:21:37,964 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:21:38,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:24:37,992 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 12:24:38,061 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:24:38,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:27:37,969 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:27:38,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:30:37,970 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:30:38,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:32:03,943 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 12:32:03,943 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 12:32:04,017 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:32:04,020 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:32:04,085 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 12:32:04,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 12:33:37,963 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:33:38,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:36:37,968 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:36:38,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:39:37,970 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:39:38,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:42:37,966 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:42:38,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:45:38,014 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:45:38,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:48:37,960 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:48:38,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:51:37,970 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:51:38,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:54:37,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:54:38,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 12:57:37,958 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 12:57:38,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 13:00:37,962 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:00:38,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 13:02:05,654 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:05,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-09 13:02:05,996 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 13:02:06,057 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:06,181 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 13:02:08,724 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:08,809 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-09 13:02:08,857 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:09,009 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 13:02:10,195 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:13,853 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 416942
2024-07-09 13:02:27,647 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:27,756 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 13:02:53,839 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:02:53,965 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 13:03:00,805 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:03:01,168 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7714
2024-07-09 13:03:04,955 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:03:05,238 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:03:37,267 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:03:37,371 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 13:06:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:06:37,359 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 13:09:37,262 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-09 13:09:37,330 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-09 13:10:17,784 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-09 13:10:17,900 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 13:10:18,050 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 13:10:40,416 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-09 13:10:40,539 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-09 13:10:40,539 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-09 13:10:43,944 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-09 13:10:44,005 token_utils.verify_access_token  30 INFO    => refresh_token: 85fc32eb-0997-40af-a8e2-9923cb3483dd
2024-07-09 13:10:44,067 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 13:10:44,140 token_utils.verify_access_token  30 INFO    => refresh_token: 85fc32eb-0997-40af-a8e2-9923cb3483dd
2024-07-09 13:10:44,140 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 13:10:44,189 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 13:10:44,190 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 13:10:44,320 token_utils.verify_access_token  30 INFO    => refresh_token: 85fc32eb-0997-40af-a8e2-9923cb3483dd
2024-07-09 13:10:44,321 token_utils.verify_access_token  30 INFO    => refresh_token: 85fc32eb-0997-40af-a8e2-9923cb3483dd
2024-07-09 13:10:44,322 token_utils.verify_access_token  30 INFO    => refresh_token: 85fc32eb-0997-40af-a8e2-9923cb3483dd
2024-07-09 13:10:44,449 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:10:44,461 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 13:10:45,007 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 13:10:45,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 13:10:48,562 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 13:10:48,707 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 13:11:44,010 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-09 13:11:44,010 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-09 13:11:47,303 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-09 13:11:47,303 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-09 13:11:51,893 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-09 13:11:51,893 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-09 13:11:58,061 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-09 13:11:58,062 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-09 13:12:03,930 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-09 13:12:03,993 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:04,068 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 13:12:04,131 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:04,132 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:04,133 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:04,134 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:04,294 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:12:04,336 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 13:12:04,591 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 13:12:04,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 13:12:35,662 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,736 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 13:12:35,794 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,820 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,820 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,845 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,845 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-09 13:12:35,848 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,848 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:35,986 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 13:12:36,041 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:36,045 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 13:12:36,112 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 13:12:36,157 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 13:12:36,220 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 13:12:36,318 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 13:12:36,349 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-09 13:12:39,167 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:39,253 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 13:12:39,315 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:39,457 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 13:12:48,449 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:48,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-09 13:12:51,082 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:51,201 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 13:12:57,194 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:57,261 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-09 13:12:57,317 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:12:57,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:16:27,294 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 13:16:31,977 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:16:32,875 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:19:39,891 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 13:19:50,937 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:19:51,385 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:30:25,421 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:30:25,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:30:26,449 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-09 13:30:26,510 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:30:26,669 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-09 13:30:56,068 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/insert_bullet_board/ HTTP/1.1" 200 0
2024-07-09 13:30:56,120 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:30:56,222 basehttp.log_message 161 INFO    => "POST /api/documents/insert_bullet_board/ HTTP/1.1" 200 69
2024-07-09 13:31:00,289 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:31:00,456 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:55:57,988 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 13:56:02,794 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:56:02,794 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:56:03,684 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 13:56:03,686 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-09 13:56:03,687 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-09 13:56:03,738 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:56:03,794 error_utils.handle_error  13 ERROR   => select_bullet_board_method - Unexpected error: must be str, not function
2024-07-09 13:56:03,899 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_bullet_board/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 170, in select_bullet_board
    return self._handle_action('bullet_board', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 97, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 89, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'TypeError' is not JSON serializable
2024-07-09 13:56:03,904 basehttp.log_message 161 ERROR   => "POST /api/documents/select_bullet_board/ HTTP/1.1" 500 137182
2024-07-09 13:57:46,172 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 13:57:49,155 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:57:49,670 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:58:32,008 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 13:58:32,541 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,620 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 13:58:32,693 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,785 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,786 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,787 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:32,921 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-09 13:58:32,973 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 13:58:32,985 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:33,162 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:58:33,217 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 13:58:33,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 13:58:34,349 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:58:34,549 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 13:59:44,648 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 13:59:44,825 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 14:00:55,216 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:00:59,337 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:00:59,889 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4858
2024-07-09 14:01:32,557 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:01:32,616 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:04:32,536 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:04:32,618 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:05:11,935 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:05:12,083 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-09 14:06:52,476 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:06:56,345 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:06:56,870 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4855
2024-07-09 14:06:58,273 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:06:58,416 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-09 14:07:01,922 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:01,994 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:07:02,196 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:07:02,378 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,446 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,447 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,449 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,511 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-09 14:07:02,569 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:02,581 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 14:07:02,851 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4855
2024-07-09 14:07:02,976 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 14:07:03,007 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 14:07:10,686 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:07:10,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-09 14:07:27,436 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 14:07:27,515 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-07-09 14:07:27,575 token_utils.verify_access_token  45 ERROR   => Refresh token 已過期
2024-07-09 14:07:27,575 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-09 14:07:27,576 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 80
2024-07-09 14:07:28,746 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 14:07:28,775 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-09 14:08:12,364 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-09 14:08:12,480 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 126
2024-07-09 14:08:12,539 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:12,595 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:08:12,654 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 14:08:12,655 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-09 14:08:12,707 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 14:08:12,708 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 14:08:12,800 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:12,803 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:12,803 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:12,832 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:13,041 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 14:08:13,047 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 14:08:13,275 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12038
2024-07-09 14:08:13,449 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-09 14:08:16,619 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:16,690 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 14:08:18,459 token_utils.verify_access_token  30 INFO    => refresh_token: 11c40a49-f07e-44cd-a434-0a0ac8da16b7
2024-07-09 14:08:18,717 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7714
2024-07-09 14:08:23,177 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 14:08:23,301 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 14:08:28,675 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-09 14:08:28,733 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:28,811 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:08:28,870 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:28,871 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:28,872 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:28,873 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:29,111 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 14:08:29,154 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9502
2024-07-09 14:08:29,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4855
2024-07-09 14:08:29,229 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5906
2024-07-09 14:08:33,789 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:33,875 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-09 14:08:33,934 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:34,017 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:08:34,077 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:34,078 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:34,079 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:34,080 token_utils.verify_access_token  30 INFO    => refresh_token: da0c7b40-8b87-4751-99cc-32378e0e977b
2024-07-09 14:08:34,282 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4855
2024-07-09 14:08:34,296 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-09 14:08:34,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9502
2024-07-09 14:08:34,608 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5906
2024-07-09 14:08:36,617 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 14:08:36,736 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 14:08:53,616 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_bullet_board/ HTTP/1.1" 200 0
2024-07-09 14:08:53,661 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:08:53,738 basehttp.log_message 161 INFO    => "POST /api/documents/delete_bullet_board/ HTTP/1.1" 200 69
2024-07-09 14:08:54,799 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:08:54,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 14:10:02,217 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:10:02,282 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:11:22,863 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:11:22,929 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-09 14:11:22,990 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:11:23,115 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 14:11:24,211 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 0
2024-07-09 14:11:24,266 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:11:24,427 basehttp.log_message 161 INFO    => "POST /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 72890
2024-07-09 14:11:42,033 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:11:42,107 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-09 14:11:42,168 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-09 14:11:42,229 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:11:42,654 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 14:13:02,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:13:03,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:16:02,990 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:16:03,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:19:02,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:19:03,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:21:41,991 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:21:45,449 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_truck/ HTTP/1.1" 200 0
2024-07-09 14:21:45,541 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:21:45,942 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-09 14:21:47,941 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:21:48,246 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 14:22:02,194 token_utils.verify_access_token  30 INFO    => refresh_token: 5087ddd2-16d2-46ba-94f2-7f847a046ac8
2024-07-09 14:22:02,255 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:22:39,037 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:23:12,001 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:23:16,683 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-09 14:23:38,672 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:23:38,939 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:23:39,193 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-09 14:23:39,259 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 14:23:39,642 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 14:23:39,691 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 14:23:39,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 14:23:51,344 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 14:23:51,565 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,567 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,567 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,569 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,570 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,570 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 14:23:51,751 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-09 14:23:51,802 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 14:23:51,859 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 14:23:51,917 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 14:23:51,977 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 14:23:52,035 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 14:23:52,090 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 14:23:55,045 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-09 14:23:55,290 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-09 14:23:59,093 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 14:23:59,284 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 14:24:00,310 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-09 14:24:00,580 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-09 14:24:06,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-09 14:24:07,800 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 14:24:07,975 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 14:24:09,330 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-07-09 14:24:09,826 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 327977
2024-07-09 14:24:20,934 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 14:24:20,990 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-09 14:24:21,124 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:24:22,174 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 14:24:22,237 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-09 14:24:22,237 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-09 14:24:22,437 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 14:24:22,437 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 14:24:24,028 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 14:24:24,080 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-09 14:24:24,267 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 14:25:48,870 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-09 14:25:48,960 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:25:49,077 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:25:49,990 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-09 14:25:50,583 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-09 14:26:12,825 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 14:26:16,374 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:26:16,483 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:26:16,785 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_fun_group/ HTTP/1.1" 200 0
2024-07-09 14:26:17,093 basehttp.log_message 161 INFO    => "POST /api/users/select_user_fun_group/ HTTP/1.1" 200 7194
2024-07-09 14:26:17,433 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:26:17,546 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:26:18,517 basehttp.log_message 161 INFO    => "POST /api/users/select_user_fun_group/ HTTP/1.1" 200 7194
2024-07-09 14:26:24,166 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:26:24,314 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:26:25,954 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-09 14:26:39,087 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 14:26:39,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:27:10,326 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:29:38,944 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:30:10,138 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:30:14,865 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-09 14:32:39,006 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:33:23,414 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:35:39,239 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:38:35,748 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 14:38:39,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:38:39,669 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:41:38,925 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:43:58,415 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:43:58,424 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:44:38,949 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:48:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:48:46,190 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:48:46,193 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:49:06,142 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:49:06,220 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:49:38,147 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:49:38,154 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:50:21,128 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:50:21,230 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:50:27,066 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:50:27,169 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:50:42,154 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:50:42,161 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:51:03,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:51:03,265 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 14:51:03,285 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 14:51:03,668 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 14:51:03,704 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 14:51:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:54:04,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:54:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:54:12,753 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 14:54:12,884 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 14:54:14,665 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:54:14,772 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:54:15,834 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:57:03,191 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:57:03,301 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:57:04,015 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:57:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:57:22,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:57:22,340 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 14:57:22,341 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 14:57:22,418 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 14:57:22,815 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 14:57:22,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 14:57:32,256 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:57:32,270 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 14:57:34,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:57:37,039 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:57:39,778 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:57:40,427 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:12,726 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 6229
2024-07-09 14:58:14,592 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:16,394 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:16,868 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:17,443 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:17,940 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:18,586 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:19,200 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:21,149 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 6229
2024-07-09 14:58:23,744 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:58:35,146 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 636
2024-07-09 14:58:36,977 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:41,680 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 636
2024-07-09 14:58:45,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:58:50,718 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:53,548 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:54,243 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:56,463 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:58:57,600 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:12,513 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:15,568 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:17,825 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:22,869 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-09 14:59:23,059 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-09 14:59:26,133 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 2151
2024-07-09 14:59:26,140 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 2151
2024-07-09 14:59:27,499 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:30,404 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:31,950 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:38,944 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 14:59:39,149 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 14:59:41,646 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 601
2024-07-09 14:59:42,799 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:49,002 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 610
2024-07-09 14:59:51,000 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 14:59:52,461 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 610
2024-07-09 14:59:54,195 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:00:03,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:00:22,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:00:59,191 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:01:04,971 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:01:05,080 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:01:05,273 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:01:05,343 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:01:05,551 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:01:05,722 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:01:05,761 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:01:07,443 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:01:07,536 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:01:14,978 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:01:32,480 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:01:35,780 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:02:15,300 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:02:18,662 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:02:23,913 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:02:24,177 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:02:24,488 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:02:24,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:02:24,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:02:25,064 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:02:25,064 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:02:26,165 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:02:26,262 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:02:27,343 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:02:57,248 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:03:04,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:03:04,261 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:03:04,481 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:03:04,592 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:03:04,788 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:03:04,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:03:04,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:03:06,055 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:03:06,062 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:03:07,171 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:03:42,617 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:03:45,760 error_utils.handle_error  13 ERROR   => select_user_data_method - Unexpected error: unsupported operand type(s) for +: 'NoneType' and 'NoneType'
2024-07-09 15:03:45,859 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 183, in select_user_data
    return self._handle_action('user_data', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'TypeError' is not JSON serializable
2024-07-09 15:03:45,865 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data/ HTTP/1.1" 500 137429
2024-07-09 15:05:52,921 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:05:55,947 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:05:59,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:05:59,647 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:05:59,873 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:05:59,942 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:06:00,146 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:06:00,318 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:06:00,351 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:06:05,907 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:06:05,954 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:06:07,150 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51154
2024-07-09 15:07:02,068 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:07:04,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51863
2024-07-09 15:07:20,276 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:08:17,415 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 15:08:23,297 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:08:59,656 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:11:59,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:14:59,655 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:17:59,662 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:20:59,647 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:23:59,644 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:26:59,672 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:27:23,399 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:29:59,644 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:32:59,657 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:35:50,452 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:35:55,475 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:35:55,679 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:35:55,813 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 15:35:55,814 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 15:35:55,814 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 15:35:55,864 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:35:56,130 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:35:56,230 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:35:56,375 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:35:56,405 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:35:57,067 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:35:57,094 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:36:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:38:55,691 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:41:55,707 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:42:30,179 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:42:30,301 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:42:30,900 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:42:32,109 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:42:32,128 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:42:33,869 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:43:20,287 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:43:20,292 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:43:32,627 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:43:36,125 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 15:43:36,205 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 15:43:36,391 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 15:43:38,864 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:43:38,962 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:43:40,241 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:43:41,824 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 15:43:49,807 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:43:49,896 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:43:50,306 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:44:28,276 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:44:28,278 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 54016)

2024-07-09 15:44:30,563 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 15:44:41,787 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:44:42,011 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:44:42,227 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:44:42,302 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:44:42,502 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:44:42,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:44:42,708 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:44:43,752 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-09 15:44:43,811 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-09 15:44:43,922 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 15:44:43,976 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:44:43,983 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 15:44:44,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 15:44:44,726 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:44:44,736 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 15:44:44,840 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 15:44:45,205 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-09 15:44:45,383 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 15:44:46,331 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 15:44:46,464 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:44:47,872 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:46:25,738 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:46:32,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:46:32,259 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:46:32,393 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:46:32,460 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:46:32,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:46:32,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:46:32,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:46:35,051 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:46:35,149 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:47:08,352 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:47:08,738 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:47:08,855 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:47:11,725 basehttp.log_message 161 INFO    => "POST /api/users/select_user_fun_group/ HTTP/1.1" 200 7194
2024-07-09 15:47:17,605 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:47:17,808 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:47:17,965 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:47:17,993 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 15:47:18,205 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:47:18,399 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:47:18,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:47:25,786 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_permissions/ HTTP/1.1" 200 0
2024-07-09 15:47:25,961 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-09 15:47:47,597 basehttp.log_message 161 INFO    => "OPTIONS /api/users/update_user_permissions/ HTTP/1.1" 200 0
2024-07-09 15:47:47,875 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:47:50,750 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:47:52,810 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:47:52,824 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:47:57,729 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:47:57,937 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:47:58,108 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 360
2024-07-09 15:47:58,212 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:47:58,355 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:47:58,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:47:58,551 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:48:07,861 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 15:48:08,001 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:48:08,882 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:48:31,356 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:48:34,963 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:48:35,121 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:48:35,300 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:48:35,370 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:48:35,540 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:48:35,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:48:35,711 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:48:37,785 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:48:37,900 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:48:42,432 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:48:42,681 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:48:42,821 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:48:42,995 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:48:43,088 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:48:43,157 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:48:43,310 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:48:44,293 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:48:44,296 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:49:03,409 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:49:05,679 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:07,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:09,978 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:49:10,186 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:49:10,394 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:49:10,436 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:49:10,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:49:10,630 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:49:10,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:49:14,857 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:49:14,870 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:49:16,244 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:38,820 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:49:41,392 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:43,481 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:46,882 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:49:49,822 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:49:49,933 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:49:50,073 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:49:50,138 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:49:50,342 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:49:50,512 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:49:50,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:49:51,496 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:49:51,505 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:49:53,261 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:50:18,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:51:19,475 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:51:23,489 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:51:27,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:51:27,196 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:51:27,334 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 15:51:27,425 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 15:51:27,619 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:51:27,752 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 15:51:27,765 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 15:51:28,203 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:51:28,209 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:51:29,220 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:51:31,943 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:51:40,437 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:51:46,428 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 15:51:51,493 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:51:51,501 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:52:00,638 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:52:27,080 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 15:53:17,849 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:54:13,041 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:54:13,158 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:54:27,241 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:54:37,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-09 15:54:50,991 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:55:33,477 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 15:55:33,479 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-09 15:55:33,651 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-09 15:55:33,651 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-09 15:55:33,704 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 15:55:38,127 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-09 15:55:38,243 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-09 15:55:38,386 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:55:38,460 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 15:55:38,460 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-09 15:55:38,512 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 15:55:38,512 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 15:55:38,679 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 15:55:38,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 15:55:38,957 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 15:55:39,027 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 15:55:40,339 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-09 15:55:40,396 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_fun_group/ HTTP/1.1" 200 0
2024-07-09 15:55:40,602 basehttp.log_message 161 INFO    => "POST /api/users/select_user_fun_group/ HTTP/1.1" 200 7194
2024-07-09 15:55:52,546 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-09 15:56:00,903 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-09 15:56:04,658 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 15:56:04,781 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:56:04,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:56:05,041 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 15:56:05,102 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-09 15:56:05,103 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-09 15:56:05,113 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 15:56:05,653 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:56:05,694 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:56:05,735 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 15:56:05,828 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 15:56:10,782 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:56:10,909 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:56:11,160 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 15:56:11,592 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 15:56:11,621 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 15:56:11,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 15:56:11,744 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 15:56:11,747 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 15:56:17,770 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:56:31,925 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 15:56:31,978 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-09 15:56:31,979 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-09 15:56:32,167 error_utils.handle_error  13 ERROR   => select_user_data_method2 - Unexpected error: 'DEPTNAME'
2024-07-09 15:56:32,179 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 15:56:32,268 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 188, in select_user_data2
    return self._handle_action('user_data', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-07-09 15:56:32,273 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data2/ HTTP/1.1" 500 136700
2024-07-09 15:56:35,800 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:56:35,935 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 15:56:36,071 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 15:56:36,173 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 15:56:36,348 error_utils.handle_error  13 ERROR   => select_user_data_method2 - Unexpected error: 'DEPTNAME'
2024-07-09 15:56:36,353 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 15:56:36,375 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 188, in select_user_data2
    return self._handle_action('user_data', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-07-09 15:56:36,378 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data2/ HTTP/1.1" 500 136700
2024-07-09 15:56:36,378 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 15:56:36,480 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 15:57:28,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:59:18,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 15:59:36,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:00:07,189 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:00:09,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:00:09,227 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:00:09,357 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:00:09,428 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:00:09,652 error_utils.handle_error  13 ERROR   => select_user_data_method2 - Unexpected error: 'DEPTNAME'
2024-07-09 16:00:09,715 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 188, in select_user_data2
    return self._handle_action('user_data', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-07-09 16:00:09,716 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data2/ HTTP/1.1" 500 136700
2024-07-09 16:00:09,982 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:00:09,983 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:00:10,105 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:00:28,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:01:41,747 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 16:01:45,457 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 16:01:51,364 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-09 16:01:51,496 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:01:51,657 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:01:51,800 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4625
2024-07-09 16:01:52,001 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:01:52,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:01:55,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:01:55,594 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:01:55,800 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-09 16:01:55,883 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 16:01:56,022 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:01:56,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 16:01:56,265 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 16:02:00,808 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:02:00,985 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:02:01,011 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:02:04,202 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:02:04,378 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:02:04,381 error_utils.handle_error  13 ERROR   => select_user_data_method2 - Unexpected error: 'DEPTNAME'
2024-07-09 16:02:04,442 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 188, in select_user_data2
    return self._handle_action('user_data', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-07-09 16:02:04,448 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data2/ HTTP/1.1" 500 136700
2024-07-09 16:02:26,106 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:03:28,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:04:39,651 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:04:41,015 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:04:41,149 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:04:41,271 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:04:41,368 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:04:41,565 error_utils.handle_error  13 ERROR   => select_user_data_method2 - Unexpected error: 'DEPTNAME'
2024-07-09 16:04:41,595 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_data2/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 188, in select_user_data2
    return self._handle_action('user_data', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 87, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 77, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-07-09 16:04:41,597 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_data2/ HTTP/1.1" 500 136700
2024-07-09 16:04:41,598 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:04:41,649 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:04:41,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:04:56,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:06:28,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:06:54,029 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 16:06:56,400 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:06:57,325 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:06:57,454 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:06:57,665 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:06:57,710 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:06:57,936 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:06:57,943 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:06:58,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:06:58,040 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:06:59,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:06:59,843 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:06:59,855 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:07:12,875 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-09 16:07:40,079 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:07:40,211 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:07:55,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:08:07,501 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:08:07,619 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:08:07,814 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:08:07,888 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:08:08,073 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:08,089 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:08,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:08:08,209 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:08:16,833 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:16,852 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:17,189 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:17,289 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:39,127 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:39,141 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:40,146 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:40,170 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:51,269 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:08:51,408 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:08:51,545 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:08:51,654 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:08:51,830 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:08:51,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:08:51,842 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:08:51,932 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:09:10,322 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:10,417 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:11,142 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:11,239 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:19,496 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:19,525 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:20,114 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:20,191 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:20,342 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:09:20,484 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:09:20,614 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:09:20,717 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:09:20,894 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:20,913 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:20,985 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:09:21,009 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:09:22,571 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-09 16:09:22,652 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 16:09:22,826 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:09:23,210 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:09:23,218 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:09:28,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:10:00,547 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:00,574 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:01,104 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:01,203 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:09,397 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:09,509 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:10,132 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:10,149 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:24,804 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:24,828 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:25,149 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:10:25,158 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:10:27,008 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-09 16:10:27,535 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 16:10:55,609 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:11:13,583 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:11:13,593 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:11:14,280 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:11:14,288 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:12:20,486 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:12:28,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:12:30,678 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:12:30,683 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:12:31,287 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:12:31,301 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:13:55,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:14:11,793 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:14:12,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:14:12,289 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 16:14:12,386 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 16:14:12,723 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 16:14:12,746 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 16:14:38,718 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:14:56,950 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:15:04,475 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:15:04,611 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:15:04,841 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:15:04,848 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:15:05,058 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:15:05,115 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:15:05,148 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:15:05,194 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:15:08,077 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 16:15:28,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:15:59,815 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:15:59,920 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:16:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:16:00,090 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:16:00,292 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:16:00,317 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:16:00,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:16:00,416 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:16:22,668 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:16:22,675 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:16:24,954 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:16:25,076 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:16:25,197 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:16:25,293 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:16:25,495 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:16:25,496 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:16:25,578 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:16:25,629 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:16:26,673 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 16:16:35,966 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:16:36,187 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:16:36,326 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 16:16:36,396 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 16:16:36,624 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 16:16:36,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 16:16:36,784 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 16:17:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:17:27,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 16:17:29,521 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:17:29,636 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:17:29,784 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:17:29,858 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:17:30,080 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:17:30,096 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:17:30,190 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:17:30,245 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:17:31,911 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51595
2024-07-09 16:19:09,229 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:19:09,304 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:19:27,230 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:19:27,324 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:19:31,316 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222775
2024-07-09 16:19:37,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:20:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:20:25,837 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:20:26,077 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:20:26,225 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 16:20:26,321 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-09 16:20:26,512 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 16:20:26,654 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 16:20:26,655 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 16:20:29,664 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:20:32,035 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:20:32,130 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-09 16:20:33,472 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222775
2024-07-09 16:23:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:23:27,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:23:29,655 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:26:04,080 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-09 16:26:04,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-09 16:26:12,225 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_dept/ HTTP/1.1" 200 0
2024-07-09 16:26:12,354 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_dept/ HTTP/1.1" 200 69
2024-07-09 16:26:12,542 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17702
2024-07-09 16:26:15,578 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222786
2024-07-09 16:26:26,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:26:29,679 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:26:33,147 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_dept/ HTTP/1.1" 200 69
2024-07-09 16:26:33,350 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17845
2024-07-09 16:26:37,289 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222846
2024-07-09 16:26:51,424 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_dept/ HTTP/1.1" 200 69
2024-07-09 16:26:51,602 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17997
2024-07-09 16:27:05,512 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_dept/ HTTP/1.1" 200 69
2024-07-09 16:27:05,689 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-09 16:27:19,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:29:25,983 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 16:29:26,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:29:29,679 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:32:26,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:32:30,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:32:35,042 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 16:32:40,039 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:32:49,061 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:32:49,257 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:32:49,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:33:23,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:33:23,292 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:33:23,481 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:33:23,530 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-09 16:33:23,774 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:33:23,802 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:33:23,867 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:33:23,914 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:33:25,195 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:33:27,150 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:33:27,814 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-09 16:33:28,015 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:33:28,025 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:34:01,573 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:34:01,740 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1581
2024-07-09 16:34:01,829 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:01,836 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:02,136 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:34:02,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38912
2024-07-09 16:34:06,307 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:34:06,308 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:34:07,026 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:07,036 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:08,715 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:34:08,717 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:34:09,412 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:09,523 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:11,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-09 16:34:11,497 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-09 16:34:11,860 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 16:34:11,955 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:13,245 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:34:15,997 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:34:16,000 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:34:16,746 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:16,764 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:17,926 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-09 16:34:18,124 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:34:18,716 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:18,732 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:19,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-09 16:34:19,487 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5675
2024-07-09 16:34:20,302 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:20,320 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:22,759 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 16:34:23,123 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:23,129 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:29,174 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-09 16:34:29,349 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:34:30,606 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:30,701 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:32,522 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 822
2024-07-09 16:34:32,691 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:34:33,811 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:33,912 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:35,465 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:34:49,302 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 16:34:49,363 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,487 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,488 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,488 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,488 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,489 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,535 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:34:49,652 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 16:34:49,701 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 16:34:49,756 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 16:34:49,826 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 16:34:49,863 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:34:49,923 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 16:34:49,973 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 16:34:51,669 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-09 16:34:51,674 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-09 16:34:52,546 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:52,642 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:53,723 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 16:34:54,720 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-09 16:34:54,821 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:34:56,043 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-09 16:35:06,147 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-09 16:35:09,627 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:35:11,561 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 0
2024-07-09 16:35:11,776 basehttp.log_message 161 INFO    => "POST /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 72890
2024-07-09 16:35:27,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:36:23,297 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:36:42,406 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-09 16:36:42,460 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-09 16:36:42,569 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:36:42,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 16:36:42,645 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:36:43,572 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5675
2024-07-09 16:36:44,737 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 16:36:44,908 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 16:36:44,927 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:36:45,012 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:36:45,691 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:36:45,734 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:36:45,751 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 16:36:47,445 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5675
2024-07-09 16:36:47,914 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-09 16:36:47,958 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:36:47,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 16:36:49,578 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-09 16:36:59,757 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240128
2024-07-09 16:37:01,690 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:37:32,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-09 16:37:32,543 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:37:34,071 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 43524
2024-07-09 16:37:38,717 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-09 16:37:38,882 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:37:39,880 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-09 16:38:27,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:38:31,939 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 2985512
2024-07-09 16:38:35,390 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 16:38:35,437 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:38:35,519 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 16:38:35,557 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 16:38:35,616 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 16:38:35,686 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 16:38:35,780 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 16:38:36,300 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-09 16:38:36,564 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-09 16:38:44,463 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 16:38:44,505 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:38:44,574 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 16:38:44,631 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 16:38:44,694 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 16:38:44,752 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 16:38:44,824 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 16:38:45,028 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-09 16:38:46,033 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prodsalesunit/ HTTP/1.1" 200 0
2024-07-09 16:38:46,155 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:38:46,238 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:38:48,817 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:38:48,869 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:38:53,020 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:38:53,075 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:38:55,145 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 653
2024-07-09 16:38:55,175 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:02,703 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:02,761 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 653
2024-07-09 16:39:05,546 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:05,595 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:06,986 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:07,033 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:08,747 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:08,795 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:09,962 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:10,017 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:12,015 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:12,064 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:17,391 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 57
2024-07-09 16:39:17,451 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:19,537 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3490
2024-07-09 16:39:19,578 basehttp.log_message 161 INFO    => "POST /api/products/select_prodsalesunit/ HTTP/1.1" 200 651
2024-07-09 16:39:23,307 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:39:57,234 basehttp.log_message 161 INFO    => "OPTIONS /api/products/delete_prodsalesunit/ HTTP/1.1" 200 0
2024-07-09 16:39:57,360 error_utils.handle_error  13 ERROR   => delete_prodsalesunit_method - Unexpected error: product_codes must be a non-empty list
2024-07-09 16:39:57,361 log.log_response 230 WARNING => Bad Request: /api/products/delete_prodsalesunit/
2024-07-09 16:39:57,361 basehttp.log_message 161 WARNING => "POST /api/products/delete_prodsalesunit/ HTTP/1.1" 400 95
2024-07-09 16:40:00,006 error_utils.handle_error  13 ERROR   => delete_prodsalesunit_method - Unexpected error: product_codes must be a non-empty list
2024-07-09 16:40:00,006 log.log_response 230 WARNING => Bad Request: /api/products/delete_prodsalesunit/
2024-07-09 16:40:00,006 basehttp.log_message 161 WARNING => "POST /api/products/delete_prodsalesunit/ HTTP/1.1" 400 95
2024-07-09 16:40:01,695 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:40:06,582 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-09 16:40:08,367 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:40:09,515 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-09 16:40:09,729 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:09,949 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:10,726 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:10,926 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:11,189 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:11,391 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:11,482 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:11,685 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:12,783 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:40:14,848 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:40:15,629 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:15,814 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:23,543 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:40:24,180 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 0
2024-07-09 16:40:25,838 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 160188
2024-07-09 16:40:39,186 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:40:40,129 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:40,308 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 16:40:46,364 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 16:40:46,501 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-09 16:41:06,679 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 16:41:06,897 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,897 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,897 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,898 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,898 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,899 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:06,957 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-09 16:41:07,245 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 16:41:07,310 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 16:41:07,332 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 16:41:07,397 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 16:41:07,460 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 16:41:07,521 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 16:41:07,569 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 16:41:08,314 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 16:41:08,518 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:41:09,902 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-09 16:41:10,125 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-09 16:41:21,014 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-09 16:41:22,276 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-09 16:41:22,483 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:41:24,448 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:41:26,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:41:27,143 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:41:28,663 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:41:29,419 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-09 16:44:26,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:46:17,949 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:46:35,583 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 0
2024-07-09 16:46:36,380 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 160283
2024-07-09 16:46:38,535 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 16:46:39,495 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-09 16:47:27,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:50:27,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:53:27,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:55:54,488 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 16:55:54,645 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 16:56:26,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 16:59:26,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:02:26,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-09 17:05:27,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:08:27,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:11:27,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:14:27,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:17:27,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:20:27,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:21:46,628 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 17:22:00,234 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-09 17:22:01,503 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:23:26,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:23:32,270 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:23:32,542 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:23:32,547 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:23:32,931 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:23:32,952 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:23:34,059 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:23:34,795 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:18,863 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:26:19,062 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:19,075 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:26:19,188 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 17:26:19,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:26:19,536 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:26:20,537 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:21,374 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:26,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:26:32,424 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:26:41,507 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:26:41,691 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:41,703 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:26:42,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:26:42,166 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:26:42,855 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:26:44,467 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:28:56,684 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:28:56,907 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:28:56,970 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:28:57,193 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:28:57,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:28:58,187 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:28:59,449 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:29:19,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:29:26,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:29:32,441 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:29:41,660 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:30:43,686 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:30:45,188 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:30:48,986 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:30:49,232 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 17:30:49,381 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:30:49,474 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 17:30:49,691 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:30:49,881 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:30:49,912 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:30:50,314 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:30:51,577 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:32:23,535 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 17:32:23,787 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 17:32:23,835 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 17:32:23,889 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 17:32:23,950 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 17:32:24,008 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 17:32:24,068 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 17:32:24,129 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 17:32:50,805 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 17:32:50,867 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 17:32:50,953 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 17:32:50,992 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 17:32:51,040 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 17:32:51,098 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 17:32:51,152 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 17:32:51,175 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:32:51,334 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:32:51,518 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:32:51,591 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:32:51,654 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:32:51,780 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:32:51,800 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:34:20,276 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-09 17:34:20,623 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-09 17:34:20,684 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 17:34:20,692 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 17:34:20,742 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 17:34:20,793 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 17:34:20,860 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 17:34:20,920 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 17:34:20,974 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 17:34:21,001 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:34:21,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:34:21,421 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:34:21,446 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:34:29,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:34:29,670 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 17:34:29,821 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-09 17:34:29,893 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:34:30,116 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-09 17:34:30,160 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-09 17:34:30,224 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-09 17:34:30,277 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-09 17:34:30,339 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-09 17:34:30,354 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:34:30,376 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:34:30,401 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-09 17:34:30,466 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-09 17:34:32,717 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 17:34:32,888 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:34:35,355 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 17:34:35,567 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-09 17:35:09,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:35:09,578 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 17:35:09,714 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 17:35:09,902 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:35:09,993 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:10,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:35:10,216 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:35:10,658 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:11,493 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:12,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:24,882 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:35:25,069 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 17:35:25,211 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 17:35:25,284 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 17:35:25,506 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:25,706 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 17:35:25,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 17:35:27,866 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:35:31,736 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 17:38:25,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:41:25,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:44:25,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:47:25,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:50:25,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:53:25,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:56:25,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 17:59:25,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:02:25,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:05:25,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:08:25,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:11:25,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:14:25,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:17:25,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:20:25,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:23:25,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:26:25,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:29:25,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:32:25,043 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 18:32:25,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:35:25,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:38:25,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:41:25,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:44:25,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:47:25,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:50:25,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:53:25,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:56:12,041 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 18:56:12,041 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 18:56:12,263 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:12,315 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:26,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:56:34,141 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:34,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:42,062 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 18:56:42,063 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 18:56:42,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 18:56:42,225 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 18:56:50,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:56:50,395 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 18:56:50,465 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 18:56:50,465 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 18:56:50,493 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 18:56:50,622 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 18:56:50,696 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 18:56:50,921 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:51,104 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 18:56:51,137 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 18:56:55,429 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:56:57,726 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:57:16,639 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 18:57:25,973 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:57:26,114 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 18:57:26,341 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 18:57:26,383 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 18:57:26,618 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:57:26,837 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 18:57:26,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 18:57:27,868 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:57:29,263 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:59:21,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 18:59:21,398 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:59:21,414 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 18:59:21,587 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 18:59:21,656 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 18:59:32,864 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 18:59:34,281 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "%": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "%": invalid identifier
2024-07-09 18:59:34,292 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 158861
2024-07-09 18:59:35,468 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "%": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "%": invalid identifier
2024-07-09 18:59:35,470 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 158861
2024-07-09 18:59:37,639 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "%": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "%": invalid identifier
2024-07-09 18:59:37,645 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 158861
2024-07-09 19:00:08,143 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 19:00:17,061 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:00:17,068 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143398
2024-07-09 19:00:27,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:01:20,657 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 19:01:30,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:01:30,199 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 19:01:30,412 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 19:01:30,482 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 19:01:30,687 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 19:01:30,873 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 19:01:30,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 19:01:31,928 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 19:01:33,339 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:01:33,346 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:01:34,745 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:01:34,747 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:01:36,854 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:01:36,856 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:02:09,387 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 19:02:11,413 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:02:11,421 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:02:14,851 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:02:14,986 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 19:02:15,196 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 19:02:15,257 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 19:02:15,473 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 19:02:15,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 19:02:15,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 19:02:16,691 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 19:02:18,127 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:02:18,128 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:02:19,242 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:02:19,244 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:02:21,377 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:02:21,378 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:02:39,286 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 19:02:39,288 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 19:05:15,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:08:15,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:11:15,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:14:15,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:17:15,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:20:15,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:23:15,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:26:15,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:29:15,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:32:15,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:35:15,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:38:15,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:41:15,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:44:15,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:47:15,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:50:15,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:53:15,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:56:15,283 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 19:59:15,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:02:15,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:05:15,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:08:15,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:11:15,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:14:15,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:17:15,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:20:15,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:23:15,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:26:15,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:29:15,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:32:15,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:35:15,027 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 20:35:15,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:38:15,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:41:15,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:44:15,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:47:13,564 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:47:13,739 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:47:13,947 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:47:14,014 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:47:14,209 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:47:14,396 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:47:14,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:47:16,067 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:47:17,094 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 20:47:17,096 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 20:47:18,208 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 20:47:18,209 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 20:47:20,329 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 20:47:20,330 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 20:49:01,661 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 114, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), ())
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 516, in _fix_for_params
    query = query % tuple(args)
TypeError: not enough arguments for format string
2024-07-09 20:49:01,663 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143418
2024-07-09 20:50:14,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:53:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-09 20:54:37,637 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 20:54:46,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:54:46,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:54:46,951 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:54:47,019 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:54:47,226 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:54:47,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:54:47,444 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:54:48,876 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:54:54,379 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:54:54,385 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143683
2024-07-09 20:54:55,493 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:54:55,495 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143683
2024-07-09 20:54:57,650 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 112, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2(7), {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:54:57,652 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143683
2024-07-09 20:55:00,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:55:00,230 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:55:00,612 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:55:00,653 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:55:00,849 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:55:00,865 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:55:00,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:56:29,857 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 20:56:36,131 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:56:36,252 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:56:36,468 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:56:36,524 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:56:36,560 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-09 20:56:36,750 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:56:36,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:56:36,972 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:56:38,053 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:56:39,678 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 111, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:56:39,684 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143677
2024-07-09 20:56:40,799 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 111, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:56:40,801 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143677
2024-07-09 20:56:42,899 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 111, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(ocv025_combobox_dealer_code_name_sql2, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:56:42,900 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 143677
2024-07-09 20:56:43,995 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:56:44,066 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 20:56:44,185 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:56:44,490 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:56:44,599 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:56:44,790 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:56:44,921 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:56:44,932 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:59:39,492 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 20:59:45,549 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 20:59:45,678 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 20:59:45,827 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-09 20:59:45,827 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-09 20:59:45,828 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-09 20:59:45,959 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 20:59:46,120 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 20:59:46,239 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:59:46,430 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 20:59:46,469 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 20:59:55,036 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 20:59:56,636 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:59:56,642 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144374
2024-07-09 20:59:57,763 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 20:59:57,764 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144374
2024-07-09 21:00:19,144 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 21:00:19,146 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144374
2024-07-09 21:00:24,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:00:24,321 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:00:24,797 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:00:24,892 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:00:25,236 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:00:25,258 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:00:25,293 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:02:09,197 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:02:09,370 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:02:09,500 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:02:09,568 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:02:09,772 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:02:09,998 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:02:10,018 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:02:11,602 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:02:13,326 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 21:02:13,328 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144374
2024-07-09 21:02:14,431 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 335
2024-07-09 21:02:14,432 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144374
2024-07-09 21:05:10,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:08:10,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:10:24,336 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:10:27,800 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 328
2024-07-09 21:10:27,807 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144209
2024-07-09 21:10:31,470 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:10:31,615 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:10:31,849 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:10:31,911 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:10:32,108 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:10:32,293 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:10:32,334 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:10:36,631 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:10:37,710 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 328
2024-07-09 21:10:37,711 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144209
2024-07-09 21:10:38,820 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 328
2024-07-09 21:10:38,821 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144209
2024-07-09 21:10:40,972 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, {})
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 520, in execute
    query, params = self._fix_for_params(query, params, unify_by_values=True)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 497, in _fix_for_params
    query = query % args
ValueError: unsupported format character ''' (0x27) at index 328
2024-07-09 21:10:40,974 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 144209
2024-07-09 21:10:43,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:10:43,223 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:10:43,648 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:10:43,725 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:10:43,943 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:10:44,104 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:10:44,120 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:12:47,133 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:12:53,289 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-09 21:12:53,300 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159546
2024-07-09 21:12:53,961 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00933: SQL command not properly ended

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00933: SQL command not properly ended
2024-07-09 21:12:53,967 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159758
2024-07-09 21:12:54,420 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-09 21:12:54,424 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159546
2024-07-09 21:12:55,100 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00933: SQL command not properly ended

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00933: SQL command not properly ended
2024-07-09 21:12:55,106 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159758
2024-07-09 21:12:56,610 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-09 21:12:56,612 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159546
2024-07-09 21:12:56,619 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 57594)

2024-07-09 21:12:57,605 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:12:57,867 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:12:58,512 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:12:58,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:12:58,758 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-01036: illegal variable name/number

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 116, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-01036: illegal variable name/number
2024-07-09 21:12:58,764 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159546
2024-07-09 21:12:59,119 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:12:59,176 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:13:39,557 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:13:52,880 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:13:58,621 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:14:00,556 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00933: SQL command not properly ended

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 117, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00933: SQL command not properly ended
2024-07-09 21:14:00,567 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159710
2024-07-09 21:14:01,677 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00933: SQL command not properly ended

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 117, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00933: SQL command not properly ended
2024-07-09 21:14:01,684 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159710
2024-07-09 21:14:05,503 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:14:05,803 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:14:06,024 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:14:06,095 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:14:06,378 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:14:06,506 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:14:06,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:14:33,728 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:14:47,589 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:14:47,752 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:14:47,962 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:14:48,148 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:14:48,270 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:14:48,325 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:14:48,475 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:15:03,384 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:15:04,982 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:15:06,075 log.log_response 230 ERROR   => Internal Server Error: /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00933: SQL command not properly ended

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\views.py", line 56, in combobox_erp_hy_ocv025_dealers_code_name
    sql_result, http_status = select_erp_hy_ocv025_dealers_code_name(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\dealers\_DealerInfo.py", line 118, in select_erp_hy_ocv025_dealers_code_name
    cursor.execute(sSTM, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00933: SQL command not properly ended
2024-07-09 21:15:06,082 basehttp.log_message 161 ERROR   => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 500 159693
2024-07-09 21:16:44,482 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:16:49,862 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-09 21:17:00,583 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-09 21:17:00,584 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-09 21:17:00,838 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-09 21:17:00,898 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-09 21:17:12,630 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:17:48,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:20:48,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:23:48,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:26:48,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:29:48,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:32:48,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:35:48,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:38:48,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:41:48,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:44:48,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:47:48,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:50:48,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:53:48,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:54:05,562 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 21:54:16,325 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:54:16,327 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:54:20,365 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:54:24,653 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-09 21:54:33,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 21:54:33,273 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-09 21:54:33,457 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-09 21:54:33,521 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-09 21:54:33,743 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:54:33,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-09 21:54:33,933 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-09 21:54:35,662 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-09 21:54:51,912 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-09 21:55:01,744 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 294
2024-07-09 21:55:01,800 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 223
2024-07-09 21:57:34,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:00:34,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:03:34,227 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:03:39,650 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-09 22:06:34,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:09:34,289 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:12:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:15:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:18:34,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:21:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:24:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:27:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:30:34,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:33:34,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:36:34,006 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-09 22:36:34,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:39:34,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:42:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:45:34,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:48:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:51:34,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:54:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 22:57:34,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:00:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:03:34,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:06:34,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:09:34,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:12:34,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:15:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:18:34,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:21:34,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:24:34,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:27:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:30:34,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:33:34,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:36:34,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:39:34,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:42:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:45:34,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:48:34,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:51:34,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:54:34,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-09 23:57:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
