2025-05-06 10:09:18,604 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:09:54,053 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 10:09:54,053 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-06 10:10:04,915 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 10:10:04,915 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-06 10:10:25,476 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-05-06 10:10:40,761 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-05-06 10:10:58,439 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-05-06 10:10:58,641 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-05-06 10:10:58,717 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 10:10:59,006 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 10:10:59,117 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 10:10:59,124 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-05-06 10:10:59,124 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-05-06 10:10:59,124 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-05-06 10:10:59,306 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:10:59,608 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1896
2025-05-06 10:10:59,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 642
2025-05-06 10:11:00,139 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:11:18,106 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2025-05-06 10:11:18,253 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2025-05-06 10:11:40,507 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 10:11:40,569 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-05-06 10:11:40,569 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-05-06 10:11:40,725 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:11:40,844 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:20:32,579 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:20:35,864 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-05-06 10:20:35,864 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-05-06 10:20:35,981 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:20:36,051 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:20:52,690 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2025-05-06 10:20:53,054 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 182819
2025-05-06 10:24:14,525 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:26:34,697 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-05-06 10:26:36,239 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:26:36,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2025-05-06 10:26:52,010 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-05-06 10:26:52,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:26:52,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 10:26:52,852 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:26:52,912 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 10:26:53,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:26:53,234 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:26:53,424 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 642
2025-05-06 10:26:53,478 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:27:13,141 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:27:28,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52723
2025-05-06 10:27:30,868 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:27:34,968 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52723
2025-05-06 10:27:44,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:28:08,375 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:28:08,453 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:28:10,699 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:28:11,143 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\37301b53-1fac-4918-bd0e-81463ac501a2.docx'
2025-05-06 10:28:11,145 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\37301b53-1fac-4918-bd0e-81463ac501a2.docx'
2025-05-06 10:28:11,146 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:28:11,146 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:28:41,253 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:28:41,319 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\7ae278a6-799d-4e38-b68b-0dee6571f78f.docx'
2025-05-06 10:28:41,321 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\7ae278a6-799d-4e38-b68b-0dee6571f78f.docx'
2025-05-06 10:28:41,321 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:28:41,322 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:29:39,341 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:29:46,092 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:29:46,178 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\40f4d1c1-0da1-4703-814c-3dccd21c79ed.docx'
2025-05-06 10:29:46,180 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\40f4d1c1-0da1-4703-814c-3dccd21c79ed.docx'
2025-05-06 10:29:46,181 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:29:46,181 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:29:52,710 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:32:54,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:33:49,389 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:33:49,499 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\da5ab89c-2f5f-4d62-a3e3-c0656e102256.docx'
2025-05-06 10:33:49,501 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\da5ab89c-2f5f-4d62-a3e3-c0656e102256.docx'
2025-05-06 10:33:49,502 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:33:49,502 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:33:50,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:33:51,033 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:34:07,995 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:34:08,081 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:34:11,128 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:34:11,187 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\b7250099-c9a7-491f-a6f8-ace1921b3efc.docx'
2025-05-06 10:34:11,188 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\b7250099-c9a7-491f-a6f8-ace1921b3efc.docx'
2025-05-06 10:34:11,189 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:34:11,189 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:34:50,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:34:50,164 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 10:34:50,375 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 10:34:50,455 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:34:50,589 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:34:50,711 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:34:50,829 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:34:50,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 642
2025-05-06 10:34:53,388 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:34:56,079 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:34:56,153 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:34:58,309 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 通知814超市114年5月促銷活動 的位置
2025-05-06 10:34:58,377 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\0dda907b-592e-42ac-a4e9-c4816a69d64c.docx'
2025-05-06 10:34:58,379 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\0dda907b-592e-42ac-a4e9-c4816a69d64c.docx'
2025-05-06 10:34:58,379 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:34:58,380 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 515
2025-05-06 10:35:35,812 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:35:50,904 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:36:32,779 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:36:36,005 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:36:36,138 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 10:36:36,296 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:36:36,414 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 10:36:36,605 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:36:36,639 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:36:36,842 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 642
2025-05-06 10:36:36,842 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:36:37,660 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52723
2025-05-06 10:36:39,222 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51397
2025-05-06 10:37:33,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:37:33,594 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2025-05-06 10:37:34,010 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:37:34,077 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:37:53,464 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:37:53,527 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:38:48,882 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:38:48,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:39:36,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:40:11,443 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:40:19,227 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 10:40:23,500 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:40:23,564 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:40:38,548 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:40:38,617 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:40:38,978 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 10:40:40,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:40:40,586 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:40:54,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:40:54,440 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:40:56,860 _DownloadBusinessNotificationInfo.select_business_notification_price_download 787 ERROR   => select_business_notification_price_download 發生錯誤: (-2147418111, '接收者已拒絕這個呼叫。', None, None)
2025-05-06 10:40:56,862 _DownloadBusinessNotificationInfo.select_business_notification_price_download 796 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\87430d19-b6c0-4ed3-8a4a-6109b00d666b.docx'
2025-05-06 10:40:56,863 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 10:40:56,864 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-05-06 10:41:24,989 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:41:34,015 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:41:34,107 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:41:34,553 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:41:34,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5408
2025-05-06 10:42:36,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:43:44,122 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:43:51,531 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 10:43:55,070 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:43:55,141 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:44:00,155 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:44:00,179 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:44:00,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:44:00,667 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5410
2025-05-06 10:45:37,896 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 10:45:41,567 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-05-06 10:45:41,719 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-05-06 10:46:27,202 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-05-06 10:46:27,387 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:46:27,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1896
2025-05-06 10:46:27,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-05-06 10:46:28,062 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:46:32,524 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:46:32,544 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:46:34,098 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-05-06 10:46:34,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:46:34,383 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:46:45,723 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:46:45,840 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:46:47,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 10:46:53,043 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:46:53,114 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:46:57,387 _DownloadBusinessNotificationInfo.get_cell_text_and_color 429 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 10:46:57,835 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 10:47:35,896 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:47:35,951 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:47:37,319 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 32726
2025-05-06 10:47:50,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 32726
2025-05-06 10:47:56,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:47:56,558 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 10:47:56,846 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 10:47:56,931 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 10:47:56,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 10:47:57,118 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 10:47:57,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-05-06 10:47:57,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 10:47:59,580 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52144
2025-05-06 10:48:02,594 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 10:48:04,625 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:48:04,698 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:48:07,638 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 32726
2025-05-06 10:49:13,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42381
2025-05-06 10:49:19,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 10:49:19,634 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 10:49:20,971 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 58582
2025-05-06 10:50:57,602 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:53:58,542 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:56:57,806 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 10:59:56,624 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:01:28,405 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 11:01:39,939 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,003 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,021 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,155 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,459 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,518 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,624 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:40,954 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:41,592 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:41,642 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:41,843 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:42,214 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:42,373 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:42,432 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,121 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,237 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,263 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,284 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,334 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,383 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,471 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,552 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:43,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:01:44,047 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7057
2025-05-06 11:02:05,180 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 11:02:05,241 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 11:02:10,693 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:02:12,134 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5248
2025-05-06 11:02:56,624 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:03:59,955 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 11:06:02,714 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:08:57,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:11:29,258 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2025-05-06 11:11:29,500 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 6018
2025-05-06 11:11:57,521 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:14:57,564 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:17:57,809 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:20:58,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:23:57,740 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:26:58,535 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:29:58,533 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:32:59,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:35:58,363 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:38:57,741 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:41:58,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:44:57,683 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:47:57,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:49:23,590 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 11:49:23,804 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 11:49:28,985 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:49:29,138 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 11:49:29,374 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 11:49:29,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 11:49:29,731 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 11:49:29,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 11:49:30,053 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-05-06 11:49:30,144 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 11:49:32,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 11:49:36,058 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 11:49:36,128 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 11:49:40,898 _DownloadBusinessNotificationInfo.get_cell_text_and_color 430 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:49:43,949 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 11:52:29,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:53:45,105 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 11:53:52,051 _DownloadBusinessNotificationInfo.select_business_notification_price_download 836 ERROR   => select_business_notification_price_download 發生錯誤: (-2147418111, '接收者已拒絕這個呼叫。', None, None)
2025-05-06 11:53:52,055 _DownloadBusinessNotificationInfo.select_business_notification_price_download 845 ERROR   => 刪除臨時文件時發生錯誤: [WinError 32] 程序無法存取檔案，因為檔案正由另一個程序使用。: 'C:\\Users\\<USER>\\PycharmProjects\\HEYSONG_ERP_HY_API\\uploads\\業務通報\\11400344\\efeef8f9-f716-4dbd-8b76-ad2eb9d87957.docx'
2025-05-06 11:53:52,056 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-05-06 11:53:52,056 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-05-06 11:54:20,965 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:54:23,032 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 11:55:29,144 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 11:56:51,639 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 11:57:01,761 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:57:04,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 11:58:36,886 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 11:58:52,054 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 11:58:55,608 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-05-06 11:58:55,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 11:58:55,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1896
2025-05-06 11:58:56,368 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-05-06 11:58:56,395 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 11:58:57,257 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 11:58:57,376 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 11:58:58,556 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 10781
2025-05-06 11:59:01,136 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 11:59:01,261 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 11:59:02,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 11:59:06,416 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 11:59:06,491 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 11:59:13,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5236
2025-05-06 11:59:20,957 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 11:59:21,034 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 11:59:41,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 11:59:44,050 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5202
2025-05-06 12:00:30,787 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 12:00:39,055 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 12:00:39,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 12:01:23,629 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 12:01:29,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:01:30,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 12:01:31,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 12:04:29,184 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:07:29,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:10:29,193 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:13:29,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:16:29,191 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:19:29,199 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:22:29,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:25:29,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:28:29,132 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-05-06 12:28:29,247 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:31:29,231 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:34:29,229 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:37:29,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:40:29,241 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:43:29,241 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:46:29,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:49:29,249 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:52:29,267 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:55:29,253 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 12:56:05,398 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 12:56:05,398 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 12:56:05,623 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 12:56:05,704 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 12:58:29,252 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:01:29,270 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:04:29,275 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:07:30,462 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:10:32,573 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:13:30,234 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:16:30,691 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:19:29,824 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:22:48,612 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:29:59,188 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 13:29:59,256 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 13:29:59,325 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:31:29,326 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-05-06 13:47:43,145 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 13:47:50,196 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-05-06 13:47:50,368 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-05-06 13:47:50,434 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 13:47:50,560 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 13:47:50,625 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 13:47:50,629 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-05-06 13:47:50,629 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-05-06 13:47:50,629 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-05-06 13:47:50,861 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2244
2025-05-06 13:47:50,909 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-05-06 13:47:51,325 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" ************-05-06 13:47:51,368 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-05-06 13:48:28,733 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 13:48:28,791 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-05-06 13:48:28,791 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-05-06 13:48:28,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-05-06 13:48:29,034 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-05-06 13:48:31,720 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52144
2025-05-06 13:48:33,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50738
2025-05-06 13:48:41,627 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-05-06 13:48:41,627 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-05-06 13:48:42,028 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-05-06 13:48:42,118 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-05-06 13:48:46,125 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-05-06 13:48:48,732 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 13:48:57,695 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 14:27:06,322 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 14:27:06,389 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 14:49:20,947 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 14:49:28,243 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 14:49:28,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5198
2025-05-06 14:50:16,731 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 14:50:24,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 14:50:25,146 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5197
2025-05-06 14:52:40,938 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-05-06 14:52:49,630 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 14:52:50,198 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5197
2025-05-06 14:58:40,345 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 14:58:40,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 15:07:31,888 _DownloadBusinessNotificationInfo.get_cell_text_and_color 432 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2025-05-06 15:07:32,989 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5197
2025-05-06 16:07:39,560 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 16:07:39,575 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 16:07:39,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 16:07:39,862 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 16:49:50,115 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-05-06 16:49:50,199 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-05-06 22:23:55,436 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 22:23:55,437 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-05-06 22:23:55,574 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-05-06 22:23:55,574 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2025-05-06 22:23:55,643 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-05-06 22:23:55,643 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
