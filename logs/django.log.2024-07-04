2024-07-04 00:01:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:01:08,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:04:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:07:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:07:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:10:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:10:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:13:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:13:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:16:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:16:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:19:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:19:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:22:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:22:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:25:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:25:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:28:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:28:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:31:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:31:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:34:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:34:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:37:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:37:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:40:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:40:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:43:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:43:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:46:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:46:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:49:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:49:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:52:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:52:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:55:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:55:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 00:58:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 00:58:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:01:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:01:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:04:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:04:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:07:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:07:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:10:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:10:08,014 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:13:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:13:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:16:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:16:08,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:19:08,007 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:19:08,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:22:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:22:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:25:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:25:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:28:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:28:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:31:07,999 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 01:31:08,043 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:31:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:34:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:34:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:37:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:37:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:40:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:40:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:43:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:43:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:46:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:46:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:49:07,977 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:49:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-04 01:52:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:52:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:55:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:55:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 01:58:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 01:58:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:01:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:01:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:04:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:07:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:07:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:10:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:10:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:13:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:13:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:16:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:16:08,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:19:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:19:08,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:22:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:22:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:25:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:25:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:28:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:28:08,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:31:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:31:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:34:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:34:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:37:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:37:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:40:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:40:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:43:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:43:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:46:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:46:08,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:49:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:49:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:52:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:52:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:55:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:55:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 02:58:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 02:58:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:01:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:01:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:04:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:04:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:07:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:07:08,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:10:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:10:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:13:07,985 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:13:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:16:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:16:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:19:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:19:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:22:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:22:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:25:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:25:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:28:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:28:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:31:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:31:08,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:34:07,996 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 03:34:08,046 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:34:08,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:37:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:37:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:40:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:40:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:43:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:43:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:46:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:46:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:49:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:49:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:52:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:52:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:55:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:55:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 03:58:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 03:58:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:01:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:01:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:04:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:07:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:07:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:10:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:10:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:13:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:13:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:16:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:16:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:19:07,955 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:19:08,013 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:22:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:22:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:25:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:25:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:28:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:28:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:31:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:31:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:34:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:34:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:37:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:37:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:40:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:40:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:43:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:43:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:46:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:46:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:49:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:49:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:52:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:52:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:55:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:55:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 04:58:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 04:58:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:01:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:01:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:04:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:04:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:07:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:07:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:10:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:10:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:13:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:13:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:16:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:16:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:19:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:19:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:22:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:22:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:25:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:25:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:28:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:28:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:31:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:31:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:34:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:34:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:37:07,987 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 05:37:08,037 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:37:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:40:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:40:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-04 05:43:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:43:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:46:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:46:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:49:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:49:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:52:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:52:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:55:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:55:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 05:58:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 05:58:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:01:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:01:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:04:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:04:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:07:07,955 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:07:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:10:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:10:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:13:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:13:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:16:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:16:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:19:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:19:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:22:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:22:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:25:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:25:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:28:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:28:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:31:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:31:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:34:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:34:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:37:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:37:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:40:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:40:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:43:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:43:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:46:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:46:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:49:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:49:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:52:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:52:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:55:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:55:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 06:58:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 06:58:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:01:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:01:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:04:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:04:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:07:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:07:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:10:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:10:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:13:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:13:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:16:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:16:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:19:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:19:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:22:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:22:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:25:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:25:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:28:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:28:08,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:31:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:31:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:34:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:34:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:37:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:37:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:38:52,042 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 07:38:52,098 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:38:52,160 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 07:39:30,969 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 07:39:31,013 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:39:31,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:43:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:43:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:46:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:46:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:49:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:49:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:52:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:52:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:55:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:55:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 07:58:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 07:58:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:00:30,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:00:31,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:04:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:04:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:07:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:07:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:10:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:10:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:13:07,994 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:13:08,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:16:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:16:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:19:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:19:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:22:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:22:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:25:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:25:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:26:30,681 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:26:30,767 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 08:27:30,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:27:31,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:30:30,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:30:31,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:34:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:34:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:37:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:37:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:40:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:40:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:43:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:43:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:45:30,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:45:31,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:49:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:49:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:52:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:52:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:55:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:55:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 08:58:07,989 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 08:58:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:01:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:01:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:04:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:04:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:07:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:07:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:10:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:10:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:13:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:13:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:16:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:16:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:19:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:19:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:22:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:22:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:25:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:25:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:28:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:28:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:31:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:31:08,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-04 09:34:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:34:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:37:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:37:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:40:07,991 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 09:40:08,051 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:40:08,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:43:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:43:08,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:46:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:46:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:49:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:49:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:52:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:52:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:55:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:55:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 09:58:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 09:58:08,271 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:01:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:01:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:04:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:04:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:07:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:07:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:10:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:10:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:13:08,017 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:13:08,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:16:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:16:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:19:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:19:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:22:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:22:08,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:25:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:25:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:28:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:28:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:28:50,942 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 10:28:50,992 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:28:51,059 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 10:29:39,422 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:39,508 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-04 10:29:39,570 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-04 10:29:39,596 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-04 10:29:39,666 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:39,670 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:39,753 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-04 10:29:39,865 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-04 10:29:40,534 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:40,596 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-04 10:29:40,654 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-04 10:29:40,708 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:40,779 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-04 10:29:41,453 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:41,533 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-04 10:29:41,598 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-04 10:29:41,654 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:41,821 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-04 10:29:42,205 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:42,275 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-04 10:29:42,665 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:42,667 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:42,778 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-04 10:29:42,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-04 10:29:45,235 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:45,396 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-04 10:29:47,910 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:47,912 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-04 10:29:47,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:48,002 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-04 10:29:48,121 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 10:29:49,294 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-04 10:29:49,345 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:49,863 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-04 10:29:54,676 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:29:55,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 221383
2024-07-04 10:30:30,278 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:30:30,346 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:31:27,314 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:31:27,449 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 10:32:42,170 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:32:42,284 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 10:33:22,712 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:33:22,856 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-04 10:33:22,914 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:33:23,028 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-04 10:33:30,276 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:33:30,342 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:35:24,335 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:24,490 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-04 10:35:27,518 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:27,522 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:27,636 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 10:35:27,641 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-04 10:35:35,682 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:36,169 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-04 10:35:42,730 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:43,240 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 221383
2024-07-04 10:35:57,004 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:35:57,167 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 10:36:07,916 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:36:08,070 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 3213
2024-07-04 10:36:30,265 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:36:30,337 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:40:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:40:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:42:30,267 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:42:30,353 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:45:30,275 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:45:30,363 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:48:30,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:48:31,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:52:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:52:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:55:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:55:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 10:58:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 10:58:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:01:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:01:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:04:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:04:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:07:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:07:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:10:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:10:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:13:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:13:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:16:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:16:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:19:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:19:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:22:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:22:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:25:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:25:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:28:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:28:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:31:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:31:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:34:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:34:08,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:37:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:37:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:40:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:40:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:43:07,997 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 11:43:08,044 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:43:08,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:46:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:46:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:49:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:49:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:52:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:52:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:55:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:55:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 11:57:45,955 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:57:45,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:57:45,994 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 11:57:46,031 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 11:57:46,092 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-04 11:57:46,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:01:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:01:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:04:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:07:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:07:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:10:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:10:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:13:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:13:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:16:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:16:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:19:07,977 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:19:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:22:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:22:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:25:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:25:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:28:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:28:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:31:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:31:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:34:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:34:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:37:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:37:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:40:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:40:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:43:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:43:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:46:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:46:08,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:49:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:49:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:52:07,990 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:52:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:55:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:55:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 12:58:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 12:58:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:01:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:01:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:04:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:04:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:07:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:07:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:10:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:10:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:13:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:13:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:16:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:16:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:19:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:19:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:22:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:22:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-04 13:25:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:25:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:28:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:28:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:31:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:31:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:34:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:34:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:37:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:37:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:40:07,983 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:40:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:43:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:43:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:46:07,988 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 13:46:08,036 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:46:08,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:49:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:49:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:52:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:52:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:55:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:55:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 13:58:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 13:58:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:01:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:01:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:04:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:06:21,939 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 14:06:21,939 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 14:06:22,009 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:22,012 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:22,085 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-04 14:06:22,139 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 14:06:30,266 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:30,326 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:06:44,172 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-04 14:06:44,217 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:44,330 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-04 14:06:44,747 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-04 14:06:44,748 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-04 14:06:44,810 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:44,810 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:44,890 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-04 14:06:44,981 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 14:06:45,739 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-04 14:06:45,787 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:06:46,259 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-04 14:10:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:10:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:13:08,007 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:13:08,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:16:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:16:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:18:57,363 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:18:57,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:22:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:22:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:25:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:25:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:28:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:28:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:31:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:31:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:34:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:34:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:37:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:37:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:40:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:40:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:43:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:43:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:46:07,980 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:46:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:47:28,606 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:47:28,610 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:47:28,692 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-04 14:47:28,746 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 14:48:30,272 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:48:30,332 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:50:31,175 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:50:31,683 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-04 14:51:30,268 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:51:30,334 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:54:30,276 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:54:30,334 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 14:57:30,271 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 14:57:30,334 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:01:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:01:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:04:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:04:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:07:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:07:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:10:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:10:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:13:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:13:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:16:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:16:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:18:41,950 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:41,955 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:42,006 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:42,040 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-04 15:18:42,075 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 15:18:42,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:18:45,695 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:45,788 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-04 15:18:45,877 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-04 15:18:45,929 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:46,067 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 15:18:50,503 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:50,574 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-04 15:18:50,632 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:50,633 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-04 15:18:50,659 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-04 15:18:50,707 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:50,709 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:50,763 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-04 15:18:50,821 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-04 15:18:50,829 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-04 15:18:53,938 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:54,014 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-04 15:18:54,079 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:18:54,193 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 15:18:57,591 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-04 15:18:57,641 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:19:11,540 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 656319
2024-07-04 15:19:17,448 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:19:19,000 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 15:21:30,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:21:31,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:22:15,227 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:22:16,604 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 15:23:47,740 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 15:23:54,066 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:23:56,110 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 15:24:30,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:24:31,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:26:33,481 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 15:26:37,015 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:26:38,630 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 15:27:30,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:27:31,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:31:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:31:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:34:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:34:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:37:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:37:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:40:07,955 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:40:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:43:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:43:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:45:58,316 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:45:58,377 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:49:07,992 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 15:49:08,043 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:49:08,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:51:06,497 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:51:06,510 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:51:06,564 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 15:51:06,619 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-04 15:51:30,270 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:51:30,332 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:54:30,275 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:54:30,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 15:58:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 15:58:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:00:48,266 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:00:48,339 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:03:30,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:03:31,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:07:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:07:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:09:43,840 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:09:43,929 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:12:30,274 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:12:30,362 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:12:34,322 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 0
2024-07-04 16:12:34,368 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:12:42,116 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 62160
2024-07-04 16:16:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:16:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:19:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:19:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:22:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:22:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:25:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:25:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:28:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:28:08,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:31:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:31:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:34:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-04 16:34:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:36:50,873 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:36:57,478 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-04 16:36:58,312 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-04 16:36:58,373 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 16:36:58,423 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:36:58,490 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 16:36:58,554 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-04 16:36:58,630 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-04 16:36:58,631 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-04 16:36:58,631 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-04 16:36:58,700 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:36:58,728 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:36:58,729 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:36:58,756 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:36:58,799 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-04 16:36:59,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3839
2024-07-04 16:36:59,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-04 16:36:59,230 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-04 16:37:02,608 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:37:02,609 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:37:02,717 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-04 16:37:02,721 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 16:37:09,224 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:37:09,437 error_utils.handle_error  13 ERROR   => select_erp_hy_sdw503_balance_of_sales_allowance - Unexpected error: ORA-06550: line 59, column 105:
PL/SQL: ORA-00918: column ambiguously defined
ORA-06550: line 36, column 9:
PL/SQL: SQL Statement ignored
ORA-06550: line 163, column 50:
PL/SQL: ORA-00904: "B"."DEPT_DESC_C007": invalid identifier
ORA-06550: line 140, column 9:
PL/SQL: SQL Statement ignored
2024-07-04 16:37:09,542 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 54, in erp_hy_sdw503_main_sales_allowance_balance
    return self._handle_action('erp_hy_sdw503_main_sales_allowance_balance', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 49, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 41, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'DatabaseError' is not JSON serializable
2024-07-04 16:37:09,548 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 500 138197
2024-07-04 16:40:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:40:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:40:59,935 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:41:02,268 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:41:02,708 error_utils.handle_error  13 ERROR   => select_erp_hy_sdw503_balance_of_sales_allowance - Unexpected error: ORA-06550: line 59, column 105:
PL/SQL: ORA-00918: column ambiguously defined
ORA-06550: line 36, column 9:
PL/SQL: SQL Statement ignored
ORA-06550: line 163, column 50:
PL/SQL: ORA-00904: "B"."DEPT_DESC_C007": invalid identifier
ORA-06550: line 140, column 9:
PL/SQL: SQL Statement ignored
2024-07-04 16:41:02,747 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 54, in erp_hy_sdw503_main_sales_allowance_balance
    return self._handle_action('erp_hy_sdw503_main_sales_allowance_balance', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 49, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 41, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'DatabaseError' is not JSON serializable
2024-07-04 16:41:02,753 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 500 138197
2024-07-04 16:43:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:43:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:46:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:46:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:48:27,049 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:48:30,298 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:48:30,388 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:48:30,620 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:48:30,784 error_utils.handle_error  13 ERROR   => select_erp_hy_sdw503_balance_of_sales_allowance - Unexpected error: ORA-06550: line 59, column 105:
PL/SQL: ORA-00918: column ambiguously defined
ORA-06550: line 36, column 9:
PL/SQL: SQL Statement ignored
2024-07-04 16:48:30,823 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 54, in erp_hy_sdw503_main_sales_allowance_balance
    return self._handle_action('erp_hy_sdw503_main_sales_allowance_balance', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 49, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 41, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'DatabaseError' is not JSON serializable
2024-07-04 16:48:30,829 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 500 138197
2024-07-04 16:52:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:52:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:52:43,789 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:52:46,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:52:48,892 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 16:54:50,460 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:54:54,737 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:54:55,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:54:55,550 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:54:57,007 error_utils.handle_error  13 ERROR   => select_erp_hy_sdw503_balance_of_sales_allowance - Unexpected error: ORA-00923: FROM keyword not found where expected
2024-07-04 16:54:57,048 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 54, in erp_hy_sdw503_main_sales_allowance_balance
    return self._handle_action('erp_hy_sdw503_main_sales_allowance_balance', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 49, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 41, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'DatabaseError' is not JSON serializable
2024-07-04 16:54:57,053 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 500 138197
2024-07-04 16:55:23,532 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:55:26,098 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:55:27,748 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-04 16:56:56,196 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 16:57:30,990 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:57:31,298 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:57:33,167 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:57:34,842 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-04 16:59:30,148 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,240 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 16:59:30,299 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,366 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 16:59:30,442 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,519 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,520 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,521 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,572 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-04 16:59:30,632 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:30,644 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-04 16:59:30,833 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 16:59:30,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-04 16:59:31,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-04 16:59:37,157 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 16:59:38,609 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-04 17:01:23,843 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-04 17:01:34,512 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:01:36,218 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-04 17:02:25,672 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:02:25,708 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:02:25,793 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:02:30,307 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:02:30,368 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:02:32,341 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 62160
2024-07-04 17:02:45,478 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:02:45,597 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:02:56,003 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:02:56,204 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:03:09,984 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:03:10,176 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:03:13,564 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:03:13,686 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:03:16,079 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:03:17,645 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-04 17:03:40,254 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:03:40,370 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-04 17:03:41,314 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:03:42,853 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-04 17:05:30,321 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:05:30,390 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:08:30,303 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:08:30,384 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:11:30,309 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:11:30,382 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:14:30,304 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:14:30,376 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:17:30,294 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:17:30,360 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:20:30,293 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:20:30,391 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:23:30,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:23:31,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:26:30,303 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:26:30,371 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:29:30,950 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:29:30,954 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:29:30,987 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:29:31,037 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-04 17:29:31,060 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-04 17:29:31,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:33:08,013 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:33:08,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:36:07,978 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:36:08,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:39:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:39:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:42:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:42:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:45:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:45:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:48:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:48:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:51:07,988 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 17:51:08,036 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:51:08,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:54:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:54:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 17:57:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 17:57:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:00:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:00:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:03:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:03:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:06:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:06:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:09:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:09:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:12:07,989 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:12:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:15:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:15:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:18:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:18:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:21:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:21:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:24:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:24:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:27:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:27:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:30:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:30:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:33:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:33:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:36:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:36:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:39:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:39:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:42:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:42:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:45:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:45:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:48:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:48:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:51:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:51:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:54:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:54:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 18:57:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 18:57:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:00:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:00:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:03:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:03:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:06:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:06:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:09:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:09:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:12:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:12:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:15:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:15:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:18:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:18:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:21:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:21:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:24:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:24:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:27:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:27:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:30:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:30:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:33:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:33:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:36:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:36:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:39:07,955 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:39:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:42:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:42:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:45:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:45:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:48:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:48:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:51:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:51:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:54:07,988 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 19:54:08,034 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:54:08,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 19:57:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 19:57:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:00:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:00:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:03:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:03:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:06:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:06:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:09:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:09:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:12:07,989 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:12:08,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:15:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:15:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:18:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:18:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:21:07,981 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:21:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:24:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:24:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:27:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:27:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-04 20:30:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:30:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:33:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:33:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:36:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:36:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:39:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:39:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:42:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:42:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:45:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:45:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:48:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:48:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:51:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:51:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:54:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:54:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 20:57:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 20:57:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:00:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:00:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:03:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:03:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:06:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:06:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:09:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:09:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:12:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:12:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:15:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:15:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:18:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:18:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:21:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:21:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:24:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:24:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:27:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:27:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:30:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:30:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:33:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:33:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:36:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:36:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:39:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:39:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:42:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:42:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:45:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:45:08,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:48:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:48:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:51:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:51:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:54:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:54:08,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 21:57:07,994 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-04 21:57:08,043 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 21:57:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:00:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:00:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:03:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:03:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:06:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:06:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:09:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:09:08,254 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:12:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:12:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:15:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:15:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:18:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:18:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:21:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:21:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:24:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:24:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:27:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:27:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:30:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:30:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:33:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:33:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:36:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:36:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:39:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:39:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:42:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:42:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:45:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:45:08,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:48:08,006 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:48:08,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:51:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:51:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:54:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:54:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 22:57:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 22:57:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:00:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:00:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:03:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:03:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:06:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:06:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:09:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:09:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:12:07,981 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:12:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:15:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:15:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:18:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:18:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:21:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:21:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:24:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:24:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:27:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:27:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:30:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:30:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:33:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:33:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:36:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:36:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:39:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:39:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:42:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:42:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:45:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:45:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:48:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:48:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:51:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:51:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:54:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:54:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-04 23:57:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-04 23:57:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
