2024-07-31 09:07:31,412 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:07:36,830 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:07:39,637 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:07:44,642 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:07:44,643 _SendEmailBusinessNotificationInfo.business_notification_email_method 118 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:07:44,657 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:07:47,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:10:20,957 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:10:23,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:12:08,288 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:12:12,674 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:12:12,675 _SendEmailBusinessNotificationInfo.business_notification_email_method 118 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:12:12,679 _SendEmailBusinessNotificationInfo.check_and_send_email_task  36 INFO    => email_infos: [('h16613', '吳榮哲', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16659', '連晉億', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16667', '蔡卓汝', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16748', '曾育修', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h35983', '鄭天壽', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TEST001', '測試員一...', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TVAdmin', '系統管理員', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h15787', '江宗衡', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16036', '葉彥伶', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16218', '陳盈芳', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16499', '曹育瑋', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None)]
2024-07-31 09:12:12,679 _SendEmailBusinessNotificationInfo.check_and_send_email_task  70 ERROR   => check_and_send_email 任務中出現異常:'function' object has no attribute 'delay'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_SendEmailBusinessNotificationInfo.py", line 39, in check_and_send_email_task
    send_report_email.delay(
AttributeError: 'function' object has no attribute 'delay'
2024-07-31 09:12:12,689 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:12:15,583 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:13:04,175 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:13:08,303 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:13:11,052 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:13:23,214 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:13:23,214 _SendEmailBusinessNotificationInfo.business_notification_email_method 119 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:13:23,221 _SendEmailBusinessNotificationInfo.check_and_send_email_task  36 INFO    => email_infos: [('h16613', '吳榮哲', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16659', '連晉億', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16667', '蔡卓汝', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16748', '曾育修', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h35983', '鄭天壽', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TEST001', '測試員一...', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TVAdmin', '系統管理員', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h15787', '江宗衡', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16036', '葉彥伶', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16218', '陳盈芳', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16499', '曹育瑋', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None)]
2024-07-31 09:13:23,222 _SendEmailBusinessNotificationInfo.check_and_send_email_task  71 ERROR   => check_and_send_email 任務中出現異常:send_report_email() missing 1 required positional argument: 'self'
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_SendEmailBusinessNotificationInfo.py", line 47, in check_and_send_email_task
    smtp_port=SMTP_PORT
TypeError: send_report_email() missing 1 required positional argument: 'self'
2024-07-31 09:13:23,225 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:13:25,985 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:13:50,377 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:13:55,395 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:13:58,177 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:14:00,070 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:14:00,070 _SendEmailBusinessNotificationInfo.business_notification_email_method 119 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:14:00,074 _SendEmailBusinessNotificationInfo.check_and_send_email_task  36 INFO    => email_infos: [('h16613', '吳榮哲', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16659', '連晉億', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16667', '蔡卓汝', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16748', '曾育修', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h35983', '鄭天壽', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TEST001', '測試員一...', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('TVAdmin', '系統管理員', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h15787', '江宗衡', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16036', '葉彥伶', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16218', '陳盈芳', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None), ('h16499', '曹育瑋', 'AD070', '<EMAIL>', '11302001', '測試(業務通報)', None)]
2024-07-31 09:14:00,074 email_utils.send_report_email  59 INFO    => 為 pudcno: 11302001 獲取到 11 條郵件信息
2024-07-31 09:14:00,308 _SendEmailBusinessNotificationInfo.check_and_send_email_task  50 INFO    => check_and_send_email_task 任務成功執行，pudcno: 11302001
2024-07-31 09:14:00,310 _SendEmailBusinessNotificationInfo.check_and_send_email_task  60 INFO    => 查詢結果 result: ('2',)
2024-07-31 09:14:00,310 _SendEmailBusinessNotificationInfo.check_and_send_email_task  63 INFO    => 查詢結果 READONLY: 2
2024-07-31 09:14:00,310 _SendEmailBusinessNotificationInfo.check_and_send_email_task  67 INFO    => READONLY 不是 1，而是 2
2024-07-31 09:14:00,313 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:14:03,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:15:02,360 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:15:13,532 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:15:16,317 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:15:18,178 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:15:18,178 _SendEmailBusinessNotificationInfo.business_notification_email_method 118 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:15:18,255 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:15:21,041 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:15:54,668 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-31 09:15:54,814 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-31 09:15:54,815 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-31 09:15:54,815 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-31 09:15:54,997 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 09:15:55,097 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 09:15:55,155 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 09:15:55,570 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-31 09:16:07,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239720
2024-07-31 09:16:09,524 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2024-07-31 09:16:09,683 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-31 09:16:09,688 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 09:16:09,698 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 09:16:09,806 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 09:16:09,827 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-31 09:16:27,759 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_letter/ HTTP/1.1" 200 0
2024-07-31 09:16:27,885 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2024-07-31 09:16:38,930 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238196
2024-07-31 09:16:43,739 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-31 09:16:43,857 _SendEmailLetterInfo.update_pudcht  26 INFO    => 成功更新 PUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:16:43,857 _SendEmailLetterInfo.letter_email_method 116 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:16:43,860 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-31 09:16:54,525 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238235
2024-07-31 09:17:01,057 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 09:17:01,157 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 09:17:01,171 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 09:17:03,656 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:17:39,768 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:17:42,562 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:17:52,638 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:17:52,638 _SendEmailBusinessNotificationInfo.business_notification_email_method 118 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:17:52,642 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:17:55,452 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 09:40:30,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 09:40:30,307 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 09:45:21,258 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 09:46:07,355 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 09:46:08,454 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-31 09:46:10,290 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148141
2024-07-31 09:46:12,141 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 09:46:12,142 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 09:46:12,170 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 09:46:14,946 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148180
2024-07-31 10:09:00,949 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 10:10:40,263 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 10:10:40,263 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 10:10:51,476 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-31 10:10:51,573 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-31 10:10:51,704 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-31 10:10:56,187 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_permissions/ HTTP/1.1" 200 0
2024-07-31 10:10:56,326 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-31 10:10:59,951 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-31 10:11:00,012 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-31 10:11:00,038 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-31 10:11:00,188 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-31 10:11:00,249 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-31 10:11:02,424 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-31 10:11:02,493 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-31 10:11:02,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-07-31 10:11:03,281 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-31 10:11:03,380 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 10:11:05,992 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-31 10:11:06,270 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 649
2024-07-31 10:11:10,074 basehttp.log_message 161 INFO    => "OPTIONS /api/users/delete_user_data/ HTTP/1.1" 200 0
2024-07-31 10:11:10,802 basehttp.log_message 161 INFO    => "POST /api/users/delete_user_data/ HTTP/1.1" 200 69
2024-07-31 10:11:11,974 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-31 10:11:12,142 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 70
2024-07-31 10:45:59,424 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 10:46:25,627 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-31 10:46:25,628 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-31 10:46:25,629 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-31 10:46:25,629 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-31 10:46:33,490 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-31 10:46:33,624 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-31 10:46:33,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 10:46:33,846 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 10:46:33,923 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-31 10:46:33,924 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-31 10:46:33,924 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-31 10:46:34,120 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 10:46:34,284 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5444
2024-07-31 10:46:34,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 10:46:34,898 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 10:46:36,331 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 10:46:36,501 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 10:46:36,556 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 10:46:36,564 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 10:46:37,351 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-31 10:46:37,416 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-31 10:46:37,593 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 10:46:38,004 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-31 10:46:38,149 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 10:46:38,254 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 10:46:38,307 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 10:46:39,329 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 10:46:39,335 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 10:46:39,435 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 10:46:42,381 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 10:47:07,597 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_business_notification/ HTTP/1.1" 200 0
2024-07-31 10:47:07,875 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 10:47:10,662 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 10:47:27,576 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_email/ HTTP/1.1" 200 0
2024-07-31 10:47:27,702 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 10:47:27,703 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 10:47:27,725 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 10:47:30,576 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 10:48:03,467 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 10:48:06,415 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 10:48:08,707 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 10:48:08,708 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 10:48:08,711 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 10:48:11,613 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 10:49:05,806 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 10:49:18,414 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 10:49:21,209 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 10:50:01,229 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 10:50:01,229 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 10:50:01,259 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 10:50:04,052 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 10:55:54,000 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 10:56:17,027 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 10:56:19,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 10:56:22,099 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 10:56:22,099 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 10:56:22,112 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 10:56:24,888 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 10:57:10,023 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 10:57:12,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 10:57:14,678 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 10:57:14,678 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 10:57:14,681 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 10:57:17,487 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:00:18,006 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:00:36,833 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:00:39,630 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:00:42,840 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:00:42,841 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:00:42,855 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:00:46,644 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:04:10,380 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:04:25,602 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:04:28,357 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:04:30,073 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:04:30,074 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:04:30,087 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:04:32,877 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:08:42,547 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:09:26,868 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:09:47,845 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:09:50,635 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:09:52,293 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:09:52,293 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:09:52,309 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:09:55,047 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:12:35,311 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:12:48,756 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:12:51,487 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:12:53,210 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:12:53,210 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:12:53,229 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:12:56,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:14:22,099 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:14:41,333 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:14:44,115 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:14:46,878 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:14:46,878 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:14:46,892 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:14:49,669 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:18:39,222 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:18:54,486 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 11:18:54,486 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 11:18:54,967 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 11:18:54,969 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 11:18:56,494 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:18:59,452 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:19:02,099 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:19:02,100 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:19:02,112 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:19:05,148 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:21:57,105 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:22:13,671 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:22:16,628 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:22:19,125 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:22:19,125 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:22:19,138 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:22:22,783 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:27:20,296 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:28:05,191 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:28:23,361 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:28:26,170 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:28:28,915 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:28:28,915 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:28:28,928 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:28:31,768 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:29:40,800 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 11:30:07,960 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 11:30:10,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 11:30:16,691 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 11:30:16,691 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 11:30:16,704 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 11:30:19,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 11:37:18,916 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 12:51:01,505 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 12:51:01,507 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 12:52:06,917 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_business_notification/ HTTP/1.1" 200 0
2024-07-31 12:52:07,193 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 12:52:08,249 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-31 12:52:10,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 12:52:12,951 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_email/ HTTP/1.1" 200 0
2024-07-31 12:52:13,087 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 12:52:13,087 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 12:52:13,108 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 12:52:15,887 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 14:34:00,169 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 14:34:00,170 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 14:34:00,352 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 14:34:00,416 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 14:44:00,541 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148318
2024-07-31 14:55:38,768 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 14:56:39,415 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-31 14:56:40,613 log.log_response 230 WARNING => Unauthorized: /api/documents/select_business_notification/
2024-07-31 14:56:40,614 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification/ HTTP/1.1" 401 79
2024-07-31 14:57:13,016 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-31 14:57:13,202 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-31 14:57:13,348 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 14:57:13,417 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 14:57:13,493 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-31 14:57:13,493 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-31 14:57:13,494 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-31 14:57:13,662 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 14:57:13,831 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5444
2024-07-31 14:57:14,172 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 14:57:14,234 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 14:58:10,423 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 14:58:10,505 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-31 14:58:10,505 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-31 14:58:10,505 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-31 14:58:10,673 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 14:58:10,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 14:58:10,774 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 14:58:13,995 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148324
2024-07-31 14:58:18,767 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_business_notification/ HTTP/1.1" 200 0
2024-07-31 14:58:19,011 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 14:58:21,829 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 14:59:01,190 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 14:59:01,208 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 14:59:01,316 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 14:59:22,231 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 14:59:22,302 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 14:59:22,307 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 14:59:48,087 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 14:59:48,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 14:59:48,210 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 14:59:58,484 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148305
2024-07-31 15:00:03,324 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_email/ HTTP/1.1" 200 0
2024-07-31 15:00:03,453 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 15:00:03,453 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 15:00:03,476 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 15:00:06,233 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148344
2024-07-31 15:02:26,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148384
2024-07-31 15:04:35,317 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 15:04:38,100 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148345
2024-07-31 15:13:03,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148345
2024-07-31 15:13:05,675 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 15:13:05,675 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 15:13:05,679 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 15:13:08,454 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148384
2024-07-31 15:13:11,678 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 15:13:14,467 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148345
2024-07-31 15:13:24,151 _SendEmailBusinessNotificationInfo.update_bpudcht  26 INFO    => 成功更新 BPUDCHT 表，pudcno: 11302001, readonly: 2
2024-07-31 15:13:24,152 _SendEmailBusinessNotificationInfo.business_notification_email_method 117 INFO    => 排程郵件任務，pudcno: 11302001
2024-07-31 15:13:24,155 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-31 15:13:26,951 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148384
2024-07-31 15:13:49,412 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_business_notification/ HTTP/1.1" 200 69
2024-07-31 15:13:52,298 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148345
2024-07-31 15:18:36,181 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 15:18:36,183 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 15:18:36,291 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 15:28:29,603 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 15:28:29,659 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 15:28:30,898 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5444
2024-07-31 16:18:54,091 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 16:19:00,509 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 16:19:02,707 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-31 16:19:02,832 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-31 16:19:03,047 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-31 16:19:11,717 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-31 16:19:11,864 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 16:19:12,074 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 16:19:12,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6486
2024-07-31 16:19:12,560 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6691
2024-07-31 16:19:12,564 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 16:19:13,303 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-31 16:19:13,489 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 16:19:13,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 16:19:22,038 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41817
2024-07-31 16:19:30,657 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-31 16:19:30,660 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-31 16:19:30,768 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-31 16:19:30,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-31 16:19:41,258 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-31 16:19:41,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-31 16:19:42,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-31 16:19:42,945 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-31 16:20:06,620 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-31 16:20:06,675 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-31 16:20:08,065 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-31 16:20:12,235 _DownloadBusinessNotificationInfo.get_cell_text_and_color 351 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-31 16:20:15,389 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7056
2024-07-31 16:50:21,023 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 16:50:21,023 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 16:50:21,204 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-31 16:50:21,260 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 17:03:24,027 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 17:03:24,027 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-31 17:03:24,028 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-31 17:03:24,028 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-31 17:03:24,270 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:03:24,415 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:03:24,639 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:03:24,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:03:56,983 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:03:57,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:03:57,406 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:03:57,416 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:04:10,996 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:04:11,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:04:11,372 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:04:11,489 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:04:24,991 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:04:25,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:04:25,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:04:25,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:04:59,149 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:04:59,192 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:04:59,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:04:59,468 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:05:21,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:05:21,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:05:21,438 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:05:21,443 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:07:42,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:07:42,229 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:07:42,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:07:42,466 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:08:22,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:09:38,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:09:38,316 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 17:09:38,433 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-31 17:09:38,519 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-31 17:09:38,524 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-31 17:09:38,712 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:09:38,927 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6250
2024-07-31 17:09:38,937 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2239
2024-07-31 17:10:42,976 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:10:58,352 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-31 17:10:58,412 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-31 17:10:58,438 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-31 17:10:58,549 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:10:58,660 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:10:59,345 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-31 17:10:59,556 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:10:59,561 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:01,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:01,525 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:11:02,185 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-07-31 17:11:03,346 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:03,349 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:11:07,735 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:07,844 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:11:08,355 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-31 17:11:08,542 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:11:10,299 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-31 17:11:10,479 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:11:13,501 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:11:15,450 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:11:15,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:15,975 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:11:15,976 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:11:22,000 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-31 17:11:22,405 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-31 17:11:22,562 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-31 17:11:29,271 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-31 17:11:29,422 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-31 17:11:29,562 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 17:11:29,630 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-31 17:11:29,700 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 17:11:30,020 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6486
2024-07-31 17:11:30,113 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 17:11:30,271 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 17:11:32,190 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-31 17:11:32,249 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-31 17:11:32,639 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-31 17:11:34,250 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_truck/ HTTP/1.1" 200 0
2024-07-31 17:11:34,429 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-31 17:11:44,883 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-31 17:12:04,844 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-31 17:12:05,039 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:12:07,559 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-31 17:12:08,158 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-31 17:12:08,260 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-31 17:12:08,429 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-07-31 17:12:14,338 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-31 17:12:14,426 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-31 17:12:14,572 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29625
2024-07-31 17:12:17,524 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:12:18,580 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-31 17:12:22,378 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:13:42,988 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:14:22,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:15:08,115 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-31 17:15:15,062 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-31 17:15:15,274 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-31 17:15:22,902 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 17:15:23,015 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-31 17:15:23,099 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:15:23,183 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:15:23,233 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 17:16:43,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:17:22,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:18:53,028 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-07-31 17:18:53,169 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:18:53,971 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:19:43,003 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:20:22,191 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:20:33,322 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:20:34,363 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:20:42,029 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:20:42,460 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:20:48,987 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:20:49,553 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:21:38,582 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:21:38,998 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:22:43,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:23:44,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:25:42,988 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:26:44,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:28:43,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:29:43,830 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 17:29:43,864 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 17:29:43,922 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:29:44,067 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 17:29:44,074 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 17:29:44,138 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:31:42,380 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:31:42,539 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 17:32:21,320 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:34:42,391 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:35:21,302 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:37:42,388 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:38:21,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:40:42,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:41:21,327 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:43:42,360 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:44:21,336 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:46:42,369 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:47:21,336 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:49:42,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:50:21,329 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:52:42,412 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:53:21,325 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:55:42,385 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:56:21,318 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:58:42,366 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 17:59:21,310 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:01:42,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:02:21,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:04:42,396 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:05:21,314 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:07:42,388 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:08:21,313 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:10:42,714 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:11:21,329 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:13:42,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:14:21,317 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:16:42,362 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:17:21,312 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:19:42,382 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:20:21,322 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:22:42,388 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:23:21,331 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:25:42,409 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:26:21,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:28:42,359 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:29:21,317 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:31:42,389 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:32:21,331 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:34:42,372 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:35:21,337 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:37:42,367 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:38:21,313 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:40:42,372 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:41:21,321 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:42:31,581 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 18:42:31,640 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 18:43:42,390 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:44:21,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:46:18,042 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:46:29,258 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:46:29,413 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 18:46:29,632 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 18:46:29,760 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 18:46:29,970 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 18:46:29,982 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 18:46:30,061 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 18:46:30,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 18:46:30,250 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 18:46:35,518 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:49:29,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:52:29,426 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:54:40,306 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:54:40,485 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:54:40,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 18:54:40,700 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:54:40,997 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 18:54:41,048 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 18:55:07,258 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:55:07,401 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-31 18:55:07,518 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-31 18:55:07,677 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 18:55:07,770 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-31 18:55:07,949 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 18:55:08,010 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 18:55:08,030 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 18:55:08,186 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 18:55:08,198 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 18:55:10,228 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:55:43,444 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:55:43,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 18:55:43,670 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-31 18:55:43,767 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 18:55:43,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-31 18:55:44,253 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-31 18:55:44,301 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-31 18:55:44,909 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:58:07,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:58:12,385 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 18:58:43,578 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 18:59:14,988 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-31 18:59:15,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-31 18:59:16,028 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 42802
2024-07-31 19:01:07,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:01:43,631 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:04:07,419 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-31 19:04:07,562 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:04:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:07:07,476 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:07:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:10:07,477 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:10:43,615 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:13:07,466 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:13:43,595 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:16:07,470 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:16:43,600 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:19:07,464 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:19:43,592 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:22:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:22:43,618 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:25:07,458 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:25:43,616 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:28:07,466 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:28:43,615 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:31:07,488 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:31:43,580 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:34:07,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:34:43,632 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:37:07,466 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:37:43,602 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:40:07,467 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:40:43,587 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:43:07,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:43:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:46:07,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:46:43,607 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:49:07,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:49:43,611 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:52:07,445 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:52:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:55:07,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:55:43,663 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:58:07,457 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 19:58:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:01:07,462 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:01:43,578 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:04:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:04:43,610 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:07:07,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:07:43,589 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:10:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:10:43,598 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:13:07,485 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:13:43,597 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:16:07,458 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:16:43,592 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:19:07,447 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:19:43,588 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:22:07,463 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:22:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:25:07,484 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:25:43,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:28:07,489 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:28:43,635 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:31:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:31:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:34:07,467 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:34:43,607 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:37:07,465 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:37:43,603 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:40:07,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:40:43,607 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:43:07,459 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:43:43,584 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:46:07,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:46:43,589 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:49:07,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:49:43,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:52:07,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:52:43,605 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:55:07,509 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:55:43,666 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:58:07,475 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 20:58:43,599 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:01:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:01:43,581 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:04:07,448 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:04:43,547 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-31 21:04:43,661 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:07:07,473 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:07:43,608 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:10:07,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:10:43,613 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:13:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:13:43,619 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:16:07,464 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:16:43,573 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:19:07,477 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:19:43,613 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:22:07,471 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:22:43,605 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:25:07,443 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:25:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:28:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:28:43,608 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:31:07,458 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:31:43,639 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:34:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:34:43,588 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:37:07,448 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:37:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:40:07,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:40:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:43:07,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:43:43,578 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:46:07,482 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:46:43,582 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:49:07,447 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:49:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:52:07,477 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:52:43,611 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:55:07,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:55:43,676 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:58:07,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 21:58:43,596 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:01:07,465 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:01:43,602 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:04:07,457 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:04:43,593 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:07:07,450 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:07:43,582 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:10:07,450 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:10:43,592 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:13:07,448 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:13:43,608 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:16:07,461 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:16:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:19:07,454 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:19:43,608 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:22:07,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:22:43,593 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:25:07,472 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:25:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:28:07,860 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:28:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:31:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:31:43,600 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:34:07,470 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:34:43,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:37:07,477 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:37:43,588 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:40:07,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:40:43,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:43:07,478 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:43:43,618 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:46:07,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:46:43,606 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:49:07,462 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:49:43,585 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:52:07,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:52:43,614 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:55:07,450 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:55:43,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:58:07,517 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 22:58:43,585 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:01:07,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:01:43,647 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:04:07,473 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:04:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:07:07,410 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-31 23:07:07,524 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:07:43,578 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:10:07,486 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:10:43,592 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:13:07,463 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:13:43,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:16:07,489 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:16:43,601 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:19:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:19:43,632 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:22:07,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:22:43,582 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:25:07,463 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:25:43,606 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:28:07,511 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:28:43,648 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:31:07,469 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:31:43,610 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:34:07,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:34:43,579 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:37:07,490 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:37:43,604 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:40:07,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:40:43,579 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:43:07,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:43:43,602 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:46:07,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:46:43,604 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:49:07,490 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:49:43,620 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:52:07,450 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:52:43,583 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:55:07,479 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:55:43,587 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:58:07,486 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-31 23:58:43,607 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
