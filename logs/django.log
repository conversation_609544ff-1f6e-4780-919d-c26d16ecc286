2025-07-23 07:39:33,198 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-23 07:39:33,278 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-23 07:39:33,278 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-23 07:39:33,289 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-23 07:39:33,289 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-23 07:39:33,289 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-23 07:39:33,289 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-23 07:39:33,289 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-23 07:39:33,289 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-23 07:39:33,290 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 114年大全聯5081檔 DM.IP 促銷通報(1140801-1140814)250722h16510B.docx  -  相容模式 - Word
2025-07-23 07:39:33,961 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-23 07:39:34,036 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-23 07:39:34,228 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-23 07:39:39,968 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-23 07:39:40,963 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-23 07:39:42,062 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-23 07:39:43,486 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-23 07:39:45,221 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-23 07:39:52,791 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-23 07:39:52,870 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-23 07:39:52,871 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-23 07:39:53,067 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-23 07:39:53,134 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-23 07:39:53,147 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-23 07:39:53,206 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-23 07:39:53,282 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-23 07:39:53,343 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-23 07:39:53,898 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-23 07:39:54,010 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-23 07:39:54,320 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-23 07:39:59,785 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-23 07:40:02,389 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-23 07:40:03,661 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-23 07:40:05,203 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:40:05,315 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-23 07:40:06,296 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-23 07:40:06,900 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-23 07:40:06,950 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-23 07:40:14,730 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-23 07:40:14,755 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-23 07:40:19,395 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-23 07:40:21,525 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-23 07:40:25,073 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:40:25,229 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-23 07:40:33,288 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:40:35,939 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:40:35,982 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-23 07:40:52,902 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:40:55,566 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:40:55,695 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-23 07:41:06,604 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:41:06,691 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-23 07:41:26,204 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:41:26,273 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-23 07:41:33,320 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:41:37,292 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:41:37,380 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-23 07:41:53,000 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:41:56,789 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:41:56,858 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-23 07:42:08,035 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:42:08,146 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-23 07:42:27,522 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:42:27,673 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-23 07:42:33,389 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:42:38,745 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:42:38,817 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-23 07:42:53,084 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:42:58,351 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:43:09,566 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:43:30,483 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:43:33,480 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:43:39,898 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:43:53,286 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:44:00,978 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:44:00,998 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-23 07:44:10,638 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:44:31,292 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:44:33,514 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:44:40,775 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:44:53,312 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:45:02,453 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:45:11,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:45:28,728 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-23 07:45:29,310 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-07-23 07:45:32,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:45:33,588 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:45:41,581 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:45:41,626 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-23 07:45:56,116 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:46:03,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:46:11,654 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-23 07:46:11,913 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:46:22,825 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-23 07:46:33,586 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:46:33,586 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-23 07:46:33,611 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:46:42,501 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:46:42,659 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-23 07:46:56,157 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:47:01,730 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-07-23 07:47:02,943 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-07-23 07:47:03,125 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2025-07-23 07:47:03,837 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:47:03,837 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-23 07:47:13,410 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:47:13,523 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-23 07:47:14,294 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-23 07:47:15,011 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-07-23 07:47:15,166 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-23 07:47:15,646 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-23 07:47:15,773 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-07-23 07:47:15,775 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-07-23 07:47:15,776 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-07-23 07:47:15,776 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-07-23 07:47:16,358 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-07-23 07:47:16,390 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1523
2025-07-23 07:47:16,857 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-07-23 07:47:17,127 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-07-23 07:47:17,693 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-07-23 07:47:17,742 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2025-07-23 07:47:17,743 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-07-23 07:47:17,743 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-07-23 07:47:17,958 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9687
2025-07-23 07:47:17,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-23 07:47:18,092 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-23 07:47:21,376 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-07-23 07:47:21,682 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-23 07:47:21,896 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-23 07:47:26,698 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-23 07:47:27,092 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-23 07:47:27,808 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1523
2025-07-23 07:47:27,899 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-23 07:47:28,326 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6652
2025-07-23 07:47:28,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 15509
2025-07-23 07:47:30,272 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-23 07:47:30,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-23 07:47:30,926 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-23 07:47:33,651 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:47:34,111 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:47:34,127 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-23 07:47:34,256 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50642
2025-07-23 07:47:35,552 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-07-23 07:47:35,553 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-07-23 07:47:36,103 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-23 07:47:36,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-23 07:47:37,511 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-07-23 07:47:38,077 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-23 07:47:38,620 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-23 07:47:38,703 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400595\55f13610-448c-4ecc-893f-50caeda8b0f4.docx
2025-07-23 07:47:38,865 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-23 07:47:39,307 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-23 07:47:39,320 word_queue_processor.submit_task 161 INFO    => 任務已提交: 3b589b83-dd90-4b11-9bea-ef1d008568f7
2025-07-23 07:47:39,334 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 3b589b83-dd90-4b11-9bea-ef1d008568f7
2025-07-23 07:47:39,334 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400595\55f13610-448c-4ecc-893f-50caeda8b0f4.docx
2025-07-23 07:47:41,230 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-23 07:47:44,245 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:47:44,396 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-23 07:47:54,950 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 56 筆數據
2025-07-23 07:47:55,048 word_queue_processor._process_tasks 120 INFO    => 任務完成: 3b589b83-dd90-4b11-9bea-ef1d008568f7
2025-07-23 07:47:55,048 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-23 07:47:55,049 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 56 筆數據
2025-07-23 07:47:55,049 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 56
2025-07-23 07:47:55,050 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-23 07:47:55,050 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 56 筆數據到 Excel
2025-07-23 07:47:55,050 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['1513', '234', '元/箱', None, None, '222.86', '元/箱']
2025-07-23 07:47:55,060 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400595\114年大全聯5081檔 DM.IP 促銷通報(1140801-1140814)250722h16510B.xlsx
2025-07-23 07:47:55,102 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-23 07:47:55,102 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400595\114年大全聯5081檔 DM.IP 促銷通報(1140801-1140814)250722h16510B.xlsx
2025-07-23 07:47:55,108 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7157
2025-07-23 07:47:56,233 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:48:04,604 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:48:04,720 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-23 07:48:14,791 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:48:33,744 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:48:35,518 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:48:45,059 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:48:56,285 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:49:13,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:49:15,982 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:49:33,767 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-23 07:49:44,914 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:49:46,166 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-23 07:49:56,299 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
