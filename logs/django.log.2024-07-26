2024-07-26 07:37:34,106 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 07:37:34,107 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 11:16:43,416 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 11:16:43,418 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-26 11:16:43,654 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 11:16:43,681 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-26 11:16:43,682 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-26 11:16:54,219 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 11:17:17,717 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-26 11:17:18,735 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-26 11:17:18,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 11:17:18,975 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 11:17:19,067 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-26 11:17:19,069 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-26 11:17:19,069 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-26 11:17:19,239 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 11:17:19,393 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 11:17:19,589 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-26 11:17:19,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 841
2024-07-26 11:17:20,592 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 11:17:20,672 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-26 11:17:20,673 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-26 11:17:20,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 11:17:20,892 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 11:17:23,673 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 11:17:25,411 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-26 11:17:25,412 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-26 11:17:25,533 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 11:17:25,593 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 11:17:28,606 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-26 11:17:28,724 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 11:17:28,740 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-26 11:18:12,641 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5850
2024-07-26 11:34:16,508 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-26 11:34:16,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-26 11:35:21,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5850
2024-07-26 11:47:39,912 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 11:47:39,976 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 11:47:41,316 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 11:47:41,317 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5850
2024-07-26 11:58:43,558 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 11:58:43,560 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5850
2024-07-26 12:00:58,137 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 12:01:11,279 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 12:01:11,286 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5850
2024-07-26 12:02:57,004 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5842
2024-07-26 12:16:48,836 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 12:16:56,957 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-26 12:16:57,005 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 12:16:58,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 12:16:58,898 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 12:17:01,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 12:17:20,311 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 12:17:28,947 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 12:18:01,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 12:18:01,074 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 12:22:48,322 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 12:22:55,583 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 12:22:55,584 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 12:23:02,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 12:23:43,205 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 12:23:47,688 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44765
2024-07-26 13:13:40,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 13:13:40,329 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 15:08:02,643 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 15:08:02,644 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 15:08:02,849 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 15:08:02,900 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 16:47:25,964 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 16:47:37,785 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-26 16:47:37,785 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-26 16:47:37,786 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-26 16:47:37,786 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-26 16:47:46,120 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-26 16:47:46,241 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-26 16:47:46,405 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 16:47:46,478 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 16:47:46,552 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-26 16:47:46,553 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-26 16:47:46,553 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-26 16:47:46,705 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-26 16:47:46,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 16:47:47,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-26 16:47:47,197 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 16:47:48,209 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-26 16:47:48,324 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 16:47:48,507 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 16:47:53,850 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-26 16:47:53,991 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 16:47:54,249 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 16:47:54,299 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 16:47:54,398 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-26 16:47:54,545 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 507
2024-07-26 16:47:55,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 16:47:55,980 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-26 16:47:55,980 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-26 16:47:56,144 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 16:47:56,148 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 16:47:58,322 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44713
2024-07-26 16:48:01,291 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-26 16:48:01,292 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-26 16:48:01,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 16:48:01,456 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 16:48:02,595 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-26 16:48:02,778 _DownloadBusinessNotificationInfo.select_business_notification_price_download 481 INFO    => Excel檔案已存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 16:48:02,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5842
2024-07-26 16:48:18,130 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6202
2024-07-26 17:17:10,408 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:17:21,297 _DownloadBusinessNotificationInfo.select_business_notification_price_download 489 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 17:17:21,299 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6202
2024-07-26 17:17:33,078 _DownloadBusinessNotificationInfo.select_business_notification_price_download 501 ERROR   => Error in select_business_notification_price_download: name 'extract_table_data' is not defined
2024-07-26 17:17:34,218 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:17:34,219 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 131
2024-07-26 17:17:57,754 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:17:59,329 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 17:17:59,385 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 17:17:59,769 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 17:17:59,769 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 17:18:03,851 _DownloadBusinessNotificationInfo.select_business_notification_price_download 501 ERROR   => Error in select_business_notification_price_download: '<win32com.gen_py.Microsoft Word 16.0 Object Library._Document instance at 0x2317916709664>' object has no attribute 'Rows'
2024-07-26 17:18:05,402 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:18:05,402 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-07-26 17:19:17,196 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:19:23,688 _DownloadBusinessNotificationInfo.select_business_notification_price_download 500 ERROR   => Error in select_business_notification_price_download: '<win32com.gen_py.Microsoft Word 16.0 Object Library._Document instance at 0x1319425353096>' object has no attribute 'Rows'
2024-07-26 17:19:23,931 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:19:23,931 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-07-26 17:23:38,684 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:23:51,765 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4917
2024-07-26 17:26:14,054 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:26:20,775 _DownloadBusinessNotificationInfo.select_business_notification_price_download 487 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-26 17:26:20,778 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4917
2024-07-26 17:26:32,767 _DownloadBusinessNotificationInfo.select_business_notification_price_download 499 ERROR   => Error in select_business_notification_price_download: name 'extract_table_data' is not defined
2024-07-26 17:26:33,172 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:26:33,173 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 131
2024-07-26 17:27:04,759 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:27:10,618 _DownloadBusinessNotificationInfo.select_business_notification_price_download 499 ERROR   => Error in select_business_notification_price_download: '<win32com.gen_py.Microsoft Word 16.0 Object Library._Document instance at 0x1824006081280>' object has no attribute 'Rows'
2024-07-26 17:27:10,834 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:27:10,835 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-07-26 17:29:20,058 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:29:32,642 _DownloadBusinessNotificationInfo.select_business_notification_price_download 499 ERROR   => Error in select_business_notification_price_download: '<win32com.gen_py.Microsoft Word 16.0 Object Library._Document instance at 0x2575599790624>' object has no attribute 'Rows'
2024-07-26 17:29:32,935 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:29:32,935 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-07-26 17:32:24,971 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 17:32:31,347 _DownloadBusinessNotificationInfo.select_business_notification_price_download 499 ERROR   => Error in select_business_notification_price_download: '<win32com.gen_py.Microsoft Word 16.0 Object Library._Document instance at 0x2438192745944>' object has no attribute 'Rows'
2024-07-26 17:32:31,702 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-26 17:32:31,703 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" ************-07-26 18:10:37,218 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 18:10:44,887 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 18:10:44,888 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 18:10:50,984 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,091 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,318 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,399 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,501 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,777 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:51,872 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:52,001 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:52,415 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:52,496 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:52,729 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:52,877 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,048 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,127 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,143 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,312 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,399 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,486 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,603 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,730 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,744 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,783 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,800 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,806 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,824 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,887 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:53,926 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:10:55,005 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6389
2024-07-26 18:15:58,583 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 18:16:09,107 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:09,212 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:09,739 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:09,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,057 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,339 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,455 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,561 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,867 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:10,983 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:11,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:11,584 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:11,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:11,969 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,021 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,223 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,334 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,454 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,570 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:12,969 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,008 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,067 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,075 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,090 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,100 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:13,172 _DownloadBusinessNotificationInfo.get_cell_text_and_color 329 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:16:14,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6461
2024-07-26 18:23:06,117 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 18:23:22,904 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:23,031 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:23,318 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:23,428 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:23,533 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:23,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:24,036 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:24,180 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:24,624 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:24,758 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,137 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,381 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,814 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,850 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:25,988 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,072 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,150 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,237 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,528 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,573 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,620 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,654 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,678 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,719 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,760 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:23:26,936 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6965
2024-07-26 18:27:18,333 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 18:27:31,678 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:31,911 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:32,193 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:32,336 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:32,440 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:32,760 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:32,885 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:33,044 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:33,394 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:33,542 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:33,869 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,052 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,257 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,368 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,389 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,524 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,604 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,687 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:34,781 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,021 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,054 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,102 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,112 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,116 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,121 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,180 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:35,207 _DownloadBusinessNotificationInfo.get_cell_text_and_color 328 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:27:36,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6672
2024-07-26 18:37:12,254 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 18:37:51,319 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:51,463 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:51,800 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:51,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:51,963 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:52,236 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:52,355 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:52,458 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:52,823 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:52,914 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,175 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,390 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,547 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,649 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,684 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,794 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,840 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:53,916 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,001 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,202 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,250 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,255 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,259 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,276 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,298 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:54,318 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 18:37:56,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6672
2024-07-26 21:03:25,645 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 21:03:25,645 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 21:03:25,866 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-26 21:03:25,867 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-26 21:03:25,923 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-26 21:03:25,923 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-26 21:03:41,293 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-26 21:03:41,406 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-26 21:03:41,568 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 21:03:41,641 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 21:03:41,642 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-26 21:03:41,694 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-26 21:03:41,694 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-26 21:03:41,870 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-26 21:03:42,158 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 21:03:42,235 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 21:03:42,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-26 21:03:44,450 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-26 21:03:44,562 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 21:03:44,761 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 21:03:53,078 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-26 21:03:53,225 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 21:03:53,387 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 21:03:53,561 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 21:03:53,785 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 507
2024-07-26 21:03:53,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-26 21:03:57,972 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 21:03:58,038 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-26 21:03:58,039 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-26 21:03:58,207 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 21:03:58,219 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 21:04:01,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44713
2024-07-26 21:04:20,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 21:04:20,357 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 21:04:30,166 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 21:04:30,494 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-26 21:07:58,674 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 21:07:58,882 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-26 21:08:13,430 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-26 21:08:13,585 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 21:08:13,730 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 21:08:13,903 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7185
2024-07-26 21:08:14,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 37442
2024-07-26 21:08:14,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-26 21:09:01,710 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 21:09:01,886 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 21:09:01,891 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 21:09:07,943 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 14892
2024-07-26 21:09:17,828 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-26 21:09:17,828 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-26 21:09:17,950 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 21:09:18,003 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 21:09:19,268 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-26 21:09:19,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 173112
2024-07-26 22:05:21,754 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 22:05:21,813 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 22:05:26,739 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 37509
2024-07-26 22:08:58,120 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:08:58,186 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-26 22:12:48,675 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-26 22:12:48,814 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:12:49,132 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 22:12:49,138 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 22:12:49,239 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 22:12:49,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 37057
2024-07-26 22:12:49,547 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-26 22:12:54,225 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44497
2024-07-26 22:13:03,173 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:03,242 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-26 22:13:09,086 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:13:09,138 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:12,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:12,754 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:13:42,481 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:42,515 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:13:48,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:48,741 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:13:55,908 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:55,957 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:13:58,111 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:13:58,213 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-26 22:14:06,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 37509
2024-07-26 22:14:13,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:14:13,428 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:14:20,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:14:20,461 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:14:22,322 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300582\MOMO-113年7-8月促銷通報240619h16692B.docx
2024-07-26 22:14:22,323 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-26 22:14:22,323 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-26 22:15:48,989 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:18:48,985 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:21:48,983 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:24:48,995 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:25:09,358 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 188622
2024-07-26 22:25:21,741 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 188622
2024-07-26 22:26:24,970 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 188622
2024-07-26 22:27:49,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:28:17,545 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:28:17,621 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:28:21,230 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-26 22:28:32,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 22:30:49,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:33:49,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:36:49,003 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:39:20,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:39:20,194 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:39:48,998 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:42:49,004 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:45:49,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:48:01,562 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 22:48:01,607 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 22:48:49,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:51:49,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:54:49,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:57:49,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 22:59:23,382 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 22:59:30,026 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 22:59:30,026 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 23:00:48,977 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:03:05,690 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:03:05,744 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:03:22,380 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:03:22,546 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 23:03:22,800 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 23:03:22,895 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 23:03:23,025 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 23:03:23,128 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 23:03:23,193 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-26 23:03:23,200 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42413
2024-07-26 23:03:29,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43659
2024-07-26 23:03:30,850 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:03:30,905 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:05:32,177 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:05:32,231 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:06:22,584 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:08:19,609 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 23:08:27,897 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:08:27,980 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 23:08:28,108 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 23:08:28,179 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-26 23:08:28,230 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-26 23:08:28,230 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-26 23:08:28,357 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 23:08:28,423 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-26 23:08:28,424 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-26 23:08:28,433 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 23:08:28,639 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 23:08:28,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 23:08:28,725 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41494
2024-07-26 23:08:28,783 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-26 23:08:38,655 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42453
2024-07-26 23:08:44,855 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:08:44,907 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:09:43,403 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-26 23:09:43,404 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-26 23:09:43,520 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:09:43,591 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:11:28,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:12:54,639 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:12:54,822 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 23:12:55,117 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-26 23:12:55,272 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 23:12:55,488 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-26 23:12:55,502 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-26 23:12:55,532 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-26 23:12:55,540 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41494
2024-07-26 23:13:02,643 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42453
2024-07-26 23:13:07,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:13:08,039 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:14:00,598 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:00,815 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,112 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,207 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,308 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,702 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:01,961 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:02,366 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:02,494 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:02,756 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:02,945 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,155 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,245 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,287 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,444 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,538 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,575 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,672 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,828 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,877 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,890 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,901 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,912 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,955 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:03,983 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:14:05,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6672
2024-07-26 23:15:37,805 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:37,927 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:38,277 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:38,367 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:38,461 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:38,865 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:39,090 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:39,331 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:39,827 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:39,958 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:40,383 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:40,700 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:40,921 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,026 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,067 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,206 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,386 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,509 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,859 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,878 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,897 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,904 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,908 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:41,942 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:15:43,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6672
2024-07-26 23:15:54,867 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:16:50,071 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:16:50,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:16:54,854 _DownloadBusinessNotificationInfo.extract_table_data 365 ERROR   => Rout position for 80904 is not defined.
2024-07-26 23:16:55,721 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 23:18:55,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:21:28,635 _DownloadBusinessNotificationInfo.extract_table_data 365 ERROR   => Rout position for 80904 is not defined.
2024-07-26 23:21:29,247 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 23:21:54,797 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:22:23,529 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:22:23,566 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:22:46,665 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:22:46,691 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:24:55,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:27:55,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:30:55,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:32:10,594 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 23:33:13,425 _DownloadBusinessNotificationInfo.extract_table_data 366 ERROR   => Rout position for 80904 is not defined.
2024-07-26 23:33:14,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 23:33:54,785 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:36:54,802 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:39:15,997 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:39:16,053 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:39:23,875 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:24,071 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:24,847 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:24,923 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:25,031 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:25,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:25,572 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:25,749 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:26,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:26,390 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:26,842 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,744 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,812 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,865 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:27,946 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,161 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,210 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,315 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,331 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,337 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,360 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,416 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:28,455 _DownloadBusinessNotificationInfo.get_cell_text_and_color 336 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-26 23:39:29,779 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6672
2024-07-26 23:39:54,841 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:40:12,614 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-26 23:40:12,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-26 23:40:18,930 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-26 23:40:19,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 188622
2024-07-26 23:41:17,037 _DownloadBusinessNotificationInfo.extract_table_data 366 ERROR   => Rout position for 80904 is not defined.
2024-07-26 23:41:17,912 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 23:42:54,795 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:45:54,825 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:48:07,542 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 23:48:32,767 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-26 23:48:32,767 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-26 23:48:38,790 _DownloadBusinessNotificationInfo.extract_table_data 365 ERROR   => Rout position for 80904 is not defined.
2024-07-26 23:48:41,154 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4935
2024-07-26 23:48:54,810 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:51:54,793 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:53:58,441 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 23:54:31,484 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4934
2024-07-26 23:54:54,801 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-07-26 23:57:23,421 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-26 23:57:39,858 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4935
2024-07-26 23:57:54,789 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
