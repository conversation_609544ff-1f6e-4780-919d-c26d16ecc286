2024-09-03 08:14:24,367 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-03 08:14:24,368 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-03 09:57:56,212 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-03 09:57:56,213 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-03 09:57:56,822 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-09-03 09:57:56,824 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-03 09:57:56,826 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-09-03 09:57:56,900 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-03 09:58:29,814 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-03 09:58:29,853 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-03 09:58:30,433 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-09-03 09:58:30,434 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-03 09:58:30,434 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-09-03 09:58:30,564 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-03 10:20:39,116 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-03 10:20:39,131 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-03 10:20:39,763 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-09-03 10:20:39,767 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-03 10:20:39,768 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-09-03 10:20:39,828 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
