2024-11-06 09:34:01,170 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-06 09:34:01,170 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-11-06 09:34:01,270 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-11-06 09:34:01,270 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-11-06 09:34:01,271 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-11-06 09:34:01,359 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
