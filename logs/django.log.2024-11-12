2024-11-12 09:14:10,829 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-11-12 09:14:11,002 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-11-12 09:14:11,002 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-11-12 13:02:17,347 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 13:02:17,347 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-11-12 13:02:17,451 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-11-12 13:02:17,452 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-11-12 13:02:17,452 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-11-12 13:02:17,532 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-12 13:02:22,969 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-11-12 13:02:23,095 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-11-12 13:02:23,239 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 13:02:23,310 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 13:02:23,311 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-11-12 13:02:23,398 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-11-12 13:02:23,399 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-11-12 13:02:23,546 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-11-12 13:02:23,631 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3249
2024-11-12 13:02:24,113 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-11-12 13:02:24,233 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-11-12 13:02:26,292 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 13:02:26,350 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-11-12 13:02:26,350 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-11-12 13:02:26,350 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-11-12 13:02:26,548 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-11-12 13:02:26,555 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-12 13:02:26,636 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-12 13:02:31,441 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-11-12 13:02:32,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 5806
2024-11-12 13:02:46,078 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 2142
2024-11-12 13:02:51,305 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 6708
2024-11-12 13:05:48,942 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-11-12 13:05:49,374 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 664
2024-11-12 13:32:49,640 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 13:32:49,693 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 14:04:00,032 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 14:04:00,092 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 14:34:45,541 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 14:34:45,601 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 15:09:20,776 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 15:09:20,776 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 15:09:20,955 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 15:09:21,016 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 15:42:01,362 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 15:42:01,413 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 15:57:16,820 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-11-12 15:57:17,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 6708
2024-11-12 16:12:20,108 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 16:12:20,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 16:43:32,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-11-12 16:43:32,057 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 17:15:11,763 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 17:15:11,764 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-12 17:15:11,959 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-12 17:15:11,990 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
