2024-07-23 14:41:21,507 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-23 14:41:21,667 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-23 14:41:21,668 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-23 14:41:25,096 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 14:41:25,123 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-23 14:41:31,629 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-23 14:41:31,788 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-23 14:41:31,933 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 14:41:32,006 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 14:41:32,006 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-23 14:41:32,060 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-23 14:41:32,060 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-23 14:41:32,333 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-23 14:41:32,379 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5228
2024-07-23 14:41:32,719 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-23 14:41:33,041 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-23 14:41:36,491 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-23 14:41:36,570 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-23 14:41:36,570 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-23 14:41:36,570 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-23 14:41:36,745 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-23 14:41:36,795 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 14:41:36,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 14:41:45,156 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-23 14:41:45,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 99001
2024-07-23 14:42:14,261 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-07-23 14:42:14,685 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1629
2024-07-23 14:42:26,023 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 14:42:26,202 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 14:42:31,855 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-23 14:42:31,984 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 14:42:32,280 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 14:42:32,329 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5228
2024-07-23 14:42:32,477 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12977
2024-07-23 14:42:32,531 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 14:42:33,733 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 14:42:33,850 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 14:42:33,954 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 14:42:42,007 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 24116
2024-07-23 14:42:44,321 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-23 14:42:44,322 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-23 14:42:44,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 14:42:44,482 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 14:42:46,130 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-23 14:42:46,252 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.docx
2024-07-23 14:42:46,252 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 14:42:46,252 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 14:42:48,557 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.docx
2024-07-23 14:42:48,558 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 14:42:48,558 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 14:43:32,914 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.docx
2024-07-23 14:43:32,914 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 14:43:32,914 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 14:45:11,470 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.docx
2024-07-23 14:45:11,470 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 14:45:11,471 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 14:45:24,436 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-23 14:45:24,455 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 14:45:25,610 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300631\113年大潤發4071檔 DM.IP 促銷通報(1130712-1130725)-試飲版240708h16510B.docx
2024-07-23 14:45:25,611 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 14:45:25,611 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 14:45:28,494 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 14:45:28,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 14:46:06,759 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 14:46:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 14:46:07,359 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 14:46:07,509 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5228
2024-07-23 14:46:07,693 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12565
2024-07-23 14:46:07,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 14:49:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 14:52:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 14:55:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 14:58:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:01:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:04:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:07:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:10:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:13:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:16:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:19:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:22:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:25:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:27:02,378 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:28:08,800 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:28:47,199 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:28:47,237 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 15:28:47,403 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5228
2024-07-23 15:28:47,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:28:47,609 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:28:47,646 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12565
2024-07-23 15:30:33,060 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:30:33,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:30:33,223 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5228
2024-07-23 15:30:33,414 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12565
2024-07-23 15:30:33,530 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:31:48,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:33:34,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:34:48,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:36:34,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:37:48,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:39:34,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:40:15,315 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 15:40:15,460 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 15:40:15,568 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 15:40:17,763 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 15:40:20,332 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:40:20,378 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:40:21,414 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 15:40:47,613 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:41:53,134 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:41:53,191 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:42:34,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:44:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:44:20,711 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:44:20,768 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:44:24,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:44:24,217 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 15:44:24,395 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 15:44:24,490 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:44:24,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 15:44:24,720 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 15:44:24,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:44:24,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 15:44:27,323 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 15:44:29,774 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:44:29,856 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:44:42,803 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:44:42,851 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:44:52,360 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:44:52,517 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 15:44:52,715 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:44:52,769 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 15:44:52,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:44:52,950 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 15:44:52,962 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 15:44:53,055 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 15:44:57,463 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 15:45:01,055 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:45:01,105 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:45:50,148 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:45:50,152 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:45:53,438 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:45:53,493 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:45:56,387 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:45:56,539 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 15:45:56,800 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:45:56,850 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 15:45:56,944 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:45:56,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 15:45:57,009 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 15:45:57,018 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 15:46:00,647 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 15:46:03,119 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:46:03,156 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:48:57,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:49:03,445 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:49:03,499 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:49:06,247 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 15:50:04,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:50:04,198 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:50:06,785 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-23 15:50:06,846 log.log_response 230 WARNING => Method Not Allowed: /api/documents/select_business_notification_price_download/
2024-07-23 15:50:06,846 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 405 41
2024-07-23 15:50:22,041 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:50:30,429 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 413, in select_business_notification_price_download
    rout = json_data['rout']
KeyError: 'rout'
2024-07-23 15:50:30,437 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 112785
2024-07-23 15:50:34,650 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 413, in select_business_notification_price_download
    rout = json_data['rout']
KeyError: 'rout'
2024-07-23 15:50:34,653 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 112785
2024-07-23 15:51:03,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:51:03,215 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:51:14,201 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:51:14,252 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:51:42,524 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:51:42,584 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:51:44,677 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 86, in _GetGoodDispatch
    IDispatch = pythoncom.connect(IDispatch)
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 441, in select_business_notification_price_download
    word = win32.gencache.EnsureDispatch('Word.Application')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\gencache.py", line 618, in EnsureDispatch
    disp = win32com.client.Dispatch(prog_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\__init__.py", line 117, in Dispatch
    dispatch, userName = dynamic._GetGoodDispatchAndUserName(dispatch, userName, clsctx)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 106, in _GetGoodDispatchAndUserName
    return (_GetGoodDispatch(IDispatch, clsctx), userName)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 89, in _GetGoodDispatch
    IDispatch, None, clsctx, pythoncom.IID_IDispatch
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2024-07-23 15:51:44,681 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 136861
2024-07-23 15:51:57,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:52:50,768 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:52:50,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 15:52:51,109 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 15:52:51,203 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 15:52:51,327 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 15:52:51,437 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 15:52:51,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 15:52:51,540 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 15:52:53,216 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 15:52:56,797 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:52:56,851 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:52:58,125 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 86, in _GetGoodDispatch
    IDispatch = pythoncom.connect(IDispatch)
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 441, in select_business_notification_price_download
    word = win32.gencache.EnsureDispatch('Word.Application')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\gencache.py", line 618, in EnsureDispatch
    disp = win32com.client.Dispatch(prog_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\__init__.py", line 117, in Dispatch
    dispatch, userName = dynamic._GetGoodDispatchAndUserName(dispatch, userName, clsctx)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 106, in _GetGoodDispatchAndUserName
    return (_GetGoodDispatch(IDispatch, clsctx), userName)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 89, in _GetGoodDispatch
    IDispatch, None, clsctx, pythoncom.IID_IDispatch
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2024-07-23 15:52:58,126 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 136861
2024-07-23 15:53:34,709 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:53:41,718 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 86, in _GetGoodDispatch
    IDispatch = pythoncom.connect(IDispatch)
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 443, in select_business_notification_price_download
    word = win32.gencache.EnsureDispatch('Word.Application')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\gencache.py", line 618, in EnsureDispatch
    disp = win32com.client.Dispatch(prog_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\__init__.py", line 117, in Dispatch
    dispatch, userName = dynamic._GetGoodDispatchAndUserName(dispatch, userName, clsctx)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 106, in _GetGoodDispatchAndUserName
    return (_GetGoodDispatch(IDispatch, clsctx), userName)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 89, in _GetGoodDispatch
    IDispatch, None, clsctx, pythoncom.IID_IDispatch
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2024-07-23 15:53:41,725 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 136901
2024-07-23 15:54:23,868 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:54:26,776 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 86, in _GetGoodDispatch
    IDispatch = pythoncom.connect(IDispatch)
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 441, in select_business_notification_price_download
    word = win32com.client.gencache.EnsureDispatch("Word.Application")
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\gencache.py", line 618, in EnsureDispatch
    disp = win32com.client.Dispatch(prog_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\__init__.py", line 117, in Dispatch
    dispatch, userName = dynamic._GetGoodDispatchAndUserName(dispatch, userName, clsctx)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 106, in _GetGoodDispatchAndUserName
    return (_GetGoodDispatch(IDispatch, clsctx), userName)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 89, in _GetGoodDispatch
    IDispatch, None, clsctx, pythoncom.IID_IDispatch
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2024-07-23 15:54:26,783 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 136881
2024-07-23 15:55:43,413 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:55:47,714 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 86, in _GetGoodDispatch
    IDispatch = pythoncom.connect(IDispatch)
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 441, in select_business_notification_price_download
    word_app = win32com.client.gencache.EnsureDispatch("Word.Application")
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\gencache.py", line 618, in EnsureDispatch
    disp = win32com.client.Dispatch(prog_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\__init__.py", line 117, in Dispatch
    dispatch, userName = dynamic._GetGoodDispatchAndUserName(dispatch, userName, clsctx)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 106, in _GetGoodDispatchAndUserName
    return (_GetGoodDispatch(IDispatch, clsctx), userName)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\win32com\client\dynamic.py", line 89, in _GetGoodDispatch
    IDispatch, None, clsctx, pythoncom.IID_IDispatch
pywintypes.com_error: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2024-07-23 15:55:47,721 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 136906
2024-07-23 15:55:50,890 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:57:48,513 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:57:57,591 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 447, in select_business_notification_price_download
    table_data = extract_table_data(doc, rout)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 386, in extract_table_data
    data.extend(extract_data_from_table(table, rout))
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 357, in extract_data_from_table
    product_code = query_product_code(rout, product_number)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 337, in query_product_code
    cursor.execute(query, qrout=rout, product_number=product_number)
TypeError: execute() got an unexpected keyword argument 'qrout'
2024-07-23 15:57:57,596 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 130301
2024-07-23 15:58:04,592 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 447, in select_business_notification_price_download
    table_data = extract_table_data(doc, rout)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 386, in extract_table_data
    data.extend(extract_data_from_table(table, rout))
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 357, in extract_data_from_table
    product_code = query_product_code(rout, product_number)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 337, in query_product_code
    cursor.execute(query, qrout=rout, product_number=product_number)
TypeError: execute() got an unexpected keyword argument 'qrout'
2024-07-23 15:58:04,594 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 130301
2024-07-23 15:58:16,469 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 15:58:16,527 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 15:58:20,568 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 447, in select_business_notification_price_download
    table_data = extract_table_data(doc, rout)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 386, in extract_table_data
    data.extend(extract_data_from_table(table, rout))
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 357, in extract_data_from_table
    product_code = query_product_code(rout, product_number)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 337, in query_product_code
    cursor.execute(query, qrout=rout, product_number=product_number)
TypeError: execute() got an unexpected keyword argument 'qrout'
2024-07-23 15:58:20,570 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 130301
2024-07-23 15:58:30,747 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 119, in select_business_notification_price_download
    sql_result, http_status = select_business_notification_price_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 447, in select_business_notification_price_download
    table_data = extract_table_data(doc, rout)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 386, in extract_table_data
    data.extend(extract_data_from_table(table, rout))
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 357, in extract_data_from_table
    product_code = query_product_code(rout, product_number)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadBusinessNotificationInfo.py", line 337, in query_product_code
    cursor.execute(query, qrout=rout, product_number=product_number)
TypeError: execute() got an unexpected keyword argument 'qrout'
2024-07-23 15:58:30,748 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 130301
2024-07-23 15:58:50,942 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 15:59:47,823 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 15:59:57,030 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5820
2024-07-23 16:01:51,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:02:08,199 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:02:21,506 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:02:21,508 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99639
2024-07-23 16:04:17,151 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:04:32,535 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:04:32,539 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99645
2024-07-23 16:04:50,944 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:05:07,316 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:05:15,841 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:05:15,845 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99625
2024-07-23 16:06:35,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:06:35,250 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:06:52,871 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:06:53,023 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 16:06:53,219 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 16:06:53,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 16:06:53,424 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 16:06:53,536 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 16:06:53,578 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 16:06:53,598 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 16:06:56,403 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 16:06:58,937 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:06:58,989 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:07:00,091 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 16:07:31,820 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:07:31,821 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99625
2024-07-23 16:09:53,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:12:53,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:13:12,471 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:13:23,578 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:13:23,582 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99625
2024-07-23 16:15:53,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:17:04,469 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:17:15,665 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:17:15,666 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99661
2024-07-23 16:17:35,432 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:17:35,433 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99605
2024-07-23 16:17:57,787 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:18:13,292 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:18:25,658 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:18:34,710 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 511, in dispatch
    self.response = self.finalize_response(request, response, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 426, in finalize_response
    % type(response)
AssertionError: Expected a `Response`, `HttpResponse` or `HttpStreamingResponse` to be returned from the view, but received a `<class 'bytes'>`
2024-07-23 16:18:34,714 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 99605
2024-07-23 16:18:53,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:21:53,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:23:30,608 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:23:40,898 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5820
2024-07-23 16:24:53,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:27:09,554 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-23 16:27:18,799 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 16:27:18,802 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5820
2024-07-23 16:27:33,734 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 16:27:48,819 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:27:48,880 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:27:49,923 _DownloadBusinessNotificationInfo.select_business_notification_price_download 444 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300771\通知 113年6月直營經銷商重點產品交易率及實銷競賽排名表!!240717h16388B.docx
2024-07-23 16:27:49,923 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2024-07-23 16:27:49,923 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2024-07-23 16:27:53,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:29:05,714 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:29:05,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:29:11,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:29:11,336 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 16:29:11,535 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 16:29:11,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 16:29:11,743 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 16:29:11,857 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 16:29:11,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 16:29:11,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 12141
2024-07-23 16:29:14,681 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 16:29:18,623 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:29:18,679 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:31:49,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:31:49,357 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:32:06,275 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:32:06,326 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:32:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:35:12,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:36:48,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:36:48,963 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:36:50,403 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 16:36:50,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 16:36:52,854 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 16:38:10,181 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:38:10,238 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:38:12,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:41:12,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:42:13,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:42:13,191 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:42:22,691 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:42:22,755 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:44,375 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-23 16:43:44,376 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-23 16:43:44,573 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:44,582 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:47,176 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:47,229 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:52,398 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:52,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:54,600 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:54,635 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:55,980 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:43:55,991 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:59,935 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:43:59,990 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:44:01,752 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 16:44:01,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 16:44:04,583 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 16:44:04,598 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 16:44:11,275 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-23 16:44:11,389 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:46:58,369 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:46:58,446 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 16:46:58,614 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 16:46:58,692 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 16:46:58,693 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-23 16:46:58,719 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-23 16:46:58,826 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 16:46:58,897 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-23 16:46:58,898 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-23 16:46:59,037 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 16:46:59,046 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 16:46:59,105 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11342
2024-07-23 16:46:59,157 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 16:46:59,203 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 16:49:59,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:52:59,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:55:59,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 16:58:59,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:01:59,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:04:59,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:07:59,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:10:59,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:13:59,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:16:59,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:19:59,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:22:59,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:25:59,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:28:59,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:31:59,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:34:59,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:37:59,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:40:59,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:43:59,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:46:59,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:49:59,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:52:59,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:55:59,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 17:58:59,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:01:59,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:04:59,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:07:59,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:10:59,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:13:59,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:16:59,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:19:59,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:22:59,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:25:59,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:28:59,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:31:34,912 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 18:31:34,976 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 18:31:39,668 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 18:31:42,685 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:31:42,740 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:31:54,983 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 24116
2024-07-23 18:31:58,490 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:32:01,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:01,056 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:03,395 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:03,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:06,206 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:06,267 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:08,599 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:08,655 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:10,982 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:11,032 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:12,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:12,186 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:13,783 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:13,835 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:16,192 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:16,237 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:18,848 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:18,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:22,911 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10402
2024-07-23 18:32:25,135 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 18:32:29,493 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:29,535 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:32:32,260 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:32:32,313 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-23 18:32:34,746 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-23 18:32:34,858 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300656\1130712通知協助配送「C&C x活力FUN大拍貼機」贊助用產品240712h15750B.docx
2024-07-23 18:32:34,858 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:32:34,858 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:34:58,518 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:37:03,405 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:37:03,462 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:37:08,290 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300656\1130712通知協助配送「C&C x活力FUN大拍貼機」贊助用產品240712h15750B.docx
2024-07-23 18:37:08,290 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:37:08,290 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:37:09,958 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300656\C&C送貨資訊240712h15750A.jpg
2024-07-23 18:37:09,958 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:37:09,958 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:37:26,741 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:37:26,791 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:37:29,279 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300656\1130712通知協助配送「C&C x活力FUN大拍貼機」贊助用產品240712h15750B.docx
2024-07-23 18:37:29,279 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:37:29,279 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:37:37,002 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:37:37,242 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 18:37:37,365 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 18:37:37,392 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 18:37:37,556 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 18:37:37,668 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9914
2024-07-23 18:37:44,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 18:37:49,296 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:37:49,355 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:37:51,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:37:51,556 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:37:53,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:37:53,463 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:37:55,205 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:37:55,206 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:37:55,206 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:37:58,515 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:40:37,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:40:58,519 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 18:41:19,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:41:19,747 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:41:22,129 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:41:22,130 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:41:22,130 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:41:24,052 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\C&C送貨資訊240715h15750A.jpg
2024-07-23 18:41:24,052 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:41:24,052 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:41:25,601 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:41:25,602 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:41:25,602 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:43:37,158 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-23 18:43:37,159 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-23 18:48:23,586 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-23 18:48:23,767 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-23 18:48:23,845 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 18:48:23,957 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 18:48:24,035 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 18:48:24,116 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-23 18:48:24,116 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-23 18:48:24,122 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-23 18:48:24,338 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-23 18:48:24,485 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5572
2024-07-23 18:48:24,794 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-23 18:48:24,832 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-23 18:48:28,738 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-23 18:48:28,872 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 18:48:29,054 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 18:48:39,720 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-23 18:48:39,894 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 18:48:40,047 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 18:48:40,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5572
2024-07-23 18:48:40,401 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 18:48:40,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9914
2024-07-23 18:48:47,716 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 18:48:47,786 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-23 18:48:47,787 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-23 18:48:47,907 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 18:48:48,016 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 18:48:50,555 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 18:48:54,088 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-23 18:48:54,089 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-23 18:48:54,211 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:48:54,254 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:48:56,856 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:48:56,903 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:48:59,467 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:48:59,519 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:49:02,356 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:49:02,407 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:49:03,643 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:49:03,643 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:49:03,644 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:49:07,077 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:49:07,078 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:49:07,078 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:49:08,803 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:49:08,803 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:49:08,803 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:49:12,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:49:12,826 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:49:14,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 18:49:19,045 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 18:49:27,216 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 18:52:46,778 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:52:46,867 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:52:51,951 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 18:52:56,880 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-23 18:52:57,006 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 18:52:57,022 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 18:53:05,862 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:53:05,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:53:10,134 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:53:10,185 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:53:11,487 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:53:11,539 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:53:14,142 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:53:14,193 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:53:15,417 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:53:15,417 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:53:15,418 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:58:25,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:58:25,483 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:58:28,026 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 18:58:28,027 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 18:58:28,027 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 18:59:41,362 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 18:59:41,498 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:59:58,941 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 18:59:58,994 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:00:03,264 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:00:03,316 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:00:04,944 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 19:00:04,944 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 19:00:04,945 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 19:00:07,620 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:00:07,674 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:00:16,374 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:00:16,412 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:00:18,755 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 19:03:25,940 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:03:25,998 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:03:28,845 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:03:28,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:03:30,491 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 19:03:34,599 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 19:03:34,600 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 19:03:48,881 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 19:03:48,937 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 19:03:50,391 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 19:03:50,391 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 19:03:50,391 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 19:03:52,731 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 19:03:52,731 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 19:03:52,732 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 21:37:34,841 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 21:37:34,841 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 21:37:35,026 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 21:37:35,078 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 21:38:05,411 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-23 21:38:05,412 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-23 21:38:05,524 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 21:38:05,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 21:38:08,563 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-23 21:38:08,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:09:19,616 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-23 22:09:19,798 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:09:19,884 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-23 22:09:20,044 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-23 22:09:20,092 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-23 22:09:20,094 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-23 22:09:20,137 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 22:09:20,187 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 22:09:20,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 22:09:20,386 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 22:09:20,407 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 22:09:20,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9914
2024-07-23 22:09:20,880 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 22:09:23,198 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:12:19,980 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:13:13,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:13:13,231 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 22:13:13,253 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 22:13:13,307 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 22:13:13,640 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9914
2024-07-23 22:13:13,647 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 22:13:17,649 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:13:22,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:13:22,937 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-23 22:13:28,832 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:13:28,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:13:29,787 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-23 22:13:29,896 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 22:13:29,896 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 22:13:29,897 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 22:15:19,978 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:16:13,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:17:39,428 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:17:39,433 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:18:19,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:19:13,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:21:19,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:22:13,192 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:24:19,958 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:25:13,198 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:27:19,972 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:28:13,188 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:30:17,695 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:30:17,735 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:30:19,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:31:13,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:31:22,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:31:22,824 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:31:25,317 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 22:31:25,318 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 22:31:25,318 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 22:31:36,802 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300657\1130715通知協助配送「C&C 活力FUN大拍貼機」贊助用產品240715h15750B.docx
2024-07-23 22:31:36,802 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 22:31:36,803 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 22:31:44,770 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:31:44,824 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:31:45,964 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 22:31:54,254 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-23 22:31:54,383 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 22:31:54,397 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 22:33:18,827 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:33:18,889 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:33:19,919 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:33:21,001 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-23 22:34:13,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:36:19,957 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:37:09,405 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:37:09,472 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:37:12,645 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-23 22:37:13,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:37:19,895 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-23 22:39:19,950 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:40:13,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:42:19,968 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:43:13,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:43:25,491 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 22:43:25,546 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 22:43:28,973 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:43:29,139 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 22:43:29,498 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 22:43:29,611 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 22:43:29,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 22:43:29,869 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 22:43:29,935 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 22:43:29,953 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9474
2024-07-23 22:43:35,366 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:43:44,589 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:43:44,641 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:43:46,454 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-23 22:46:29,190 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 22:48:17,768 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:48:17,820 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:48:25,425 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:48:25,477 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:48:26,722 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 22:48:26,743 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 22:49:29,171 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-23 22:49:29,171 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-23 22:49:38,568 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-23 22:49:38,725 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-23 22:49:38,884 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 22:49:38,979 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-23 22:49:39,128 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-23 22:49:39,463 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5572
2024-07-23 22:49:39,681 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-23 22:49:39,704 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-23 22:49:42,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-23 22:49:42,095 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-23 22:49:42,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 22:49:42,279 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 22:49:42,331 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-23 22:49:46,870 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-23 22:49:46,985 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 22:49:47,165 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-23 22:49:54,762 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-23 22:49:54,947 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 22:49:55,103 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 22:49:55,279 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5572
2024-07-23 22:49:55,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 22:49:55,567 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9474
2024-07-23 22:49:58,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 22:49:58,562 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 22:49:58,692 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 22:50:01,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:50:06,617 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:50:06,669 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:50:07,990 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-23 22:59:00,110 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:59:00,112 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:59:34,009 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:59:34,054 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 22:59:38,947 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 22:59:48,512 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 22:59:48,571 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:01:06,089 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:01:06,116 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:04:13,770 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 23:06:11,962 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:06:12,034 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:06:13,355 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 23:06:19,359 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:06:19,508 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 23:06:19,687 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 23:06:19,750 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 23:06:19,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 23:06:20,012 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 23:06:20,081 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9474
2024-07-23 23:06:20,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 23:06:24,393 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 23:06:27,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:06:27,566 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:06:30,349 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 23:06:30,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 23:06:35,398 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 23:09:19,534 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:09:46,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:09:46,526 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:11:34,477 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:11:34,536 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:11:47,305 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:11:47,352 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:11:49,217 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300771\通知 113年6月直營經銷商重點產品交易率及實銷競賽排名表!!240717h16388B.docx
2024-07-23 23:11:49,217 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-23 23:11:49,218 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-23 23:12:19,502 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:15:19,501 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:20:02,202 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:20:02,448 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-23 23:20:02,853 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-23 23:20:02,925 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-23 23:20:03,073 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-23 23:20:03,181 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-23 23:20:03,240 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9474
2024-07-23 23:20:03,258 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-23 23:20:07,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-23 23:20:10,893 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:20:10,955 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:20:12,843 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 23:20:17,520 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:20:17,521 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:20:17,521 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:20:23,376 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:20:23,377 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:20:23,377 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:20:24,965 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:20:24,965 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:20:24,966 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:21:58,171 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:21:58,172 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:21:58,172 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:23:02,389 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:23:10,257 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:23:10,258 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:23:10,258 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:25:23,256 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-23 23:25:31,764 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:25:31,764 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:25:31,765 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:26:02,416 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:27:04,008 _DownloadBusinessNotificationInfo.select_business_notification_price_download 478 ERROR   => Error in select_business_notification_price_download: 'rout'
2024-07-23 23:27:04,009 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-07-23 23:27:04,009 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 63
2024-07-23 23:27:13,632 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-23 23:27:13,753 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-23 23:27:14,530 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-23 23:27:14,532 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-23 23:29:02,402 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:32:02,381 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:35:02,399 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:38:02,401 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:41:02,406 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:44:02,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:47:02,406 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:50:02,392 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:53:02,408 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:56:02,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-23 23:59:02,384 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
