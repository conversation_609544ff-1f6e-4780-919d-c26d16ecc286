2024-07-28 00:22:16,949 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 00:22:16,951 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 00:22:17,137 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 00:22:17,188 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 00:22:39,704 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-28 00:22:39,755 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-28 00:22:45,411 _DownloadBusinessNotificationInfo.extract_table_data 379 ERROR   => Rout position for 113年8月小北百貨促銷活動通報 is not defined.
2024-07-28 00:22:46,437 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4909
2024-07-28 00:23:01,975 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-28 00:23:02,259 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 58928
2024-07-28 00:23:49,914 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-28 00:23:49,974 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-28 00:27:14,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-28 00:27:14,580 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-28 00:27:21,141 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:21,314 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:21,915 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:22,092 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:22,297 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:22,821 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:23,620 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:23,844 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:23,937 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:23,977 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,136 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,212 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,334 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,571 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,613 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,650 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,680 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,740 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,783 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,862 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:24,899 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:27:25,316 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 00:31:24,207 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:31:37,578 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:37,742 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:38,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:38,550 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:38,773 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:39,473 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,010 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,202 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,262 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,272 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,359 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,408 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,417 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,504 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,684 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,722 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,790 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,893 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:40,952 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:41,004 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:41,270 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:41,369 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:31:41,729 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6682
2024-07-28 00:32:15,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 54866
2024-07-28 00:33:15,159 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:33:38,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:39,119 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:39,861 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:40,108 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:40,285 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:40,828 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:42,095 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:42,552 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:42,641 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:42,735 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:43,251 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:43,386 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:43,439 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:43,685 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:43,934 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,063 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,147 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,218 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,352 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,468 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,612 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:44,659 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:33:46,301 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 00:36:38,954 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:36:52,532 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:52,624 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:53,077 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:53,327 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:53,558 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:54,521 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:55,369 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:56,005 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:56,250 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:56,467 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,062 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,265 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,316 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,424 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,563 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,580 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,612 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,698 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,921 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:57,955 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:36:59,145 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6678
2024-07-28 00:43:01,195 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:43:13,594 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:13,758 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:14,737 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:15,017 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:15,311 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,013 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,517 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,631 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,695 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,835 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,889 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,897 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:16,979 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,117 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,177 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,205 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,265 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,296 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,395 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,433 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:43:17,798 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6670
2024-07-28 00:43:58,533 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 54866
2024-07-28 00:47:06,234 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:47:23,032 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:23,243 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:24,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:24,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:24,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,099 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,852 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,899 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,911 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,921 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:26,981 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,022 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,036 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,072 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,257 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,346 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,409 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,447 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,496 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,528 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,620 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:27,660 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:47:28,653 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 00:52:17,385 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:52:23,361 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 00:52:23,363 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 00:52:30,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:30,296 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:30,847 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:31,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:31,302 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:31,887 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,708 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,761 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,786 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,908 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:32,996 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,045 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,180 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,286 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,309 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,335 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,385 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,432 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,459 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:33,559 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:52:34,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 00:53:28,944 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:53:38,733 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:38,857 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:39,296 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:39,551 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:39,796 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:40,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,326 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,594 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,680 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,742 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,902 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,956 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:41,968 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,127 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,434 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,496 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,580 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,607 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,676 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:42,702 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:53:43,866 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 00:54:11,672 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 00:54:20,860 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:20,987 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:21,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:21,577 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:21,784 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:22,543 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,361 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,645 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,723 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,766 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,852 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,948 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:23,971 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:24,195 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:24,689 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:24,737 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:24,811 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:24,944 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:25,025 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:25,042 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:25,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:25,268 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 00:54:25,760 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 01:06:23,222 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:06:31,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:31,226 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:31,372 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:31,419 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:31,632 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:31,664 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:32,748 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:32,791 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:32,943 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:32,974 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:33,666 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:33,737 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:33,966 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:34,006 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:34,175 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:34,210 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:34,670 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:34,689 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:36,481 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:36,542 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,171 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,350 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,394 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:37,693 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:38,785 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:38,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,167 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,278 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,294 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,863 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:39,865 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,603 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,603 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,652 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,761 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,762 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,845 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,846 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:40,902 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:41,221 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:41,223 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:41,320 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:41,325 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:06:42,609 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6587
2024-07-28 01:06:44,099 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6587
2024-07-28 01:08:38,597 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:09:02,133 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:09:10,639 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:10,771 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:11,232 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:11,383 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:11,585 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:12,176 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:12,821 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,097 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,161 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,195 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,372 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,432 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,443 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,645 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,894 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:13,963 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,022 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,063 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,134 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,266 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,295 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:09:14,598 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6564
2024-07-28 01:10:43,969 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:10:49,226 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:49,302 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:49,938 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:50,209 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:50,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:51,591 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:53,046 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:53,684 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:53,842 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:53,948 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:54,264 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:54,361 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:54,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:54,617 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:54,943 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,016 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,079 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,139 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,214 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,255 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,388 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:55,429 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:10:56,420 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6682
2024-07-28 01:15:43,656 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:15:52,967 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:53,151 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:53,692 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:53,889 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:54,064 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:54,583 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:55,402 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:55,901 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:55,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:55,947 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,047 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,105 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,323 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,504 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,548 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,607 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,650 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,737 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,775 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,891 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:56,935 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:15:57,278 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6697
2024-07-28 01:21:08,882 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:21:18,394 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:19,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:19,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:20,051 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:20,320 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:21,022 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:21,797 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,156 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,271 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,355 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,481 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,525 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,541 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,627 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,804 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,841 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,877 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,912 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:22,996 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:23,043 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:23,143 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:23,193 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:21:23,624 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6580
2024-07-28 01:22:47,634 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:22:51,439 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 01:22:51,439 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 01:22:57,705 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:22:57,867 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:22:58,298 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:22:58,583 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:22:58,899 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:22:59,601 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,188 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,398 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,449 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,552 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,593 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,603 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,725 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:00,971 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,025 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,238 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,359 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,544 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,651 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,810 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:01,870 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:23:02,287 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6580
2024-07-28 01:24:26,948 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:24:37,009 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:37,695 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:38,414 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:38,768 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:39,079 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:39,761 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,416 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,518 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,542 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,560 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,631 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,710 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,722 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:40,835 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,058 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,132 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,171 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,211 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,281 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,474 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,682 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:41,775 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:24:42,145 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6696
2024-07-28 01:25:32,079 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:25:42,605 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:42,711 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:43,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:43,454 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:43,755 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:44,567 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,360 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,550 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,582 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,593 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,632 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,645 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,661 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,769 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,876 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,905 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,919 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:45,939 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:46,028 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:46,099 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:46,262 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:46,333 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:25:46,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6682
2024-07-28 01:27:03,707 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:27:14,998 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:15,170 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:15,834 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:16,144 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:16,507 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:17,289 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:17,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,047 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,066 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,085 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,158 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,246 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,264 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,428 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,690 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,746 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:18,899 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:19,024 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:19,084 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:19,168 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:19,193 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:27:19,738 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6682
2024-07-28 01:30:54,023 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 01:31:02,622 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:02,776 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:03,322 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:03,491 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:03,695 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:04,394 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:05,473 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:05,824 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:05,953 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:06,026 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:06,123 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:06,353 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:06,380 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:06,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,133 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,396 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,666 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:07,997 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:08,085 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:08,228 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 01:31:08,776 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6682
2024-07-28 10:10:37,456 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 10:10:37,456 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 10:10:37,634 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-28 10:10:37,635 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-28 10:10:37,731 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-28 10:10:37,732 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-28 10:11:08,817 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-28 10:11:08,951 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-28 10:11:09,151 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 10:11:09,225 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 10:11:09,226 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-28 10:11:09,279 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-28 10:11:09,279 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-28 10:11:09,504 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-28 10:11:09,560 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6167
2024-07-28 10:11:09,798 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 38737
2024-07-28 10:11:09,974 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 17626
2024-07-28 10:12:15,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 10:12:15,862 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-28 10:12:15,862 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-28 10:12:16,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 10:12:16,107 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 10:12:29,683 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41761
2024-07-28 10:12:31,823 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-28 10:12:31,823 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-28 10:12:31,937 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-28 10:12:31,993 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-28 10:12:33,278 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-28 10:12:39,035 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:39,160 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:39,561 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:39,687 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:39,809 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:40,177 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:40,375 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:40,826 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:41,368 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:41,454 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:41,738 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:42,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:42,408 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:42,761 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:42,872 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,273 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,506 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,672 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,765 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,870 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,885 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,944 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,949 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,961 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:43,972 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:44,052 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:44,084 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:12:44,505 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6818
2024-07-28 10:16:27,114 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:16:41,090 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:41,317 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:41,698 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:41,794 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:41,960 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:42,244 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:42,371 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:42,503 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:42,845 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:42,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:43,324 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:43,594 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:43,894 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,111 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,317 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,366 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,394 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,426 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,545 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,575 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,621 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,632 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,641 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,742 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:44,806 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:16:45,399 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6686
2024-07-28 10:20:14,996 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:20:26,749 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:27,048 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:27,659 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:27,776 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:27,872 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:28,128 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:28,261 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:28,458 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:28,780 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:28,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:29,407 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:29,839 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,121 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,280 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,324 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,481 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,554 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,612 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,766 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,860 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,887 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,914 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,928 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:30,982 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:31,015 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:20:31,414 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6685
2024-07-28 10:24:47,987 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:24:55,500 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,518 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,564 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,578 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,586 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,627 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,685 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,720 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:55,924 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:56,014 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:56,255 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:56,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:56,988 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:57,386 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:57,740 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:58,705 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:59,251 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:24:59,783 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:00,502 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,560 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,823 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,852 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,890 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:01,921 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:02,043 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:02,125 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:25:02,818 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6684
2024-07-28 10:27:25,359 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:27:38,006 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:38,287 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:38,814 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:38,922 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:39,095 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:39,473 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:39,656 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:39,825 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:40,340 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:40,473 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:40,923 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,184 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,362 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,538 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,595 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,753 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,844 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,895 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:41,968 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,061 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,101 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,170 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,182 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,197 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,208 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,297 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,351 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:27:42,870 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6685
2024-07-28 10:32:21,172 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:32:29,599 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:29,683 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:30,052 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:30,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:30,341 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:30,738 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:30,864 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:31,040 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:31,460 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:31,589 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:32,024 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:32,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:32,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:33,019 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:33,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:33,682 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,015 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,269 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,516 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,736 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,765 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,820 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,848 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,863 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:34,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:35,022 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:32:35,452 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6685
2024-07-28 10:43:21,439 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:43:24,540 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 10:43:24,540 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 10:43:30,059 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:30,408 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:30,727 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:30,842 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:30,938 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:31,289 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:31,402 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:31,532 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:31,912 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:32,021 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:32,381 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:32,682 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:32,930 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,091 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,139 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,304 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,419 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,526 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,660 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,846 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,956 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,967 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:33,987 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:34,023 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:34,144 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:34,209 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:43:34,859 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6798
2024-07-28 10:47:40,706 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:47:49,007 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:49,176 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:49,918 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:49,918 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 7 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:50,096 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:50,096 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 8 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:50,246 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:50,246 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 9 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:50,659 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:50,659 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 11 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:50,807 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:50,808 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 12 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:50,953 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:50,953 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 13 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:51,313 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:51,465 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:51,884 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:51,884 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 18 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:52,223 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,223 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 20 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:52,460 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,460 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 22 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:52,639 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,703 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,921 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,967 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:52,967 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 31 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:52,993 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,027 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,028 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 36 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:53,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,182 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,182 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 45 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:53,302 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,311 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,331 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,355 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,502 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,502 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 54 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:53,533 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:47:53,533 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 處理行 56 時發生錯誤: expected string or bytes-like object
2024-07-28 10:47:53,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6319
2024-07-28 10:48:58,605 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:49:08,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:08,212 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:08,550 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:08,550 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 7 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:08,739 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:08,740 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 8 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:08,879 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:08,879 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 9 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:09,490 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:09,490 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 11 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:09,671 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:09,671 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 12 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:09,818 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:09,818 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 13 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:10,294 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:10,422 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:10,651 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:10,651 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 18 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:10,867 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:10,867 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 20 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:11,193 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:11,194 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 22 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:11,553 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:11,649 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:12,282 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:12,561 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:12,561 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 31 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:12,734 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:12,982 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:12,982 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 36 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:13,450 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,517 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,517 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 45 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:13,635 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,655 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,671 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,689 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,788 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,789 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 54 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:13,819 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:49:13,819 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 56 時發生錯誤: expected string or bytes-like object
2024-07-28 10:49:15,147 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6234
2024-07-28 10:50:30,699 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:50:42,363 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:42,524 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:43,249 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:43,250 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 7 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:43,412 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:43,412 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 8 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:43,531 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:43,531 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 9 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:43,940 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:43,941 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 11 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:44,075 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:44,075 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 12 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:44,174 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:44,174 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 13 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:44,596 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:44,754 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,138 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,138 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 18 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:45,431 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,431 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 20 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:45,626 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,626 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 22 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:45,777 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,820 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,917 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,958 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:45,958 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 31 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:45,983 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,056 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,056 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 36 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:46,198 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,247 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,247 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 45 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:46,305 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,315 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,328 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,337 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,373 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,373 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 54 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:46,404 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:50:46,404 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 56 時發生錯誤: expected string or bytes-like object
2024-07-28 10:50:46,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6233
2024-07-28 10:51:51,656 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:52:01,096 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:01,230 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:01,585 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:01,586 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 7 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:01,715 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:01,715 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 8 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:01,809 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:01,809 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 9 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:02,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:02,120 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 11 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:02,241 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:02,241 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 12 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:02,374 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:02,374 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 13 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:02,760 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:02,874 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:03,278 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:03,278 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 18 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:03,531 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:03,531 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 20 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:03,788 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:03,788 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 22 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:03,993 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,044 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,282 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,432 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,432 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 31 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:04,495 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,586 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,586 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 36 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:04,723 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,792 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,792 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 45 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:04,894 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,902 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,926 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:04,964 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:05,058 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:05,058 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 54 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:05,094 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:05,094 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 處理行 56 時發生錯誤: expected string or bytes-like object
2024-07-28 10:52:06,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6233
2024-07-28 10:52:45,544 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:52:54,422 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:54,519 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:54,734 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:54,838 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:54,909 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:55,421 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:55,597 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:55,792 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:56,398 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:56,553 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,113 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,436 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,627 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,746 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,786 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,883 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,917 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:57,950 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,018 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,191 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,210 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,276 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,333 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,354 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,365 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,436 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,502 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:52:58,951 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6799
2024-07-28 10:54:16,416 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:54:25,375 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:25,600 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:25,955 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:26,095 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:26,304 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:26,775 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:26,944 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:27,164 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:27,655 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:27,877 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:28,367 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:28,618 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:28,860 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,012 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,083 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,287 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,383 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,488 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,575 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:29,947 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,063 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,327 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,361 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,387 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,418 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,536 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:30,598 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:54:31,273 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6800
2024-07-28 10:57:34,864 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:57:42,757 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:57:50,987 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,113 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,365 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,450 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,520 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,782 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:51,950 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:52,064 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:52,381 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:52,481 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:52,878 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:53,229 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:53,596 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:53,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:54,013 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:54,474 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:54,716 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:54,869 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:54,936 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,029 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,046 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,108 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,124 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,135 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,144 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,232 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:57:55,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5921
2024-07-28 10:58:56,542 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 10:59:06,281 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:06,392 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:06,714 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:06,878 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:07,049 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:07,555 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:07,753 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:07,923 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:08,472 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:08,546 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:08,837 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:09,198 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:09,723 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:10,185 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:10,348 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:10,985 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:11,256 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:11,446 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:11,760 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,203 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,284 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,468 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,490 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,559 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,786 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:12,915 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 10:59:13,668 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6685
2024-07-28 11:02:21,596 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:02:31,500 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:31,618 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:32,003 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:32,168 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:32,374 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:32,764 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:32,932 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:33,083 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:33,517 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:33,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:34,101 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:34,541 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:34,765 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:34,959 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,129 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,161 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,194 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,240 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,364 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,385 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,433 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,449 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,461 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,469 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,522 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,553 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:02:35,937 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6799
2024-07-28 11:03:57,616 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:04:06,843 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:07,070 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:07,585 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:07,686 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:07,819 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:08,253 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:08,429 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:08,596 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:09,039 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:09,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:09,594 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:09,810 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,000 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,226 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,340 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,639 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,703 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,817 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:10,972 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,010 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,132 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,165 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,204 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,310 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,373 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:04:11,861 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6799
2024-07-28 11:06:10,828 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:06:20,477 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:20,684 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:21,161 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:21,314 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:21,463 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:21,854 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:21,975 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:22,132 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:22,565 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:22,692 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:23,097 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:23,445 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:23,702 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:23,902 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:23,968 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,204 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,312 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,410 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,515 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,643 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,678 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,772 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,786 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,795 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,810 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,853 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:24,882 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:06:26,106 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6798
2024-07-28 11:07:04,501 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:07:15,270 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:15,426 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:15,708 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:15,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:15,889 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:16,298 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:16,507 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:16,707 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:17,282 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:17,484 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:17,885 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,119 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,301 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,454 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,497 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,644 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,678 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,705 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,745 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:18,964 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,027 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,109 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,125 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,133 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,196 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,230 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:07:19,729 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6376
2024-07-28 11:09:07,954 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:09:17,074 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:17,331 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:17,764 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:18,133 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:18,206 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:18,583 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:18,731 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:18,923 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:19,454 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:19,629 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:20,201 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:20,498 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:20,804 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,062 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,130 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,393 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,475 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,543 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,621 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:21,955 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,044 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,228 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,270 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,336 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,452 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:22,542 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:09:23,169 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6376
2024-07-28 11:11:24,712 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:12:39,610 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:12:47,181 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:47,447 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:48,337 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:48,458 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:48,623 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:49,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:49,157 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:49,356 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:49,881 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:50,086 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:50,590 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:50,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,249 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,420 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,495 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,780 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,871 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:51,960 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,053 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,575 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,700 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,845 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,881 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:52,919 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:53,079 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:53,196 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:12:53,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6376
2024-07-28 11:15:32,618 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 11:15:32,679 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 11:16:47,335 exception.response_for_exception 101 ERROR   => Invalid HTTP_HOST header: '**************:8000'. You may need to add '**************' to ALLOWED_HOSTS.
2024-07-28 11:16:47,413 log.log_response 230 WARNING => Bad Request: /
2024-07-28 11:16:47,414 basehttp.log_message 161 WARNING => "GET / HTTP/1.1" 400 78936
2024-07-28 11:16:48,672 exception.response_for_exception 101 ERROR   => Invalid HTTP_HOST header: '**************:8000'. You may need to add '**************' to ALLOWED_HOSTS.
2024-07-28 11:16:48,690 log.log_response 230 WARNING => Bad Request: /favicon.ico
2024-07-28 11:16:48,691 basehttp.log_message 161 WARNING => "GET /favicon.ico HTTP/1.1" 400 78994
2024-07-28 11:18:09,833 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:18:18,222 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:18,503 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:19,111 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:19,239 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:19,356 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:19,748 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:19,919 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:20,069 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:20,507 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:20,658 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:21,077 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:21,409 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:21,674 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:21,888 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:21,954 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,171 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,276 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,335 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,459 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,847 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,930 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,942 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,956 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:22,973 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:23,122 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:23,234 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:18:24,013 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6376
2024-07-28 11:19:34,980 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:19:43,561 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:43,715 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:44,121 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:44,329 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:44,433 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:45,015 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:45,220 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:45,437 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:46,017 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:46,200 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:46,668 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:46,962 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,342 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,386 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,494 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,537 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,574 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,621 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,757 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,791 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,862 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,877 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,885 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,951 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:47,998 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:19:48,472 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6799
2024-07-28 11:21:40,876 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:21:50,737 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:50,889 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,011 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,111 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,194 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,610 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,778 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:52,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:53,338 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:53,520 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:53,911 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,164 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,451 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,659 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,702 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,833 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,890 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:54,933 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,009 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,278 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,353 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,459 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,477 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,499 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,517 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,595 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:55,631 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:21:56,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6376
2024-07-28 11:23:10,086 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:23:18,875 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:19,617 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:19,907 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:20,018 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:20,110 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:20,550 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:20,705 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:20,847 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:21,256 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:21,434 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:21,814 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,117 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,366 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,533 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,574 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,715 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,792 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,822 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:22,876 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,211 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,274 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,392 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,406 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,424 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,440 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,498 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,525 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:23:23,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6798
2024-07-28 11:25:12,516 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:25:24,811 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:25,068 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:25,395 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:25,486 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:25,612 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:26,050 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:26,235 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:26,392 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:26,805 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:26,907 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:27,390 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:27,701 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:27,878 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,014 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,038 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,116 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,151 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,177 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,217 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,384 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,414 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,462 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,471 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,480 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,499 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,555 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:28,573 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:25:29,082 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6550
2024-07-28 11:29:52,372 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:29:59,070 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,088 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,168 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,192 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,199 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,263 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,435 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,516 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,801 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:29:59,912 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:00,792 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:01,193 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:01,641 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:01,949 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:02,131 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:02,703 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:03,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:03,692 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:04,209 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,066 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,215 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,297 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,309 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,320 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,338 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,400 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,423 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:30:05,942 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6799
2024-07-28 11:33:08,229 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:33:16,641 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:16,943 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:17,469 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:17,540 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:17,625 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:17,991 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:18,141 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:18,246 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:18,668 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:18,831 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:19,309 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:19,722 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:19,970 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,115 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,175 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,538 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,642 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,732 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:20,842 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,022 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,044 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,110 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,119 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,152 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,168 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,208 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,236 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:33:21,661 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6741
2024-07-28 11:34:06,845 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-28 11:34:17,171 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:17,302 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:17,592 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:17,665 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:17,766 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:18,061 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:18,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:18,238 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:18,695 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:18,847 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:19,336 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:19,812 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,062 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,273 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,362 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,816 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,907 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:20,975 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,169 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,201 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,299 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,357 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,385 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,404 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,480 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:21,593 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:34:22,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6661
2024-07-28 11:42:49,078 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:49,237 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:49,706 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:49,862 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:50,530 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:51,055 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:51,201 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:51,335 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:51,680 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:51,762 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,033 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,244 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,436 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,584 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,613 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,694 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,780 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,867 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:52,917 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,016 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,043 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,099 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,107 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,118 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,123 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,162 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:42:53,570 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6661
2024-07-28 11:43:03,505 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-28 11:43:03,568 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-28 11:43:08,329 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 1 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:08,507 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 2 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:08,693 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 3 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:08,793 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:43:08,793 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 4 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,044 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 1 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,215 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 2 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,433 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 3 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,563 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 4 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,759 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 5 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:09,922 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 6 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:10,190 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 7 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:10,481 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 8 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:10,738 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 9 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:10,975 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 10 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:11,208 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 11 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:11,432 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 12 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:11,606 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 13 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:11,776 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 14 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:11,936 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 15 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,091 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 16 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,210 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 17 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,376 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 18 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,483 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 19 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,578 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 20 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,630 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 21 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,699 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 22 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,772 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 23 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,817 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 24 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,856 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 25 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:12,947 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 26 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,075 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 27 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,154 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 28 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,199 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 29 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,255 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 30 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,306 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 31 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,354 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:43:13,354 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 1 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,388 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 2 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,420 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 3 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,438 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:43:13,438 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 4 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,465 _DownloadBusinessNotificationInfo.get_cell_text_and_color 349 ERROR   => Error in get_cell_text_and_color: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-07-28 11:43:13,465 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 5 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,513 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 6 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,567 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 7 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,598 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 8 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,634 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 9 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,685 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 10 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,714 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 11 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,728 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 12 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,759 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 13 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,802 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 14 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,838 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 15 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,864 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 16 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,872 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 17 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,897 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 18 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,916 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 19 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,933 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 20 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,955 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 21 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:13,985 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 22 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,004 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 23 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,020 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 24 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,034 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 25 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,053 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 26 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,071 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 27 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,086 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 28 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,105 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 29 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,113 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 30 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,128 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 31 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,141 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 32 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,153 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 33 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,174 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 34 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,180 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 35 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,184 _DownloadBusinessNotificationInfo.extract_table_data 469 ERROR   => 處理行 36 時發生錯誤: process_price() missing 1 required positional argument: 'previous_unit_price_unit'
2024-07-28 11:43:14,708 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 4942
2024-07-28 12:01:14,006 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" ************-07-28 12:01:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:06:21,914 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 21:06:21,914 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 21:06:22,096 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-28 21:06:22,098 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-28 21:06:22,159 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-28 21:06:22,159 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-28 21:22:51,647 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-28 21:22:51,803 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-28 21:22:51,979 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:22:52,055 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 21:22:52,132 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-28 21:22:52,133 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-28 21:22:52,133 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-28 21:22:52,329 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:22:52,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6167
2024-07-28 21:22:52,763 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:22:52,765 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:23:23,480 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-28 21:23:23,549 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-28 21:23:23,550 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-28 21:23:23,550 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-28 21:23:23,723 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:23:23,780 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:23:23,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:23:27,132 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:23:27,324 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:23:27,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:23:27,426 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:24:32,789 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:24:32,797 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:24:32,897 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:24:35,187 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:24:35,234 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:24:35,238 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:32:45,394 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-28 21:32:45,566 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:32:45,708 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:32:45,914 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:32:46,049 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:32:46,194 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:32:46,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:32:46,307 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:32:46,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:32:46,477 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:33:13,705 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 21:33:13,705 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-28 21:33:13,884 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-28 21:33:13,932 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-28 21:33:13,932 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-28 21:33:20,567 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-28 21:33:20,689 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-07-28 21:33:20,689 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-07-28 21:33:29,879 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-28 21:33:30,002 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:33:30,079 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-28 21:33:30,127 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-28 21:33:30,130 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-28 21:33:30,131 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-28 21:33:30,363 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:33:30,425 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6167
2024-07-28 21:33:30,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:33:30,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:33:32,860 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-28 21:33:32,932 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-28 21:33:33,057 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-28 21:33:36,237 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_permissions/ HTTP/1.1" 200 0
2024-07-28 21:33:36,436 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-28 21:35:45,706 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:36:22,249 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-28 21:36:24,065 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-28 21:36:31,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:36:31,387 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:36:31,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:36:31,636 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:36:31,842 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:36:31,857 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:36:31,960 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:36:32,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:36:32,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:36:35,030 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:36:35,107 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:36:36,413 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:36:42,237 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:36:52,209 basehttp.log_message 161 INFO    => "OPTIONS /api/users/update_user_permissions/ HTTP/1.1" 200 0
2024-07-28 21:36:52,533 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:37:01,896 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:37:02,147 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:37:02,366 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:37:02,407 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:37:02,603 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:37:02,662 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:37:02,667 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:37:02,771 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:37:02,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:37:20,531 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:37:25,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:37:25,212 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:37:25,385 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:37:25,474 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:37:25,648 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:37:25,661 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:37:25,768 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:37:25,825 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:37:25,849 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:37:30,707 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-28 21:37:30,914 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:37:30,925 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:37:31,027 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:37:31,638 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:37:31,693 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:37:31,712 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:37:36,995 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:37:37,158 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:37:37,419 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-28 21:37:37,495 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:37:37,589 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:37:37,603 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:37:37,622 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:37:37,702 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:37:37,786 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:37:40,219 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:37:41,753 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-28 21:37:41,874 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:37:41,980 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:37:42,118 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:37:42,160 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-28 21:37:42,359 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-28 21:37:42,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:37:42,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:37:46,862 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:38:21,777 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:38:36,659 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:38:42,463 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:38:42,531 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:38:49,787 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:38:53,916 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:39:03,786 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:39:08,172 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:39:15,530 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:39:21,268 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:39:28,498 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:39:34,406 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:39:42,585 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:39:46,628 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:40:09,818 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:40:18,957 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:40:27,229 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10892
2024-07-28 21:40:33,157 basehttp.log_message 161 INFO    => "POST /api/users/update_user_permissions/ HTTP/1.1" 200 63
2024-07-28 21:40:34,923 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:40:35,041 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:40:35,162 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-28 21:40:35,230 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:40:35,397 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-28 21:40:35,581 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:40:35,743 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:40:37,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:40:44,057 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-28 21:40:44,118 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-28 21:40:44,169 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-28 21:40:44,170 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-28 21:40:44,335 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:40:44,390 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:40:44,428 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:43:09,294 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:43:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-28 21:43:09,573 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-28 21:43:09,666 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-28 21:43:09,862 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-28 21:43:09,874 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-28 21:43:09,978 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-28 21:43:10,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-28 21:43:10,028 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-28 21:43:37,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:46:09,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:46:37,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:49:09,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:49:37,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:52:09,476 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:52:37,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:55:09,502 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:55:37,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:58:09,453 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 21:58:37,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:01:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:01:37,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:04:09,429 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:04:37,171 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:07:09,459 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:07:37,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:10:09,436 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:10:37,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:13:09,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:13:37,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:16:09,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:16:37,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:19:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:19:37,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:22:09,435 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:22:37,387 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:25:09,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:25:37,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:28:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:28:37,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:31:09,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:31:37,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:34:09,431 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:34:37,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:37:09,436 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:37:37,231 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:40:09,435 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:40:37,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:43:09,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:43:37,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:46:09,422 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:46:37,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:49:09,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:49:37,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:52:09,438 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:52:37,176 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:55:09,498 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:55:37,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:58:09,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 22:58:37,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:01:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:01:37,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:04:09,473 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:04:37,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:07:09,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:07:37,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:10:09,421 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:10:37,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:13:09,428 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:13:37,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:16:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:16:37,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:19:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:19:37,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:22:09,437 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:22:37,144 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:25:09,435 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:25:37,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:28:09,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:28:37,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:31:09,441 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:31:37,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:34:09,436 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:34:37,120 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-28 23:34:37,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:37:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:37:37,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:40:09,395 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-28 23:40:09,511 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:40:37,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:43:09,444 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:43:37,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:46:09,431 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:46:37,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:49:09,432 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:49:37,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:52:09,436 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:52:37,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:55:09,505 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:55:37,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:58:09,430 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-28 23:58:37,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
