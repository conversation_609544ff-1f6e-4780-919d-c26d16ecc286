2024-07-24 00:02:02,391 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:05:02,398 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:08:02,396 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:11:02,345 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-24 00:11:02,459 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:14:02,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:17:02,379 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:20:02,397 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:23:02,405 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:26:02,399 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:29:02,387 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:32:02,406 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:35:02,465 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:38:02,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:41:02,404 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:44:02,374 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:47:02,405 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:50:02,378 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:53:02,402 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:56:02,424 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 00:59:02,410 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:02:02,403 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:05:02,384 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:08:02,415 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:11:02,401 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:14:02,402 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:17:02,427 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:20:02,385 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:23:02,392 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:26:02,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:29:02,408 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:32:02,400 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:35:02,384 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:38:02,393 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:41:02,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:44:02,404 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:47:02,397 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:50:02,385 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:53:02,396 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:56:02,424 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 01:59:02,383 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:02:02,386 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:05:02,403 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:08:02,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:11:02,395 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:14:02,349 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-24 02:14:02,486 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:17:02,390 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:20:02,400 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:23:02,376 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:26:02,386 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:29:02,386 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:32:02,385 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:35:02,386 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:38:02,395 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:41:02,375 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:44:02,392 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:47:02,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 02:50:02,393 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-24 02:50:02,393 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-24 09:05:11,035 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-24 09:05:11,044 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-24 09:05:11,263 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-24 09:05:11,264 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-24 09:05:11,320 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-24 09:05:11,320 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-24 09:05:51,880 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-24 09:05:52,291 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-24 09:05:52,514 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-24 09:05:52,595 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-24 09:05:52,596 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-24 09:05:52,649 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-24 09:05:52,649 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-24 09:05:52,836 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-24 09:05:53,086 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4908
2024-07-24 09:05:53,317 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:05:53,496 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 09:06:12,342 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-24 09:06:12,476 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-24 09:06:12,787 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-24 09:06:27,170 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-24 09:06:27,319 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-24 09:06:27,537 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 09:06:27,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4908
2024-07-24 09:06:27,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-24 09:06:27,985 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9474
2024-07-24 09:06:38,268 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-24 09:06:38,328 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-24 09:06:38,329 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-24 09:06:38,451 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:06:38,556 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:06:39,702 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-24 09:06:39,892 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:06:39,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:06:42,087 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-24 09:08:02,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:08:02,864 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:08:06,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 54202
2024-07-24 09:08:08,598 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 800
2024-07-24 09:08:16,671 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-07-24 09:08:16,672 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-24 09:08:16,798 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 09:08:16,862 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:08:22,923 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2024-07-24 09:08:23,087 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300297\函知舉辦立頓產品113年中元節中大型點隨貨搭贈促進活動240703h16515P.docx
2024-07-24 09:08:23,087 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 09:08:23,088 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 09:12:46,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:12:46,222 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:12:48,135 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-24 09:12:57,553 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-24 09:12:57,618 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:12:57,689 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:13:00,370 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-24 09:13:00,535 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-24 09:25:51,509 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:25:51,572 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:25:59,112 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-24 09:27:45,844 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:27:45,912 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:28:24,929 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:28:24,992 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:32:37,746 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 607
2024-07-24 09:32:39,373 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:32:39,494 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:33:05,120 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:33:05,127 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:33:08,885 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-24 09:33:38,512 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:33:38,565 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:34:51,979 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:34:52,002 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:34:55,373 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 54202
2024-07-24 09:34:58,647 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:34:58,708 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 568
2024-07-24 09:35:13,867 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:35:13,933 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:35:28,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 830
2024-07-24 09:35:30,703 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:35:30,761 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:35:35,069 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知舉辦113年中元節PET535ml立頓奶茶系列產品盤商專案活動240703h16515P.docx
2024-07-24 09:35:35,069 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 09:35:35,069 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 09:36:03,389 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:36:03,406 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:36:22,621 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 773
2024-07-24 09:36:24,782 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:36:24,837 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:36:34,844 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-24 09:36:34,928 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 34848
2024-07-24 09:36:53,696 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-24 09:36:53,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 34848
2024-07-24 09:37:04,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 34848
2024-07-24 09:37:22,891 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 3551
2024-07-24 09:37:26,742 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:37:26,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:37:28,948 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:37:29,002 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:37:30,668 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:37:30,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:37:34,156 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:37:34,223 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:37:43,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 20292
2024-07-24 09:37:45,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 188
2024-07-24 09:37:45,462 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:38:10,875 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 24363
2024-07-24 09:38:43,572 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 09:38:43,694 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-24 09:38:43,708 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 09:38:53,627 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-24 09:39:00,337 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 16842
2024-07-24 09:39:02,554 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:39:02,597 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:39:05,010 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:39:05,060 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:39:07,130 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:39:07,174 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:39:08,939 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:39:08,986 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:39:40,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 172954
2024-07-24 09:41:28,453 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:41:28,534 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:41:57,826 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 172947
2024-07-24 09:42:03,015 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:42:03,067 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:48:03,329 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:48:03,378 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:48:06,632 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 103, in select_business_notification_download
    sql_result, http_status = select_business_notification_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 254, in wrapper
    json_data = json.loads(request.body)
  File "C:\python3.6\lib\json\__init__.py", line 354, in loads
    return _default_decoder.decode(s)
  File "C:\python3.6\lib\json\decoder.py", line 339, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\python3.6\lib\json\decoder.py", line 357, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2024-07-24 09:48:06,642 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 500 119025
2024-07-24 09:48:16,135 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 103, in select_business_notification_download
    sql_result, http_status = select_business_notification_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 254, in wrapper
    json_data = json.loads(request.body)
  File "C:\python3.6\lib\json\__init__.py", line 354, in loads
    return _default_decoder.decode(s)
  File "C:\python3.6\lib\json\decoder.py", line 339, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\python3.6\lib\json\decoder.py", line 357, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2024-07-24 09:48:16,138 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 500 119025
2024-07-24 09:50:29,464 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 103, in select_business_notification_download
    sql_result, http_status = select_business_notification_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 254, in wrapper
    json_data = json.loads(request.body)
  File "C:\python3.6\lib\json\__init__.py", line 354, in loads
    return _default_decoder.decode(s)
  File "C:\python3.6\lib\json\decoder.py", line 339, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "C:\python3.6\lib\json\decoder.py", line 357, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)
2024-07-24 09:50:29,465 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 500 119025
2024-07-24 09:53:31,853 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:53:31,888 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:55:05,218 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:55:05,277 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:55:07,694 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 173148
2024-07-24 09:55:28,833 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 172957
2024-07-24 09:56:11,164 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:11,209 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:13,798 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:13,827 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:14,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:15,025 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:16,536 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:16,594 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:18,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:18,417 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:20,221 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:20,281 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:22,213 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:22,268 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:23,587 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:23,639 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:25,361 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:25,416 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:27,188 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:27,225 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:28,514 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:28,569 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:29,569 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:56:29,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:35,370 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:56:35,751 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:56:38,651 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:56:38,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:56:43,884 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-24 09:56:48,153 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 09:56:48,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:56:51,622 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300558\通知自販客訴系統機台QRCode張貼注意事項240617h16733B.docx
2024-07-24 09:56:51,622 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-24 09:56:51,622 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-24 09:58:20,848 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:58:20,900 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:58:23,061 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:58:23,110 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:58:25,425 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300620\業推部人員工作職掌1130702(修改版)240702h15750B.docx
2024-07-24 09:58:25,426 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-24 09:58:25,426 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-24 09:58:32,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 09:58:32,606 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:58:34,843 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 174049
2024-07-24 09:58:47,651 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:58:47,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-24 09:58:51,286 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 561
2024-07-24 09:58:53,262 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 09:58:53,313 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 09:59:10,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 172954
2024-07-24 10:03:17,911 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:03:17,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 2676
2024-07-24 10:03:21,497 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 173148
2024-07-24 10:03:38,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 172956
2024-07-24 10:10:24,617 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-24 10:10:24,686 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-24 10:10:27,090 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:10:27,191 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:10:27,194 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-24 10:10:30,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 54202
2024-07-24 10:10:36,749 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:10:36,806 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:10:49,555 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知舉辦113年中元節PET535ml立頓奶茶系列產品盤商專案活動240703h16515P.docx
2024-07-24 10:10:49,555 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 10:10:49,556 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 10:10:56,934 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知舉辦113年中元節PET535ml立頓奶茶系列產品盤商專案活動240703h16515P.docx
2024-07-24 10:10:56,935 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 10:10:56,935 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 10:10:58,406 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知舉辦113年中元節PET535ml立頓奶茶系列產品盤商專案活動240703h16515P.docx
2024-07-24 10:10:58,407 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 10:10:58,407 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 10:10:59,514 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知113年中元節535立頓奶茶系列盤商專案活動-內文240703h16515A.docx
2024-07-24 10:10:59,514 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 10:10:59,514 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 10:11:00,819 _DownloadLetterInfo.select_letter_download 250 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300300\函知113年中元節535立頓奶茶系列盤商專案活動-內文240703h16515A.docx
2024-07-24 10:11:00,819 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-07-24 10:11:00,820 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-07-24 10:11:27,514 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 55138
2024-07-24 10:11:32,439 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 20292
2024-07-24 10:11:35,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 188
2024-07-24 10:11:35,569 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:11:53,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 24365
2024-07-24 10:12:58,558 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:12:58,568 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:13:04,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 43192
2024-07-24 10:13:12,221 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 527
2024-07-24 10:13:29,100 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 527
2024-07-24 10:13:30,936 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:13:30,964 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:24:14,855 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 527
2024-07-24 10:24:17,813 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:24:17,817 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:34:27,875 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-24 10:34:36,877 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:34:36,926 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:34:39,273 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 58422
2024-07-24 10:34:51,097 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:34:51,159 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:35:27,985 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:35:28,078 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:35:36,185 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:35:36,231 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:35:48,862 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 1281
2024-07-24 10:35:48,935 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:37:20,621 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:37:20,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 1281
2024-07-24 10:37:30,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 517
2024-07-24 10:37:30,663 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:37:35,021 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:37:35,065 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:39:40,855 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 517
2024-07-24 10:39:40,887 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:02,206 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 517
2024-07-24 10:40:02,231 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:04,536 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:04,565 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:40:06,379 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 568
2024-07-24 10:40:06,382 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:15,240 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 568
2024-07-24 10:40:15,289 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:16,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:40:16,093 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:17,843 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 568
2024-07-24 10:40:17,894 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:20,232 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:20,285 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:40:21,833 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:40:21,891 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 517
2024-07-24 10:43:49,766 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-24 10:44:30,251 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-24 10:44:30,251 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-24 10:44:32,207 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:44:32,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:44:32,270 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-24 10:44:35,054 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:44:42,233 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:44:42,303 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:44:49,654 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:44:49,720 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:44:51,896 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-07-24 10:44:52,011 _DownloadBusinessNotificationInfo.select_business_notification_price_download 466 INFO    => Excel file already exists: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300770\113年大潤發4072+4081中元檔 DM.IP 促銷通報(1130726-1130822)240717h16510B.xlsx
2024-07-24 10:44:52,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5819
2024-07-24 10:44:56,618 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
2024-07-24 10:45:22,351 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-24 10:45:22,480 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:45:22,720 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:45:22,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:45:22,788 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:45:23,276 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:45:23,284 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:45:25,489 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:47:13,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:47:13,193 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:47:13,298 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:47:13,317 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:47:13,643 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:47:13,655 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:48:16,940 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:48:22,626 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:49:38,979 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:49:39,169 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:49:39,182 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:49:39,227 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:49:39,547 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:49:39,582 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:49:41,827 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:50:13,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:50:25,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:50:25,359 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:50:25,461 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:50:25,478 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:50:25,783 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:50:25,802 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:50:27,674 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:50:30,893 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:51:22,636 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:52:39,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:53:13,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:53:25,381 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:53:32,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:53:32,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:53:32,349 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:53:32,385 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:53:32,644 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:53:32,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:53:35,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:53:47,268 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:53:47,406 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:53:47,512 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:53:47,523 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:53:47,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:53:47,864 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:54:07,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:54:22,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:54:39,006 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:54:39,275 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-24 10:54:39,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-24 10:54:39,405 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-24 10:54:39,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-24 10:54:39,466 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 8817
2024-07-24 10:54:41,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44992
2024-07-24 10:54:44,283 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:54:44,286 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:54:48,868 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:54:48,925 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:55:39,144 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:56:13,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:56:13,939 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:13,967 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:16,668 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:16,706 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:24,080 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:24,139 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:25,350 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:56:32,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:56:34,595 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:34,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:36,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:36,809 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:39,236 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:39,261 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:41,158 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:41,208 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:42,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:42,564 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:46,761 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:46,818 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:47,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:56:48,706 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:56:48,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:50,376 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:50,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:52,347 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:56:52,402 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:54,330 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:54,408 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:55,530 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:55,585 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:56:58,379 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:56:58,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:56:59,519 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:56:59,571 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:00,738 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:00,790 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:02,549 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:02,597 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:03,935 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:03,988 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:06,256 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:06,280 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:07,405 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:07,458 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:10,296 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:10,349 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:11,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:11,594 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:12,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:12,843 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:14,684 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:14,743 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:15,982 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:16,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:17,710 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:17,748 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:19,749 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:19,802 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:22,611 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:57:23,355 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:23,405 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:24,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:24,956 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:25,522 _DownloadBusinessNotificationInfo.select_business_notification_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300610\1130628通知協助配送「C&C x夏日松一夏-松菸動物園」贊助用產品240628h15750B.docx
2024-07-24 10:57:25,522 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-24 10:57:25,522 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-24 10:57:28,158 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:28,241 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:31,685 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:31,741 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:33,141 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:33,204 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:36,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:36,304 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:39,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:57:39,860 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:39,894 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:40,962 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:41,014 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:42,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:42,654 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:43,871 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:43,930 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:45,448 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:45,500 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:46,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:46,565 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:47,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:47,596 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:49,174 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:49,232 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-24 10:57:50,710 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:50,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:51,708 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:51,775 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:52,584 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:52,636 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:54,649 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:54,699 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:55,714 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:55,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:57,071 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:57:57,111 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:58,980 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:57:59,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:00,115 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:00,168 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:01,163 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:01,217 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:03,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:03,347 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:08,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:08,071 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:09,237 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:09,296 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:10,557 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:10,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:11,762 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:11,810 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:13,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:13,807 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:14,714 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:14,769 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:16,993 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:17,045 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:17,985 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:18,037 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:20,754 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:20,799 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:22,275 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:22,334 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:23,504 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:23,559 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:25,215 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:25,270 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:26,270 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:26,319 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:28,109 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:28,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:30,761 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:30,810 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:31,800 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165942
2024-07-24 10:58:34,143 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:34,199 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:39,597 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:58:42,668 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:42,735 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:44,085 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:44,138 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:47,297 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:47,355 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:48,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:48,863 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:50,070 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:50,114 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:51,918 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:51,969 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:52,903 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:52,957 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:54,021 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:54,069 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:55,712 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:55,758 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:56,872 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:56,923 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:57,995 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:58,048 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:58:59,567 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:58:59,613 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:01,660 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:01,723 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:02,842 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:02,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:06,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:06,468 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:09,699 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:09,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:11,506 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:11,555 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:12,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:12,417 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:13,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:59:13,276 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:13,334 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:14,128 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 10:59:14,179 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-24 10:59:25,384 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:59:32,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:59:41,329 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44768
2024-07-24 10:59:47,406 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 10:59:50,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 1272
2024-07-24 10:59:50,898 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-24 11:00:08,200 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 168184
2024-07-24 11:00:22,617 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:00:39,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:01:40,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:02:14,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:02:26,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:02:33,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:02:48,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:03:23,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:03:40,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:04:40,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:05:33,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:05:41,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:05:41,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:05:48,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:06:40,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:06:42,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:07:43,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:08:13,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:08:25,373 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:08:32,208 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:08:47,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:09:22,639 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:09:39,177 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:10:39,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:11:13,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:11:25,363 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:11:32,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:11:47,425 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:12:22,635 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:12:39,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:13:39,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:14:13,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:14:25,372 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:14:32,193 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:14:47,412 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:15:22,637 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:15:39,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:16:39,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:17:13,201 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:17:25,381 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:17:32,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:17:47,423 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:18:22,663 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:18:39,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:19:39,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:20:13,189 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:20:25,401 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:20:32,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:20:47,435 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:21:22,634 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:21:39,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:22:39,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:23:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:23:26,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:23:33,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:23:48,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:24:49,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:24:49,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:25:50,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:26:51,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:26:51,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:26:51,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:26:51,256 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:27:52,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:27:52,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:28:53,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:29:54,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:29:54,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:29:54,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:29:54,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:30:55,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:30:55,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:31:56,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:32:57,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:32:57,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:32:57,264 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:32:57,335 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:33:58,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:33:58,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:34:59,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:36:00,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:36:00,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:36:00,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:36:00,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:37:01,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:37:01,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:38:02,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:39:03,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:39:03,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:39:03,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:39:03,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:40:04,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:40:04,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:41:05,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:42:06,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:42:06,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:42:06,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:42:06,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:43:07,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:43:07,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:44:08,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:45:08,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:45:08,177 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:45:08,227 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:45:08,278 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:46:08,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:46:08,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:47:08,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:48:08,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:48:08,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:48:08,208 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:48:08,260 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:49:08,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:49:08,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:50:08,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:51:08,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:51:08,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:51:08,239 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:51:08,305 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:52:08,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:52:08,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:53:08,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:54:08,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:54:08,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:54:08,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:54:08,251 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:55:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:55:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:56:08,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:57:08,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:57:08,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:57:08,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:57:08,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:58:08,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:58:08,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 11:59:08,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:00:08,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:00:08,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:00:08,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:00:08,263 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:01:08,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:01:08,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:02:08,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:03:08,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:03:08,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:03:08,192 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:03:08,246 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:04:08,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:04:08,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:05:08,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:06:08,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:06:08,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:06:08,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:06:08,283 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:07:08,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:07:08,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:08:08,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:09:08,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:09:08,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:09:08,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:09:08,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:10:08,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:10:08,197 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:11:08,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:12:08,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:12:08,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:12:08,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:12:08,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:13:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:13:08,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:14:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:15:08,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:15:08,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:15:08,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:15:08,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:16:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:16:08,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:17:08,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:18:08,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:18:08,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:18:08,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:18:08,255 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:19:08,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:19:08,184 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:20:08,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:21:08,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:21:08,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:21:08,208 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:21:08,257 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:22:08,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:22:08,171 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:23:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:24:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:24:08,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:24:08,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:24:08,300 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:25:08,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:25:08,171 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:26:08,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:27:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:27:08,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:27:08,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:27:08,256 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:28:08,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:28:08,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:29:08,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:30:08,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:30:08,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:30:08,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:30:08,274 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:31:08,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:31:08,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:32:08,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:33:08,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:33:08,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:33:08,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:33:08,272 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:34:08,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:34:08,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:35:08,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:36:08,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:36:08,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:36:08,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:36:08,279 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:37:08,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:37:08,184 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:38:08,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:39:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:39:08,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:39:08,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:39:08,263 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:40:08,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:40:08,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:41:08,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:42:08,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:42:08,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:42:08,247 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:42:08,301 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:43:08,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:43:08,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:44:08,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:45:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:45:08,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:45:08,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:45:08,269 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:46:08,062 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-24 12:46:08,063 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-24 12:46:08,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:46:08,249 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:47:08,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:48:08,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:48:08,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:48:08,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:48:08,273 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:49:08,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:49:08,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:50:08,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:51:08,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:51:08,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:51:08,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:51:08,240 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:52:08,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:52:08,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:53:08,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:54:08,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:54:08,135 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:54:08,198 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:54:08,253 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:55:08,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:55:08,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:56:08,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:57:08,418 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:57:08,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:57:08,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:57:08,434 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:58:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:58:08,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 12:59:08,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:00:08,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:00:08,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:00:08,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:00:08,280 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:01:08,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:01:08,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:02:08,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:03:08,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:03:08,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:03:08,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:03:08,257 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:04:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:04:08,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:05:08,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:06:08,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:06:08,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:06:08,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:06:08,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-24 13:07:08,084 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-24 13:07:08,084 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-24 13:07:08,136 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-24 13:07:08,136 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
