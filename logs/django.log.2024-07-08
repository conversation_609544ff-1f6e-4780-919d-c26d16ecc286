2024-07-08 00:02:28,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:02:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:05:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:05:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:08:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:08:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:11:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:11:29,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:14:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:14:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:17:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:17:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:20:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:20:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:23:29,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:23:29,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:26:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:26:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:29:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:29:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:32:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:32:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:35:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:35:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:38:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:38:29,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:41:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:41:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:44:28,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:44:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 00:47:29,016 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:47:29,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:50:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:50:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:53:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:53:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:56:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:56:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 00:59:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 00:59:29,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:02:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:02:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:05:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:05:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:08:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:08:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:11:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:11:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:14:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:14:29,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:17:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:17:29,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:20:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:20:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:23:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:23:29,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:26:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:26:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:29:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:29:29,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:32:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:32:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:35:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:35:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:38:29,006 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 01:38:29,053 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:38:29,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:41:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:41:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:44:29,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:44:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:47:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:47:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:50:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:50:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:53:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:53:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:56:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:56:29,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 01:59:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 01:59:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:02:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:02:29,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:05:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:05:29,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:08:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:08:29,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:11:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:11:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:14:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:14:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:17:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:17:29,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:20:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:20:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:23:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:23:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:26:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:26:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:29:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:29:29,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:32:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:32:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:35:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:35:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:38:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:38:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:41:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:41:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:44:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:44:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:47:29,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:47:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:50:29,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:50:29,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:53:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:53:29,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:56:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:56:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 02:59:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 02:59:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:02:29,006 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:02:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:05:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:05:29,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:08:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:08:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:11:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:11:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:14:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:14:29,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:17:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:17:29,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:20:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:20:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:23:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:23:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:26:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:26:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:29:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:29:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:32:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:32:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:35:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:35:29,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:38:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:38:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:41:29,002 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 03:41:29,049 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:41:29,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:44:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:44:29,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:47:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:47:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:50:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:50:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:53:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:53:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:56:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:56:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 03:59:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 03:59:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:02:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:02:29,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:05:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:05:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:08:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:08:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:11:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:11:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:14:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:14:29,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:17:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:17:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:20:28,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:20:29,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:23:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:23:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:26:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:26:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:29:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:29:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:32:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:32:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:35:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:35:29,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 04:38:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:38:29,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:41:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:41:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:44:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:44:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:47:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:47:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:50:29,037 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:50:29,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:53:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:53:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:56:28,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:56:29,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 04:59:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 04:59:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:02:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:02:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:05:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:05:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:08:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:08:29,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:11:29,050 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:11:29,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:14:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:14:29,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:17:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:17:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:20:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:20:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:23:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:23:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:26:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:26:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:29:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:29:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:32:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:32:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:35:29,010 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:35:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:38:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:38:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:41:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:41:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:44:29,011 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 05:44:29,055 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:44:29,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:47:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:47:29,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:50:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:50:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:53:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:53:29,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:56:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:56:29,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 05:59:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 05:59:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:02:28,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:02:29,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:05:28,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:05:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:08:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:08:29,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:11:29,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:11:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:14:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:14:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:17:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:17:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:20:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:20:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:23:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:23:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:26:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:26:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:29:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:29:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:32:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:32:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:35:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:35:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:38:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:38:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:41:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:41:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:44:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:44:29,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:47:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:47:29,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:50:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:50:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:53:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:53:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:56:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:56:29,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 06:59:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 06:59:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:02:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:02:29,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:05:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:05:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:08:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:08:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:11:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:11:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:14:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:14:29,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:17:28,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:17:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:20:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:20:29,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:23:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:23:29,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:26:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:26:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:29:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:29:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:32:13,696 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 07:32:13,696 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 07:32:13,772 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:32:13,775 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:32:13,843 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 07:32:13,892 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 07:32:29,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:32:29,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:35:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:35:29,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:38:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:38:29,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:41:29,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:41:29,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:44:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:44:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:47:29,003 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 07:47:29,051 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:47:29,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:50:28,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:50:29,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:53:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:53:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:56:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:56:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 07:59:28,996 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 07:59:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:02:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:02:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:05:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:05:29,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:08:29,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:08:29,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:11:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:11:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:14:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:14:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:17:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:17:29,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:20:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:20:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:23:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:23:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:26:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:26:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 08:29:28,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:29:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:29:33,536 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:29:33,540 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:29:33,601 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 08:29:33,652 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 08:32:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:32:29,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:35:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:35:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:38:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:38:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:41:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:41:29,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:44:28,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:44:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:47:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:47:29,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:50:29,020 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:50:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:53:29,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:53:29,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:56:29,028 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:29,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:56:39,122 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 08:56:39,347 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,414 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 08:56:39,474 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,550 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 08:56:39,550 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-08 08:56:39,551 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-08 08:56:39,694 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,695 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,696 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,703 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 08:56:39,781 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,782 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,782 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,859 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,860 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,860 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,883 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-08 08:56:39,912 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 08:56:39,913 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,914 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,966 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:39,968 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:40,080 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-08 08:56:40,087 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:40,167 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-08 08:56:40,173 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-08 08:56:40,173 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-08 08:56:40,182 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:40,182 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:56:40,208 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-08 08:56:40,240 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-08 08:56:40,287 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-08 08:56:40,330 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-08 08:56:40,552 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-08 08:59:40,219 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 08:59:40,308 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:02:40,089 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:02:40,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:05:40,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:05:40,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:08:40,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:08:40,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:11:39,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:11:40,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:14:39,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:14:40,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:17:39,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:17:40,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:20:39,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:20:40,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:23:40,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:23:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:26:40,029 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:26:40,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:29:39,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:29:40,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:32:40,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:32:40,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:35:40,044 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:35:40,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:38:40,021 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:38:40,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:40:45,287 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 09:40:45,288 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 09:40:45,339 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:40:45,368 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:40:45,429 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 09:40:45,499 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 09:41:40,025 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:41:40,250 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:44:39,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:44:40,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:47:40,029 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 09:47:40,086 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:47:40,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:50:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:50:40,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:53:39,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:53:40,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:56:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:56:40,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 09:59:39,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 09:59:40,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:02:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:02:40,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:05:40,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:05:40,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:08:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:08:40,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:11:39,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:11:40,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:14:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:14:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:17:39,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:17:40,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:20:39,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:20:40,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:23:39,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:23:40,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:26:39,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:26:40,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:29:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:29:40,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:32:39,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:32:40,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:35:40,017 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:35:40,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:38:40,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:38:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:41:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:41:40,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:44:39,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:44:40,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:47:39,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:47:40,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:49:36,904 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:49:36,908 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:49:36,984 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 10:49:37,037 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 10:50:39,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:50:40,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:53:39,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:53:40,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:56:40,020 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:56:40,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 10:59:39,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 10:59:40,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:02:40,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:02:40,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:05:39,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:05:40,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:08:39,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:08:40,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:11:40,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:11:40,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:14:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:14:40,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:17:39,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:17:40,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:20:39,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:20:40,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:23:40,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:23:40,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:26:40,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:26:40,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:29:39,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:29:40,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:32:40,024 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:32:40,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:35:40,020 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:35:40,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:38:39,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:38:40,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:41:39,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:41:40,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:44:40,089 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:44:40,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:47:40,045 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:47:40,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:50:40,036 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 11:50:40,161 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:50:40,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:53:39,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:53:40,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:56:39,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:56:40,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 11:59:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 11:59:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:02:40,060 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:02:40,131 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:05:40,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:05:40,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:08:39,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:08:40,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:11:39,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:11:40,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:14:40,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:14:40,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:17:40,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:17:40,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 12:20:40,044 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:20:40,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:23:40,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:23:40,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:26:40,057 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:26:40,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:29:39,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:29:40,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:32:39,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:32:40,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:35:39,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:35:40,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:38:39,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:38:40,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:41:40,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:41:40,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:44:39,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:44:40,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:47:39,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:47:40,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:50:40,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:50:40,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:53:39,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:53:40,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:56:39,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:56:40,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 12:59:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 12:59:40,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:02:40,039 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:02:40,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:05:39,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:05:40,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:08:40,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:08:40,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:11:39,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:11:40,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:14:39,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:14:40,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:17:39,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:17:40,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:20:39,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:20:40,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:23:39,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:23:40,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:26:39,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:26:40,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:29:39,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:29:40,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:32:40,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:32:40,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:35:39,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:35:40,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:36:04,597 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 13:36:04,597 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 13:36:04,695 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:36:04,700 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:36:04,766 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 13:36:04,820 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 13:38:39,341 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:38:39,412 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:41:39,338 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:41:39,416 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:44:39,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:44:40,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:47:40,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:47:40,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:50:40,026 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:50:40,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:53:40,007 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 13:53:40,058 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:53:40,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:56:39,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:56:40,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 13:59:40,036 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 13:59:40,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:01:59,170 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,255 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:01:59,435 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,498 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 14:01:59,580 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,659 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 14:01:59,659 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-08 14:01:59,659 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-08 14:01:59,788 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,789 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,790 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,796 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 14:01:59,867 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,867 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,891 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,943 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,944 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,945 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,987 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-08 14:01:59,996 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-08 14:01:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:01:59,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,049 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,050 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,156 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-08 14:02:00,203 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,211 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-08 14:02:00,217 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,231 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11614
2024-07-08 14:02:00,239 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:02:00,255 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-08 14:02:00,270 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-08 14:02:00,324 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-08 14:02:00,390 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-08 14:02:00,426 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-08 14:02:00,485 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-08 14:04:59,359 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:04:59,423 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:08:00,001 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:08:00,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:10:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:11:00,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:13:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:14:00,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:16:59,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:17:00,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:19:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:20:00,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:22:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:23:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:26:00,061 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:26:00,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:28:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:29:00,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:31:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:32:00,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:35:00,033 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:35:00,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:37:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:38:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:41:00,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:41:00,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:43:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:44:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:46:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:47:00,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:50:00,087 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:50:00,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:52:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:53:00,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:56:00,106 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:56:00,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 14:59:00,128 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 14:59:00,189 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:02:00,031 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:02:00,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:05:00,102 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:05:00,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:07:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:08:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:11:00,116 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:11:00,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:13:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:14:00,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:16:59,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:17:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:20:00,114 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:20:00,186 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:22:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:23:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:25:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:26:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:28:59,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:29:00,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:31:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:32:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:35:00,036 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:35:00,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:37:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:38:00,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:41:00,030 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:41:00,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:43:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:44:00,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:47:00,033 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:47:00,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:49:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:50:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:53:00,045 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:53:00,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:56:00,021 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 15:56:00,079 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:56:00,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 15:59:00,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 15:59:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:02:00,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:02:00,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:02:45,271 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 16:02:45,271 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 16:02:45,351 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:02:45,355 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:02:45,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 16:02:45,469 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 16:04:59,356 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:04:59,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:07:59,354 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:07:59,426 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 16:10:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:11:00,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:13:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:14:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:17:00,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:17:00,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:19:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:20:00,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:22:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:23:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:26:00,094 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:26:00,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:28:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:29:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:31:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:32:00,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:34:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:35:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:37:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:38:00,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:40:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:41:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:43:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:44:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:46:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:47:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:49:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:50:00,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:53:00,212 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:53:00,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:56:00,196 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:56:00,271 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 16:58:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 16:59:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:01:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:02:00,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:04:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:05:00,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:07:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:08:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:11:00,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:11:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:14:00,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:14:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:15:36,920 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:15:36,923 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:15:36,990 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-08 17:15:37,052 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 17:15:38,324 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-08 17:15:38,375 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:15:38,583 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-08 17:16:59,355 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:16:59,467 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:17:16,929 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:17:17,022 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-08 17:17:17,091 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-08 17:17:17,142 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:17:17,285 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-08 17:18:07,763 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:18:07,855 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-08 17:18:12,124 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:18:12,189 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-08 17:18:12,247 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:18:12,366 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-08 17:19:59,996 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:20:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:22:59,996 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:23:00,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:25:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:26:00,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:28:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:29:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:31:59,362 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:31:59,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:35:00,059 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:35:00,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:37:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:38:00,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:40:59,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:41:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:43:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:44:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:46:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:47:00,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:49:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:50:00,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:53:00,044 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:53:00,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:56:00,031 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:56:00,299 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 17:59:00,005 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 17:59:00,065 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 17:59:00,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:02:00,050 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:02:00,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:05:00,054 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:05:00,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:08:00,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:08:00,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:10:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:11:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:14:00,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:14:00,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:17:00,045 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:17:00,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:19:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:20:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:22:24,274 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 18:22:24,274 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-08 18:22:24,351 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:22:24,355 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:22:24,436 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-08 18:22:24,502 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-08 18:23:00,055 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:23:00,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:26:00,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:26:00,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:28:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:29:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:31:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:32:00,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:34:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:35:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:37:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:38:00,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:40:59,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:41:00,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:44:00,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:44:00,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:46:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:47:00,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:49:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:50:00,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:52:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:53:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:55:59,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:56:00,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 18:58:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 18:59:00,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:01:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:02:00,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:04:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:05:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:07:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:08:00,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:10:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:11:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:13:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:14:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:16:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:17:00,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:19:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:20:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:22:59,999 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:23:00,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:25:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:26:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:28:59,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:29:00,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:31:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:32:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:34:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:35:00,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:38:00,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:38:00,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:40:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:41:00,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:43:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:44:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:46:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:47:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:49:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:50:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:52:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:53:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:55:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:56:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 19:58:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 19:59:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 20:02:00,008 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 20:02:00,066 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:02:00,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:05:00,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:05:00,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:07:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:08:00,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:10:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:11:00,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:13:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:14:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:16:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:17:00,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:19:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:20:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:22:59,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:23:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:25:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:26:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:28:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:29:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:31:59,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:32:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:34:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:35:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:37:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:38:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:40:59,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:41:00,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:43:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:44:00,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:46:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:47:00,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:49:59,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:50:00,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:52:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:53:00,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:55:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:56:00,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 20:58:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 20:59:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:01:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:02:00,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:04:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:05:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:07:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:08:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:10:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:11:00,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:13:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:14:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:16:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:17:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:19:59,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:20:00,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:22:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:23:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:26:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:29:00,032 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:29:00,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:31:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:32:00,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:34:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:35:00,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:37:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:38:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:41:00,014 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:41:00,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:43:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:44:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:46:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:47:00,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:49:59,972 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:50:00,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:52:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:53:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:55:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:56:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 21:58:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 21:59:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:01:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:02:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:05:00,014 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-08 22:05:00,062 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:05:00,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:07:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:08:00,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:10:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:11:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:13:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:14:00,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:16:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:17:00,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:19:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:20:00,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:23:00,010 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:23:00,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:25:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:26:00,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:28:59,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:29:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:31:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:32:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:34:59,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:35:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:37:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:38:00,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:40:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:41:00,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:43:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:44:00,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:46:59,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:47:00,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:49:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:50:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:52:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:53:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:55:59,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:56:00,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 22:58:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 22:59:00,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:01:59,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:02:00,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:05:00,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:05:00,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:07:59,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:08:00,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:11:00,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:11:00,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:13:59,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:14:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:16:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:17:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:19:59,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:20:00,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:22:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:23:00,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:25:59,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:26:00,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:28:59,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:29:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:31:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:32:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:34:59,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:35:00,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:37:59,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:38:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:40:59,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:41:00,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:43:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:44:00,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:46:59,997 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:47:00,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:49:59,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:50:00,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-08 23:52:59,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:53:00,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:55:59,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:56:00,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-08 23:58:59,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-08 23:59:00,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
