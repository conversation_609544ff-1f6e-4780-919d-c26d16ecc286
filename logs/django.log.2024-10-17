2024-10-17 09:54:19,724 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 09:55:18,333 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 09:55:18,334 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-10-17 09:55:33,695 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 09:55:33,696 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-10-17 09:55:33,831 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-10-17 09:55:33,831 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-10-17 09:55:33,832 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-10-17 09:55:33,892 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-10-17 09:55:41,591 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-10-17 09:55:41,747 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-10-17 09:55:41,997 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 09:55:42,063 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 09:55:42,064 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-10-17 09:55:42,115 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-10-17 09:55:42,115 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-10-17 09:55:42,286 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-10-17 09:55:42,372 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6784
2024-10-17 09:55:42,844 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-10-17 09:55:42,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-10-17 09:55:45,853 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-10-17 09:55:45,984 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-10-17 09:55:49,185 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-10-17 09:55:49,249 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,250 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,250 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,274 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,302 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,302 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,356 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:49,519 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-10-17 09:55:49,638 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:55:49,686 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-10-17 09:55:49,709 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 52487
2024-10-17 09:55:49,739 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-10-17 09:55:49,796 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-10-17 09:55:49,835 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-10-17 09:55:50,990 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-10-17 09:55:51,275 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49178
2024-10-17 09:55:52,814 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-10-17 09:55:52,874 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-10-17 09:55:53,034 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:55:53,829 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-10-17 09:55:54,125 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 8900
2024-10-17 09:55:57,219 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-10-17 09:56:12,001 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-10-17 09:56:12,059 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 52487
2024-10-17 09:56:12,113 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-10-17 09:56:12,160 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:56:12,219 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-10-17 09:56:12,262 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-10-17 09:56:12,324 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-10-17 09:56:13,568 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49178
2024-10-17 09:56:19,177 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:56:20,681 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 8900
2024-10-17 09:56:22,257 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-10-17 09:56:23,599 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 09:56:23,779 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:56:24,923 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-10-17 09:56:25,436 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 262183
2024-10-17 09:56:30,030 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-10-17 09:56:30,193 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:56:32,414 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-10-17 09:56:32,609 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:56:39,284 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-10-17 09:56:39,488 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:56:48,977 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-10-17 09:56:49,028 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-10-17 09:56:49,028 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-10-17 09:56:49,175 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:56:49,236 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:56:49,239 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:56:53,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-10-17 09:56:53,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:56:53,434 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:56:53,552 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:56:56,508 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:56:56,559 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:56:56,562 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:57:01,661 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:57:01,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:57:01,817 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:57:02,388 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-10-17 09:57:02,616 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6784
2024-10-17 09:57:05,355 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-10-17 09:57:05,534 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:57:06,698 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-10-17 09:57:06,755 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-10-17 09:57:07,176 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-10-17 09:57:08,477 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:57:09,286 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 0
2024-10-17 09:57:09,499 basehttp.log_message 161 INFO    => "POST /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 72880
2024-10-17 09:57:11,384 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-17 09:57:14,949 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:57:15,055 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:57:15,095 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:57:16,600 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:57:16,606 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:57:16,710 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:57:17,155 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:57:17,158 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:57:17,265 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 09:57:17,798 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-10-17 09:57:17,947 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 43026
2024-10-17 09:59:03,456 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 09:59:03,461 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 09:59:07,254 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 43026
2024-10-17 10:04:00,962 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 10:04:00,964 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:04:01,075 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:04:01,606 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:04:01,609 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 10:04:01,757 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:04:10,510 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-10-17 10:04:11,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:04:11,312 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:04:14,731 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:04:14,827 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:04:17,104 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-10-17 10:04:17,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-10-17 10:04:26,218 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41775
2024-10-17 10:04:57,679 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:04:57,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:04:57,802 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 10:04:59,341 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-10-17 10:04:59,891 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 70
2024-10-17 10:05:00,106 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 70
2024-10-17 10:05:00,196 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:05:00,292 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:05:02,753 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-10-17 10:05:03,098 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-10-17 10:05:04,019 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:05:04,024 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 10:05:04,129 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:05:05,532 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:05:05,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:05:11,488 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:05:11,490 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:05:35,180 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 45715
2024-10-17 10:05:41,525 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41116
2024-10-17 10:06:17,217 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-10-17 10:06:17,249 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:06:17,252 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:06:19,884 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 141288
2024-10-17 10:06:25,215 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:06:25,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 70
2024-10-17 10:06:30,402 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 133431
2024-10-17 10:08:48,809 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-10-17 10:08:49,204 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1589
2024-10-17 10:08:52,890 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-10-17 10:08:52,893 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-10-17 10:08:53,013 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:08:53,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:08:53,884 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-10-17 10:08:54,004 _DownloadBusinessNotificationInfo.select_business_notification_download 324 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300921\楓康113年10檔促銷活動通報(1004-1107) - 發文241001h37314B.docx
2024-10-17 10:08:54,004 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-10-17 10:08:54,005 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-10-17 10:16:07,934 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 65316
2024-10-17 10:16:55,383 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 10:17:02,313 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-10-17 10:17:02,498 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-10-17 10:17:09,108 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-10-17 10:17:09,248 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 10:17:09,474 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 10:17:09,637 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6784
2024-10-17 10:17:09,892 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 10:17:09,938 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 10:17:16,247 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 10:17:16,368 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:17:16,481 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:17:26,098 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2198
2024-10-17 10:17:27,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:17:27,595 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:17:28,361 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-10-17 10:17:30,859 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 未定義 10201 的位置
2024-10-17 10:17:31,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-10-17 10:18:10,930 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 未定義 10201 的位置
2024-10-17 10:18:11,113 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-10-17 10:18:22,352 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-10-17 10:18:22,483 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:18:22,676 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 10:18:23,012 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 10:18:23,084 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 10:18:23,192 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 10:18:23,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 10:18:23,299 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 10:18:23,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 10:18:31,376 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-10-17 10:18:31,745 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-10-17 10:18:32,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2198
2024-10-17 10:19:06,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:19:06,766 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:19:09,770 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 未定義 10201 的位置
2024-10-17 10:19:09,945 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-10-17 10:20:33,745 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 10:20:41,707 _DownloadBusinessNotificationInfo.extract_table_data 465 ERROR   => 未定義 10201 的位置
2024-10-17 10:20:41,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-10-17 10:20:46,363 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2198
2024-10-17 10:21:23,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:21:36,577 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 10:21:40,141 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:21:40,192 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:21:43,334 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 未定義 10201 的位置
2024-10-17 10:21:43,561 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-10-17 10:24:23,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:25:31,121 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 10:25:39,377 _DownloadBusinessNotificationInfo.extract_table_data 464 ERROR   => 未定義 10201 的位置
2024-10-17 10:25:39,618 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5039
2024-10-17 10:26:48,430 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 10:26:53,591 _DownloadBusinessNotificationInfo.extract_table_data 466 ERROR   => 未定義 10201 的位置
2024-10-17 10:26:53,784 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5038
2024-10-17 10:27:22,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:29:41,331 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:29:41,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:29:43,557 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2198
2024-10-17 10:29:46,263 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-10-17 10:29:46,318 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-10-17 10:29:58,893 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:58,906 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,051 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,053 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,055 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,057 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,059 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,216 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-10-17 10:29:59,386 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6042
2024-10-17 10:30:23,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:34:04,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:37:04,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:40:04,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:43:04,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:43:40,318 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 102395
2024-10-17 10:45:22,671 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:48:22,676 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:49:22,171 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 10:49:22,231 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 10:52:04,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:55:04,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 10:56:38,082 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 102395
2024-10-17 10:56:48,828 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 102395
2024-10-17 10:57:23,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:00:23,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:04:04,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:06:23,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:10:04,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:10:34,979 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 102395
2024-10-17 11:10:54,876 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 102395
2024-10-17 11:11:07,181 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 11:11:13,813 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:11:13,949 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 11:11:14,186 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 11:11:14,292 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 11:11:14,408 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 11:11:14,517 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 11:11:14,621 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 11:11:14,678 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 11:11:16,458 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51418
2024-10-17 11:11:18,828 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128899
2024-10-17 11:14:14,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:17:14,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:20:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:23:14,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:27:04,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:30:04,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:33:04,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:36:04,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:38:37,418 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:41:13,960 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:42:27,161 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 11:42:27,218 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 11:45:04,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:47:14,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:49:58,433 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128955
2024-10-17 11:50:14,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:54:04,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:56:31,527 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 11:59:14,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:03:04,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:06:04,411 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_check_user/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
cx_Oracle.DatabaseError: ORA-12505: TNS:listener does not currently know of SID given in connect descriptor

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 93, in select_check_user
    sql_result, http_status = select_verify_user(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\_UserVerifyInfo.py", line 16, in select_verify_user
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 37, in verify_access_token
    with connection.cursor() as cursor:
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 259, in cursor
    return self._cursor()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 235, in _cursor
    self.ensure_connection()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
django.db.utils.DatabaseError: ORA-12505: TNS:listener does not currently know of SID given in connect descriptor
2024-10-17 12:06:04,417 basehttp.log_message 161 ERROR   => "POST /api/users/select_check_user/ HTTP/1.1" 500 177836
2024-10-17 12:06:05,265 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-10-17 12:06:05,585 log.log_response 230 ERROR   => Internal Server Error: /api/accounts/logout/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
cx_Oracle.DatabaseError: ORA-12505: TNS:listener does not currently know of SID given in connect descriptor

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\accounts\views.py", line 39, in logout
    sql_result, http_status = select_logout(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\accounts\accountInfo.py", line 200, in select_logout
    with connection.cursor() as cursor:
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 259, in cursor
    return self._cursor()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 235, in _cursor
    self.ensure_connection()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
django.db.utils.DatabaseError: ORA-12505: TNS:listener does not currently know of SID given in connect descriptor
2024-10-17 12:06:05,591 basehttp.log_message 161 ERROR   => "POST /api/accounts/logout/ HTTP/1.1" 500 173854
2024-10-17 12:09:04,945 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:09:05,040 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 12:09:05,159 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-10-17 12:09:05,159 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-10-17 12:09:05,160 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-10-17 12:09:05,452 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 12:09:06,319 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6784
2024-10-17 12:09:06,805 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 12:09:06,810 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 12:12:04,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:15:04,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:16:42,706 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 12:16:42,828 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 12:17:14,246 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:20:13,979 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-10-17 12:20:14,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:24:04,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:27:04,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:30:04,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:33:04,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:36:04,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:39:04,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:42:04,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:45:04,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:48:04,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:51:04,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:53:51,636 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 12:53:51,760 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:54:49,628 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 12:54:49,779 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 12:54:50,041 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 12:54:50,100 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6784
2024-10-17 12:54:50,439 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 12:54:50,442 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 12:54:53,981 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-10-17 12:54:54,211 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-10-17 12:54:55,367 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-10-17 12:54:55,421 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,544 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,545 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,545 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,546 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,546 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,599 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 12:54:55,760 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-10-17 12:54:55,817 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 52487
2024-10-17 12:54:55,855 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 12:54:55,914 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-10-17 12:54:55,967 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-10-17 12:54:56,008 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-10-17 12:54:56,059 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-10-17 12:57:50,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:00:50,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:03:50,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:05:43,609 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-10-17 13:05:43,893 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49178
2024-10-17 13:05:55,063 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 13:05:55,117 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-10-17 13:05:55,270 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 13:05:55,272 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 13:05:56,461 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-10-17 13:05:56,732 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128955
2024-10-17 13:06:49,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:07:20,172 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128955
2024-10-17 13:07:33,258 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128955
2024-10-17 13:09:33,903 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 13:09:33,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 13:09:34,971 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 128955
2024-10-17 13:09:49,782 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:09:57,769 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 13:10:08,331 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:10:08,499 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 13:10:08,765 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 13:10:08,801 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 13:10:08,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 13:10:09,026 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 13:10:09,094 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 13:10:09,176 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 13:10:12,729 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53330
2024-10-17 13:13:09,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:14:48,090 log.log_response 230 WARNING => Not Found: /api2/blog/
2024-10-17 13:14:48,091 basehttp.log_message 161 WARNING => "GET /api2/blog/ HTTP/1.1" 404 2289
2024-10-17 13:16:09,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:16:38,040 log.log_response 230 WARNING => Not Found: /api2/blog/
2024-10-17 13:16:38,040 basehttp.log_message 161 WARNING => "GET /api2/blog/ HTTP/1.1" 404 2289
2024-10-17 13:19:09,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:22:09,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:26:04,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:29:04,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:32:04,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:35:04,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:38:04,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:41:04,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:44:04,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:47:04,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:50:04,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:53:04,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:56:04,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 13:59:04,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:02:04,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:05:04,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:08:04,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:11:04,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:14:04,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:17:04,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:20:04,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:23:04,010 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-10-17 14:23:04,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:26:04,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:26:10,343 log.log_response 230 WARNING => Not Found: /api2/blog/
2024-10-17 14:26:10,343 basehttp.log_message 161 WARNING => "GET /api2/blog/ HTTP/1.1" 404 2289
2024-10-17 14:26:31,852 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 14:26:31,855 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 14:26:32,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 14:26:32,074 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 14:29:04,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:32:04,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:35:04,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:38:04,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:41:04,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:44:04,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:47:04,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:50:04,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:53:04,328 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:56:04,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 14:59:04,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:02:04,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:05:04,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:08:04,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:08:55,129 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 15:08:55,135 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 15:10:08,476 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:13:09,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:17:04,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:20:04,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:23:04,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:26:04,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:29:04,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:32:04,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:35:04,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:38:04,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:41:04,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:41:42,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 15:41:42,054 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 15:43:09,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:44:08,483 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 15:44:17,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:44:17,390 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 15:44:17,567 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 15:44:17,567 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-10-17 15:44:17,568 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-10-17 15:44:17,702 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 15:44:17,755 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-10-17 15:44:17,756 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-17 15:44:17,864 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 15:44:17,929 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 15:44:18,033 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 15:44:18,096 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 15:44:18,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 15:44:18,909 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-10-17 15:44:19,035 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53330
2024-10-17 15:44:42,308 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:44:42,464 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 15:44:42,638 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 15:44:42,716 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-17 15:44:42,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 15:44:42,953 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 15:44:43,096 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4457
2024-10-17 15:44:43,105 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-17 15:44:44,619 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53330
2024-10-17 15:45:30,387 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 15:47:43,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:50:43,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:50:55,002 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53330
2024-10-17 15:51:14,803 basehttp.log_message 161 INFO    => "OPTIONS /api/products/insert_prod_code_map/ HTTP/1.1" 200 0
2024-10-17 15:51:14,928 error_utils.handle_error  13 ERROR   => insert_prod_code_map_method - Unexpected error: 'owner_id'
2024-10-17 15:51:15,061 log.log_response 230 ERROR   => Internal Server Error: /api/products/insert_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 145, in insert_prod_code_map
    return self._handle_action('prodcodemap', 'insert')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'KeyError' is not JSON serializable
2024-10-17 15:51:15,069 basehttp.log_message 161 ERROR   => "POST /api/products/insert_prod_code_map/ HTTP/1.1" 500 137107
2024-10-17 15:52:43,225 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53330
2024-10-17 15:52:52,558 basehttp.log_message 161 INFO    => "POST /api/products/insert_prod_code_map/ HTTP/1.1" 200 69
2024-10-17 15:52:52,680 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-17 15:53:43,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:54:54,744 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-17 15:55:08,592 basehttp.log_message 161 INFO    => "OPTIONS /api/products/update_prod_code_map/ HTTP/1.1" 200 0
2024-10-17 15:55:08,771 error_utils.handle_error  13 ERROR   => update_prod_code_map_method - Unexpected error: ORA-00001: unique constraint (B2B.PROD_CODE_MAP_KEY) violated
2024-10-17 15:55:08,797 log.log_response 230 ERROR   => Internal Server Error: /api/products/update_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 150, in update_prod_code_map
    return self._handle_action('prodcodemap', 'update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'IntegrityError' is not JSON serializable
2024-10-17 15:55:08,798 basehttp.log_message 161 ERROR   => "POST /api/products/update_prod_code_map/ HTTP/1.1" 500 137420
2024-10-17 15:55:37,785 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 15:55:45,552 error_utils.handle_error  13 ERROR   => update_prod_code_map_method - Unexpected error: ORA-00001: unique constraint (B2B.PROD_CODE_MAP_KEY) violated
2024-10-17 15:55:45,596 log.log_response 230 ERROR   => Internal Server Error: /api/products/update_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 150, in update_prod_code_map
    return self._handle_action('prodcodemap', 'update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'IntegrityError' is not JSON serializable
2024-10-17 15:55:45,602 basehttp.log_message 161 ERROR   => "POST /api/products/update_prod_code_map/ HTTP/1.1" 500 137420
2024-10-17 15:56:45,588 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 15:56:49,912 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 15:56:54,546 error_utils.handle_error  13 ERROR   => update_prod_code_map_method - Unexpected error: ORA-00001: unique constraint (B2B.PROD_CODE_MAP_KEY) violated
2024-10-17 15:56:54,586 log.log_response 230 ERROR   => Internal Server Error: /api/products/update_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 150, in update_prod_code_map
    return self._handle_action('prodcodemap', 'update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'IntegrityError' is not JSON serializable
2024-10-17 15:56:54,591 basehttp.log_message 161 ERROR   => "POST /api/products/update_prod_code_map/ HTTP/1.1" 500 137420
2024-10-17 15:57:46,081 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 15:57:53,839 error_utils.handle_error  13 ERROR   => update_prod_code_map_method - Unexpected error: ORA-00001: unique constraint (B2B.PROD_CODE_MAP_KEY) violated
2024-10-17 15:57:53,904 log.log_response 230 ERROR   => Internal Server Error: /api/products/update_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 150, in update_prod_code_map
    return self._handle_action('prodcodemap', 'update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'IntegrityError' is not JSON serializable
2024-10-17 15:57:53,910 basehttp.log_message 161 ERROR   => "POST /api/products/update_prod_code_map/ HTTP/1.1" 500 137420
2024-10-17 15:58:17,698 error_utils.handle_error  13 ERROR   => update_prod_code_map_method - Unexpected error: ORA-00001: unique constraint (B2B.PROD_CODE_MAP_KEY) violated
2024-10-17 15:58:17,769 log.log_response 230 ERROR   => Internal Server Error: /api/products/update_prod_code_map/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 150, in update_prod_code_map
    return self._handle_action('prodcodemap', 'update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 46, in _handle_action
    return handle_response(sql_result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\products\views.py", line 38, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'IntegrityError' is not JSON serializable
2024-10-17 15:58:17,770 basehttp.log_message 161 ERROR   => "POST /api/products/update_prod_code_map/ HTTP/1.1" 500 137420
2024-10-17 16:00:30,741 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:02:42,449 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:03:59,186 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-17 16:06:37,324 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-10-17 16:06:39,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:06:52,249 basehttp.log_message 161 INFO    => "POST /api/products/update_prod_code_map/ HTTP/1.1" 200 69
2024-10-17 16:07:13,632 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-17 16:07:13,637 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-17 16:07:14,187 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-17 16:08:42,446 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:11:42,439 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:14:42,459 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:15:11,378 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 16:15:11,435 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 16:17:42,458 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:20:42,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:23:42,411 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-10-17 16:23:42,588 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:26:42,499 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:29:42,475 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:32:42,451 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:35:42,446 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:39:46,858 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:42:04,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:45:04,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:48:04,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:48:23,767 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 16:48:23,767 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-17 16:48:23,890 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-17 16:48:23,941 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-17 16:51:04,184 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:54:04,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 16:57:04,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:00:04,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:03:04,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:06:04,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:09:04,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:12:04,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:15:04,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:18:04,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:21:04,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:24:04,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:27:04,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:30:04,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:33:04,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:36:04,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:39:04,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:42:04,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:45:04,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:48:04,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:51:04,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:54:04,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 17:57:04,131 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:00:04,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:03:04,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:06:04,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:09:04,199 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:12:04,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:15:04,203 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-10-17 18:18:04,180 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-10-17 18:18:04,181 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
