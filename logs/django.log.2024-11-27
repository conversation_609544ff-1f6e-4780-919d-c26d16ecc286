2024-11-27 11:11:08,127 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-27 11:11:08,130 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-11-27 11:11:08,236 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-11-27 11:11:08,236 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-11-27 11:11:08,237 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-11-27 11:11:08,312 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
