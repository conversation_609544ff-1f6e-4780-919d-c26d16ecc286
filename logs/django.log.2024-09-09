2024-09-09 08:43:44,462 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 08:43:44,652 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-09 08:43:44,653 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-09-09 08:56:33,361 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-09-09 08:56:33,362 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-09 08:56:33,363 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-09-09 08:57:08,299 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-09-09 08:57:08,510 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-09-09 08:57:08,704 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 08:57:08,783 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 08:57:08,859 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-09-09 08:57:08,861 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-09-09 08:57:08,862 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-09-09 08:57:09,204 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-09-09 08:57:09,481 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 08:57:09,772 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-09-09 08:57:09,856 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-09-09 08:57:45,046 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-09-09 08:57:45,253 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-09-09 08:58:19,281 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-09 08:58:19,444 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 08:58:19,636 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 09:05:17,988 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-09 09:05:57,852 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 09:05:57,852 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-09 09:06:21,810 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-09-09 09:06:21,991 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 09:06:22,335 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-09-09 09:06:22,474 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 09:06:22,801 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-09-09 09:06:22,806 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-09-09 09:06:26,749 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-09-09 09:06:26,983 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:26,984 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:26,985 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:26,986 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:26,987 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:26,987 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:27,058 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:27,270 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-09-09 09:06:27,315 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-09-09 09:06:27,439 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-09-09 09:06:27,486 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-09-09 09:06:27,558 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-09-09 09:06:27,580 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50744
2024-09-09 09:06:27,624 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-09-09 09:06:29,307 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-09-09 09:06:29,386 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-09-09 09:06:29,613 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-09 09:09:21,631 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-09-09 09:14:16,481 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-09-09 09:19:43,372 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-09-09 09:19:43,479 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-09-09 09:19:43,677 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29693
2024-09-09 09:19:44,173 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 09:19:44,254 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-09-09 09:19:44,564 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 09:19:45,368 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29693
2024-09-09 09:19:46,061 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 09:37:06,765 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 09:49:53,090 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 10:11:51,176 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 10:20:00,342 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 10:49:27,944 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 10:49:28,171 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 10:49:29,901 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 11:03:07,305 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-09-09 11:03:07,511 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-09-09 11:21:00,590 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 11:37:41,588 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-09-09 11:37:41,707 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-09-09 11:37:41,910 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29693
2024-09-09 11:37:55,647 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-09-09 11:37:55,815 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 11:37:55,950 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 11:38:07,542 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-09 11:38:08,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:38:09,086 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 11:38:09,374 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 11:38:09,481 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-09-09 11:38:09,481 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-09-09 11:38:09,939 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 11:38:10,019 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-09-09 11:38:10,216 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 11:38:10,360 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-09-09 11:38:10,424 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-09-09 11:38:56,259 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 11:39:43,977 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-09-09 11:41:08,616 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:44:08,704 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:47:08,617 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:50:08,640 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:53:08,630 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:56:08,629 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 11:59:08,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:02:08,612 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:05:08,619 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:08:08,633 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:11:08,625 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:14:08,629 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:17:08,639 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:20:08,629 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:23:08,623 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:26:08,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:29:08,631 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:32:08,607 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:35:08,617 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:38:08,633 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:41:08,633 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:44:08,629 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:47:08,648 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:50:08,605 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:53:08,630 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:56:08,612 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 12:59:08,620 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:02:08,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:05:08,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:08:08,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:10:20,785 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 13:10:20,786 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 13:10:20,968 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 13:10:21,027 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-09-09 13:11:08,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:14:08,623 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:17:08,640 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:20:08,642 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:23:08,644 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:26:09,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:29:09,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:32:09,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:35:08,618 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:36:55,276 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-09-09 13:36:55,425 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-09-09 13:38:09,026 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-09 13:38:09,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:41:09,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:44:09,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:47:09,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:50:09,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:53:09,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:56:09,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 13:59:09,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:02:09,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:02:55,428 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 14:05:08,654 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:08:08,641 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:11:08,609 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:14:08,637 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:17:08,625 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:20:08,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:23:08,646 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:26:09,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:29:09,301 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:32:09,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:35:06,560 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 14:35:08,612 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:38:09,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:41:09,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:44:09,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:47:09,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:50:09,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:53:09,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:56:09,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 14:59:09,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:02:09,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:05:09,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:05:55,537 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 15:08:09,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:11:09,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:14:09,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:17:09,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:20:09,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:23:09,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:26:09,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:29:09,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:32:09,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:35:09,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:36:42,391 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 15:36:42,568 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 15:38:09,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:41:09,003 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-09 15:41:09,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:44:09,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:47:08,651 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:50:08,631 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:53:09,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:56:09,185 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 15:59:09,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:02:09,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:05:09,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:08:09,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:08:15,167 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 16:11:09,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:14:08,637 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:17:09,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:20:09,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:23:09,188 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:26:09,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:29:08,616 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-09-09 16:29:40,508 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-09 16:29:40,667 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:29:40,824 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:30:17,021 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-09-09 16:30:17,202 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-09-09 16:30:17,373 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 16:30:17,450 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-09-09 16:30:17,529 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-09-09 16:30:17,531 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-09-09 16:30:17,532 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-09-09 16:30:17,762 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-09 16:30:17,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 16:30:18,188 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 6078
2024-09-09 16:30:18,292 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 6630
2024-09-09 16:30:22,288 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-09-09 16:30:22,355 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-09-09 16:30:22,355 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-09-09 16:30:22,509 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-09-09 16:30:22,624 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-09-09 16:30:23,578 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-09-09 16:30:23,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-09-09 16:30:23,854 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-09-09 16:30:25,018 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 39487
2024-09-09 16:31:09,351 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-09-09 16:31:09,351 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-09-09 16:31:09,508 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:31:09,574 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:31:10,906 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-09-09 16:31:11,178 _DownloadBusinessNotificationInfo.select_business_notification_download 311 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300832\業務通報-113年9月大潤發中秋節擴大陳列活動240823h16510B.docx
2024-09-09 16:31:11,179 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-09-09 16:31:11,179 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-09-09 16:33:58,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 38426
2024-09-09 16:42:03,770 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-09 16:42:38,822 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:42:39,032 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:42:44,598 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 126
2024-09-09 16:42:44,784 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 16:42:45,102 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-09 16:42:45,361 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 16:42:45,457 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5446
2024-09-09 16:42:45,559 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4793
2024-09-09 16:42:47,239 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-09-09 16:42:47,393 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-09-09 16:42:47,507 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-09-09 16:42:49,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 32220
2024-09-09 16:42:51,052 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51816
2024-09-09 16:42:56,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:42:56,498 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:42:57,807 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-09-09 16:42:58,387 _DownloadBusinessNotificationInfo.extract_table_data 452 ERROR   => 未定義 通知喜美超市113年18-19檔促銷活動通報 的位置
2024-09-09 16:42:59,626 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5038
2024-09-09 16:43:13,893 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 180548
2024-09-09 16:44:11,730 _DownloadBusinessNotificationInfo.extract_table_data 452 ERROR   => 未定義 通知喜美超市113年18-19檔促銷活動通報 的位置
2024-09-09 16:44:13,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5037
2024-09-09 16:47:10,507 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:47:10,805 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-09 16:47:32,705 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-09-09 16:47:32,705 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-09-09 16:47:35,265 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-09-09 16:47:35,265 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-09-09 16:47:38,148 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-09-09 16:47:38,336 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-09 16:47:38,538 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-09 16:47:38,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3881
2024-09-09 16:47:38,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5658
2024-09-09 16:47:39,040 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7049
2024-09-09 16:47:44,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-09-09 16:47:44,940 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-09-09 16:47:45,061 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-09-09 16:47:48,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 39067
2024-09-09 16:48:03,257 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:48:03,283 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:48:04,532 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 34448
2024-09-09 16:48:16,408 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:48:16,459 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:48:20,626 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:48:20,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:48:22,701 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:48:22,729 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:48:32,981 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-09-09 16:48:33,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-09-09 16:48:37,169 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,173 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,187 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,191 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,199 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,207 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,216 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,220 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,235 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,238 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,252 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,267 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,283 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,297 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,301 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,325 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,343 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,362 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,407 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,502 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,521 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,590 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,611 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,645 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,677 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:37,945 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:38,045 _DownloadBusinessNotificationInfo.get_cell_text_and_color 416 ERROR   => get_cell_text_and_color 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -2146822347), None)
2024-09-09 16:48:38,552 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7063
2024-09-09 16:49:08,036 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165632
