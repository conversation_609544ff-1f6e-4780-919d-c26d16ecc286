2025-06-25 11:56:52,810 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-25 11:56:52,978 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-25 11:56:53,255 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-25 11:56:53,255 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-25 11:56:53,515 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-25 11:56:53,544 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-25 11:56:53,572 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-25 11:56:53,606 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-25 11:56:53,645 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-25 11:56:53,683 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-25 11:56:54,287 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 11:56:54,469 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 11:56:54,775 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-25 11:57:25,704 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 11:57:25,974 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 11:57:26,485 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 92.4%
2025-06-25 11:57:30,488 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-25 11:57:53,304 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 1, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 11:57:56,936 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-25 11:57:56,958 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-25 11:57:57,241 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 11:57:57,381 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-25 11:57:57,382 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-25 11:57:57,445 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 1
2025-06-25 11:57:57,817 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-25 11:57:57,833 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 11:57:57,880 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-25 11:57:57,909 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-25 11:57:57,940 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-25 11:57:58,008 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-25 11:57:58,057 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-25 11:57:58,631 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 11:57:58,803 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 11:57:59,192 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-25 11:58:06,968 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-25 11:58:08,355 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-25 11:58:15,353 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-25 11:58:28,653 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 11:58:28,779 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-06-25 11:58:29,979 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 11:58:30,081 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 11:58:30,175 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-25 11:58:31,992 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-06-25 11:58:38,428 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-06-25 11:58:39,398 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-25 11:58:44,890 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-25 11:58:53,366 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 4, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 11:58:53,675 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-25 11:58:54,172 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-25 11:58:57,416 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 1, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 11:58:59,590 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 11:58:59,724 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 11:59:00,491 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 11:59:00,504 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 1
2025-06-25 11:59:04,122 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-25 11:59:10,340 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-25 11:59:16,928 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-25 11:59:18,941 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-25 11:59:26,006 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-25 11:59:30,584 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 11:59:30,706 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-25 11:59:30,820 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 11:59:30,914 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 11:59:53,460 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 11:59:57,497 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:00:01,380 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:00:01,534 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:00:01,571 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:00:01,699 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:00:32,083 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:00:32,107 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-25 12:00:32,261 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:00:32,400 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:00:53,522 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:00:57,539 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:01:02,419 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:01:02,574 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:01:03,097 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:01:03,237 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-06-25 12:01:32,947 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:01:32,955 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-25 12:01:33,604 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:01:53,597 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:01:57,646 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:02:03,235 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:02:03,330 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-25 12:02:04,313 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:02:04,443 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:02:22,226 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-25 12:02:22,310 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-25 12:02:33,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:02:33,827 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-25 12:02:35,008 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:02:53,613 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:02:57,723 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:03:04,138 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:03:04,222 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:03:05,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:03:34,575 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:03:34,580 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:03:35,423 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:03:53,623 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:03:57,795 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:04:04,718 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:04:04,727 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:04:05,560 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:04:35,181 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:04:35,198 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 109.5%
2025-06-25 12:04:35,709 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:04:53,633 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:04:57,827 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:05:05,449 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:05:05,467 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:05:05,855 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:05:35,886 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:05:35,924 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:05:36,068 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:05:36,082 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:05:53,648 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:05:57,846 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:06:06,098 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:06:06,101 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:06:06,278 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:06:36,472 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:06:36,476 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 12:06:36,493 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:06:36,524 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:06:53,679 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:06:57,890 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:07:06,801 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:07:06,917 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:07:06,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:07:07,050 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:07:07,052 word_processor_cache.get  67 ERROR   => 獲取緩存失敗 word_processor_alerts: Ran out of input
2025-06-25 12:07:37,254 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:07:37,254 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:07:37,301 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:07:37,301 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:07:53,751 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:07:57,948 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:08:07,495 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:08:07,523 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:08:07,539 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:08:07,553 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:08:37,945 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:08:38,029 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:08:38,105 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:08:38,208 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:08:53,826 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:08:57,956 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:09:08,744 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:09:08,759 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:09:08,868 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:09:08,868 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-06-25 12:09:09,056 word_processor_cache.get  67 ERROR   => 獲取緩存失敗 word_processor_alerts: Ran out of input
2025-06-25 12:09:39,465 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:09:39,510 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-25 12:09:39,528 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:09:39,572 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 12:09:53,887 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:09:57,967 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:10:09,710 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:10:09,720 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:10:09,724 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-25 12:10:40,034 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:10:40,118 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
%
2025-06-25 12:10:40,214 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-25 12:10:53,979 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:10:57,992 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:11:10,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:11:10,875 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:11:10,912 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:11:11,010 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-25 12:11:11,133 word_processor_cache.get  67 ERROR   => 獲取緩存失敗 word_processor_alerts: Ran out of input
2025-06-25 12:11:41,673 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:11:41,754 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:11:41,794 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:11:41,847 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 12:11:54,041 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:11:58,024 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:12:12,358 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:12:12,397 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-25 12:12:12,463 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:12:12,507 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-25 12:12:42,580 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:12:42,584 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:12:42,658 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:12:54,064 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:12:58,083 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:13:12,731 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:13:12,800 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:13:43,092 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:13:43,173 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:13:43,226 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:13:43,323 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:13:54,103 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:13:58,098 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:14:13,854 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:14:13,858 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:14:13,997 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:14:14,107 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:14:44,061 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:14:44,064 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:14:44,325 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:14:54,168 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:14:58,115 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:15:14,204 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:15:14,207 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:15:14,467 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:15:44,375 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:15:44,380 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-25 12:15:44,613 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:15:54,249 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:15:58,168 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:16:14,678 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:16:14,795 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:16:14,857 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:16:14,900 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 12:16:45,288 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:16:45,348 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:16:45,378 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:16:45,479 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:16:54,308 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:16:58,178 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:17:15,759 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:17:15,804 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:17:15,819 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:17:15,821 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:17:46,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:17:46,325 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:17:47,128 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-25 12:17:47,193 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:17:47,373 word_processor_cache.get  67 ERROR   => 獲取緩存失敗 word_processor_alerts: Ran out of input
2025-06-25 12:17:54,342 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:17:58,207 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:18:17,778 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:18:17,914 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:18:17,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:18:18,021 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:18:48,156 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:18:48,217 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:18:48,220 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:18:54,356 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:18:58,255 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:18:59,702 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-06-25 12:19:00,616 token_utils.verify_access_token  42 ERROR   => Refresh token 無效
2025-06-25 12:19:00,616 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-06-25 12:19:00,617 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 77
2025-06-25 12:19:08,232 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-25 12:19:08,390 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2025-06-25 12:19:08,390 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2025-06-25 12:19:09,094 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2025-06-25 12:19:09,095 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2025-06-25 12:19:12,519 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-06-25 12:19:12,596 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-25 12:19:13,024 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-25 12:19:13,107 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-25 12:19:13,109 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-25 12:19:13,111 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-25 12:19:13,111 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-25 12:19:13,317 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-06-25 12:19:13,581 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1195
2025-06-25 12:19:13,921 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1655
2025-06-25 12:19:14,263 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-25 12:19:18,574 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:19:18,657 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:19:18,719 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:19:18,747 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:19:27,167 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-06-25 12:19:27,230 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2025-06-25 12:19:27,230 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-06-25 12:19:27,230 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-25 12:19:27,396 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-25 12:19:27,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-25 12:19:27,570 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9687
2025-06-25 12:19:30,488 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-25 12:19:30,706 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-25 12:19:31,069 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-25 12:19:41,804 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-25 12:19:42,513 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-25 12:19:43,397 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1195
2025-06-25 12:19:43,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5011
2025-06-25 12:19:43,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7782
2025-06-25 12:19:43,815 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-25 12:19:46,078 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-06-25 12:19:46,643 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-25 12:19:46,838 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-25 12:19:48,088 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2025-06-25 12:19:48,634 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 5802
2025-06-25 12:19:49,349 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:19:49,334 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:19:49,478 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-25 12:19:49,513 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 143.3%
2025-06-25 12:19:49,514 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-25 12:19:49,681 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-25 12:19:51,008 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50667
2025-06-25 12:19:54,389 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:19:58,279 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:20:12,212 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-06-25 12:20:12,212 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-06-25 12:20:12,857 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-25 12:20:13,099 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-06-25 12:20:14,583 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-06-25 12:20:14,983 word_enterprise_processor._process_document_internal 343 ERROR   => 處理文檔失敗: Word.Application.Documents
2025-06-25 12:20:14,983 word_queue_processor.submit_task 161 INFO    => 任務已提交: 04d049cf-2abc-43eb-861c-e7136de675da
2025-06-25 12:20:14,983 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 04d049cf-2abc-43eb-861c-e7136de675da
2025-06-25 12:20:20,645 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:20:20,661 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:20:20,803 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.2%
2025-06-25 12:20:20,803 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:20:22,436 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,047 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,128 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,223 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,261 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,302 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,336 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,406 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,430 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,457 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,487 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,547 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,581 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,614 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,683 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,717 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,808 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,843 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,878 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:23,916 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,038 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,104 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,138 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,198 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,227 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,261 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,299 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,328 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,358 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,387 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,416 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,452 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,510 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,567 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,615 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,675 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,719 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,814 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,883 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,958 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:24,986 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:25,412 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:27,591 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:29,225 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:29,833 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:29,901 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,025 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,092 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,147 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,181 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,212 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,257 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,305 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,391 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,660 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,883 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:30,951 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,010 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,075 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,127 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,152 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,183 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,210 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,243 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,284 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,320 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 ERROR   => get_cell_text_and_color 發生錯誤: wdColorAutomatic
2025-06-25 12:20:31,453 word_queue_processor._process_tasks 120 INFO    => 任務完成: 04d049cf-2abc-43eb-861c-e7136de675da
2025-06-25 12:20:31,507 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5076
2025-06-25 12:20:51,702 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:20:51,839 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:20:51,839 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 80.1%
2025-06-25 12:20:51,924 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-25 12:20:52,103 word_processor_cache.get  67 ERROR   => 獲取緩存失敗 word_processor_alerts: Ran out of input
2025-06-25 12:20:54,419 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:20:58,301 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:21:22,527 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:21:22,629 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:21:22,657 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:21:52,730 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:21:53,300 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:21:53,437 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-25 12:21:54,451 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:21:58,409 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:22:23,088 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:22:23,231 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:22:23,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:22:23,852 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-25 12:22:53,706 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:22:53,722 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:22:54,000 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:22:54,003 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:22:54,506 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:22:58,436 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:23:24,101 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:23:24,179 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:23:24,337 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:23:24,453 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-25 12:23:54,569 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:23:54,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:23:54,945 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:23:55,072 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:23:55,183 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:23:58,481 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:24:25,659 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:24:25,757 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-06-25 12:24:26,036 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:24:26,135 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:24:54,621 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:24:56,181 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:24:56,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:24:57,071 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:24:58,556 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:25:26,582 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:25:27,863 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:25:28,006 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:25:54,674 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:25:57,029 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:25:57,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-25 12:25:58,163 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:25:58,166 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:25:58,610 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:26:27,763 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:26:27,876 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:26:28,575 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:26:28,782 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:26:54,764 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:26:58,546 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:26:58,604 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:26:58,634 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:26:59,609 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:26:59,754 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-25 12:27:29,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:27:30,581 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:27:30,653 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:27:54,860 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:27:58,741 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:27:59,302 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:28:01,112 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:28:01,115 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:28:29,633 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:28:29,708 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:28:31,313 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:28:31,316 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-06-25 12:28:54,902 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:28:58,783 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:29:00,258 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:29:00,370 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:29:01,457 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:29:01,461 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:29:30,844 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:29:30,943 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:29:31,821 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:29:31,936 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:29:54,937 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:29:58,803 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:30:01,607 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:30:01,691 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:30:02,696 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:30:02,791 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.2%
2025-06-25 12:30:32,192 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:30:33,500 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:30:33,621 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:30:55,025 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:30:58,826 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:31:02,712 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:31:02,786 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-06-25 12:31:04,151 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:31:04,156 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:31:33,183 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:31:33,308 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:31:34,627 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:31:34,754 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-25 12:31:55,086 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:31:58,887 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:32:03,995 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:32:04,090 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:32:05,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:32:05,727 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-25 12:32:30,864 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-25 12:32:30,868 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-25 12:32:30,915 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-25 12:32:30,915 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-25 12:32:31,119 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-25 12:32:31,145 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-25 12:32:31,172 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-25 12:32:31,197 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-25 12:32:31,264 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-25 12:32:31,314 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-25 12:32:31,845 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 12:32:32,064 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 12:32:32,530 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:32:35,617 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-25 12:32:36,713 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-25 12:32:39,214 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-25 12:32:42,293 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-25 12:32:45,204 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-25 12:32:51,849 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-25 12:32:51,854 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-25 12:32:51,892 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-25 12:32:51,893 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-25 12:32:51,898 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-25 12:32:51,898 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-25 12:32:51,898 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-25 12:32:51,899 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-25 12:32:51,899 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-25 12:32:51,899 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-25 12:32:52,556 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-25 12:32:52,746 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-25 12:32:53,024 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-06-25 12:32:57,524 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-25 12:33:01,101 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-25 12:33:03,080 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-25 12:33:03,236 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:33:03,273 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:33:07,520 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-25 12:33:07,790 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-25 12:33:08,147 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-25 12:33:17,010 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-06-25 12:33:17,052 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-06-25 12:33:22,269 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-25 12:33:23,938 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:33:26,211 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-25 12:33:28,388 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-25 12:33:28,696 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-25 12:33:28,993 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-25 12:33:29,110 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1195
2025-06-25 12:33:29,629 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5011
2025-06-25 12:33:29,686 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7782
2025-06-25 12:33:30,944 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:33:31,340 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-06-25 12:33:31,890 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-25 12:33:31,995 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-25 12:33:33,565 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:33:33,690 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-25 12:33:35,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50667
2025-06-25 12:33:37,249 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-06-25 12:33:37,339 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-25 12:33:39,571 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1031 INFO    => 嘗試使用企業級處理器
2025-06-25 12:33:39,729 _DownloadBusinessNotificationInfo.extract_data_worker 1012 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\a4686f40-ce9d-48ce-afe3-aa6e0eb0f09c.docx
2025-06-25 12:33:40,748 _DownloadBusinessNotificationInfo.extract_data_worker 1018 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-06-25 12:33:41,280 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1039 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-06-25 12:33:41,313 word_queue_processor.submit_task 161 INFO    => 任務已提交: 405e95ad-652e-46fb-a46d-e405d2516087
2025-06-25 12:33:41,328 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 405e95ad-652e-46fb-a46d-e405d2516087
2025-06-25 12:33:41,384 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1048 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\a4686f40-ce9d-48ce-afe3-aa6e0eb0f09c.docx
2025-06-25 12:33:51,913 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:33:53,500 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-06-25 12:33:53,552 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (4, 3) 可能不存在或已合併
2025-06-25 12:33:53,705 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (6, 3) 可能不存在或已合併
2025-06-25 12:33:54,147 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (11, 3) 可能不存在或已合併
2025-06-25 12:33:54,390 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (14, 3) 可能不存在或已合併
2025-06-25 12:33:54,503 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (15, 3) 可能不存在或已合併
2025-06-25 12:33:54,598 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:33:54,604 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:33:54,620 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (17, 3) 可能不存在或已合併
2025-06-25 12:33:54,736 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (19, 3) 可能不存在或已合併
2025-06-25 12:33:54,785 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (20, 3) 可能不存在或已合併
2025-06-25 12:33:54,841 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (21, 3) 可能不存在或已合併
2025-06-25 12:33:59,832 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (24, 3) 可能不存在或已合併
2025-06-25 12:34:00,131 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (28, 3) 可能不存在或已合併
2025-06-25 12:34:00,171 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (29, 3) 可能不存在或已合併
2025-06-25 12:34:00,210 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (30, 3) 可能不存在或已合併
2025-06-25 12:34:00,860 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (41, 3) 可能不存在或已合併
2025-06-25 12:34:01,142 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (44, 3) 可能不存在或已合併
2025-06-25 12:34:01,189 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (45, 3) 可能不存在或已合併
2025-06-25 12:34:01,235 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (46, 3) 可能不存在或已合併
2025-06-25 12:34:01,345 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (48, 3) 可能不存在或已合併
2025-06-25 12:34:01,451 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (50, 3) 可能不存在或已合併
2025-06-25 12:34:01,733 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (55, 3) 可能不存在或已合併
2025-06-25 12:34:04,172 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:34:05,871 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (57, 3) 可能不存在或已合併
2025-06-25 12:34:06,738 _DownloadBusinessNotificationInfo.get_cell_text_and_color 578 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-06-25 12:34:07,019 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1051 INFO    => Legacy 處理器成功提取 55 筆數據
2025-06-25 12:34:07,147 word_queue_processor._process_tasks 120 INFO    => 任務完成: 405e95ad-652e-46fb-a46d-e405d2516087
2025-06-25 12:34:07,147 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1064 INFO    => 原始處理器執行成功
2025-06-25 12:34:07,147 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1073 INFO    => 成功提取 55 筆數據
2025-06-25 12:34:07,258 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1078 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\114年大潤發5071檔 DM.IP 促銷通報(1140704-1140717)250625h16510B.xlsx
2025-06-25 12:34:07,264 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7132
2025-06-25 12:34:24,757 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:34:24,772 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-25 12:34:30,958 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:34:34,744 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-25 12:34:34,929 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-25 12:34:51,989 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-25 12:34:54,073 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 114年大潤發5071檔 DM.IP 促銷通報(1140704-1140717)250625h16510B.docx  -  相容模式 - Word
2025-06-25 12:34:54,078 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 正在開啟 - Word
2025-06-25 12:34:54,858 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 114年大潤發5071檔 DM.IP 促銷通報(1140704-1140717)250625h16510B.docx  -  相容模式 - Word
2025-06-25 12:34:55,630 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
