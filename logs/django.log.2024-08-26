2024-08-26 08:59:30,645 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-26 09:00:23,467 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-26 09:00:51,515 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 09:00:51,515 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-08-26 09:00:51,773 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-08-26 09:00:51,774 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-08-26 09:00:51,815 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-08-26 09:00:57,803 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-08-26 09:00:57,959 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-08-26 09:00:58,136 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 09:00:58,202 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 09:00:58,203 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-08-26 09:00:58,253 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-08-26 09:00:58,253 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-08-26 09:00:58,596 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:00:58,870 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:00:59,032 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:00:59,100 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:03:53,019 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-26 09:03:53,207 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:03:53,310 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:03:53,335 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:03:53,557 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:03:53,589 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:04:05,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:04:05,162 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:04:05,399 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:04:05,568 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:04:05,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:04:46,082 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:04:46,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:04:46,305 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:04:46,571 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:04:46,603 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:07:06,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:07:47,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:10:06,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:10:47,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:12:14,170 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:12:14,371 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:12:14,385 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:12:14,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:12:14,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:13:47,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:15:15,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:16:47,579 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:17:10,092 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:17:10,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:17:10,284 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:17:10,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:17:10,628 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:17:42,273 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:17:42,287 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:17:42,367 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:17:42,618 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:17:42,652 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:18:43,713 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:18:47,700 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:18:47,761 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:18:48,079 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:18:48,226 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:18:57,865 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:19:06,600 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:19:06,819 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:19:07,061 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:19:07,083 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:19:12,098 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:19:15,251 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:19:15,257 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:19:15,604 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:19:15,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:20:11,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:20:19,077 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:21,874 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:21,943 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:20:22,262 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:20:22,406 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:20:40,441 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:42,550 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:20:50,970 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:54,136 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:54,301 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:20:54,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:20:54,654 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:20:56,237 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 09:20:56,311 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:20:56,493 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 09:20:56,553 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 09:23:11,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:23:43,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:26:11,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:26:43,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:27:17,177 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 09:29:11,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:29:43,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:32:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:33:07,654 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 09:33:07,711 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:35:12,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:36:12,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:38:12,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:39:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:41:12,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:42:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:44:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:44:42,543 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:47:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:48:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:50:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:51:12,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:53:12,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:54:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:56:11,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:57:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:59:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 09:59:43,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:02:11,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:02:43,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:05:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:06:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:07:30,339 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 10:08:11,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:08:58,344 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:11:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:12:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:14:12,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:15:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:17:12,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:18:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:20:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:20:59,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:23:11,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:23:43,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:26:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:27:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:29:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:30:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:32:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:33:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:35:12,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:36:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:38:11,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:38:43,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:39:49,376 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 10:41:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:42:12,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:44:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:45:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:47:12,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:48:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:50:11,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:51:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:53:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:53:43,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:56:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:57:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 10:59:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:00:12,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:02:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:03:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:05:12,008 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-26 11:05:12,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:06:12,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:08:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:09:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:11:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:12:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:14:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:15:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:17:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:18:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:20:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:21:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:23:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:24:12,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:26:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:27:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:29:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:30:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:32:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:33:12,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:35:12,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:36:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:37:44,245 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 11:37:44,366 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 11:38:11,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:38:43,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:41:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:42:12,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:44:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:45:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:47:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:48:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:50:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:51:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:53:12,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:54:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:56:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:57:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 11:59:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:00:12,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:02:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:03:12,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:05:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:06:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:08:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:09:12,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:11:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:12:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:14:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:15:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:17:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:18:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:20:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:21:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:23:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:24:12,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:26:12,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:27:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:29:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:30:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:32:12,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:33:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:35:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:36:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:38:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:39:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:41:12,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:42:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:44:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:45:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:47:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:48:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:50:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:51:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:52:00,034 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 12:53:12,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:54:09,833 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:56:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:56:43,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 12:59:12,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:00:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:02:11,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:02:43,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:05:11,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:05:43,000 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-26 13:05:43,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:08:11,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:08:43,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:11:12,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:12:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:14:11,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:14:43,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:17:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:18:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:20:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:20:43,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:22:16,925 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 13:23:11,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:24:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:26:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:27:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:29:11,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:29:43,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:32:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:32:43,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:35:11,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:35:43,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:38:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:39:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:41:11,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:41:43,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:44:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:44:24,915 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 13:44:25,038 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-08-26 13:44:25,094 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,220 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,223 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,223 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,223 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,224 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,273 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-26 13:44:25,528 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-08-26 13:44:25,638 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-08-26 13:44:25,682 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-08-26 13:44:25,694 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50798
2024-08-26 13:44:25,733 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-08-26 13:44:25,780 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-08-26 13:44:25,841 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-08-26 13:44:43,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:47:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:48:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:50:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:51:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:53:12,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:54:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:56:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:57:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 13:59:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:00:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:02:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:03:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:05:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:06:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:08:12,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:09:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:11:12,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:12:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:14:12,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:15:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:17:12,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:18:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:20:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:21:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:23:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:24:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:26:12,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:27:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:29:12,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:30:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:32:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:33:12,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:35:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:36:12,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:38:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:39:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:41:12,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:42:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:44:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:45:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:47:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:48:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:50:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:51:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:53:12,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:54:12,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:56:12,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:57:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 14:59:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:00:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:02:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:03:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:05:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:06:11,996 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-26 15:06:12,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:08:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:09:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:11:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:12:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:14:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:15:12,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:17:12,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:18:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:20:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:21:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:23:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:24:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:26:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:27:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:29:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:30:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:32:12,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:33:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:35:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:36:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:38:12,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:39:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:41:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:42:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:44:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:45:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:47:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:48:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:50:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:51:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:53:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:54:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:56:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:57:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 15:59:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:00:12,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:02:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:03:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:05:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:06:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:08:12,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:08:58,420 log.log_response 230 WARNING => Not Found: /validate-token/
2024-08-26 16:08:58,421 basehttp.log_message 161 WARNING => "GET /validate-token/ HTTP/1.1" 404 2136
2024-08-26 16:09:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:09:32,377 log.log_response 230 WARNING => Not Found: /validate-token/
2024-08-26 16:09:32,377 basehttp.log_message 161 WARNING => "GET /validate-token/ HTTP/1.1" 404 2136
2024-08-26 16:11:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:12:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:12:12,424 log.log_response 230 WARNING => Not Found: /validate-token/
2024-08-26 16:12:12,424 basehttp.log_message 161 WARNING => "GET /validate-token/ HTTP/1.1" 404 2136
2024-08-26 16:12:14,203 log.log_response 230 WARNING => Not Found: /validate-token/
2024-08-26 16:12:14,203 basehttp.log_message 161 WARNING => "GET /validate-token/ HTTP/1.1" 404 2136
2024-08-26 16:12:28,585 log.log_response 230 WARNING => Not Found: /validate-token/
2024-08-26 16:12:28,586 basehttp.log_message 161 WARNING => "GET /validate-token/ HTTP/1.1" 404 2136
2024-08-26 16:14:12,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:15:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:17:12,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:18:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:20:12,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:21:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:23:12,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:24:12,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:26:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:27:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:29:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:30:12,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:32:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:33:12,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:35:12,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:36:12,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:38:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:39:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:41:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:42:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:44:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:45:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:47:12,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:48:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:50:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:51:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:53:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:54:12,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:56:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:57:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 16:59:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 17:00:12,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-26 17:02:12,027 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-08-26 17:02:12,027 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-08-26 17:14:59,123 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-08-26 17:14:59,297 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-08-26 17:14:59,388 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 17:14:59,534 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-26 17:14:59,603 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-08-26 17:14:59,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-08-26 17:14:59,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-08-26 17:14:59,693 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-08-26 17:14:59,860 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-26 17:15:00,087 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
2024-08-26 17:15:00,327 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-26 17:15:00,374 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-26 17:15:03,655 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 8699
