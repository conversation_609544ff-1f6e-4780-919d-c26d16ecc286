2024-05-13 06:15:51,737 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-05-13 06:15:51,944 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-05-13 06:15:51,944 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-05-13 06:15:59,894 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-05-13 06:15:59,973 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 06:16:00,033 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:00,122 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 06:16:00,190 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 06:16:00,266 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-13 06:16:00,267 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-13 06:16:00,267 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-13 06:16:00,319 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:00,410 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:00,412 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:00,412 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:00,545 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-13 06:16:00,722 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1828
2024-05-13 06:16:00,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 06:16:00,926 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 06:16:19,217 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:19,292 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-13 06:16:19,357 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-13 06:16:19,413 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:19,551 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:16:21,763 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:21,827 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-13 06:16:21,881 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:22,017 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:16:23,891 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:23,953 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 06:16:24,015 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:24,131 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:16:53,789 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:53,855 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 06:16:53,913 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:16:54,021 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:16:56,421 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-05-13 06:16:56,476 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:02,378 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 2512441
2024-05-13 06:17:31,388 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:31,455 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-05-13 06:17:31,518 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-05-13 06:17:31,519 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 06:17:31,519 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-13 06:17:31,571 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:31,572 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:31,573 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:31,686 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-13 06:17:31,741 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 06:17:31,752 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:17:33,544 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-05-13 06:17:33,593 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:41,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 218086
2024-05-13 06:17:50,246 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-05-13 06:17:50,247 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-05-13 06:17:50,304 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:50,305 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:50,373 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 06:17:50,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 5389
2024-05-13 06:17:57,693 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2024-05-13 06:17:57,753 token_utils.verify_access_token  30 INFO    => refresh_token: 60970a9f-6dee-49b3-9ca5-b5b63f9b5e70
2024-05-13 06:17:57,851 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 17752
2024-05-13 06:18:38,515 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-05-13 06:18:38,651 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-05-13 06:18:38,852 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-05-13 06:18:47,106 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-05-13 06:18:47,177 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:18:47,178 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:18:47,181 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:18:47,182 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:18:47,318 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 06:18:47,342 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1828
2024-05-13 06:18:47,565 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 06:18:47,642 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4899
2024-05-13 06:19:06,856 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:06,992 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:19:15,938 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:15,940 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:15,941 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:16,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 06:19:16,109 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:19:16,155 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-13 06:19:18,768 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:19,085 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 70
2024-05-13 06:19:19,149 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:19,310 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 70
2024-05-13 06:19:23,657 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 06:19:23,712 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:23,782 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:19:23,843 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:23,899 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 06:19:23,958 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:23,958 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:23,959 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:23,960 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:24,018 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 06:19:24,076 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:24,103 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:24,143 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 06:19:24,235 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 06:19:24,278 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4899
2024-05-13 06:19:24,337 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:19:24,381 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 06:19:27,492 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:27,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 53615
2024-05-13 06:19:32,040 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:32,042 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:32,110 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 188
2024-05-13 06:19:32,162 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 06:19:36,355 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:36,355 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:36,455 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 06:19:36,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 311
2024-05-13 06:19:39,496 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:19:39,581 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 35654
2024-05-13 06:20:18,012 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:20:26,179 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 35876
2024-05-13 06:21:36,697 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:21:36,698 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:21:36,767 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 697
2024-05-13 06:21:36,823 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 06:21:42,741 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:21:42,812 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 36703
2024-05-13 06:22:04,810 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:22:13,098 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 37116
2024-05-13 06:22:23,878 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:22:23,964 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:25:23,869 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:23,945 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:25:39,233 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,324 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 06:25:39,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,555 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-05-13 06:25:39,556 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,558 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,559 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,561 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,562 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,676 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:25:39,683 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:39,724 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-13 06:25:39,792 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-13 06:25:39,853 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-13 06:25:39,909 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-13 06:25:39,974 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-13 06:25:40,167 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-13 06:25:41,520 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-05-13 06:25:41,566 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:25:41,848 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44116
2024-05-13 06:26:19,744 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:19,809 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-13 06:26:19,864 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:20,003 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:26:36,428 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-05-13 06:26:36,478 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:36,728 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 19326
2024-05-13 06:26:45,123 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:45,242 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:26:48,237 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:48,361 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:26:53,853 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:53,973 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:26:55,885 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:26:56,029 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 19326
2024-05-13 06:27:51,500 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:27:51,573 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-13 06:27:51,636 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:27:51,758 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:27:52,977 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-05-13 06:27:53,022 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:27:53,288 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 1644
2024-05-13 06:28:23,867 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:28:23,930 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:28:25,191 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:28:25,313 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:28:26,589 basehttp.log_message 161 INFO    => "OPTIONS /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 0
2024-05-13 06:28:26,645 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:28:26,907 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 15717
2024-05-13 06:31:23,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:24,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:31:33,362 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,424 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:31:33,483 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,549 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 06:31:33,663 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,694 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,695 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,696 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,829 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-13 06:31:33,894 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:33,897 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 06:31:33,982 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 06:31:34,057 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:31:34,162 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4899
2024-05-13 06:31:41,411 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:41,492 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-13 06:31:41,552 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:41,677 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:31:43,591 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:43,735 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:31:48,238 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:48,366 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:31:49,862 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:50,012 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-13 06:31:50,066 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:50,199 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-13 06:31:52,813 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:52,939 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:31:59,420 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:31:59,585 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 1644
2024-05-13 06:32:02,310 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:32:02,385 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 06:32:02,453 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:32:02,573 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:32:07,793 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-05-13 06:32:07,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:32:10,835 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 40254
2024-05-13 06:33:30,516 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 0
2024-05-13 06:33:30,573 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:33:39,479 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/ HTTP/1.1" 200 104625
2024-05-13 06:34:33,488 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:34:33,552 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:35:09,573 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 0
2024-05-13 06:35:09,627 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:35:10,101 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12504
2024-05-13 06:35:47,934 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 0
2024-05-13 06:35:47,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:35:48,235 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 769
2024-05-13 06:35:59,076 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 0
2024-05-13 06:35:59,121 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:35:59,294 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 06:36:58,082 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:36:58,228 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 06:37:17,375 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:37:17,438 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 06:37:17,494 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:37:17,665 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:37:19,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:37:20,682 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 06:37:33,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:37:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:39:43,356 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:39:44,172 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 999259
2024-05-13 06:40:19,045 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:19,124 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 06:40:19,177 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:19,203 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:19,311 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 06:40:19,386 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:40:23,715 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:24,004 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 53615
2024-05-13 06:40:32,594 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:32,595 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:32,668 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 06:40:32,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 188
2024-05-13 06:40:33,484 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:40:33,568 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:41:54,283 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:41:54,376 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-05-13 06:41:57,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:41:58,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2518
2024-05-13 06:42:06,506 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:06,579 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-05-13 06:42:06,627 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:06,748 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:42:12,535 basehttp.log_message 161 INFO    => "OPTIONS /api/pallet/select_pallet_account/ HTTP/1.1" 200 0
2024-05-13 06:42:12,580 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:21,727 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 21335
2024-05-13 06:42:41,393 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,463 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 06:42:41,524 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,525 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,623 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,626 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,627 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,627 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,742 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:42:41,746 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:41,814 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-13 06:42:41,865 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-13 06:42:41,925 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-13 06:42:41,980 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-13 06:42:42,049 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-13 06:42:42,113 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-13 06:42:43,170 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:43,239 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-13 06:42:43,292 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:43,411 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:42:45,428 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:45,559 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:42:47,766 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-05-13 06:42:47,792 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-05-13 06:42:47,837 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:47,839 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:42:48,007 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-05-13 06:42:48,035 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-05-13 06:43:09,692 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-05-13 06:43:09,692 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-05-13 06:43:09,768 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-05-13 06:43:09,769 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-05-13 06:43:09,769 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-05-13 06:43:09,818 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:09,821 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:09,821 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:09,822 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:09,823 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:09,942 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-13 06:43:10,020 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:10,024 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-13 06:43:10,040 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:10,059 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-05-13 06:43:10,115 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:10,611 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:43:10,621 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:43:10,624 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:43:10,624 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:43:10,629 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:43:33,492 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:43:33,561 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:44:09,390 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-05-13 06:44:09,441 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:09,548 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-05-13 06:44:11,605 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:11,768 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 21994
2024-05-13 06:44:45,536 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,538 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,539 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,540 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,541 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,541 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,798 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:45,804 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,848 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:45,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,908 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:45,916 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,941 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-13 06:44:45,962 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:45,969 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:45,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:46,014 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,020 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:46,025 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-13 06:44:46,029 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:46,209 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,255 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,285 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,371 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,431 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:46,478 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:44:56,353 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:44:56,496 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 21994
2024-05-13 06:45:13,919 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:13,920 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:13,921 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:13,922 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:13,923 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:13,924 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:14,148 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:45:14,153 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:14,217 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:45:14,267 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:45:14,320 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-13 06:45:14,350 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:45:14,383 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-13 06:45:14,411 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:45:37,621 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:37,702 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-05-13 06:45:39,764 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:45:39,924 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 22432
2024-05-13 06:46:02,157 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,159 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,163 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,164 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,165 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,166 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,397 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,402 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,459 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,464 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:02,540 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,582 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,620 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-13 06:46:02,632 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,668 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:02,701 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-13 06:46:33,075 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,076 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,076 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,077 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,078 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,078 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,347 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,353 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,381 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,386 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,456 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,500 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:46:33,501 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,532 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-13 06:46:33,549 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,596 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-13 06:46:33,612 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-13 06:46:33,615 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:48:01,418 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:01,541 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:48:04,400 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:04,520 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:48:07,107 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:07,251 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-13 06:48:07,307 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:07,445 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-13 06:48:15,086 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,087 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,088 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,088 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,089 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,090 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,211 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-13 06:48:15,215 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:15,251 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 06:48:15,373 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-13 06:48:15,376 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-13 06:48:15,425 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-13 06:48:15,479 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-13 06:48:15,548 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-13 06:48:18,350 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:48:18,529 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44116
2024-05-13 06:49:07,797 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:07,928 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:49:09,876 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:10,053 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 22432
2024-05-13 06:49:14,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:15,195 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:49:16,514 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 0
2024-05-13 06:49:16,558 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:17,947 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 32777
2024-05-13 06:49:23,412 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:23,531 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 06:49:25,105 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_shipment_order/ HTTP/1.1" 200 0
2024-05-13 06:49:25,154 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:25,796 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 42020
2024-05-13 06:49:33,503 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:49:33,568 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:52:33,492 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:52:33,562 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:55:33,490 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:55:33,549 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 06:58:33,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 06:58:33,554 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:01:33,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:01:33,556 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:04:33,487 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:04:33,552 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:07:33,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:07:33,553 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:10:33,528 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:10:33,614 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:13:33,490 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:13:33,558 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:16:33,497 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:16:33,573 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:19:33,484 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:19:33,554 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:22:33,483 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:22:33,544 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:25:33,502 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:25:33,580 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:28:33,482 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:28:33,576 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:31:33,483 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:31:33,548 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:34:33,488 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:34:33,593 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:37:33,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:37:33,554 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:37:51,547 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:37:51,574 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:37:51,650 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 07:37:51,708 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-13 07:40:33,968 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:40:34,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:44:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:44:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:47:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:47:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:50:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:50:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:53:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:53:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:56:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:56:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 07:59:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 07:59:08,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:02:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:02:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:05:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:05:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:08:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:08:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:11:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:11:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-13 08:14:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:14:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:17:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:17:08,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:20:07,986 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 08:20:08,037 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:20:08,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:23:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:23:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:26:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:26:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:29:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:29:08,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:32:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:32:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:35:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:35:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:38:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:38:08,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:41:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:41:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:44:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:44:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:47:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:47:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:50:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:50:08,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:53:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:53:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:56:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:56:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:59:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 08:59:30,694 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 08:59:30,695 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 08:59:30,755 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:30,783 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:30,845 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 08:59:30,907 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-13 08:59:31,821 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:31,890 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-13 08:59:31,957 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:32,014 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:32,143 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 08:59:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,258 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 08:59:33,352 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,352 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,411 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,413 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,423 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,424 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,458 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,458 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,460 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 08:59:33,485 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,561 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,563 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,566 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,652 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 08:59:33,658 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:33,665 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-13 08:59:33,707 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-13 08:59:33,768 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-13 08:59:33,818 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-13 08:59:33,878 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-13 08:59:33,965 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-13 08:59:34,110 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-13 08:59:34,166 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 08:59:34,354 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1828
2024-05-13 09:01:33,484 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:33,570 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:01:51,901 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:51,904 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:51,905 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:51,905 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:51,906 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:51,907 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:52,084 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 09:01:52,090 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:52,129 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-13 09:01:52,134 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-13 09:01:52,194 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-13 09:01:52,244 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-13 09:01:52,320 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-13 09:01:52,357 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-13 09:01:53,545 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:01:53,715 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2884
2024-05-13 09:03:20,801 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:03:20,900 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 09:03:20,956 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:03:20,958 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-13 09:03:21,013 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:03:21,074 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 09:03:21,081 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 09:03:23,922 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-13 09:03:23,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:03:24,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 44333
2024-05-13 09:04:33,483 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:04:33,557 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:06:11,496 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 09:06:11,497 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-05-13 09:06:11,617 token_utils.verify_access_token  30 INFO    => refresh_token: None
2024-05-13 09:06:11,696 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-05-13 09:06:11,763 token_utils.verify_access_token  38 ERROR   => Refresh token 無效
2024-05-13 09:06:11,764 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-05-13 09:06:11,764 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-05-13 09:06:17,228 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-05-13 09:06:17,377 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-05-13 09:06:17,429 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:17,494 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 09:06:17,554 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 09:06:17,555 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-13 09:06:17,605 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-13 09:06:17,606 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-13 09:06:17,681 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:17,706 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:17,709 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:17,712 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:17,797 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-13 09:06:17,940 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2884
2024-05-13 09:06:18,524 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42319
2024-05-13 09:06:18,573 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 46450
2024-05-13 09:06:19,305 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:19,380 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-13 09:06:19,437 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-05-13 09:06:19,437 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-13 09:06:19,438 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 09:06:19,492 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:19,493 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:19,495 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:19,616 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-13 09:06:19,666 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 09:06:19,681 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 09:06:20,752 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-05-13 09:06:20,808 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:22,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 151523
2024-05-13 09:06:28,670 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:28,671 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:28,795 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 09:06:28,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 09:06:30,332 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:30,998 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42486
2024-05-13 09:06:42,077 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:42,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-05-13 09:06:42,217 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:42,218 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:42,218 token_utils.verify_access_token  30 INFO    => refresh_token: dd535df1-84a2-4fb6-ac67-241dbdd84602
2024-05-13 09:06:42,333 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-13 09:06:42,337 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 09:06:42,498 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 09:07:33,484 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:07:33,558 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:10:33,487 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:10:33,551 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:13:33,495 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:13:33,563 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:14:09,030 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-05-13 09:14:09,031 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-05-13 09:14:09,082 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:14:09,108 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:14:09,178 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 69
2024-05-13 09:14:09,231 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 1272
2024-05-13 09:14:48,039 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 09:14:53,264 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:14:53,265 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:14:53,398 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-05-13 09:14:53,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 1272
2024-05-13 09:15:47,868 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:15:47,934 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 09:15:47,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:15:48,101 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:15:49,422 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-05-13 09:15:49,467 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:15:50,050 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:16:33,506 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:16:33,594 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:19:33,514 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:19:33,585 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:22:33,510 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:22:33,719 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:23:14,699 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:14,839 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:23:16,205 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:16,803 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:23:40,557 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:40,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:23:40,682 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:40,743 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 09:23:40,809 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:40,885 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:40,886 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 09:23:40,886 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-13 09:23:40,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:41,007 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:41,029 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 09:23:41,076 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 09:23:41,101 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:41,260 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4899
2024-05-13 09:23:41,264 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:23:41,320 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 09:23:42,030 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:42,586 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:23:57,388 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:23:57,509 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:26:10,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:26:10,964 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:26:30,556 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:26:30,685 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:26:40,701 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:26:40,785 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:28:14,541 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:28:14,674 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:28:20,879 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:28:20,990 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:29:04,179 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:29:04,348 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24815
2024-05-13 09:29:06,832 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:29:06,902 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 113
2024-05-13 09:29:09,673 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:29:10,228 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:29:40,704 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:29:40,790 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:31:04,670 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 09:31:09,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:31:09,646 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:32:40,706 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:32:40,792 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:35:40,714 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:35:40,780 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:38:40,713 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:38:40,781 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:41:40,716 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:41:40,784 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:44:40,719 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:44:40,797 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:47:40,698 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:47:40,760 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:50:19,286 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 09:50:23,628 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:50:28,281 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 417543
2024-05-13 09:50:40,701 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:50:40,775 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:51:51,492 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 09:51:53,782 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:51:58,208 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419541
2024-05-13 09:52:06,124 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:52:06,220 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 113
2024-05-13 09:52:09,890 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:52:14,622 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 09:53:40,702 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:53:40,777 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:54:51,258 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:54:51,284 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:54:51,416 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 09:54:51,483 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 09:56:40,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:56:41,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 09:59:40,938 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 09:59:41,005 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:02:41,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:02:41,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-13 10:05:40,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:05:41,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:08:40,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:08:41,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:11:40,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:11:41,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:14:41,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:14:41,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:17:40,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:17:41,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:20:41,015 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 10:20:41,067 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:20:41,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:23:41,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:23:41,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:26:40,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:26:41,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:29:41,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:29:41,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:32:40,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:32:41,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:32:53,187 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:32:53,218 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:32:53,331 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 10:32:53,337 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 10:35:41,030 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:35:41,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:38:40,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:38:41,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:41:41,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:41:41,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:44:40,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:44:41,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:47:41,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:47:41,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:50:41,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:50:41,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:53:40,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:53:41,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:56:41,006 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:56:41,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 10:59:41,015 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 10:59:41,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:02:41,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:02:41,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:05:41,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:05:41,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:05:52,654 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 11:05:52,655 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 11:05:52,712 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:05:52,738 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:05:52,795 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 11:05:52,851 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 11:08:41,028 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:08:41,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:11:41,012 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:11:41,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:14:40,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:14:41,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:17:41,051 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:17:41,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:20:40,942 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:20:41,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:23:40,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:23:41,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:26:41,051 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:26:41,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:29:40,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:29:41,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:32:40,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:32:41,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:35:41,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:35:41,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:38:40,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:38:41,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:41:40,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:41:41,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:44:40,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:44:41,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:47:40,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:47:41,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:50:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:50:41,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:53:40,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:53:41,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:55:36,670 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:55:36,696 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:55:36,756 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 11:55:36,829 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 11:56:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:56:41,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 11:59:40,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 11:59:41,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:02:41,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:02:41,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:05:41,126 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:05:41,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:08:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:08:41,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:11:40,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:11:41,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:14:40,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:14:41,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:17:41,080 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:17:41,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:20:40,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:20:41,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:23:41,031 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 12:23:41,079 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:23:41,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:26:40,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:26:41,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:29:41,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:29:41,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:30:32,382 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:30:32,409 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:30:32,488 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 12:30:32,569 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 12:32:40,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:32:41,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:35:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:35:41,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:38:40,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:38:41,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:41:41,010 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:41:41,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:44:40,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:44:41,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:47:40,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:47:41,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:50:40,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:50:41,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:53:40,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:53:41,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:56:40,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:56:41,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 12:59:41,013 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 12:59:41,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:02:40,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:02:41,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:03:58,906 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:03:58,933 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:03:59,025 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 13:03:59,066 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 13:05:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:05:41,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:08:40,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:08:41,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:11:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:11:41,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:14:40,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:14:41,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:17:41,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:17:41,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:20:40,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:20:41,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:23:40,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:23:41,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:26:41,000 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:26:41,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:29:41,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:29:41,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:32:40,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:32:41,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:35:40,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:35:41,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:38:41,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:38:41,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:38:43,446 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 13:38:43,447 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 13:38:43,496 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:38:43,522 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:38:43,597 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 13:38:43,639 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 13:41:41,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:41:41,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:44:40,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:44:41,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:47:41,141 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:47:41,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:50:40,703 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:50:40,767 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:53:40,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:53:41,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-13 13:56:41,007 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:56:41,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 13:59:41,019 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 13:59:41,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:02:41,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:02:41,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:05:41,010 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:05:41,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:08:29,399 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:08:29,473 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 14:08:29,553 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-13 14:08:29,884 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:08:30,007 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:08:30,309 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:08:30,460 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:08:31,676 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-05-13 14:08:31,726 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:08:36,325 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 14:08:40,698 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:08:40,795 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:09:16,005 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 14:09:18,682 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:18,682 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:18,749 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:18,817 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 14:09:18,817 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 14:09:18,860 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 14:09:18,965 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-13 14:09:18,966 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-13 14:09:19,015 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:19,016 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:19,154 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 14:09:19,163 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 14:09:20,086 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-13 14:09:20,145 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:20,436 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4679
2024-05-13 14:09:22,329 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:22,629 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42433
2024-05-13 14:09:24,908 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:09:25,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4679
2024-05-13 14:10:56,520 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:10:56,527 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-13 14:10:56,583 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:10:56,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4679
2024-05-13 14:10:56,879 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:11:40,716 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:11:40,783 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:12:56,030 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:56,097 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-13 14:12:56,160 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:56,161 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:56,269 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 14:12:56,277 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 14:12:58,143 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:58,422 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:12:58,478 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:58,746 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:12:59,723 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:59,724 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:12:59,837 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 14:12:59,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 14:13:00,419 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:00,694 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4679
2024-05-13 14:13:05,997 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-05-13 14:13:05,997 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-05-13 14:13:06,053 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:06,054 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:06,146 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:06,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:07,754 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:08,015 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4116
2024-05-13 14:13:09,393 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:09,394 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:09,458 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:09,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:10,434 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:10,435 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:10,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:10,551 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:11,270 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:11,271 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:11,338 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:11,402 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:12,101 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:12,102 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:12,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:12,238 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:12,520 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:12,521 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:12,592 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:12,648 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:13,011 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,012 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:13,144 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:13,299 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,300 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,380 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:13,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:13,628 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,629 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:13,728 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:13,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:14,083 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:14,084 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:14,159 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:14,215 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:15,469 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:15,470 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:15,552 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-05-13 14:13:15,616 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-05-13 14:13:16,329 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:16,598 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 14:13:16,657 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:16,930 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 14:13:22,074 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:22,339 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 14:13:22,402 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:22,661 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 14:13:24,428 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:24,429 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:24,568 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-13 14:13:24,575 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-13 14:13:25,452 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:25,719 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:13:25,774 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:26,049 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:13:27,408 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:27,544 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:13:28,668 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:13:33,220 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 14:14:28,721 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:14:33,349 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 14:14:40,684 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:14:40,756 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:14:46,253 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:14:50,770 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 14:15:00,705 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:00,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:15:00,839 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:00,897 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 14:15:00,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:00,968 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:00,968 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 14:15:00,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:01,073 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:01,079 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-13 14:15:01,131 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:01,232 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 14:15:01,306 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 14:15:01,348 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:15:01,372 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 14:15:02,425 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:06,953 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419539
2024-05-13 14:15:30,960 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:31,090 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:15:35,470 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:35,546 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 84
2024-05-13 14:15:51,960 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:15:56,479 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419538
2024-05-13 14:16:04,754 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:04,830 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 113
2024-05-13 14:16:07,765 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:12,316 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419536
2024-05-13 14:16:25,209 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:25,341 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:16:28,697 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:28,781 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 84
2024-05-13 14:16:32,679 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:37,399 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419535
2024-05-13 14:16:47,628 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:47,726 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 14:16:47,778 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:16:47,887 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:17:00,361 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 0
2024-05-13 14:17:00,410 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:17:00,630 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12504
2024-05-13 14:18:00,858 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:18:00,920 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:19:30,840 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:19:30,963 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 14:21:00,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:21:01,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:24:00,886 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 14:24:00,935 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:24:00,999 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:27:00,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:27:00,941 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:30:00,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:30:00,942 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:33:00,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:33:00,919 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:36:00,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:36:00,919 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:39:00,861 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:39:00,943 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:42:00,855 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:42:00,920 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:45:00,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:45:00,944 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:48:00,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:48:00,939 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:51:00,862 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:51:00,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:51:43,426 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:51:43,456 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:51:43,551 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 14:51:43,596 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 14:54:00,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:54:01,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 14:57:00,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 14:57:01,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:00:01,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:00:01,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:02:41,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:02:42,093 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:03:00,860 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:03:00,955 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:03:40,919 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:03:41,039 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:03:45,398 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:03:45,601 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12504
2024-05-13 15:06:00,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:06:01,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:07:31,111 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:07:31,225 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:07:33,728 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:07:33,931 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12504
2024-05-13 15:09:00,860 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:09:00,936 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:09:04,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:09:05,113 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:10:26,909 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:10:27,041 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:10:34,211 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:10:34,424 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12504
2024-05-13 15:10:42,468 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:10:42,597 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 111
2024-05-13 15:12:00,863 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:12:00,986 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:15:00,856 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:15:00,937 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:16:23,042 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:16:45,401 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:16:48,543 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:16:48,731 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-24450: Cannot pre-process OCI statement

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1043, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-24450: Cannot pre-process OCI statement
2024-05-13 15:16:48,736 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 215654
2024-05-13 15:18:00,858 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:18:00,946 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:18:39,168 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:18:40,946 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:18:41,124 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-24450: Cannot pre-process OCI statement

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1045, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-24450: Cannot pre-process OCI statement
2024-05-13 15:18:41,128 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 215654
2024-05-13 15:19:32,174 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:19:33,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:19:34,174 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-24450: Cannot pre-process OCI statement

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1045, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-24450: Cannot pre-process OCI statement
2024-05-13 15:19:34,185 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 205730
2024-05-13 15:19:39,124 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:19:39,208 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-24450: Cannot pre-process OCI statement

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1045, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-24450: Cannot pre-process OCI statement
2024-05-13 15:19:39,211 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 205730
2024-05-13 15:20:58,094 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:21:00,460 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:21:00,665 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "AGENT_CODE": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1043, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "AGENT_CODE": invalid identifier
2024-05-13 15:21:00,673 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 215608
2024-05-13 15:21:00,836 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:21:00,901 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:23:21,892 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:23:24,623 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:23:24,624 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:23:24,669 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:23:24,745 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 15:23:24,746 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 15:23:24,896 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "AGENT_CODE": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1045, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "AGENT_CODE": invalid identifier
2024-05-13 15:23:24,902 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 215608
2024-05-13 15:24:00,860 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:24:00,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:24:12,825 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:24:15,455 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:24:15,662 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-00904: "TRACK_CHECK": invalid identifier

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 64, in erp_hy_ocv211_214_main_allowance_detail_schedule_b
    return self._handle_action('erp_hy_ocv211_214_main_allowance_detail_schedule_b', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1043, in select_erp_hy_ocv211_214_main_allowance_detail_schedule_b
    cursor.execute(ranked_sql1, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-00904: "TRACK_CHECK": invalid identifier
2024-05-13 15:24:15,667 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 500 215817
2024-05-13 15:24:38,496 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:24:39,578 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:24:40,074 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12882
2024-05-13 15:24:45,781 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:24:45,855 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 111
2024-05-13 15:24:48,256 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:24:48,770 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 12880
2024-05-13 15:25:17,169 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 0
2024-05-13 15:25:17,214 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:25:17,354 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 15:26:56,630 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:26:56,761 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 15:27:00,855 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:27:00,925 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:28:42,417 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 0
2024-05-13 15:28:42,466 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:28:42,597 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 769
2024-05-13 15:29:05,694 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:29:08,779 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:09,043 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 769
2024-05-13 15:29:20,910 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:29:22,609 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:22,886 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 69, in erp_hy_sdv250_main_proof_of_single_allowance
    return self._handle_action('erp_hy_sdv250_main_proof_of_single_allowance', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1251, in select_erp_hy_sdv250_main_proof_of_single_allowance
    report = generate_proof_of_single_allowance(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1273, in generate_proof_of_single_allowance
    "readStatus": group[0]["ACTIONTYPE"],  # 使用者是否讀取此筆資料
KeyError: 'ACTIONTYPE'
2024-05-13 15:29:22,888 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 500 142379
2024-05-13 15:29:36,127 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:29:37,363 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:37,638 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 789
2024-05-13 15:29:38,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:39,115 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 789
2024-05-13 15:29:42,658 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:42,734 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 82
2024-05-13 15:29:44,892 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:29:45,020 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 788
2024-05-13 15:30:00,863 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:30:00,950 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:30:56,944 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:30:57,086 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 15:31:27,365 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 15:31:31,847 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:31:32,098 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 15:33:00,868 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:33:00,933 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:36:00,877 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:36:00,972 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:39:00,863 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:39:00,931 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:42:01,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:42:01,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:45:00,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:45:01,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:48:00,997 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:48:01,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:51:01,000 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:51:01,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:54:01,009 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:54:01,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 15:55:42,820 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 15:55:42,822 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 15:55:42,870 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:55:42,896 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:55:42,963 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 15:55:43,032 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 15:57:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 15:57:01,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:00:01,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:00:01,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:03:00,870 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:03:00,938 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:06:00,866 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:06:00,943 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:09:01,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:09:01,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:12:00,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:12:01,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:15:01,011 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:15:01,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:18:01,021 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:18:01,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:21:01,000 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:21:01,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:24:00,857 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:24:00,925 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:27:00,906 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 16:27:00,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:27:01,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:29:13,138 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-13 16:29:13,198 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:29:13,337 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:29:25,768 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:29:25,795 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:29:25,867 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 16:29:25,937 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 16:30:01,028 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:30:01,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:33:00,997 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:33:01,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:36:00,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:36:01,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:39:01,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:39:01,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:42:00,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:42:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:45:00,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:45:00,922 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:45:25,377 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:45:25,519 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 16:47:14,346 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:47:14,464 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:47:16,098 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:47:16,234 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 16:47:39,271 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:47:39,399 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 788
2024-05-13 16:48:00,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:48:00,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:48:37,816 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:48:37,945 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:48:40,155 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:48:40,317 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 16:49:23,242 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:49:23,377 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:50:01,577 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:50:01,698 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:50:02,810 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:50:02,945 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 16:51:00,852 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:51:00,928 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:53:49,824 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:53:49,969 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:53:50,996 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:53:51,151 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 16:54:00,848 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:54:00,913 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:57:00,862 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:57:00,926 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 16:59:42,933 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:59:43,071 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 16:59:44,223 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:59:44,251 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:59:44,327 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 16:59:44,387 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 16:59:44,727 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 16:59:44,913 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 17:00:00,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:00:00,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:00:07,672 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:00:07,841 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 17:00:08,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:00:09,161 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 43781
2024-05-13 17:00:31,008 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-13 17:00:33,554 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:00:33,732 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 44721
2024-05-13 17:03:00,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:03:01,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:04:42,153 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:04:42,295 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 44721
2024-05-13 17:04:45,071 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-05-13 17:04:45,126 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:04:45,228 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 115
2024-05-13 17:04:47,410 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:04:47,549 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 44719
2024-05-13 17:04:52,344 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:04:52,462 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 145
2024-05-13 17:04:57,638 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:04:57,765 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 44718
2024-05-13 17:05:04,995 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:05:05,124 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 788
2024-05-13 17:05:33,101 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:05:33,232 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 788
2024-05-13 17:06:00,857 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:06:00,931 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:09:00,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:09:00,912 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:12:01,007 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:12:01,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:15:00,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:15:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:18:01,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:18:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:21:01,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:21:01,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:24:01,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:24:01,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:27:01,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:27:01,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:30:01,003 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:30:01,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:32:35,674 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:32:35,701 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:32:35,770 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 17:32:35,829 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 17:33:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:33:01,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:36:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:36:01,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:39:01,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:39:01,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:42:01,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:42:01,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:45:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:45:01,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-13 17:48:01,016 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:48:01,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:51:00,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:51:01,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:54:00,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:54:01,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 17:57:01,000 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 17:57:01,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:00:00,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:00:01,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:03:01,007 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:03:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:06:00,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:06:01,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:09:01,065 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:09:01,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:12:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:12:01,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:15:00,973 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:15:01,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:18:00,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:18:01,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:21:00,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:21:01,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:24:00,995 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:24:01,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:27:01,007 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 18:27:01,058 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:27:01,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:28:33,070 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 18:28:33,096 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 18:28:33,140 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:28:33,167 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:28:33,228 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 18:28:33,293 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 18:30:01,012 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:30:01,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:33:00,858 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:33:00,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:36:00,864 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:36:00,947 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:39:00,858 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:39:00,922 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:42:00,872 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:42:00,933 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:45:00,852 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:45:00,937 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:48:00,850 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:48:00,911 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:51:00,864 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:51:00,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:54:00,882 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:54:00,954 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 18:57:00,857 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 18:57:00,943 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:00:00,875 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:00:00,944 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:03:00,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:03:01,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:06:23,552 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:06:23,631 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:09:00,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:09:01,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:12:00,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:12:01,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:15:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:15:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:18:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:18:01,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:21:00,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:21:01,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:24:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:24:01,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:27:00,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:27:01,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:30:00,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:30:01,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:33:00,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:33:01,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:36:00,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:36:01,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:39:00,996 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:39:01,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:42:00,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:42:01,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:45:00,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:45:01,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:48:00,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:48:01,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:51:00,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:51:01,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:54:01,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:54:01,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 19:57:00,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 19:57:01,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:00:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:00:01,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:03:01,005 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:03:01,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:06:00,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:06:01,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:09:01,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:09:01,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:12:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:12:01,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:15:00,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:15:01,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:18:00,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:18:01,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:21:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:21:01,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:24:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:24:01,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:27:00,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:27:01,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:30:01,009 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 20:30:01,062 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:30:01,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:33:00,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:33:01,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:36:00,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:36:01,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:39:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:39:01,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:42:00,973 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:42:01,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:45:00,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:45:01,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:48:00,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:48:01,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:51:00,996 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:51:01,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:54:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:54:01,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 20:57:00,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 20:57:01,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:00:00,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:00:01,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:03:00,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:03:01,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:06:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:06:01,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:09:00,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:09:01,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:12:00,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:12:01,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:15:00,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:15:01,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:18:00,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:18:01,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:21:00,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:21:01,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:24:00,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:24:01,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:27:00,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:27:01,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:30:00,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:30:01,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:33:00,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:33:01,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:36:00,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:36:01,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-13 21:39:00,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:39:01,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:42:00,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:42:01,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:45:00,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:45:01,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:48:00,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:48:01,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:51:01,006 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:51:01,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:54:00,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:54:01,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 21:57:00,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 21:57:01,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:00:00,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:00:01,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:03:00,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:03:01,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:06:00,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:06:01,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:09:00,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:09:01,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:12:00,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:12:01,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:15:00,997 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:15:01,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:16:04,405 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 22:16:04,406 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 22:16:04,462 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:04,491 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:04,578 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 22:16:04,633 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 22:16:11,831 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:11,902 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:16:11,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-13 22:16:12,138 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,139 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-13 22:16:12,139 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-13 22:16:12,165 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-13 22:16:12,220 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,270 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,271 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,282 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-13 22:16:12,344 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-13 22:16:12,392 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:16:12,415 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-13 22:16:12,604 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-13 22:16:12,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-13 22:16:12,745 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-13 22:19:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:19:12,230 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:22:11,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:22:12,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:25:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:25:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:28:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:28:12,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:31:12,015 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-13 22:31:12,069 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:31:12,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:34:11,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:34:12,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:37:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:37:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:40:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:40:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:43:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:43:12,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:46:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:46:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:49:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:49:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:52:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:52:12,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:55:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:55:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 22:58:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 22:58:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:01:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:01:12,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:04:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:04:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:07:11,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:07:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:10:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:10:12,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:13:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:13:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:16:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:16:12,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:19:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:19:12,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:22:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:22:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:25:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:25:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:28:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:28:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:31:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:31:12,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:34:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:34:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:37:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:37:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:40:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:40:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:43:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:43:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:46:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:46:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:49:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:49:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:52:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:52:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:55:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:55:12,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-13 23:58:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-13 23:58:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
