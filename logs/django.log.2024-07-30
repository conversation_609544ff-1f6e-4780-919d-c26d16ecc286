2024-07-30 06:14:20,655 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 06:14:20,655 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 06:14:20,926 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 06:14:20,927 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 06:14:20,969 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 06:14:20,969 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 06:14:59,516 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-30 06:14:59,656 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-30 06:14:59,802 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 06:14:59,871 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 06:14:59,872 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-30 06:14:59,923 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-30 06:14:59,923 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-30 06:15:00,210 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 06:15:00,331 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 06:15:00,438 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 06:15:00,837 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 06:15:06,685 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 06:15:06,762 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-30 06:15:06,763 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-30 06:15:06,763 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-30 06:15:06,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:15:06,974 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:15:07,032 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:15:13,811 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 06:15:13,883 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-30 06:15:14,239 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:15:17,410 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-30 06:15:17,600 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:25:18,032 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:26:14,761 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-30 06:26:14,859 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-30 06:26:15,031 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18395
2024-07-30 06:26:18,710 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:26:21,423 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 06:26:21,651 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 06:26:24,863 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-30 06:26:25,053 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 06:26:40,116 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 06:26:42,271 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 06:27:09,657 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:27:09,677 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:27:09,770 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:27:15,144 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-30 06:27:17,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 06:27:29,772 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-30 06:27:29,933 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:27:29,941 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:27:30,094 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:27:30,144 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 06:30:37,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 06:30:45,959 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 06:31:14,977 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:31:37,636 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:32:09,842 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:32:35,985 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:32:40,465 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:32:52,473 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:32:54,855 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:32:59,356 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:36:47,200 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:36:48,243 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:37:14,990 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:41:36,522 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:41:37,688 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:46:17,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 06:46:17,201 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 06:52:27,802 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:52:28,821 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:52:46,196 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-30 06:52:46,377 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:52:48,420 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:52:49,952 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 06:52:57,500 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 06:52:59,471 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 06:53:17,959 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 06:53:18,062 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:53:18,129 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:53:18,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:53:20,182 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 06:53:22,509 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:53:22,569 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:53:22,577 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:53:25,716 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 06:53:28,597 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 06:53:28,620 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:53:28,641 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:53:28,748 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:56:17,497 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 06:56:17,518 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:56:17,585 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:56:17,590 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:57:13,892 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-30 06:57:14,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 06:57:14,205 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 06:57:14,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 06:57:14,600 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 06:57:14,749 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:57:14,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:57:14,857 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 06:57:14,868 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 06:57:15,096 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 06:57:18,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 06:57:21,062 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 06:57:21,093 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 06:57:21,106 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 06:57:21,265 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:00:14,203 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:03:14,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:06:14,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:09:14,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:12:14,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:15:14,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:18:14,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:21:14,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:24:14,209 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:27:14,198 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:30:14,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:33:14,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:36:14,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:36:18,224 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 07:36:18,279 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 07:36:39,512 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-07-30 07:36:39,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:36:39,679 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:36:39,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:36:39,790 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:36:39,800 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 07:39:14,191 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:41:46,588 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 07:41:53,061 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-30 07:41:53,191 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 07:41:56,029 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 07:41:57,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:41:57,965 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:41:58,023 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:41:58,050 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:41:58,086 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 07:42:14,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:45:07,432 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 07:45:10,504 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 07:45:13,249 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 07:45:14,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:45:14,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:45:14,774 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:45:14,855 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:45:14,910 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:45:14,919 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 07:46:34,884 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:46:35,012 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 07:46:35,176 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 07:46:35,242 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 07:46:35,724 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 07:46:35,728 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 07:46:50,530 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:46:50,693 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:46:50,800 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:46:50,857 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:46:50,933 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 07:46:51,319 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 07:46:51,324 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 07:46:53,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 07:46:54,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:46:54,793 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:46:54,823 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 07:46:54,864 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:46:54,959 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:49:35,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:49:51,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:52:19,181 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:52:19,187 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:52:19,312 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:52:23,892 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:52:24,110 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 07:52:24,337 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 07:52:24,458 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 07:52:24,559 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:52:24,568 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:52:24,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 07:52:24,698 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:52:24,937 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 07:52:27,616 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 146636
2024-07-30 07:52:30,598 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 07:52:33,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 07:52:35,566 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 07:52:38,320 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 07:52:40,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:52:40,198 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 07:52:40,299 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:52:40,355 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:52:40,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:52:44,874 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 07:52:58,037 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-07-30 07:52:58,448 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2024-07-30 07:53:01,016 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 414
2024-07-30 07:53:06,647 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 70
2024-07-30 07:53:16,639 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 07:53:19,451 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 07:55:06,325 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:55:06,329 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:06,438 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:11,182 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:11,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:11,305 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:55:16,255 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:16,264 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:16,370 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:55:23,354 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:55:23,392 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:23,402 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:25,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:55:27,218 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:27,223 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:27,341 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:55:57,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:55:57,284 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:55:57,291 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:57:09,162 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:57:09,227 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:57:09,239 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:57:10,808 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 07:57:10,992 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:57:10,993 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:57:11,091 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:57:12,010 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 07:57:22,563 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242158
2024-07-30 07:57:55,741 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2024-07-30 07:57:55,907 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 07:57:55,919 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 07:57:55,942 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 07:57:56,038 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 07:57:56,058 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-30 07:58:07,997 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239054
2024-07-30 07:58:24,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 07:58:37,581 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_letter/ HTTP/1.1" 200 0
2024-07-30 07:58:37,738 basehttp.log_message 161 INFO    => "POST /api/documents/delete_letter/ HTTP/1.1" 200 69
2024-07-30 07:58:48,474 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242158
2024-07-30 08:00:11,228 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 08:00:11,241 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 08:00:11,363 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 08:00:14,674 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 08:00:14,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 08:00:18,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:00:33,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 08:00:34,993 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 08:01:24,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:04:24,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:07:24,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:10:24,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:13:24,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:16:24,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:19:24,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:22:24,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:26:24,543 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:29:14,972 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:30:26,875 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 08:30:26,876 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 08:30:27,038 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 08:30:27,113 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 08:31:43,688 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-30 08:31:43,907 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:32:15,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:32:44,492 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:35:14,946 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:35:50,331 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-30 08:35:50,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 08:35:51,568 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:36:28,754 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 08:36:28,820 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-30 08:36:29,195 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 08:36:30,411 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-30 08:36:30,599 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 08:36:49,964 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 08:37:21,914 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-30 08:37:21,965 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-30 08:37:21,965 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-30 08:37:22,101 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 08:37:22,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 08:37:22,264 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 08:37:22,307 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 08:37:23,654 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-30 08:37:23,710 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 08:37:23,813 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 08:37:23,875 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 08:37:23,891 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-30 08:37:24,012 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 08:37:53,478 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-30 08:37:55,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 147463
2024-07-30 08:37:59,734 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 08:38:02,512 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 08:38:06,617 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 08:38:06,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 08:38:06,725 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 08:38:06,781 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 08:38:14,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:38:18,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 08:38:22,052 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:38:29,113 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/insert_bullet_board/ HTTP/1.1" 200 0
2024-07-30 08:38:29,276 basehttp.log_message 161 INFO    => "POST /api/documents/insert_bullet_board/ HTTP/1.1" 200 69
2024-07-30 08:38:31,776 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:38:32,986 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:41:14,956 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:44:00,249 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:44:05,994 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:44:06,144 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 08:44:06,280 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 08:44:06,281 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-30 08:44:06,281 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-30 08:44:06,417 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 08:44:06,556 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 08:44:06,741 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:44:06,860 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 08:44:07,023 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 08:44:08,011 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:44:24,987 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:44:25,125 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 08:44:25,266 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:44:25,597 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 08:44:25,749 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 08:44:27,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:46:06,963 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:46:07,309 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 08:46:07,457 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:46:07,808 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 08:46:07,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 08:46:08,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:46:21,104 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:47:06,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:47:11,210 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:47:25,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:49:07,971 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:50:07,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:50:26,011 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:51:07,035 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:51:16,946 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:51:21,414 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:51:32,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:51:32,321 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 08:51:32,592 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 08:51:32,663 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 08:51:32,930 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:51:33,106 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 08:51:33,143 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 08:51:34,251 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:52:21,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:52:51,005 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:53:37,302 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:54:32,976 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:55:04,137 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:55:32,839 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 08:55:33,085 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 08:55:33,209 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 08:55:33,272 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 08:55:33,535 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 08:55:33,727 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 08:55:33,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 08:55:34,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 08:58:33,994 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-30 08:58:34,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:00:53,531 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:01:01,327 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:01:01,463 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 09:01:01,660 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 09:01:01,727 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 09:01:02,012 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 09:01:02,175 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 09:01:02,226 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 09:01:03,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:03:42,975 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:03:43,848 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_bullet_board/ HTTP/1.1" 200 0
2024-07-30 09:03:43,976 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:03:46,275 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6022
2024-07-30 09:03:50,354 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:03:54,543 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:03:56,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:04:01,471 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:04:02,122 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:04:13,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:05:17,175 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:07:01,996 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:09:19,037 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:09:24,746 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:10:00,386 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:10:01,462 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:10:48,086 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:11:35,338 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:11:38,366 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:11:43,023 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:11:45,647 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:13:02,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:13:49,229 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:13:54,125 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:13:56,355 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:13:57,859 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:14:02,639 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:14:04,874 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:14:07,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:14:14,353 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:16:01,986 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:16:15,045 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:16:29,876 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:16:44,509 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:16:49,781 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:16:52,027 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:16:53,386 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:18:10,025 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:19:01,999 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:19:08,028 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:19:14,336 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:19:17,976 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:19:21,866 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:20:20,607 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:20:22,534 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:20:24,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:20:25,955 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:20:31,422 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:20:33,665 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6028
2024-07-30 09:20:37,661 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:20:43,698 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:20:45,934 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6028
2024-07-30 09:20:51,619 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:21:00,486 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:21:02,709 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6028
2024-07-30 09:21:03,680 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:21:07,372 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:21:09,600 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:21:13,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:21:13,473 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 09:21:13,682 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 09:21:13,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 09:21:14,005 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:21:14,197 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 09:21:14,241 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 09:21:16,785 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:21:20,252 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:21:22,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6026
2024-07-30 09:21:25,745 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_bullet_board/ HTTP/1.1" 200 0
2024-07-30 09:21:25,860 basehttp.log_message 161 INFO    => "POST /api/documents/delete_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:21:27,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 09:21:29,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:21:29,314 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 09:21:29,456 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-30 09:21:29,593 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 09:21:29,790 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 09:21:29,978 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 09:21:29,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 09:21:31,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:21:43,726 basehttp.log_message 161 INFO    => "POST /api/documents/insert_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:21:48,043 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6027
2024-07-30 09:21:51,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:21:59,070 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30386
2024-07-30 09:22:06,224 basehttp.log_message 161 INFO    => "POST /api/documents/update_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:22:08,447 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6031
2024-07-30 09:22:11,315 basehttp.log_message 161 INFO    => "POST /api/documents/delete_bullet_board/ HTTP/1.1" 200 69
2024-07-30 09:22:12,537 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 09:22:47,866 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5792
2024-07-30 09:22:49,989 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 09:22:50,126 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:22:50,229 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:22:50,298 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:22:53,175 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-30 09:22:53,398 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:22:55,089 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 09:22:55,467 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 09:22:56,628 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:23:06,865 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:23:15,036 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:23:41,014 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/insert_dealer_group/ HTTP/1.1" 200 0
2024-07-30 09:23:41,306 basehttp.log_message 161 INFO    => "POST /api/dealers/insert_dealer_group/ HTTP/1.1" 200 69
2024-07-30 09:23:45,968 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3298
2024-07-30 09:23:47,557 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:23:53,419 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/update_dealer_group/ HTTP/1.1" 200 0
2024-07-30 09:23:53,553 basehttp.log_message 161 INFO    => "POST /api/dealers/update_dealer_group/ HTTP/1.1" 200 69
2024-07-30 09:23:55,941 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3333
2024-07-30 09:23:57,714 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:24:26,572 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/delete_dealer_group/ HTTP/1.1" 200 0
2024-07-30 09:24:26,718 basehttp.log_message 161 INFO    => "POST /api/dealers/delete_dealer_group/ HTTP/1.1" 200 69
2024-07-30 09:24:28,100 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-30 09:24:29,279 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:24:39,619 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-30 09:24:39,794 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-30 09:24:41,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-30 09:24:41,276 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-30 09:24:41,390 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-30 09:24:41,442 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-30 09:24:41,495 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:24:41,555 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-30 09:24:41,621 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-30 09:24:41,674 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-30 09:24:41,745 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50625
2024-07-30 09:24:43,695 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 09:24:43,884 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:24:43,885 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:24:43,990 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:24:55,296 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242158
2024-07-30 09:25:54,637 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:25:54,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:25:54,713 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:26:23,072 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:26:23,131 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:26:23,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:26:26,271 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-30 09:26:26,524 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 09:26:37,715 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242119
2024-07-30 09:27:04,969 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-30 09:27:05,117 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 09:27:16,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242138
2024-07-30 09:27:29,306 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:28:09,104 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242138
2024-07-30 09:28:33,965 log.log_response 230 WARNING => Not Found: /api/documents/delete_letter/
2024-07-30 09:28:33,965 basehttp.log_message 161 WARNING => "POST /api/documents/delete_letter/ HTTP/1.1" 404 111
2024-07-30 09:28:38,641 main_utils.remove_folder 412 INFO    => Deleted folder: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001
2024-07-30 09:28:38,645 basehttp.log_message 161 INFO    => "POST /api/documents/delete_letter/ HTTP/1.1" 200 69
2024-07-30 09:28:49,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 245153
2024-07-30 09:29:05,347 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 245153
2024-07-30 09:29:09,877 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1092
2024-07-30 09:29:14,062 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:29:14,065 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:29:14,188 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 09:29:14,201 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-30 09:29:14,281 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:30:29,397 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:33:33,135 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:36:30,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:39:30,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:40:23,169 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_upload/ HTTP/1.1" 200 0
2024-07-30 09:40:23,381 main_utils.remove_file 426 INFO    => Deleted file: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 09:40:23,381 log.log_response 230 WARNING => Bad Request: /api/documents/select_letter_upload/
2024-07-30 09:40:23,381 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_upload/ HTTP/1.1" 400 159
2024-07-30 09:41:05,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_upload/ HTTP/1.1" 200 75
2024-07-30 09:41:22,883 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_upload/ HTTP/1.1" 200 78
2024-07-30 09:41:59,908 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_letter/ HTTP/1.1" 200 0
2024-07-30 09:42:00,092 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2024-07-30 09:42:11,557 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242184
2024-07-30 09:42:20,843 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1103
2024-07-30 09:42:29,306 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:42:44,555 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 1103
2024-07-30 09:42:57,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42265
2024-07-30 09:42:57,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 09:42:57,612 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 09:42:57,690 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 09:43:08,485 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2024-07-30 09:43:20,153 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242193
2024-07-30 09:43:43,938 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 09:43:55,536 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242212
2024-07-30 09:45:29,994 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:48:30,011 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:48:54,699 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 09:49:08,339 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 242193
2024-07-30 09:51:29,966 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:52:36,010 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 09:52:37,860 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-07-30 09:52:37,860 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-30 09:52:38,035 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 614
2024-07-30 09:52:38,066 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-30 09:52:39,995 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2024-07-30 09:52:40,491 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 22280
2024-07-30 09:53:43,237 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 09:53:51,678 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 09:54:03,198 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 09:54:30,000 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:55:29,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 09:57:20,617 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 09:57:29,307 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 09:57:41,553 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 09:57:44,805 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 09:57:55,507 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 09:57:58,865 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 09:58:09,420 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:00:29,962 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:03:29,979 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:04:17,111 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:04:21,444 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:04:22,543 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 10:04:33,012 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:04:35,215 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:04:45,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:05:05,169 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:05:16,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:05:19,236 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 10:05:19,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 10:05:19,360 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 10:05:21,535 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:05:32,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:06:28,286 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 10:06:28,398 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 10:06:28,443 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 10:06:29,304 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:06:48,778 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:06:59,313 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:08:08,214 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:09:03,859 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:09:14,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:09:18,134 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:09:29,119 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:09:29,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:10:32,197 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:10:32,367 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 10:10:32,806 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 10:10:32,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6145
2024-07-30 10:10:33,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 10:10:33,415 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 10:12:29,978 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 10:15:29,972 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-30 10:15:29,972 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-30 10:16:59,240 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:18:04,830 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:19:51,454 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:20:03,109 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:20:12,965 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-30 10:20:13,466 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-30 10:20:13,597 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 10:20:13,835 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 10:20:13,982 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6145
2024-07-30 10:20:14,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 10:20:14,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 10:20:16,054 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 10:20:16,195 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 10:20:16,296 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 10:20:16,360 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 10:20:18,958 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148225
2024-07-30 10:20:26,035 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_email/ HTTP/1.1" 200 0
2024-07-30 10:20:26,262 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_email/ HTTP/1.1" 200 69
2024-07-30 10:20:29,009 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148244
2024-07-30 10:23:27,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148264
2024-07-30 10:23:30,786 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-30 10:23:30,930 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-30 10:23:33,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148931
2024-07-30 10:23:34,569 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 10:23:34,755 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 10:23:34,784 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 10:23:34,913 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 10:23:46,138 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 10:23:50,213 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:24:01,808 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:24:05,954 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:24:16,639 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:26:43,353 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:26:56,007 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:29:04,375 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:29:24,653 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:36:51,017 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:37:03,691 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:38:41,785 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 10:39:42,142 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:39:51,384 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:39:51,828 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:40:05,601 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:40:06,221 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:40:13,973 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:40:26,179 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 10:41:41,053 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:41:53,456 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 10:42:17,180 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:42:25,408 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:42:28,373 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:42:36,549 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:42:41,421 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 10:42:41,421 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 105
2024-07-30 10:43:20,861 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:43:26,220 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 10:43:26,221 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 125
2024-07-30 10:43:46,890 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:43:50,523 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 10:43:50,524 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 125
2024-07-30 10:52:29,603 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:52:33,648 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 10:52:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 10:52:35,095 email_utils.send_report_email  64 INFO    => 開始執行郵件發送任務，PUDCNO: 11302001
2024-07-30 10:52:35,099 email_utils.send_report_email  69 INFO    => 為 PUDCNO: 11302001 獲取到 24 條郵件信息
2024-07-30 10:52:35,100 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,120 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,121 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,137 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,138 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,152 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,153 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,168 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,169 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,186 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,187 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,199 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,200 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,215 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,215 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,216 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,216 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,229 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,230 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,230 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,231 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,245 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,246 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,246 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,246 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,262 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,262 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,263 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,263 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,276 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,277 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,277 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,277 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,292 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,293 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,293 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,293 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,309 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,309 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,309 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,310 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,324 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,324 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,325 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,325 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,340 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,341 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,341 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,341 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,356 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,357 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,357 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,357 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,371 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,372 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,372 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,372 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,402 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,403 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,403 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,403 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,418 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,418 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,419 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,419 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,436 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,437 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,437 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,437 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,449 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,450 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,450 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,450 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,466 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,466 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,466 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,467 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,480 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,480 email_utils.send_report_email  78 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:52:35,480 email_utils.send_report_email  93 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,481 email_utils.send_report_email 103 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11302001\測試001.docx
2024-07-30 10:52:35,495 email_utils.send_report_email 111 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:52:35,510 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:52:46,634 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 10:58:31,130 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 10:58:44,939 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 10:58:46,109 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 10:59:00,095 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 10:59:21,190 email_utils.send_report_email  57 INFO    => 開始執行郵件發送任務，PUDCNO: 11302001
2024-07-30 10:59:21,194 email_utils.send_report_email  62 INFO    => 為 PUDCNO: 11302001 獲取到 24 條郵件信息
2024-07-30 10:59:21,194 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,230 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,231 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,258 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,259 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,277 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,277 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,292 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,293 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,308 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,309 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,324 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,324 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,339 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,340 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,340 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,363 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,387 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,388 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,388 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,389 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,418 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,419 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,419 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,420 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,449 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,450 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,450 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,451 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,465 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,466 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,466 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,467 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,497 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,497 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,497 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,499 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,528 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,529 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,529 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,531 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,557 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,558 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,558 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,559 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,590 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,591 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,591 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,592 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,621 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,621 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,621 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,623 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,652 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,652 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,653 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,654 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,683 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,684 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,684 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,686 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,714 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,715 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,715 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,716 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,746 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,747 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,747 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,749 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,777 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,778 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,778 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,779 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,808 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,809 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,809 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,811 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,839 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,840 email_utils.send_report_email  71 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 10:59:21,840 email_utils.send_report_email  86 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,842 email_utils.attach_file_to_email  51 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 10:59:21,870 email_utils.send_report_email 104 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 10:59:21,878 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 10:59:33,855 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 11:03:39,435 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:05:48,138 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:06:05,933 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:06:16,960 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:06:19,696 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 11:06:19,697 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 122
2024-07-30 11:06:52,222 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:06:56,617 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 11:06:56,618 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 105
2024-07-30 11:07:24,070 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:07:28,130 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:07:40,397 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:07:50,761 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-30 11:07:50,877 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-30 11:07:51,204 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 11:07:51,308 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 11:07:51,384 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-30 11:07:51,384 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-30 11:07:51,385 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-30 11:07:51,568 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 11:07:51,793 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6145
2024-07-30 11:07:51,925 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 11:07:52,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 11:13:47,371 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:13:56,014 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:14:07,263 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:14:10,587 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:14:21,698 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:17:09,000 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 11:19:10,287 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:19:18,926 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:19:31,613 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:19:34,717 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:19:47,194 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:20:00,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:20:37,912 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:21:58,139 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:23:29,535 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 11:26:02,715 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:26:19,298 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:26:32,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:26:35,750 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 11:26:35,750 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 133
2024-07-30 11:27:07,321 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:27:19,648 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-30 11:27:20,153 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:27:32,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:31:19,780 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:31:35,369 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:31:50,943 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 11:31:52,188 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-30 11:31:52,490 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:32:03,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:32:13,658 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:32:24,777 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:35:24,170 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:35:32,694 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:35:45,892 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:35:51,020 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:36:04,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:40:12,233 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:41:39,277 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:41:46,072 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:41:58,223 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:42:05,932 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:42:17,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:47:22,625 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:47:28,664 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:47:40,337 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:47:52,111 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:48:03,525 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:52:00,786 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:52:08,016 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:52:19,398 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:52:23,199 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:52:34,153 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:57:56,448 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:58:00,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 11:58:01,754 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:58:13,356 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 11:58:16,207 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 11:58:27,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 11:59:38,614 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 11:59:47,120 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 11:59:58,157 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:00:00,888 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:00:11,926 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 12:21:21,260 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:21:26,423 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 12:21:28,056 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:21:29,144 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 12:21:39,940 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:21:43,326 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 12:21:43,326 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 105
2024-07-30 12:21:48,130 log.log_response 230 WARNING => Bad Request: /api/documents/letter_email/
2024-07-30 12:21:48,130 basehttp.log_message 161 WARNING => "POST /api/documents/letter_email/ HTTP/1.1" 400 105
2024-07-30 12:22:08,033 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:22:11,930 email_utils.send_report_email  58 INFO    => 開始執行郵件發送任務，pudcno: 11302001
2024-07-30 12:22:11,934 email_utils.send_report_email  63 INFO    => 為 pudcno: 11302001 獲取到 24 條郵件信息
2024-07-30 12:22:11,934 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:11,954 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:11,954 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:11,967 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:11,968 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:11,984 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:11,984 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:11,998 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:11,999 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,017 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,018 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,032 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,032 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,045 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,045 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,045 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx
2024-07-30 12:22:12,068 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx
2024-07-30 12:22:12,122 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,122 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,123 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx
2024-07-30 12:22:12,123 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx
2024-07-30 12:22:12,140 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,141 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,141 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,142 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,155 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,156 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,156 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,157 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,173 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,173 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,173 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,174 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,186 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,187 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,187 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,187 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,203 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,204 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,204 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,204 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,220 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,220 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,220 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,221 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,234 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,235 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,235 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,236 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,251 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,252 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,252 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,252 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,265 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,265 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,266 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,266 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,282 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,282 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,282 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,283 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,297 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,298 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,298 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,299 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,313 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,314 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,314 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,314 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,328 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,329 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,329 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,330 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,344 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,345 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,345 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,345 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,359 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,359 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:22:12,359 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,360 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:22:12,376 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:22:12,383 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:22:23,989 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 12:23:19,066 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:23:26,724 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:23:38,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:23:41,702 email_utils.send_report_email  58 INFO    => 開始執行郵件發送任務，pudcno: 11302001
2024-07-30 12:23:41,707 email_utils.send_report_email  63 INFO    => 為 pudcno: 11302001 獲取到 24 條郵件信息
2024-07-30 12:23:41,707 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,734 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,735 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,749 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,750 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,768 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,769 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,781 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,781 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,799 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,800 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,813 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,814 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,828 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,828 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,829 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx
2024-07-30 12:23:41,830 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx
2024-07-30 12:23:41,858 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,859 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,859 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx
2024-07-30 12:23:41,859 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx
2024-07-30 12:23:41,877 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,878 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,878 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,878 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,891 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,893 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,893 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,893 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,907 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,907 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,908 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,908 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,922 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,922 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,940 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,941 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,953 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,953 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,953 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,954 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,969 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,970 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,970 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,970 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,983 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:41,984 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:41,984 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:41,984 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,003 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,003 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,004 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,004 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,016 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,016 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,017 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,017 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,032 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,032 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,032 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,033 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,046 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,046 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,047 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,047 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,062 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,063 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,063 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,063 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,077 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,078 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,078 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,078 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,093 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,093 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,094 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,094 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,111 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,112 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:23:42,112 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,113 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx/測試001.docx
2024-07-30 12:23:42,125 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:23:42,132 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:23:54,352 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 12:26:39,304 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:26:46,332 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:26:58,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:27:02,202 email_utils.send_report_email  58 INFO    => 開始執行郵件發送任務，pudcno: 11302001
2024-07-30 12:27:02,205 email_utils.send_report_email  63 INFO    => 為 pudcno: 11302001 獲取到 24 條郵件信息
2024-07-30 12:27:02,206 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,235 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,235 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,251 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,253 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,267 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,267 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,283 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,283 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,300 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,301 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,314 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,315 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,328 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,329 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,329 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:27:02,331 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:27:02,360 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,360 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,361 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx
2024-07-30 12:27:02,361 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx
2024-07-30 12:27:02,375 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,375 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,375 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,376 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,393 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,394 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,394 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,394 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,408 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,408 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,409 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,409 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,433 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,434 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,434 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,434 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,455 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,455 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,456 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,456 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,470 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,471 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,471 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,471 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,486 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,486 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,487 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,487 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,500 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,501 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,501 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,502 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,531 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,532 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,532 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,532 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,546 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,547 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,547 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,548 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,563 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,563 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,564 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,564 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,578 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,579 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,579 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,579 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,594 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,595 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,595 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,595 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,608 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,609 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,609 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,610 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,626 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,628 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:27:02,628 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,628 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:27:02,640 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:27:02,647 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:27:14,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 12:28:08,378 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:28:14,746 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 12:28:16,640 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:28:28,124 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:28:31,705 email_utils.send_report_email  58 INFO    => 開始執行郵件發送任務，pudcno: 11302001
2024-07-30 12:28:31,708 email_utils.send_report_email  63 INFO    => 為 pudcno: 11302001 獲取到 24 條郵件信息
2024-07-30 12:28:31,708 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,736 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,737 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,749 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,750 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,764 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,765 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,780 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,781 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,804 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,805 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,831 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,831 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,843 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,844 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,844 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:28:31,845 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:28:31,877 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,878 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,878 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx
2024-07-30 12:28:31,878 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx
2024-07-30 12:28:31,890 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,891 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,891 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,892 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,906 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,907 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,907 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,907 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,920 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,921 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,921 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,921 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,937 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,937 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,938 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,938 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,954 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,955 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,955 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,955 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,968 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,968 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,968 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,969 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,984 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:31,985 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:31,985 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:31,985 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,002 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,002 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,003 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,003 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,015 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,015 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,015 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,016 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,036 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,036 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,037 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,037 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,048 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,049 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,049 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,049 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,062 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,063 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,063 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,064 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,076 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,077 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,077 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,077 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,093 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,094 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,094 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,094 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,126 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,126 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:28:32,127 email_utils.send_report_email  88 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,127 email_utils.send_report_email  98 ERROR   => 文件不存在: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx\測試001.docx
2024-07-30 12:28:32,140 email_utils.send_report_email 106 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:28:32,148 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:28:43,800 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 12:29:43,944 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:29:49,477 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:30:00,570 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:30:12,728 email_utils.send_report_email  58 INFO    => 開始執行郵件發送任務，pudcno: 11302001
2024-07-30 12:30:12,732 email_utils.send_report_email  63 INFO    => 為 pudcno: 11302001 獲取到 24 條郵件信息
2024-07-30 12:30:12,732 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,765 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,766 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,780 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,781 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,796 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,796 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,812 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,812 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,827 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,827 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,844 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,845 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,859 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,859 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,860 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,861 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,891 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,892 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,892 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,894 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,920 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,921 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,921 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,923 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,954 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,954 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,954 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,956 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,984 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:12,984 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:12,985 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:12,986 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,019 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,019 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,020 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,021 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,063 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,064 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,064 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,070 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,093 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,094 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,094 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,103 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,124 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,125 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,125 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,128 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,155 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,156 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,156 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,158 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,187 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,188 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,188 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,189 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,218 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,219 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,219 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,220 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,249 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,249 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,250 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,252 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,280 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,281 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,281 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,282 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,296 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,297 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,297 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,298 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,312 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,312 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,312 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,314 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,331 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,331 email_utils.send_report_email  72 INFO    => 準備發送郵件給: <EMAIL>
2024-07-30 12:30:13,331 email_utils.send_report_email  87 INFO    => 嘗試附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,333 email_utils.attach_file_to_email  52 INFO    => 成功附加文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11302001\測試001.docx
2024-07-30 12:30:13,359 email_utils.send_report_email 105 INFO    => 成功發送郵件給: <EMAIL>
2024-07-30 12:30:13,366 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:30:25,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240510
2024-07-30 12:31:19,293 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 12:31:54,496 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 12:32:07,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240471
2024-07-30 12:32:10,435 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 12:32:22,225 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 240490
2024-07-30 13:45:18,331 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 13:45:18,332 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 13:45:18,496 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 13:45:18,553 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 13:45:34,622 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239131
2024-07-30 13:46:56,622 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 13:47:58,829 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-30 13:47:59,840 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 13:48:11,846 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239092
2024-07-30 13:48:19,972 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-30 13:48:20,134 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 13:48:30,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239111
2024-07-30 13:51:50,996 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 13:52:04,946 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239092
2024-07-30 13:52:07,191 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 13:52:19,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239111
2024-07-30 13:52:52,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239111
2024-07-30 13:52:55,296 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 13:53:06,196 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239092
2024-07-30 13:56:03,343 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 13:58:03,798 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 13:58:32,775 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 13:58:44,842 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239111
2024-07-30 14:00:18,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 239111
2024-07-30 14:29:59,440 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 14:29:59,441 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 14:29:59,444 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 14:29:59,444 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 14:30:12,627 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-30 14:30:12,747 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-30 14:30:12,905 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 14:30:12,983 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 14:30:13,059 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-30 14:30:13,060 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-30 14:30:13,060 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-30 14:30:13,247 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 14:30:13,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6145
2024-07-30 14:30:13,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 14:30:13,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 14:30:14,977 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2024-07-30 14:30:15,043 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-30 14:30:15,044 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-30 14:30:15,044 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-30 14:30:15,233 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 14:30:15,278 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 14:30:15,341 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 14:30:19,398 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 14:30:19,614 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 14:30:19,616 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 14:30:19,725 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 14:30:20,120 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 14:30:30,752 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:30:44,910 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:30:56,439 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:31:27,490 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:31:39,300 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:33:47,673 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:34:31,704 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:35:25,553 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:35:37,996 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:35:50,376 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:36:01,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:38:01,930 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:38:13,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:38:31,574 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:38:43,519 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:39:17,205 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:39:28,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:39:31,872 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:39:44,208 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:39:50,955 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:40:21,907 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:40:32,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:40:37,767 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:40:48,658 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:42:12,383 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:43:02,348 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:43:29,011 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:43:40,955 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:43:44,953 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:43:55,819 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 14:57:21,278 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:57:36,760 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 14:57:42,874 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 14:57:54,987 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 14:58:03,625 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 14:58:14,416 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 15:00:03,942 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 15:00:22,284 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 15:00:22,286 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 15:00:24,019 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 15:00:36,088 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 15:00:48,746 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 15:01:00,264 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 15:04:30,397 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 15:05:08,374 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 15:05:21,043 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 15:35:31,009 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 15:35:31,066 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 15:57:35,092 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 15:58:01,817 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 15:58:04,393 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-30 15:58:04,600 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 15:58:15,707 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 15:59:58,786 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:00:16,002 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/cancel_letter/ HTTP/1.1" 200 0
2024-07-30 16:00:16,506 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:00:27,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:00:31,192 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:00:42,310 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:02:46,590 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:03:03,917 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:03:15,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:03:19,694 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:03:31,394 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:12:17,917 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:12:32,703 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 16:12:32,703 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 16:12:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 16:12:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 16:12:35,457 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:12:47,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:13:00,105 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:13:11,622 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:15:42,874 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:15:50,897 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:16:01,954 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:16:09,547 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:16:20,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:21:45,295 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:21:51,277 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:22:03,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:22:10,110 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:22:21,252 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:28:39,372 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:28:48,615 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:29:00,190 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:29:06,921 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:29:18,250 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:30:07,348 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:30:18,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:30:20,766 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:30:21,869 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 16:30:32,148 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:31:08,869 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:31:24,643 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:31:36,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:31:38,921 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:31:49,816 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:33:57,470 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:34:14,253 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:34:26,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:34:28,763 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:34:41,231 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:34:50,715 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:34:55,167 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:35:06,749 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:35:08,678 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:35:19,755 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:35:31,456 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:36:03,594 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:36:05,971 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:36:16,892 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 16:39:05,115 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:41:59,910 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:42:25,829 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:46:13,134 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:50:22,216 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:54:53,888 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 16:55:21,610 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 16:55:21,610 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 16:55:23,315 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 16:55:36,323 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 16:55:38,682 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 16:55:49,820 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:00:16,408 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:00:34,616 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 17:00:46,937 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 17:00:49,152 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 17:01:00,378 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:03:54,152 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:04:11,999 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 17:04:23,880 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 17:04:32,640 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 17:04:44,217 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:05:41,760 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:05:56,018 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 17:06:08,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 17:06:10,816 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 17:06:21,865 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:15:58,536 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:17:29,728 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:17:42,218 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:17:49,040 basehttp.log_message 161 INFO    => "POST /api/documents/cancel_letter/ HTTP/1.1" 200 69
2024-07-30 17:18:00,428 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238483
2024-07-30 17:18:02,729 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 17:18:13,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:18:56,038 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238502
2024-07-30 17:24:18,631 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-30 17:25:57,240 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 17:25:57,243 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 22:56:34,961 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 22:56:34,961 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 22:56:35,141 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 22:56:35,142 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 22:56:35,215 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-30 22:56:35,216 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-30 22:56:44,558 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-30 22:56:44,683 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-30 22:56:44,836 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-30 22:56:44,914 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-30 22:56:44,915 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-30 22:56:44,967 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-30 22:56:44,968 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-30 22:56:45,150 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-30 22:56:45,289 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6498
2024-07-30 22:56:45,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-30 22:56:45,707 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-30 22:56:47,403 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-30 22:56:47,468 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-30 22:56:47,469 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-30 22:56:47,469 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-30 22:56:47,632 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9480
2024-07-30 22:56:47,653 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-30 22:56:47,762 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-30 22:56:49,447 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-30 22:57:01,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238284
2024-07-30 22:57:08,031 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_email/ HTTP/1.1" 200 0
2024-07-30 22:57:08,259 basehttp.log_message 161 INFO    => "POST /api/documents/letter_email/ HTTP/1.1" 200 69
2024-07-30 22:57:21,528 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238303
2024-07-30 22:57:41,121 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 238303
