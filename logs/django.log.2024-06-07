2024-06-07 11:46:27,379 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 11:46:27,449 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-06-07 11:46:27,549 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-06-07 11:46:27,550 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-06-07 11:46:27,550 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-06-07 11:46:27,621 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-06-07 11:46:27,700 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-06-07 14:56:39,801 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-06-07 14:56:39,805 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-06-07 14:56:39,910 token_utils.verify_access_token  30 INFO    => refresh_token: None
2024-06-07 14:56:40,003 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-06-07 14:56:40,043 token_utils.verify_access_token  38 ERROR   => Refresh token 無效
2024-06-07 14:56:40,044 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-06-07 14:56:40,044 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-06-07 14:56:45,760 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-06-07 14:56:45,906 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-06-07 14:56:45,969 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:56:46,037 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 14:56:46,109 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-06-07 14:56:46,110 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-06-07 14:56:46,161 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-06-07 14:56:46,162 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-06-07 14:56:46,205 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:56:46,206 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:56:46,270 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:56:46,273 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:56:46,333 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 14:56:46,487 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 14:56:46,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 14:56:46,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 14:57:11,317 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2024-06-07 14:57:11,318 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-06-07 14:57:11,386 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:57:11,387 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:57:11,455 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 14:57:11,616 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 118, in select_letter_download
    sql_result, http_status = select_letter_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadLetterInfo.py", line 216, in select_letter_download
    noModifyFile = json_data['noModifyFile']
KeyError: 'noModifyFile'
2024-06-07 14:57:11,627 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 111882
2024-06-07 14:57:11,639 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 14:57:11,961 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:00:29,579 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:00:29,880 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:00:48,963 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:00:49,286 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:00:50,914 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:00:51,207 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:03:07,577 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:07,851 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:03:18,855 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 15:03:18,911 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:18,973 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:03:19,043 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:19,119 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:03:19,221 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:19,276 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:19,278 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:19,279 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:03:19,346 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:03:19,488 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:03:19,662 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:03:19,732 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:04:28,391 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:28,672 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:04:38,933 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,016 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:04:39,086 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,154 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:04:39,225 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,226 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,228 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,230 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:04:39,304 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:04:39,435 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:04:39,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:04:39,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:06:08,671 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:06:08,674 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:06:08,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:06:09,005 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:06:17,193 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:06:17,458 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:06:25,861 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:06:26,132 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:06:42,801 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:06:43,079 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:07:21,657 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:07:21,966 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:07:39,100 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:07:39,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:07:40,319 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:07:40,627 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:08:22,231 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:08:22,296 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:08:22,361 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:08:22,363 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:08:22,364 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:08:22,366 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:08:22,530 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:08:22,555 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:08:22,733 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:08:22,809 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:10:39,112 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:39,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:10:51,799 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:51,881 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:10:51,943 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:51,944 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:51,946 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:51,947 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:10:52,030 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:10:52,222 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:10:52,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:10:52,396 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:11:14,950 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:15,230 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:11:22,370 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:22,440 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:11:52,809 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:52,814 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-06-07 15:11:52,866 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:52,888 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:11:52,993 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165830
2024-06-07 15:11:53,011 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:53,308 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:11:58,018 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:11:58,162 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,229 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:11:58,314 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,315 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,317 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,324 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:11:58,406 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:11:58,616 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:11:58,682 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:11:58,959 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:12:13,980 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:12:14,149 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,220 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:12:14,291 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,292 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,293 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,294 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:14,435 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:12:14,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:12:14,594 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:12:14,798 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:12:15,960 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:15,961 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:16,035 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 165830
2024-06-07 15:12:16,060 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:16,085 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:12:16,371 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:12:21,495 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:21,586 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:12:21,649 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:12:21,851 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:21,854 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:21,855 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:21,857 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:22,002 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:12:22,134 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:12:22,297 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:12:22,358 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:12:38,632 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:38,973 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:12:57,999 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:12:58,138 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,243 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:12:58,343 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,346 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,347 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,349 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:12:58,595 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:12:58,627 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:12:58,713 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:12:58,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:13:29,137 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:13:29,324 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,400 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:13:29,470 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,471 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,472 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,473 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:29,656 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:13:29,693 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:13:29,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:13:29,995 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:13:41,623 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:41,903 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:13:51,208 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:13:51,531 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:14:36,789 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:14:36,790 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:14:36,877 _DownloadLetterInfo.select_letter_download 249 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11300249\上市函文-PKL300立頓沁香奶綠240606h16515P.docx
2024-06-07 15:14:36,879 log.log_response 230 WARNING => Not Found: /api/documents/select_letter_download/
2024-06-07 15:14:36,880 basehttp.log_message 161 WARNING => "POST /api/documents/select_letter_download/ HTTP/1.1" 404 81
2024-06-07 15:14:36,929 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:14:36,934 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:14:37,293 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:16:29,333 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:16:29,417 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:19:29,368 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:29,438 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:19:42,888 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:42,917 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:42,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:19:43,033 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:19:47,985 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:19:48,165 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,233 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:19:48,300 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,301 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,355 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,356 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:19:48,493 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:19:48,505 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:19:48,744 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:19:48,807 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:22:48,270 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:22:48,344 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:25:48,159 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:25:48,238 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:28:08,015 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:28:15,411 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:28:16,473 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:28:48,181 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:28:48,263 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:29:44,016 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:29:44,181 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,266 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:29:44,392 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,471 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,476 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,481 token_utils.verify_access_token  30 INFO    => refresh_token: 742fb95c-1405-498b-9781-c4c3ccd9f6d9
2024-06-07 15:29:44,569 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:29:44,702 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:29:44,856 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:29:44,969 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:29:48,724 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-06-07 15:29:48,857 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-06-07 15:29:54,992 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-06-07 15:29:55,065 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:29:55,143 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:29:55,232 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:29:55,234 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:29:55,235 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:29:55,237 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:29:55,394 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:29:55,430 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:29:55,587 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:29:55,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:34:06,092 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:06,407 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:34:14,515 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:14,516 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:14,633 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:34:14,648 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-06-07 15:34:14,655 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:14,977 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:34:18,995 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,358 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:34:19,437 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,505 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:34:19,575 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,579 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,581 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,582 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:34:19,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:34:19,799 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 15:34:19,972 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:34:20,061 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:36:22,312 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:36:24,573 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:36:24,574 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:36:25,029 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:36:25,060 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:36:25,087 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:36:25,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:37:19,448 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:37:19,512 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:38:09,064 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:09,140 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-06-07 15:38:09,245 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-06-07 15:38:09,246 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-07 15:38:09,298 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:09,300 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:09,417 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:38:09,418 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:38:11,939 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:12,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-06-07 15:38:12,100 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:12,101 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:12,212 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:38:12,218 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:38:13,692 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:13,692 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:13,818 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:38:13,847 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:38:27,106 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:27,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:38:31,466 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:31,467 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:31,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:38:31,589 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:38:35,985 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:38:36,236 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,328 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:38:36,407 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,408 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,459 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,460 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:38:36,570 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-06-07 15:38:36,624 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,625 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:38:36,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:38:36,799 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:38:36,821 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1726
2024-06-07 15:38:36,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:41:07,576 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:41:10,774 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:41:10,775 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:41:10,905 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-06-07 15:41:10,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:41:10,942 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:41:11,229 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 491
2024-06-07 15:41:36,255 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:41:36,322 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:44:36,224 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:44:36,293 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:46:49,274 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:46:52,250 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:52,250 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:52,734 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-06-07 15:46:52,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 666590
2024-06-07 15:46:57,862 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:57,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:46:58,018 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,083 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:46:58,168 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,169 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,221 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,222 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,289 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:46:58,337 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-06-07 15:46:58,397 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,398 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:46:58,514 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:46:58,627 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:46:58,641 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-07 15:46:58,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1350
2024-06-07 15:49:58,054 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:49:58,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:52:53,238 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:52:58,107 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:52:58,963 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:53:08,097 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:53:10,468 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:53:11,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 53706
2024-06-07 15:53:14,579 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:53:14,606 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-06-07 15:53:14,685 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:53:14,696 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-06-07 15:53:14,776 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-06-07 15:53:27,403 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-06-07 15:55:32,224 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:32,893 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 15:55:32,949 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,016 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 15:55:33,082 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,162 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,164 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,165 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,235 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-06-07 15:55:33,316 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,326 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:55:33,365 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 15:55:33,460 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 15:55:33,577 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 15:55:33,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-07 15:55:33,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1350
2024-06-07 15:58:33,007 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 15:58:33,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 16:01:33,076 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 16:01:33,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 17:20:54,197 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 17:20:54,282 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,358 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-06-07 17:20:54,422 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-06-07 17:20:54,476 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,542 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 17:20:54,635 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-06-07 17:20:54,712 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-06-07 17:20:54,712 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-06-07 17:20:54,712 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-06-07 17:20:54,757 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,862 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,879 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,880 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:20:54,925 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-06-07 17:20:55,118 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 17:20:55,194 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 517
2024-06-07 17:20:55,298 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-07 17:21:02,613 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:21:02,678 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-06-07 17:21:02,734 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-06-07 17:21:02,793 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:21:02,933 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-06-07 17:21:15,297 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-06-07 17:21:15,359 token_utils.verify_access_token  30 INFO    => refresh_token: 828a479c-1ddb-4683-af8e-5ebbbacf38d9
2024-06-07 17:21:16,909 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 30690
2024-06-07 17:21:31,630 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-06-07 17:21:31,749 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-06-07 17:21:31,940 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-06-07 17:21:38,319 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-06-07 17:21:38,438 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2024-06-07 17:21:38,439 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2024-06-07 17:21:42,999 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-06-07 17:21:43,065 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:43,128 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 17:21:43,183 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:43,186 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:43,187 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:43,188 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:43,265 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-06-07 17:21:43,514 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2431
2024-06-07 17:21:43,788 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-06-07 17:21:43,843 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-07 17:21:45,852 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:45,938 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-06-07 17:21:45,998 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:45,999 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:46,000 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-06-07 17:21:46,058 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:46,115 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 17:21:46,118 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 17:21:46,230 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-06-07 17:21:48,057 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:48,059 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:48,124 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 17:21:48,227 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 17:21:48,916 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:48,917 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:48,918 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:49,032 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-07 17:21:49,081 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-06-07 17:21:49,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-07 17:21:49,918 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:50,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-06-07 17:21:50,600 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:21:50,864 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-06-07 17:21:53,628 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:06,946 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:22:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,162 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 888816
2024-06-07 17:22:07,169 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 59699)

2024-06-07 17:22:07,174 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-07 17:22:07,233 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,234 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,235 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,262 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,406 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-06-07 17:22:07,440 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-06-07 17:22:07,498 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:07,618 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-06-07 17:22:07,628 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-06-07 17:22:07,865 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-07 17:22:11,368 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:22:12,956 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 39211
2024-06-07 17:23:53,440 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:23:54,957 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 39211
2024-06-07 17:25:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:25:07,177 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:25:48,727 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 0
2024-06-07 17:25:48,776 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:25:48,953 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 47316
2024-06-07 17:28:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:28:07,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:31:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:31:07,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:34:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:34:07,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:37:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:37:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:40:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:40:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:43:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:43:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:46:07,083 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:46:07,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:49:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:49:07,206 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:52:07,074 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:52:07,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:55:07,090 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:55:07,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 17:58:07,086 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 17:58:07,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:01:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:01:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:04:07,090 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:04:07,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:07:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:07:07,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:10:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:10:07,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:13:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:13:07,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:16:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:16:07,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:19:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:19:07,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:22:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:22:07,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:25:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:25:07,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:28:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:28:07,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:31:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:31:07,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:34:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:34:07,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:37:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:37:07,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:40:07,076 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:40:07,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:43:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:43:07,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:46:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:46:07,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:49:07,112 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:49:07,227 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:52:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:52:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:55:07,095 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:55:07,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 18:58:07,086 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 18:58:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:01:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:01:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:04:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:04:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:07:07,086 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:07:07,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:10:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:10:07,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:13:07,090 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:13:07,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:16:07,090 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:16:07,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:19:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:19:07,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:22:07,119 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 19:22:07,171 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:22:07,258 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:25:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:25:07,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:28:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:28:07,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:31:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:31:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:34:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:34:07,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:37:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:37:07,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:40:07,091 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:40:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:43:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:43:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:46:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:46:07,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:49:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:49:07,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:52:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:52:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:55:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:55:07,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 19:58:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 19:58:07,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:01:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:01:07,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:04:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:04:07,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:07:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:07:07,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:10:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:10:07,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:13:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:13:07,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:16:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:16:07,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:19:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:19:07,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:22:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:22:07,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:25:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:25:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:28:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:28:07,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:31:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:31:07,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:34:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:34:07,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:37:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:37:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:40:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:40:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:43:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:43:07,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:46:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:46:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:49:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:49:07,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:52:07,076 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:52:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:55:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:55:07,189 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 20:58:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 20:58:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:01:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:01:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:04:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:04:07,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:07:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:07:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:10:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:10:07,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:13:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:13:07,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-06-07 21:16:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:16:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:19:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:19:07,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:22:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:22:07,188 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:25:07,113 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 21:25:07,165 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:25:07,263 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:28:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:28:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:31:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:31:07,177 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:34:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:34:07,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:37:07,087 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:37:07,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:40:07,083 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:40:07,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:43:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:43:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:46:07,083 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:46:07,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:49:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:49:07,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:52:07,113 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:52:07,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:55:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:55:07,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 21:58:07,110 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 21:58:07,160 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 21:58:07,231 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:01:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:01:07,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:04:07,108 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:04:07,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:07:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:07:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:10:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:10:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:13:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:13:07,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:16:07,086 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:16:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:19:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:19:07,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:22:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:22:07,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:25:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:25:07,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:28:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:28:07,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:31:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:31:07,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:34:07,112 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:34:07,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:37:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:37:07,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:40:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:40:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:43:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:43:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:46:07,078 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:46:07,312 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:49:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:49:07,270 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:52:07,092 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:52:07,228 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:55:07,118 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 22:55:07,170 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:55:07,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 22:58:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 22:58:07,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:01:07,094 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:01:07,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:04:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:04:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:07:07,084 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:07:07,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:10:07,082 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:10:07,252 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:13:07,079 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:13:07,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:16:07,088 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:16:07,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:19:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:19:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:22:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:22:07,189 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:25:07,074 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:25:07,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:28:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:28:07,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:31:07,106 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-07 23:31:07,159 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:31:07,248 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:34:07,089 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:34:07,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:37:07,076 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:37:07,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:40:07,077 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:40:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:43:07,080 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:43:07,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:46:07,083 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:46:07,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:49:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:49:07,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:52:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:52:07,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:55:07,081 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:55:07,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-07 23:58:07,085 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-07 23:58:07,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
