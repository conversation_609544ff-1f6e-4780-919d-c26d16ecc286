2024-05-20 13:14:57,269 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-20 13:15:53,046 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 13:15:53,047 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-05-20 13:15:57,849 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 13:15:57,949 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-20 13:15:59,144 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-05-20 13:15:59,144 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-05-20 13:15:59,145 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-05-20 13:16:11,998 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 13:16:12,068 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-05-20 13:16:17,245 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-05-20 13:16:18,011 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-05-20 13:16:18,084 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:18,209 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 13:16:18,282 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 13:16:18,283 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-20 13:16:18,335 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-20 13:16:18,335 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-20 13:16:18,410 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:18,461 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:18,463 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:18,464 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:18,715 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 13:16:19,800 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 13:16:21,977 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1483
2024-05-20 13:16:22,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 13:16:30,541 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:30,602 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-20 13:16:30,664 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-20 13:16:30,719 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:30,902 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-05-20 13:16:31,846 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:31,912 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-05-20 13:16:31,968 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:32,085 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-05-20 13:16:34,612 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:34,712 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 13:16:34,767 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-05-20 13:16:34,768 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-20 13:16:34,769 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-20 13:16:34,824 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:34,826 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:34,827 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:34,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 13:16:34,969 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 13:16:35,080 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 13:16:46,196 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-05-20 13:16:46,249 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:16:47,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148720
2024-05-20 13:17:19,238 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-05-20 13:17:19,288 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:17:19,590 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-05-20 13:17:20,661 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:17:21,989 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149525
2024-05-20 13:21:13,330 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:21:13,331 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:21:13,332 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:21:13,333 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-05-20 13:21:13,333 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-05-20 13:21:13,412 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:21:13,416 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 13:21:13,426 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 13:21:13,501 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 13:21:13,545 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 13:21:13,596 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-05-20 13:21:13,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 14:29:48,095 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 14:29:48,096 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 14:29:48,222 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 14:29:48,265 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 15:04:31,115 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:04:31,117 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:04:31,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 15:04:31,238 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 15:28:16,582 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 15:28:16,655 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:16,761 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:28:16,828 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 15:28:16,884 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:16,974 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 15:28:17,036 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,037 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 15:28:17,089 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-20 15:28:17,089 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-20 15:28:17,184 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,185 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,213 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,219 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 15:28:17,282 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-05-20 15:28:17,282 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-20 15:28:17,283 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-20 15:28:17,297 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 15:28:17,335 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,360 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,364 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:17,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 15:28:17,536 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 15:28:17,595 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 15:28:17,601 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 15:28:17,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 15:28:18,762 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-05-20 15:28:18,815 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:20,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 15:28:21,875 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-05-20 15:28:21,922 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:21,925 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:21,927 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:21,929 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:28:22,056 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 15:28:22,101 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 15:28:22,160 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 15:28:22,182 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 15:31:16,844 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:31:16,910 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:34:16,829 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:34:16,912 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:37:16,834 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:37:16,895 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:40:16,956 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:40:17,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:43:48,811 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:43:48,903 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:46:16,859 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:46:16,925 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:50:06,977 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:50:07,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:52:16,834 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:52:16,901 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:55:16,834 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:55:16,899 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 15:58:16,837 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 15:58:16,906 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:01:16,830 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:01:16,893 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:01:28,501 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:01:28,510 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:01:28,564 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:01:28,622 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:04:16,833 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:04:16,909 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:07:16,833 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:07:16,902 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:10:16,829 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:10:16,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:13:16,837 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:13:16,902 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:16:16,831 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:16:16,912 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:19:16,841 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:19:16,903 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:22:16,832 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:22:16,915 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:25:16,828 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:25:16,891 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:28:16,835 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:28:16,948 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:31:16,832 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:31:16,934 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:34:16,826 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:34:16,889 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:37:16,826 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:37:16,889 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:40:16,844 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:40:16,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:42:27,252 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:42:27,255 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:42:27,325 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:42:27,384 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:43:16,842 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:43:16,903 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:46:00,950 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:46:01,079 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,156 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:46:01,223 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,225 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,287 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,289 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,356 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 16:46:01,405 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:46:01,468 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,494 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,498 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:01,619 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:46:01,619 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:46:01,759 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:46:01,779 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 16:46:01,836 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 16:46:04,186 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:04,187 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:04,262 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:46:04,370 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:46:05,719 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:05,720 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:05,721 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:05,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:46:05,839 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:46:05,933 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:46:06,577 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:07,979 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 16:46:09,447 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:09,483 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:09,484 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:09,485 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:46:09,604 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 16:46:09,629 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:46:09,692 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:46:09,696 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:49:01,104 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:01,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:49:50,136 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,202 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:49:50,266 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,326 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:49:50,396 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,396 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,397 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,398 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,474 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 16:49:50,568 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:49:50,624 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,625 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,626 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:50,734 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:49:50,758 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:49:50,830 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 16:49:50,865 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:49:50,914 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 16:49:54,254 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:54,255 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:54,357 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:49:54,427 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:49:55,671 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:55,674 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:55,675 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:55,787 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:49:55,790 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:49:55,889 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:49:56,402 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:57,736 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 16:49:59,634 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:59,666 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:59,667 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:59,668 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:49:59,784 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 16:49:59,821 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:49:59,875 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:49:59,877 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:51:59,904 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:51:59,990 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:52:00,050 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,132 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:52:00,196 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,197 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,198 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,198 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,268 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:52:00,332 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,359 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,361 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:00,468 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 16:52:00,578 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:52:00,583 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:52:00,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 16:52:00,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 16:52:00,709 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:52:05,058 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:06,460 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 16:52:09,568 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:09,610 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:09,612 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:09,612 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:52:09,720 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 16:52:09,740 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:52:09,799 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:52:09,813 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:55:00,071 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:55:00,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:56:18,685 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:18,772 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:56:18,834 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:18,902 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:56:18,968 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:18,969 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:18,970 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:18,971 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:19,041 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:56:19,106 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:19,107 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:19,108 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:19,231 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 16:56:19,331 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:56:19,379 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:56:19,393 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:56:19,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 16:56:19,457 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 16:56:20,283 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:21,637 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 16:56:23,083 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:23,115 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:23,116 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:23,116 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:56:23,226 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 16:56:23,261 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:56:23,334 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:56:23,337 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:58:25,454 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,522 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 16:58:25,577 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 16:58:25,730 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,731 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,732 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,733 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,815 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 16:58:25,863 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,864 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,866 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:25,868 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 16:58:26,071 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:58:26,129 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 16:58:26,133 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:58:26,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 16:58:26,229 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 16:58:34,229 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:58:35,698 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 16:59:10,757 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:59:10,792 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:59:10,793 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:59:10,794 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 16:59:10,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 16:59:10,942 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 16:59:10,947 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 16:59:11,073 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:01:26,015 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:01:26,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:04:25,580 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:04:25,668 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:07:25,621 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:07:25,703 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-05-20 17:10:25,578 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:25,647 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:10:47,735 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:47,829 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:10:47,880 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:47,965 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:10:48,029 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,106 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,107 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,109 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,173 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:10:48,239 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,292 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,293 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:48,308 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:10:48,489 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:10:48,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:10:48,631 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:10:48,688 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:10:48,702 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:10:49,318 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:50,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:10:55,140 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:55,179 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:55,180 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:55,181 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:10:55,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:10:55,308 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:10:55,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:10:55,398 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:11:47,434 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:48,939 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:11:52,497 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:53,849 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:11:54,844 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:54,864 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:54,865 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:54,866 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:11:54,972 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:11:54,989 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:11:55,082 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:11:55,126 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:12:27,311 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,382 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:12:27,450 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,519 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:12:27,587 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,588 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,589 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,589 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:12:27,826 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:12:27,895 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,896 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,896 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:27,962 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:12:28,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:12:28,088 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:12:28,091 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:12:28,132 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:12:28,667 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:30,131 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:12:31,575 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:31,602 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:31,603 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:31,604 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:12:31,714 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:12:31,760 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:12:31,766 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:12:31,871 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:13:10,986 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:13:11,108 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,175 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:13:11,236 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,237 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,238 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,239 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,318 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:13:11,417 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:13:11,476 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,476 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,477 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:11,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:13:11,649 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:13:11,655 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:13:11,707 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:13:11,795 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:13:12,207 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:13,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:13:15,102 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:15,127 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:15,127 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:15,128 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:13:15,231 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:13:15,280 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:13:15,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:13:15,386 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:16:11,142 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:11,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:16:19,139 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,203 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:16:19,268 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,329 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:16:19,398 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,398 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,399 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,400 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,532 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:16:19,575 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:16:19,627 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,628 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,629 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:16:19,736 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:16:19,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:16:19,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:16:19,858 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:16:19,955 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:17:59,754 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:18:01,293 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:18:05,170 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:18:05,202 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:18:05,203 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:18:05,203 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:18:05,313 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:18:05,343 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:18:05,411 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:18:05,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:19:19,287 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:19:19,356 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:22:19,276 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:19,366 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:22:24,448 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,528 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:22:24,587 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,669 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:22:24,736 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,736 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,737 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,738 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,851 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:22:24,900 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,952 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,953 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:24,973 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:22:25,053 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:22:25,108 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:22:25,118 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:22:25,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:22:25,273 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:22:25,826 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:27,175 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:22:28,527 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:28,563 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:28,565 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:28,566 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:22:28,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:22:28,683 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:22:28,693 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:22:28,787 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:23:26,352 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:23:27,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:23:29,267 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:23:29,298 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:23:29,299 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:23:29,299 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:23:29,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:23:29,429 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:23:29,442 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:23:29,544 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:25:24,614 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:25:24,681 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:27:58,068 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:27:58,188 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,280 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:27:58,337 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,338 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,339 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,340 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,402 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:27:58,456 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,508 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,510 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:27:58,517 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:27:58,654 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:27:58,662 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:27:58,763 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:27:58,816 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:27:58,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:28:02,160 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:28:03,473 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:28:05,022 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:28:05,058 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:28:05,059 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:28:05,060 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:28:05,167 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:28:05,192 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:28:05,255 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:28:05,262 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:30:32,917 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 17:30:32,970 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:30:33,148 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 17:30:33,202 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,292 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-20 17:30:33,370 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,371 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-20 17:30:33,406 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-20 17:30:33,407 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-20 17:30:33,454 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,455 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,456 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,467 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-20 17:30:33,520 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-05-20 17:30:33,521 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-20 17:30:33,521 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-20 17:30:33,576 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,577 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,578 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:33,633 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-20 17:30:33,675 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:30:33,774 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:30:33,819 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-20 17:30:33,825 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:30:33,842 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-20 17:30:34,401 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-05-20 17:30:34,453 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:35,767 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:30:37,454 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-05-20 17:30:37,484 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:37,484 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:37,485 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:37,511 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:30:37,595 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:30:37,599 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:30:37,701 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:30:37,760 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:32:40,960 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:32:42,359 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148609
2024-05-20 17:32:44,059 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:32:44,084 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:32:44,084 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:32:44,085 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:32:44,213 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-05-20 17:32:44,243 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-05-20 17:32:44,301 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-20 17:32:44,314 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-20 17:33:33,172 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:33:33,237 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:36:33,158 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:36:33,236 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:39:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:39:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:42:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:42:33,245 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:45:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:45:33,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:48:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:48:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:51:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:51:33,231 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:54:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:54:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 17:57:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 17:57:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:00:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:00:33,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:03:33,157 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:03:33,243 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:06:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:06:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:09:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:09:33,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:12:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:12:33,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:15:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:15:33,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:18:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:18:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:21:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:21:33,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:24:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:24:33,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:27:33,165 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:27:33,249 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:30:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:30:33,243 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:33:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:33:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:36:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:36:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:39:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:39:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:42:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:42:33,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:45:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:45:33,234 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:48:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:48:33,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:51:33,149 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:51:33,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:54:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:54:33,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 18:57:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 18:57:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:00:33,158 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:00:33,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:03:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:03:33,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:06:33,189 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:06:33,271 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:09:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:09:33,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:12:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:12:33,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:15:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:15:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:18:33,156 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:18:33,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:21:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:21:33,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:24:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:24:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:27:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:27:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:30:33,170 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 19:30:33,219 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:30:33,289 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:33:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:33:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:36:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:36:33,284 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:39:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:39:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:42:33,159 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:42:33,227 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:45:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:45:33,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:48:33,158 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:48:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:51:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:51:33,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:54:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:54:33,208 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 19:57:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 19:57:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:00:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:00:33,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:03:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:03:33,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:06:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:06:33,240 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:09:33,162 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:09:33,243 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:12:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:12:33,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:15:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:15:33,246 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:18:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:18:33,209 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:21:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:21:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:24:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:24:33,229 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:27:33,142 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:27:33,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:30:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:30:33,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:33:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:33:33,242 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:36:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:36:33,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:39:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:39:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:42:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:42:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:45:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:45:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:48:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:48:33,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:51:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:51:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:54:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:54:33,247 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 20:57:33,149 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 20:57:33,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-05-20 21:00:33,156 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:00:33,234 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:03:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:03:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:06:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:06:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:09:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:09:33,264 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:12:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:12:33,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:15:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:15:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:18:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:18:33,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:21:33,156 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:21:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:24:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:24:33,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:27:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:27:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:30:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:30:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:33:33,177 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 21:33:33,228 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:33:33,326 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:36:33,149 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:36:33,249 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:39:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:39:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:42:33,160 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:42:33,234 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:45:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:45:33,221 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:48:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:48:33,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:51:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:51:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:54:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:54:33,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 21:57:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 21:57:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:00:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:00:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:03:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:03:33,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:06:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:06:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:09:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:09:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:12:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:12:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:15:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:15:33,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:18:33,158 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:18:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:21:33,149 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:21:33,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:24:33,150 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:24:33,231 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:27:33,152 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:27:33,216 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:30:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:30:33,281 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:33:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:33:33,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:36:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:36:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:39:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:39:33,230 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:42:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:42:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:45:33,159 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:45:33,252 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:48:33,145 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:48:33,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:51:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:51:33,230 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:54:33,157 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:54:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 22:57:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 22:57:33,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:00:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:00:33,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:03:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:03:33,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:06:33,143 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:06:33,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:09:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:09:33,238 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:12:33,151 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:12:33,232 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:15:33,146 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:15:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:18:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:18:33,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:21:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:21:33,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:24:33,157 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:24:33,240 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:27:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:27:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:30:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:30:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:33:33,154 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:33:33,224 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:36:33,176 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-20 23:36:33,226 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:36:33,310 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:39:33,155 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:39:33,222 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:42:33,149 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:42:33,214 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:45:33,147 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:45:33,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:48:33,148 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:48:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:51:33,157 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:51:33,245 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:54:33,144 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:54:33,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-20 23:57:33,153 token_utils.verify_access_token  30 INFO    => refresh_token: c9e506c9-3f0f-4997-ac48-8f0db229eea2
2024-05-20 23:57:33,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
