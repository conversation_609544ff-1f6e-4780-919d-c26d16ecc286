2024-07-10 00:00:34,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:03:34,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:06:34,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:09:34,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:12:34,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:15:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:18:34,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:21:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:24:34,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:27:34,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:30:34,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:33:34,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:36:34,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:39:34,010 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 00:39:34,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:42:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:45:34,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-10 00:48:34,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:51:34,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:54:34,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 00:57:34,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:00:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:03:34,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:06:34,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:09:34,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:12:34,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:15:34,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:18:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:21:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:24:34,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:27:34,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:30:34,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:33:34,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:36:34,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:39:34,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:42:34,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:45:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:48:34,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:51:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:54:34,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 01:57:34,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:00:34,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:03:34,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:06:34,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:09:34,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:12:34,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:15:34,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:18:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:21:34,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:24:34,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:27:34,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:30:34,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:33:34,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:36:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:39:34,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:42:34,052 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 02:42:34,220 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:45:34,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:48:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:51:34,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:54:34,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 02:57:34,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:00:34,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:03:34,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:06:34,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:09:34,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:12:34,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:15:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:18:34,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:21:34,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:24:34,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:27:34,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:30:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:33:34,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:36:34,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:39:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:42:34,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:45:34,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:48:34,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:51:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:54:34,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 03:57:34,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:00:34,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:03:34,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:06:34,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:09:34,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:12:34,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:15:34,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:18:34,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:21:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:24:34,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:27:34,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:30:34,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:33:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:36:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-10 04:39:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:42:34,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:45:34,006 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 04:45:34,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:48:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:51:34,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:54:34,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 04:57:34,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:00:34,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:03:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:06:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:09:34,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:12:34,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:15:34,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:18:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:21:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:24:34,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:27:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:30:34,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:33:34,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:36:34,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:39:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:42:34,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:45:34,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:48:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:51:34,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:54:34,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 05:57:34,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:00:34,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:03:34,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:06:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:09:34,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:12:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:15:34,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:18:34,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:21:34,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:24:34,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:27:34,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:30:34,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:33:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:36:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:39:34,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:42:34,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:45:34,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:48:34,042 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 06:48:34,211 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:51:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:54:34,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 06:57:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:00:34,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:03:34,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:06:34,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:09:34,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:12:34,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:15:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:18:34,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:21:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:24:34,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:27:34,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:30:34,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:33:34,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:36:34,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:39:34,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:41:41,824 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 07:41:41,824 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 07:41:41,984 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 07:41:42,036 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 07:42:34,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:45:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:48:34,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:51:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:54:34,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 07:57:34,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:00:34,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:03:34,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:06:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:09:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:12:34,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:15:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:18:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:21:34,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:24:34,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:27:34,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-10 08:30:34,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:33:34,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:36:34,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:39:34,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:42:34,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:45:34,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:48:34,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:49:06,131 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 08:49:06,186 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 08:51:34,053 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 08:51:34,175 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:54:33,283 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:54:43,486 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 08:54:43,660 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 08:54:43,724 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 08:54:43,775 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 08:54:43,776 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 08:54:43,909 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 08:54:43,973 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 08:54:44,054 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 08:54:44,218 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:54:44,379 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 08:54:44,490 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 08:55:16,969 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-10 08:55:17,039 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,039 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,040 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,117 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,122 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,122 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,169 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 08:55:17,348 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 08:55:17,353 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 08:55:17,410 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 08:55:17,472 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 08:55:17,533 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 08:55:17,580 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 08:55:17,639 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 08:55:18,397 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-10 08:55:18,597 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:55:22,009 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-10 08:55:22,190 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:55:23,475 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 08:55:23,679 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:55:27,871 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-10 08:55:32,518 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 08:55:38,269 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-10 08:55:38,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4979
2024-07-10 08:55:42,855 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 08:55:42,890 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 08:55:42,953 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 08:55:43,009 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 08:55:43,071 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 08:55:43,120 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 08:55:43,177 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 08:55:43,735 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-10 08:55:43,971 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-10 08:55:46,987 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:55:48,554 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 08:57:44,413 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:00:44,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:03:44,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:06:44,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:09:44,422 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:12:44,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:15:44,273 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:18:44,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:21:44,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:24:44,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:27:44,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:30:44,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:32:36,921 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 09:32:36,977 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 09:33:43,671 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:34:04,185 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 09:34:08,158 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-10 09:34:08,344 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 09:34:08,533 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 09:34:10,875 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 09:36:44,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:39:44,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:42:44,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:45:44,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:48:44,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:51:44,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:54:44,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 09:57:44,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:00:44,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:03:44,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:06:44,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:07:25,634 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 10:07:25,635 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 10:07:25,795 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:07:25,852 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:09:44,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:11:13,292 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 10:11:23,743 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:11:26,497 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-10 10:11:26,498 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-10 10:11:26,652 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 10:11:26,702 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 10:11:52,927 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:11:53,939 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:12:05,883 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:12:08,308 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 10:12:08,360 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 611
2024-07-10 10:12:11,618 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 10:12:11,619 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-10 10:12:11,695 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 10:12:11,696 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-10 10:12:11,698 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-10 10:12:12,007 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 10:12:12,019 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 10:12:12,193 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 10:12:12,257 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 10:12:12,284 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 10:12:12,286 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 10:12:12,409 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 10:12:42,102 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:12:43,606 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:15:43,627 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:18:43,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:20:52,170 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:21:43,656 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:24:43,632 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:25:10,978 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:27:43,688 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:29:03,012 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:30:43,660 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:33:43,628 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:36:43,657 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:39:15,596 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:39:15,932 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:39:15,951 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:39:16,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:39:16,054 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:39:16,497 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:39:16,619 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:39:17,553 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:39:21,048 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:39:43,646 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:42:15,758 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:42:43,634 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:43:22,692 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:43:22,923 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:43:23,080 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:43:23,192 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:43:23,373 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:43:23,553 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:43:23,604 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:43:24,952 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:43:26,527 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:43:27,264 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:43:32,060 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:43:32,133 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:43:32,136 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 10:43:32,175 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:43:32,207 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:43:32,277 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 10:43:32,348 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:43:48,692 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:43:48,896 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:43:49,151 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:43:49,215 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:43:49,389 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:43:49,425 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:43:49,432 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:44:44,176 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:45:12,438 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:45:20,219 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:45:21,128 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:46:48,928 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:47:17,773 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:47:17,981 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:47:18,116 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:47:18,181 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:47:18,396 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:47:18,583 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:47:18,602 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:47:19,459 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:47:20,111 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:48:48,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:48:48,230 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:48:48,370 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:48:48,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:48:48,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:48:49,748 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:48:50,755 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:49:39,693 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:49:39,899 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:49:39,986 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:49:40,132 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:49:40,390 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:49:41,273 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:49:42,543 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:50:17,995 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:50:45,903 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:50:46,196 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:50:46,207 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:50:46,324 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:50:46,469 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:51:48,174 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 10:51:48,288 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:52:39,874 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:53:17,961 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:53:18,727 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:53:22,304 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:53:22,504 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:53:22,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:53:22,694 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:53:22,910 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:53:23,098 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:53:23,127 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:53:23,848 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:53:24,964 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:55:26,709 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:55:26,837 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:55:26,917 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 10:55:26,984 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 10:55:26,984 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 10:55:27,047 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:55:27,113 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 10:55:27,118 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:55:27,322 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:55:27,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:55:27,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:55:28,378 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:55:29,021 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:56:38,914 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:56:39,118 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:56:39,432 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:56:39,478 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:56:39,676 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:56:39,853 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:56:39,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:56:40,338 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:56:42,145 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:58:54,436 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 10:58:54,686 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 10:58:54,819 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 10:58:54,871 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 10:58:55,079 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 10:58:55,305 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 10:58:55,306 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 10:58:56,109 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 10:58:56,834 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 10:59:39,943 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:59:40,006 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:59:40,062 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:59:40,096 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 10:59:40,121 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 10:59:40,163 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 10:59:40,172 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 11:01:17,690 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:01:19,567 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:01:21,699 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:01:21,750 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:01:27,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:01:27,273 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:01:27,405 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:01:27,584 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:01:27,681 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:01:27,778 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:01:27,891 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:01:35,319 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:01:38,141 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:01:38,328 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:01:39,769 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:01:41,374 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:01:41,422 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:04:08,790 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:04:11,356 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:04:11,546 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:04:15,572 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:04:15,759 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 11:04:18,350 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:04:18,567 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:04:18,702 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:04:18,878 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:04:18,995 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:04:19,110 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:04:19,143 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:04:19,652 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:04:20,356 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 11:04:24,788 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:04:26,626 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:04:26,677 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:07:18,567 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:08:45,880 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 11:08:55,492 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:08:55,612 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:08:55,813 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:08:55,879 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:08:56,083 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:08:56,265 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:08:56,306 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:08:57,837 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:09:09,665 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:09:11,671 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:09:11,722 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:11:55,640 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:14:55,648 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:17:26,698 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 11:17:33,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:17:33,295 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:17:33,558 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:17:33,604 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:17:33,839 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:17:33,999 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:17:34,041 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:17:34,622 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:17:35,688 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:17:37,082 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:17:37,156 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:20:33,331 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:23:33,342 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:24:17,106 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 11:24:24,918 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:24:25,049 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:24:25,249 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:24:25,328 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:24:25,546 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:24:25,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:24:25,760 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:24:26,505 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:24:27,469 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:24:29,305 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:24:29,341 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:24:34,988 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 11:24:51,132 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:24:59,762 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:25:10,841 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:26:39,260 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 11:26:42,462 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 11:26:44,410 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:27:05,516 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 906
2024-07-10 11:27:05,573 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 617
2024-07-10 11:27:25,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:27:40,587 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 11:27:40,602 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 11:27:40,635 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 11:27:40,676 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2481
2024-07-10 11:27:40,760 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 11:30:25,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:30:52,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:30:52,629 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 11:30:52,774 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 11:30:52,817 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 11:30:53,035 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 11:30:53,220 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 11:30:53,239 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 11:30:54,512 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:31:01,762 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:31:03,661 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:31:03,716 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:33:52,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:36:52,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:38:04,683 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 11:38:04,710 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 11:38:04,793 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 11:38:04,847 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 11:38:04,873 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 11:39:20,908 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 11:39:22,424 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 11:39:22,462 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 11:39:52,606 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:42:52,601 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:45:52,615 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:48:52,628 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:51:53,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:54:53,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 11:57:53,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:00:53,138 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:03:53,321 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:06:53,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:09:53,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:13:19,378 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:21:47,372 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 12:21:47,373 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 12:21:47,493 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-10 12:21:47,565 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:21:47,621 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:22:09,799 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-10 12:22:09,799 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-10 12:22:09,982 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:22:10,038 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:23:46,747 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:23:46,782 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:24:52,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:24:54,751 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:24:54,831 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:26:03,955 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 12:26:15,250 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:26:15,372 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:26:15,574 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:26:15,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:26:15,863 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:26:16,149 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:26:16,170 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:26:16,715 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:26:17,665 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:26:19,622 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:26:19,679 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:26:25,688 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-10 12:26:25,689 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 12:26:25,689 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-10 12:26:25,689 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 12:26:25,689 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-10 12:26:25,937 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:26:25,963 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:26:25,972 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 12:26:26,104 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:26:26,145 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:26:26,253 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:26:36,674 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-10 12:26:36,902 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131576
2024-07-10 12:29:15,418 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:30:58,361 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:31:00,238 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:31:00,297 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:32:11,087 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:32:11,150 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:32:15,397 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:32:27,577 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:32:27,671 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:32:45,571 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:32:45,639 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:34:32,693 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 12:34:46,170 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:34:46,286 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:34:46,490 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:34:46,560 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:34:46,768 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:34:46,951 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:34:46,994 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:34:47,930 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:34:49,240 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:34:50,773 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:34:50,840 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:34:54,017 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:34:54,165 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:34:54,226 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:34:54,260 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:34:54,286 log.log_response 230 ERROR   => Internal Server Error: /api/orders/select_order_serial/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-12899: value too large for column "B2B"."ORDERHT"."ODDATE" (actual: 24, maximum: 7)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\views.py", line 130, in select_order_serial
    return self._handle_action('order_serial', 'insert_update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\views.py", line 65, in _handle_action
    sql_result, http_status = ORDER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\_OrderInputInfo.py", line 83, in select_order_serial_method
    return {"results": 'C' + select_get_serial('********', date, user_id, json_data)}, status.HTTP_200_OK
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\main_utils.py", line 330, in select_get_serial
    cursor.execute(insert_orderht_sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-12899: value too large for column "B2B"."ORDERHT"."ODDATE" (actual: 24, maximum: 7)
2024-07-10 12:34:54,298 basehttp.log_message 161 ERROR   => "POST /api/orders/select_order_serial/ HTTP/1.1" 500 168184
2024-07-10 12:34:54,394 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:34:58,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:34:58,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:34:58,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:34:58,603 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:34:58,778 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:34:58,794 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:34:58,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:35:39,694 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 12:35:40,848 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:35:41,570 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-10 12:35:42,753 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:35:45,410 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:35:45,463 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:35:48,256 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:35:48,335 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:35:48,356 log.log_response 230 ERROR   => Internal Server Error: /api/orders/select_order_serial/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
cx_Oracle.DatabaseError: ORA-12899: value too large for column "B2B"."ORDERHT"."ODDATE" (actual: 24, maximum: 7)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\views.py", line 130, in select_order_serial
    return self._handle_action('order_serial', 'insert_update')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\views.py", line 65, in _handle_action
    sql_result, http_status = ORDER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\orders\_OrderInputInfo.py", line 83, in select_order_serial_method
    return {"results": 'C' + select_get_serial('********', date, user_id, json_data)}, status.HTTP_200_OK
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\main_utils.py", line 330, in select_get_serial
    cursor.execute(insert_orderht_sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 98, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 66, in execute
    return self._execute_with_wrappers(sql, params, many=False, executor=self._execute)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 75, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\utils.py", line 82, in _execute
    return self.cursor.execute(sql)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 523, in execute
    return self.cursor.execute(query, self._param_generator(params))
django.db.utils.DatabaseError: ORA-12899: value too large for column "B2B"."ORDERHT"."ODDATE" (actual: 24, maximum: 7)
2024-07-10 12:35:48,358 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:35:48,360 basehttp.log_message 161 ERROR   => "POST /api/orders/select_order_serial/ HTTP/1.1" 500 168184
2024-07-10 12:35:48,429 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:35:51,393 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:35:51,552 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:35:51,698 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:35:51,818 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:35:51,964 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:35:52,135 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:35:52,152 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:37:44,155 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:37:45,162 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:37:49,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:37:49,373 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:37:49,517 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:37:49,643 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:37:49,800 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:37:49,972 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:37:49,992 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:37:50,237 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:37:51,066 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:37:52,327 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:37:52,398 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:37:54,167 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:37:54,208 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:37:54,215 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:37:54,264 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:37:54,273 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 12:38:57,273 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 12:39:03,572 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:39:03,688 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:39:03,930 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:39:03,969 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:39:04,186 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:39:04,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:39:04,388 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:39:05,260 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:39:06,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:39:07,566 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:39:07,629 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:39:10,401 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:39:10,423 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:39:10,465 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 12:39:10,528 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:39:10,563 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:39:26,190 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:39:26,411 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:39:26,544 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:39:26,668 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:39:26,825 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:39:27,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:39:27,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:41:45,383 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 12:41:49,385 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:41:49,981 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:41:51,376 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:41:51,425 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:41:54,534 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:41:54,669 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:41:54,678 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 12:41:54,741 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:41:54,800 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:41:54,900 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:42:26,422 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:43:43,898 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:43:43,956 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:43:47,442 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:43:47,671 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:43:48,117 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:43:48,167 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:43:48,329 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:43:48,453 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:43:48,569 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:44:07,257 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 12:44:18,438 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:44:19,519 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:44:20,980 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:44:21,008 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 12:44:24,282 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 12:44:24,365 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 12:44:24,373 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:44:24,429 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:44:24,467 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 12:44:24,599 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 12:46:47,666 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:49:47,692 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:52:47,601 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 12:52:47,733 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:55:47,657 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:58:47,645 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:58:51,262 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 12:58:51,383 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 12:58:51,528 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 12:58:51,528 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 12:58:51,529 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 12:58:51,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 12:58:51,733 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 12:58:51,803 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 12:58:51,966 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 12:58:52,060 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 12:58:52,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 12:59:01,804 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:59:03,044 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 12:59:05,092 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 12:59:05,141 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:01:17,812 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:01:51,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:04:51,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:07:51,410 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:08:39,684 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:08:43,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:08:43,300 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:08:43,502 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:08:43,571 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:08:43,808 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:08:43,958 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:08:43,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:08:44,741 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:08:46,063 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:08:47,476 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 13:08:47,522 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:08:53,319 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:08:53,361 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:08:53,395 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 13:08:53,418 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:08:53,489 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:09:20,511 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131605
2024-07-10 13:11:01,851 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131605
2024-07-10 13:11:43,250 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:12:25,489 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:12:25,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:12:25,885 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:12:25,958 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:12:26,136 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:12:26,161 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:12:26,372 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:12:28,264 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131605
2024-07-10 13:12:30,173 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:12:30,688 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131605
2024-07-10 13:14:32,042 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:14:37,539 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131605
2024-07-10 13:15:25,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:18:25,680 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:19:58,984 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:20:02,498 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:20:09,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:20:09,251 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:20:09,465 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:20:09,532 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:20:09,735 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:20:09,921 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:20:09,958 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:20:11,087 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:20:13,535 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1039
2024-07-10 13:22:46,827 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1039
2024-07-10 13:22:51,523 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:22:51,721 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:22:51,896 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:22:51,922 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:22:52,136 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:22:52,322 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:22:52,343 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:22:53,067 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:22:55,547 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:24:22,649 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:24:22,865 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:24:23,000 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:24:23,069 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:24:23,300 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:24:23,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:24:23,503 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:24:24,123 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:24:26,787 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:25:00,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:25:00,324 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:25:00,485 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:25:00,607 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:25:00,758 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:25:00,917 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:25:00,943 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:25:01,482 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:25:03,334 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:25:44,552 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:25:44,762 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:25:44,904 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:25:44,972 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:25:45,183 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:25:45,364 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:25:45,396 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:25:45,669 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:25:47,377 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:28:44,742 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:30:32,889 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 13:30:32,940 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 13:30:33,012 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 13:30:33,051 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 13:30:33,080 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:30:33,105 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:30:33,115 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 13:31:21,604 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:31:21,757 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:31:21,901 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:31:22,200 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:31:22,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:31:23,299 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:31:27,588 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:31:28,315 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:31:32,123 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:31:32,148 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:31:32,187 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:31:32,333 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:31:44,720 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:31:55,494 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:31:55,736 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:31:55,978 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:31:56,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:31:56,155 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:31:56,230 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:31:56,254 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:31:57,638 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:32:00,699 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:32:19,439 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:32:19,474 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:32:19,510 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:33:12,610 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:33:12,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:33:12,924 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:33:13,025 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:33:13,211 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:33:13,353 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:33:13,379 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:33:15,591 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:33:19,442 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:33:20,597 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131604
2024-07-10 13:34:36,750 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:34:38,450 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:34:38,502 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 13:34:59,801 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:34:59,841 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 13:35:04,728 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:35:04,765 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 13:35:04,800 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:35:04,935 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:35:04,952 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:35:05,058 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:35:50,474 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:35:50,511 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 13:36:12,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:37:13,523 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:37:13,577 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:38:10,305 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:38:10,366 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:38:53,455 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:38:53,663 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:38:53,698 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:38:54,067 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:38:54,296 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:38:56,240 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:38:57,933 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 132521
2024-07-10 13:39:01,249 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:39:01,292 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:39:01,309 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:39:12,758 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:39:17,313 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:39:17,363 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:39:20,339 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:39:20,462 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:39:20,595 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:39:20,656 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:39:20,895 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:39:21,041 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:39:21,073 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:41:05,753 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:41:11,445 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:41:11,712 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:41:15,271 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 132521
2024-07-10 13:41:20,923 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:41:20,972 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:41:20,985 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:42:08,669 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:42:16,624 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 132521
2024-07-10 13:42:19,778 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:42:19,846 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:42:19,849 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:42:20,046 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:42:20,485 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:42:41,899 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:42:46,494 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 132521
2024-07-10 13:42:50,205 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:42:50,255 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:42:50,280 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:43:07,128 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1039
2024-07-10 13:43:15,565 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:43:15,586 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:43:15,615 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:43:15,794 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:43:42,430 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:43:47,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:43:47,340 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:43:47,533 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:43:47,624 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:43:47,816 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:43:47,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:43:48,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:43:48,795 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:43:51,459 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1039
2024-07-10 13:43:54,771 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:43:54,817 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:43:54,957 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:43:55,035 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:43:55,067 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:46:45,618 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:46:47,710 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:46:50,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:46:50,545 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:46:50,762 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:46:50,827 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:46:51,054 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:46:51,254 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:46:51,255 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:46:51,776 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:46:52,939 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 128368
2024-07-10 13:46:56,656 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:46:56,693 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:46:56,717 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:47:21,966 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 13:47:26,723 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:47:26,854 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:47:27,060 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:47:27,131 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:47:27,328 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:47:27,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:47:27,545 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:47:28,044 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:47:29,199 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 128375
2024-07-10 13:47:33,229 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:47:33,286 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:47:33,413 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:47:33,502 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:47:33,558 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:48:19,208 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-07-10 13:48:19,357 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 13:48:21,608 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 129702
2024-07-10 13:48:36,769 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:48:36,807 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:48:36,866 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:48:36,891 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:48:36,943 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:48:36,950 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:48:41,865 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 13:48:44,097 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 129699
2024-07-10 13:49:20,979 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:49:21,041 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:49:21,096 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:49:21,126 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:49:21,154 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:49:21,191 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:49:43,036 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 13:49:45,275 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 129699
2024-07-10 13:50:19,378 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:50:19,521 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,572 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,641 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,663 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:50:19,688 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,819 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,853 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,929 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:19,980 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:26,873 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:50:52,975 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 129699
2024-07-10 13:50:58,164 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:58,219 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:58,307 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:50:58,308 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:58,332 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:50:58,396 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:51:40,821 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:51:42,864 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 13:51:42,906 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:51:47,220 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 13:51:47,406 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 13:51:48,629 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:51:50,408 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 13:51:50,463 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 611
2024-07-10 13:51:53,066 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:51:53,124 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:51:53,154 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 13:51:53,232 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:51:53,266 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:51:53,431 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:53:26,868 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:56:26,943 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:56:49,120 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:56:49,189 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:57:08,711 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 980
2024-07-10 13:57:13,546 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 980
2024-07-10 13:57:21,638 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:57:21,753 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:57:21,937 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:57:22,012 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:57:22,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:57:22,401 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 13:57:22,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:57:23,426 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 13:57:24,647 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130557
2024-07-10 13:57:29,634 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:57:29,688 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:57:29,745 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:57:29,780 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:57:29,810 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:57:29,840 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:57:47,177 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 13:57:49,415 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130562
2024-07-10 13:58:31,286 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:58:31,344 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:58:31,386 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:58:31,441 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:58:31,442 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:58:31,582 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 13:58:41,146 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 13:58:43,405 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130142
2024-07-10 13:59:29,232 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 13:59:29,358 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 13:59:29,387 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:59:29,508 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:59:29,598 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 13:59:56,890 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 13:59:57,113 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 13:59:57,247 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 13:59:57,313 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 13:59:57,519 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 13:59:57,732 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 13:59:57,749 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 14:01:08,623 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130142
2024-07-10 14:01:09,527 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:01:10,312 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130142
2024-07-10 14:01:13,424 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:01:13,477 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:01:13,490 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:01:13,733 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:02:57,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:04:51,107 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:04:51,172 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:05:18,188 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:05:18,238 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:05:58,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:06:11,136 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:06:11,177 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:06:18,199 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:06:18,259 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:08:57,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:11:19,798 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:11:22,042 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131893
2024-07-10 14:11:25,419 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:11:25,480 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:11:25,541 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:11:25,602 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:11:25,614 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:11:25,664 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:11:25,701 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:11:46,212 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:11:48,446 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131928
2024-07-10 14:11:57,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:12:15,794 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:12:15,863 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:15,957 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,020 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,075 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,100 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:12:16,127 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,241 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,318 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,363 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,429 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:12:16,522 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:13:20,273 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:13:22,509 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131932
2024-07-10 14:14:57,427 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:15:31,168 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:31,222 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:31,232 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:15:31,285 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:31,404 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:31,423 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:15:31,473 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:34,854 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:15:37,094 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131929
2024-07-10 14:15:45,220 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:45,281 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:45,339 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:45,358 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:15:45,385 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:15:45,495 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:15:45,523 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2886
2024-07-10 14:16:09,826 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:16:12,057 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 131921
2024-07-10 14:17:57,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:18:06,827 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:18:07,055 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 14:18:07,189 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 14:18:07,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 14:18:07,460 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:18:07,678 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 14:18:07,715 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 14:18:08,423 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:18:09,846 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:10,055 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:11,554 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:18:12,264 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:12,489 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:19,351 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 14:18:19,351 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-10 14:18:19,508 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-10 14:18:19,561 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-10 14:18:19,561 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-07-10 14:18:37,801 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-10 14:18:37,918 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-10 14:18:38,032 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 14:18:38,106 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 14:18:38,107 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-10 14:18:38,158 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 14:18:38,159 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 14:18:38,339 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-10 14:18:38,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5906
2024-07-10 14:18:38,695 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4979
2024-07-10 14:18:38,698 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9897
2024-07-10 14:18:39,547 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-10 14:18:39,611 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,612 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,613 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,637 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,663 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,663 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,718 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:39,889 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:18:39,936 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 14:18:39,989 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 14:18:40,046 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 14:18:40,089 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 14:18:40,172 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 14:18:40,208 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 14:18:40,214 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-10 14:18:40,274 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:40,466 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:18:41,463 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:18:42,076 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-10 14:18:42,309 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:42,513 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:43,163 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:43,359 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:43,502 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:43,709 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:43,856 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:44,055 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 70
2024-07-10 14:18:51,977 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-10 14:18:51,978 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-10 14:18:52,151 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-07-10 14:18:52,207 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-07-10 14:18:55,983 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:55,984 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:55,984 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:18:55,984 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-10 14:18:55,985 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-10 14:18:56,270 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:18:56,273 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 14:18:56,288 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:18:56,328 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:18:56,515 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:19:08,213 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-07-10 14:19:08,331 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:19:10,541 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:19:13,485 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:19:17,501 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:19:18,329 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:20:09,942 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:20:09,948 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:20:10,110 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,181 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,245 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,310 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,365 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,426 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,496 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,541 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,609 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,690 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,734 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:10,807 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 14:20:12,884 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:20:15,089 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:21:07,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:23:53,224 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:23:54,523 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:24:07,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:25:54,325 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:25:56,137 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 14:25:57,435 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-10 14:25:57,435 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-10 14:25:57,636 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 618
2024-07-10 14:25:57,687 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 900
2024-07-10 14:26:00,681 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:26:00,807 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 14:26:00,818 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:26:00,876 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:26:00,915 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:26:01,055 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:26:05,547 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:26:07,768 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 3227
2024-07-10 14:27:06,085 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 14:27:07,823 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:27:10,187 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-10 14:27:10,300 error_utils.handle_error  13 ERROR   => ORA-01008: not all variables bound - Unexpected error: 400
2024-07-10 14:27:10,301 log.log_response 230 ERROR   => Internal Server Error: /api/orders/select_order_input/
2024-07-10 14:27:10,301 basehttp.log_message 161 ERROR   => "POST /api/orders/select_order_input/ HTTP/1.1" 500 58
2024-07-10 14:27:37,213 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 14:27:40,731 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1049
2024-07-10 14:28:02,951 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 14:28:06,509 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1049
2024-07-10 14:28:16,916 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:28:35,889 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-10 14:28:35,891 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:28:35,891 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-10 14:28:36,096 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:28:36,164 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:28:36,306 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:28:36,379 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:28:36,430 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:28:47,742 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:28:49,980 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1890
2024-07-10 14:30:07,009 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:33:07,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:36:07,010 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:37:47,053 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 2301
2024-07-10 14:37:59,318 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1890
2024-07-10 14:38:10,803 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1890
2024-07-10 14:39:07,004 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:40:05,537 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:40:05,583 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:40:05,617 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 14:40:05,635 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 2459
2024-07-10 14:40:05,682 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 14:40:13,206 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 14:40:15,442 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1887
2024-07-10 14:41:05,150 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 14:41:05,268 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-10 14:41:05,453 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:41:06,768 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-07-10 14:41:07,189 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 302343
2024-07-10 14:41:16,455 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-10 14:41:16,666 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:41:18,217 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-10 14:41:18,413 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:41:23,704 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-10 14:41:37,357 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 14:41:37,547 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-10 14:41:37,608 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 14:41:37,734 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 14:41:37,788 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 802131
2024-07-10 14:41:37,814 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 14:41:37,891 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-10 14:41:37,892 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 14:41:37,892 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 14:41:38,063 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-10 14:41:38,246 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4979
2024-07-10 14:41:38,414 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9897
2024-07-10 14:41:38,430 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5906
2024-07-10 14:41:50,060 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-10 14:41:50,112 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,113 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,113 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,113 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,164 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,165 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,265 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:41:50,384 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 14:41:50,441 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 14:41:50,508 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:41:50,581 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 14:41:50,612 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 14:41:50,672 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 14:41:50,735 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 14:41:52,504 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-10 14:41:52,712 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-10 14:42:07,000 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:42:11,438 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-10 14:42:33,773 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-10 14:42:51,080 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-10 14:42:51,142 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,267 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,269 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,269 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,269 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,269 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,323 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-10 14:42:51,475 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 14:42:51,512 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 14:42:51,573 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 14:42:51,631 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:42:51,691 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 14:42:51,737 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 14:42:51,791 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 14:42:53,593 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:42:56,981 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:42:57,676 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_shipment_order/ HTTP/1.1" 200 0
2024-07-10 14:42:59,353 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 145602
2024-07-10 14:43:03,194 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 14:43:05,485 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-10 14:43:05,555 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-10 14:43:05,557 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-10 14:43:05,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:43:05,688 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:43:05,797 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:43:06,548 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-10 14:43:16,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232625
2024-07-10 14:43:27,238 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-07-10 14:43:27,239 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-10 14:43:27,360 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-10 14:43:27,433 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-07-10 14:43:42,768 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-10 14:43:42,952 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:43:42,953 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:43:43,075 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:43:43,748 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-10 14:43:45,569 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148727
2024-07-10 14:44:24,241 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 150774
2024-07-10 14:44:27,891 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-10 14:44:28,091 log.log_response 230 ERROR   => Internal Server Error: /api/documents/delete_business_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 215, in delete_business_notification
    return self._handle_action('business_notification', 'delete')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 96, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 395, in delete_business_notification_method
    pudcno = json_data["pudcno"]
KeyError: 'pudcno'
2024-07-10 14:44:28,095 basehttp.log_message 161 ERROR   => "POST /api/documents/delete_business_notification/ HTTP/1.1" 500 116352
2024-07-10 14:44:37,682 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-07-10 14:44:42,378 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-10 14:44:42,483 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-10 14:44:42,656 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-10 14:45:07,001 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:47:14,856 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:47:14,868 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:47:14,995 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:47:24,393 log.log_response 230 ERROR   => Internal Server Error: /api/documents/delete_business_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 215, in delete_business_notification
    return self._handle_action('business_notification', 'delete')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 96, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 395, in delete_business_notification_method
    pudcno = json_data["pudcno"]
KeyError: 'pudcno'
2024-07-10 14:47:24,394 basehttp.log_message 161 ERROR   => "POST /api/documents/delete_business_notification/ HTTP/1.1" 500 116352
2024-07-10 14:47:43,137 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:47:43,163 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:47:43,238 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:47:45,462 log.log_response 230 ERROR   => Internal Server Error: /api/documents/delete_business_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 215, in delete_business_notification
    return self._handle_action('business_notification', 'delete')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 96, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 395, in delete_business_notification_method
    pudcno = json_data["pudcno"]
KeyError: 'pudcno'
2024-07-10 14:47:45,464 basehttp.log_message 161 ERROR   => "POST /api/documents/delete_business_notification/ HTTP/1.1" 500 116352
2024-07-10 14:48:07,002 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:48:15,924 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:48:15,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:48:16,059 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:48:17,268 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 14:48:18,424 log.log_response 230 ERROR   => Internal Server Error: /api/documents/delete_business_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 215, in delete_business_notification
    return self._handle_action('business_notification', 'delete')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 96, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 395, in delete_business_notification_method
    pudcno = json_data["pudcno"]
KeyError: 'pudcno'
2024-07-10 14:48:18,425 basehttp.log_message 161 ERROR   => "POST /api/documents/delete_business_notification/ HTTP/1.1" 500 116352
2024-07-10 14:49:43,660 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:49:43,690 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:49:43,778 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:49:45,148 log.log_response 230 ERROR   => Internal Server Error: /api/documents/delete_business_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 215, in delete_business_notification
    return self._handle_action('business_notification', 'delete')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 96, in _handle_action
    sql_result, http_status = DOCUMENT_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 286, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_BusinessNotificationInfo.py", line 395, in delete_business_notification_method
    pudcno = json_data["pudcno"]
KeyError: 'pudcno'
2024-07-10 14:49:45,149 basehttp.log_message 161 ERROR   => "POST /api/documents/delete_business_notification/ HTTP/1.1" 500 116352
2024-07-10 14:51:07,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:54:06,981 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 14:54:07,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:54:48,187 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:54:48,192 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:54:48,293 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:54:49,635 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:54:49,802 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 14:54:54,052 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148727
2024-07-10 14:55:00,734 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 150774
2024-07-10 14:55:42,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148727
2024-07-10 14:55:47,705 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-10 14:55:50,349 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149695
2024-07-10 14:57:07,008 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 14:58:17,042 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:58:17,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:58:17,138 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:59:08,592 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:59:08,600 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:59:08,690 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:59:18,182 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232625
2024-07-10 14:59:24,109 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 14:59:24,145 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 14:59:24,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 14:59:33,919 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232625
2024-07-10 14:59:46,017 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 222609
2024-07-10 14:59:59,942 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 226133
2024-07-10 15:00:00,422 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 222609
2024-07-10 15:00:03,357 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232625
2024-07-10 15:00:07,005 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:00:11,176 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232625
2024-07-10 15:00:23,006 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 222609
2024-07-10 15:02:03,508 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:02:03,563 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:02:03,564 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:02:17,075 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 15:02:17,128 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:02:17,154 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 15:02:17,211 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 15:02:17,262 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 15:02:17,325 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 15:02:17,381 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 15:02:18,738 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 15:02:18,815 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-10 15:02:18,926 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:02:19,782 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1887
2024-07-10 15:03:07,012 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:05:01,124 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1887
2024-07-10 15:05:02,575 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 15:05:03,646 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 1887
2024-07-10 15:06:07,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:08:36,185 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:08:36,200 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:08:36,296 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:08:39,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149695
2024-07-10 15:08:55,375 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-10 15:08:55,376 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-07-10 15:08:55,515 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:08:55,551 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:08:55,600 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:08:55,688 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-10 15:08:55,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-10 15:09:07,010 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:10:46,109 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_business_notification/ HTTP/1.1" 200 0
2024-07-10 15:10:46,253 basehttp.log_message 161 INFO    => "POST /api/documents/update_business_notification/ HTTP/1.1" 200 69
2024-07-10 15:10:48,884 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148710
2024-07-10 15:10:52,226 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-10 15:10:52,332 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-10 15:10:52,399 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-10 15:10:54,258 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-10 15:10:54,300 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:10:54,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:10:54,424 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:09,625 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:09,676 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:09,680 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:09,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-10 15:11:10,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-10 15:11:10,736 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:10,740 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:10,852 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:12,065 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 42097
2024-07-10 15:11:12,099 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:12,149 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:12,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:25,236 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-10 15:11:25,297 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-10 15:11:25,541 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4979
2024-07-10 15:11:26,279 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:26,383 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:26,425 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:31,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:31,094 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:31,142 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:32,073 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:32,128 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:32,138 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:32,957 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:33,048 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:33,098 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:35,017 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:35,116 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:35,168 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:37,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:37,815 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:11:37,905 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:11:38,838 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_kind/ HTTP/1.1" 200 0
2024-07-10 15:11:39,017 basehttp.log_message 161 INFO    => "POST /api/documents/select_kind/ HTTP/1.1" 200 2785
2024-07-10 15:11:59,488 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:11:59,506 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:00,193 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:12:00,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:12:00,311 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:01,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_kind/ HTTP/1.1" 200 2785
2024-07-10 15:12:01,969 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:01,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:12:02,424 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-10 15:12:02,529 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:02,581 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:12:03,939 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-10 15:12:03,947 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:12:05,548 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 15:12:05,587 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:05,638 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 15:12:05,691 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 15:12:05,747 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 15:12:05,799 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 15:12:05,870 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 15:12:06,998 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:12:23,241 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:12:23,247 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-10 15:12:26,307 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:12:26,354 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 15:12:26,431 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 15:12:26,457 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 15:12:26,517 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 15:12:26,572 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 15:12:26,634 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 15:12:29,586 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:12:30,971 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:12:31,518 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 0
2024-07-10 15:12:32,246 basehttp.log_message 161 INFO    => "POST /api/orders/select_pre_shipment_order/ HTTP/1.1" 200 176208
2024-07-10 15:14:31,105 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:14:34,970 basehttp.log_message 161 INFO    => "POST /api/orders/select_shipment_order/ HTTP/1.1" 200 148627
2024-07-10 15:14:55,520 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-10 15:14:55,598 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-10 15:14:55,765 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-10 15:14:56,269 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-10 15:14:56,331 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-10 15:14:56,462 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:14:57,043 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-10 15:14:57,108 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-10 15:14:57,133 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-10 15:14:57,296 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-10 15:14:57,316 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-10 15:14:58,670 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-10 15:15:00,371 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:15:01,601 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-10 15:15:05,225 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:15:06,978 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:15:07,390 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-10 15:15:10,618 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:15:10,638 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:15:11,957 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-10 15:15:12,611 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-10 15:15:31,933 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_fun_group/ HTTP/1.1" 200 0
2024-07-10 15:15:32,152 basehttp.log_message 161 INFO    => "POST /api/users/select_user_fun_group/ HTTP/1.1" 200 7194
2024-07-10 15:15:42,674 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:15:46,191 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_permissions/ HTTP/1.1" 200 0
2024-07-10 15:15:46,345 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-10 15:15:51,124 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-10 15:15:52,277 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-10 15:16:01,235 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-10 15:16:01,237 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-10 15:16:12,596 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:16:13,913 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 18131
2024-07-10 15:16:20,038 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-10 15:16:20,451 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-10 15:16:21,375 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 222883
2024-07-10 15:17:27,512 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-10 15:17:27,586 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-10 15:17:28,624 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:17:48,885 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_truck/ HTTP/1.1" 200 0
2024-07-10 15:17:49,076 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-10 15:17:50,773 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:17:52,305 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-10 15:17:54,207 basehttp.log_message 161 INFO    => "POST /api/dealers/select_truck/ HTTP/1.1" 200 1575
2024-07-10 15:17:54,894 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:17:56,212 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:18:07,015 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:20:16,684 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:20:18,053 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:20:18,176 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:20:30,460 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:20:30,658 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:20:30,731 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 15:20:30,782 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 15:20:30,782 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 15:20:30,839 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-10 15:20:30,937 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:20:31,295 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:20:31,306 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:20:31,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:20:32,350 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:22:55,072 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:23:04,481 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:23:29,875 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:23:30,595 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:23:34,416 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:23:34,601 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:23:34,933 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-10 15:23:35,015 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:23:35,370 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-10 15:23:35,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:23:35,430 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:23:36,957 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:23:55,353 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:24:11,595 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:24:15,258 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-10 15:24:15,444 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:24:16,518 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-10 15:24:16,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4979
2024-07-10 15:24:17,968 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-10 15:24:18,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30268
2024-07-10 15:24:34,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-10 15:24:35,145 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 15:24:35,183 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 15:24:35,240 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 15:24:35,298 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 15:24:35,359 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 15:24:35,407 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 15:24:35,461 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 15:24:37,173 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-10 15:24:37,364 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-10 15:25:12,512 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:25:12,701 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:25:15,743 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-10 15:26:07,926 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 2985512
2024-07-10 15:26:21,400 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:26:34,619 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:29:34,629 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:30:04,329 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:32:34,591 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:35:34,625 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:36:42,696 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:36:51,448 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:37:51,740 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:37:51,948 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:37:52,173 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:37:52,220 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:37:52,417 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:37:52,610 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:37:52,629 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:38:35,065 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:39:26,439 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 2985512
2024-07-10 15:39:29,031 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:39:37,423 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:40:09,743 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:40:52,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:41:50,161 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:43:31,598 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:43:41,852 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:43:52,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:45:37,156 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:46:52,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:47:16,708 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:47:23,251 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:47:23,394 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:47:23,610 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:47:23,687 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:47:23,891 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:47:24,090 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:47:24,141 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:47:44,248 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:47:44,447 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:47:44,713 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:47:44,737 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:47:44,932 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:47:45,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:47:45,162 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:48:35,757 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:48:39,937 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:48:40,112 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:48:40,504 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:48:40,541 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:48:40,734 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:48:40,747 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:48:40,820 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:49:18,108 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:49:21,290 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:49:24,974 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:49:25,133 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:49:25,529 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:49:25,605 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:49:25,709 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:49:25,778 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:49:25,876 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:49:43,052 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:49:45,057 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:50:29,743 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:50:38,585 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:50:38,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 15:50:38,901 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-10 15:50:38,970 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 15:50:39,210 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 15:50:39,383 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 15:50:39,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 15:50:46,938 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:51:08,091 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 427637
2024-07-10 15:52:46,137 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:52:59,279 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 427637
2024-07-10 15:53:17,033 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:53:25,381 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 427637
2024-07-10 15:53:39,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:54:57,361 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:55:11,566 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 429568
2024-07-10 15:55:31,507 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:56:25,548 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:56:33,195 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:56:39,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 15:56:56,341 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 15:57:06,135 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 15:59:39,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:01:29,218 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:01:38,494 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 16:02:39,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:04:00,825 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:04:11,042 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 16:04:38,000 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 429568
2024-07-10 16:05:11,680 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 423842
2024-07-10 16:05:40,938 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:05:42,720 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-10 16:05:42,876 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-10 16:13:46,035 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:16:02,793 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:16:28,111 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-10 16:16:28,512 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-10 16:16:28,661 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 16:16:28,901 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 16:16:29,070 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5335
2024-07-10 16:16:29,389 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 16:16:29,505 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 16:16:30,648 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-10 16:16:30,888 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 16:16:30,934 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 16:16:31,005 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 16:16:31,031 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 16:16:31,114 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 16:16:31,151 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 16:16:31,210 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 16:16:32,439 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49194
2024-07-10 16:17:28,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:17:28,263 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 16:17:28,591 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-10 16:17:28,632 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-10 16:17:28,803 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-10 16:17:28,857 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50894
2024-07-10 16:17:28,905 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-10 16:17:28,974 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-10 16:17:29,013 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-10 16:17:29,023 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-10 16:17:29,031 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-10 16:17:29,089 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-10 16:17:29,140 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-10 16:17:30,214 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49194
2024-07-10 16:18:48,936 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 16:18:53,351 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49194
2024-07-10 16:20:28,293 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:21:59,672 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 98295
2024-07-10 16:23:28,270 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:26:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:29:29,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:32:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:35:29,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:38:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:41:29,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:44:29,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:47:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:50:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:53:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:56:29,017 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 16:56:29,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 16:59:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:02:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:05:29,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:08:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:11:29,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:14:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:17:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:20:29,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:23:29,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:26:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:26:49,032 log.log_response 230 WARNING => Unauthorized: /api/pallet/select_pallet_account/
2024-07-10 17:26:49,033 basehttp.log_message 161 WARNING => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 401 77
2024-07-10 17:26:55,433 log.log_response 230 WARNING => Unauthorized: /api/pallet/select_pallet_account/
2024-07-10 17:26:55,433 basehttp.log_message 161 WARNING => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 401 77
2024-07-10 17:27:08,085 log.log_response 230 WARNING => Unauthorized: /api/pallet/select_pallet_account/
2024-07-10 17:27:08,086 basehttp.log_message 161 WARNING => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 401 77
2024-07-10 17:27:39,692 log.log_response 230 WARNING => Unauthorized: /api/pallet/select_pallet_account/
2024-07-10 17:27:39,693 basehttp.log_message 161 WARNING => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 401 77
2024-07-10 17:29:29,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:32:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:35:29,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:38:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:41:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:44:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:47:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:50:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:53:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:56:29,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 17:59:29,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:02:29,188 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:05:29,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:08:29,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:11:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:14:29,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:17:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:20:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:23:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:26:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:29:29,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:32:29,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:35:29,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:38:29,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:41:29,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:44:29,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:47:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:50:29,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:53:29,206 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 18:56:29,161 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-10 18:56:29,279 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-10 20:21:32,982 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-10 20:23:29,772 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-10 20:23:29,774 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-10 22:06:40,495 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-10 22:06:40,749 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-07-10 22:06:40,833 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 22:06:40,968 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 22:06:41,042 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-10 22:06:41,118 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-10 22:06:41,119 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-10 22:06:41,119 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-10 22:06:41,322 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-10 22:06:41,685 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 5649
2024-07-10 22:06:41,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5906
2024-07-10 22:06:41,922 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11145
2024-07-10 22:06:48,927 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-10 22:06:48,992 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-10 22:06:49,159 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 22:06:53,972 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-10 22:06:54,172 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 22:06:55,794 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 22:08:07,311 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-10 22:08:07,311 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-10 22:08:07,481 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-07-10 22:08:07,566 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-07-10 22:08:11,276 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 22:08:11,277 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-10 22:08:11,302 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-10 22:08:11,302 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-10 22:08:11,305 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-10 22:08:11,529 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-10 22:08:11,620 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 22:08:11,642 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:08:11,742 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 22:08:11,822 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:08:11,918 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:28:38,229 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-07-10 22:28:38,395 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 22:28:40,473 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-10 22:28:40,672 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 4472
2024-07-10 22:31:22,433 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-10 22:31:23,874 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 22:31:25,159 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 4472
2024-07-10 22:31:31,821 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 4586
2024-07-10 22:31:33,268 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 4472
2024-07-10 22:31:37,618 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:31:37,673 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:31:37,679 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-10 22:31:37,736 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:31:37,775 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-10 22:31:37,901 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-10 22:32:10,485 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-10 22:32:12,715 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 5726
2024-07-10 22:46:53,405 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-10 22:46:53,472 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-07-10 22:46:57,516 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-10 22:46:57,529 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-10 22:47:02,475 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-10 22:47:03,988 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
