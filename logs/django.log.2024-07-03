2024-07-03 08:50:54,480 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-03 08:51:32,290 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-03 08:51:32,385 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-07-03 08:51:33,414 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-07-03 08:51:33,414 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-03 08:51:33,414 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-03 08:51:34,883 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 08:51:34,978 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-03 08:51:40,355 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-03 08:51:40,502 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-03 08:51:40,569 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:40,654 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 08:51:40,731 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 08:51:40,731 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-03 08:51:40,782 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-03 08:51:40,782 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-03 08:51:40,864 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:40,930 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:40,931 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:40,932 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:41,176 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-03 08:51:41,260 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3534
2024-07-03 08:51:41,506 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-03 08:51:41,509 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-03 08:51:51,794 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:51,858 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 08:51:51,919 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-03 08:51:51,975 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:52,100 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 08:51:55,183 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:51:55,310 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 08:52:02,715 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:02,833 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 08:52:06,060 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:06,174 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 08:52:12,660 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-03 08:52:12,660 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-03 08:52:12,708 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:12,711 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:12,957 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-07-03 08:52:12,977 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-07-03 08:52:25,059 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-03 08:52:25,061 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-03 08:52:25,061 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-03 08:52:25,062 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-03 08:52:25,066 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-03 08:52:25,111 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,113 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,113 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,114 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,142 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,487 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-03 08:52:25,499 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-03 08:52:25,554 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,557 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-03 08:52:25,568 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:25,647 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 08:52:25,670 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 08:52:25,826 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 08:52:25,873 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 08:52:50,392 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-07-03 08:52:50,444 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:50,593 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-03 08:52:52,653 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-03 08:52:52,698 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 08:52:53,020 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 127990
2024-07-03 09:22:56,696 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 09:22:56,698 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 09:22:56,784 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 09:22:56,875 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 09:58:35,375 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 09:58:35,377 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 09:58:35,446 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 09:58:35,499 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 10:30:15,334 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 10:30:15,336 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 10:30:15,416 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 10:30:15,466 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 11:05:17,413 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 11:05:17,414 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 11:05:17,510 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:05:17,511 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:05:17,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 11:05:17,637 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 11:23:40,280 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,359 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-03 11:23:40,567 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,569 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,571 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,574 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,574 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,580 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,627 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,628 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:40,718 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,721 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,725 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,759 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,762 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,843 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-03 11:23:40,893 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-03 11:23:40,894 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:40,933 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-03 11:23:40,984 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 11:23:41,043 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-03 11:23:41,093 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-03 11:23:41,144 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-03 11:23:42,154 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-03 11:23:42,207 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:23:42,329 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 11:42:34,188 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:42:34,192 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:42:34,261 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 11:42:34,350 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 11:55:10,896 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:55:11,033 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 11:55:14,226 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-03 11:55:14,288 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:55:14,511 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 35092
2024-07-03 11:55:27,216 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:55:27,380 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 35092
2024-07-03 11:56:02,010 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:02,173 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 11:56:07,168 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-07-03 11:56:07,169 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-07-03 11:56:07,267 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:07,275 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:07,404 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-07-03 11:56:07,461 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-07-03 11:56:09,349 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-03 11:56:09,350 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-07-03 11:56:09,425 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-07-03 11:56:09,425 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-07-03 11:56:09,434 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-07-03 11:56:09,468 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,469 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,559 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,560 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,563 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,671 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-03 11:56:09,737 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:56:09,793 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:56:09,822 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-03 11:56:09,837 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-07-03 11:56:09,893 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:56:10,096 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:57:23,746 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-07-03 11:57:23,798 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:57:23,899 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-03 11:57:26,050 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:57:26,229 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 37779
2024-07-03 11:59:36,314 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,317 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,319 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,320 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,321 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,322 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,571 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:59:36,576 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:36,637 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:59:36,694 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:59:36,733 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-07-03 11:59:36,743 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:59:36,811 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 18893
2024-07-03 11:59:36,812 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3587
2024-07-03 11:59:41,952 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:42,059 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-07-03 11:59:44,120 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 11:59:44,308 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 37789
2024-07-03 13:02:15,100 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 13:02:15,101 token_utils.verify_access_token  30 INFO    => refresh_token: 195c215d-7308-44e3-a511-b404ea3af73a
2024-07-03 13:02:15,455 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-07-03 13:02:15,455 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-03 13:02:15,456 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-03 13:02:15,458 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-07-03 13:02:15,459 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-07-03 13:02:15,459 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2024-07-03 14:16:29,189 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-03 14:16:29,398 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-03 14:16:29,472 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 14:16:29,518 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:29,580 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 14:16:29,643 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 14:16:29,735 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-03 14:16:29,736 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-03 14:16:29,737 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-03 14:16:29,768 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:29,858 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:29,859 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:29,860 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:29,950 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-03 14:16:30,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3534
2024-07-03 14:16:30,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-03 14:16:30,467 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-03 14:16:51,997 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:52,061 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-03 14:16:52,119 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-03 14:16:52,180 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:52,564 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:52,599 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-03 14:16:52,639 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-03 14:16:52,705 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-03 14:16:52,763 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:52,920 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 14:16:53,800 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:16:54,113 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-03 14:17:45,166 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:17:45,227 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-03 14:17:45,291 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:17:45,407 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 14:17:47,018 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-03 14:17:47,077 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:18:37,974 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 367430
2024-07-03 14:20:15,857 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:15,925 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-03 14:20:15,981 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-03 14:20:15,982 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-03 14:20:15,982 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 14:20:16,038 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:16,042 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:16,042 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:16,105 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-03 14:20:16,212 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-03 14:20:16,258 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 14:20:18,777 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:18,835 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-03 14:20:18,894 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:19,024 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 14:20:56,944 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-03 14:20:57,000 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:20:59,028 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 21519
2024-07-03 14:56:11,432 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:56:11,433 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 14:56:11,504 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-03 14:56:11,553 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 15:27:50,779 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 15:27:50,780 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 15:27:50,883 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 15:27:50,924 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-03 17:20:56,264 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-03 17:21:23,966 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 17:21:23,966 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-03 17:21:24,789 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-03 17:21:24,882 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:25,648 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:21:30,121 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:21:30,283 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 17:21:30,333 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,390 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 17:21:30,468 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 17:21:30,545 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-03 17:21:30,546 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-03 17:21:30,546 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-03 17:21:30,588 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,678 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,679 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,680 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:21:30,762 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-03 17:21:30,936 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3839
2024-07-03 17:21:31,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-03 17:21:31,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-03 17:23:22,541 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,640 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-03 17:23:22,700 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,725 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,726 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,726 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,754 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,754 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,803 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:22,804 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,805 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,806 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,829 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,857 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,858 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:22,971 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-03 17:23:22,977 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:23,081 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-03 17:23:23,138 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-03 17:23:23,155 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-03 17:23:23,219 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-03 17:23:23,281 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 17:23:23,316 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-03 17:23:24,249 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-03 17:23:24,302 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:24,537 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-03 17:23:29,167 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:29,250 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-03 17:23:29,927 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:29,999 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-03 17:23:30,062 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-03 17:23:30,119 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:30,248 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:23:31,374 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-03 17:23:31,426 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:23:31,617 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-03 17:24:30,297 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:24:30,377 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:25:11,170 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:11,242 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-03 17:25:11,307 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:11,308 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-03 17:25:11,308 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-03 17:25:11,363 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:11,364 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:11,437 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-03 17:25:11,441 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 17:25:11,544 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-03 17:25:12,791 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:12,865 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-03 17:25:12,921 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:12,921 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:12,922 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:13,039 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-03 17:25:13,041 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 17:25:13,165 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-03 17:25:15,837 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:15,838 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:15,839 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:15,957 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 17:25:16,010 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-03 17:25:16,023 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-03 17:25:16,793 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:16,872 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-03 17:25:16,932 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:17,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3839
2024-07-03 17:25:20,822 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:20,823 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:20,823 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:20,899 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-03 17:25:20,991 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-03 17:25:21,046 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-03 17:25:22,057 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:22,232 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3839
2024-07-03 17:25:24,018 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:24,117 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-03 17:25:24,165 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:24,278 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:25:26,366 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:26,432 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-03 17:25:26,494 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-03 17:25:26,550 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:26,851 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-03 17:25:35,173 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:35,311 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:25:36,867 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:36,996 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:25:38,586 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:38,708 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:25:41,379 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:41,437 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-03 17:25:41,487 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:25:41,615 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:26:02,843 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:02,919 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-03 17:26:02,977 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-03 17:26:03,034 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:03,189 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-03 17:26:07,889 basehttp.log_message 161 INFO    => "OPTIONS /api/users/update_user_dept/ HTTP/1.1" 200 0
2024-07-03 17:26:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:08,071 basehttp.log_message 161 INFO    => "POST /api/users/update_user_dept/ HTTP/1.1" 200 69
2024-07-03 17:26:17,680 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:17,740 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-03 17:26:17,798 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-03 17:26:17,851 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:17,948 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-03 17:26:23,396 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:23,465 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-03 17:26:23,516 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:26:23,634 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-03 17:27:30,297 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:27:30,354 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:30:30,274 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:30:30,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:33:30,291 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:33:30,382 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:33:52,805 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-03 17:33:55,832 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-03 17:33:55,921 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:34:09,917 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 656319
2024-07-03 17:35:23,066 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-03 17:35:29,506 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:35:42,648 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 656319
2024-07-03 17:36:31,056 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:36:31,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:40:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:40:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:43:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:43:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:46:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:46:08,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:49:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:49:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:52:07,985 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:52:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:55:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:55:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 17:58:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 17:58:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:01:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:01:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:04:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:04:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:07:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:07:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-03 18:10:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:10:08,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:13:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:13:08,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:16:07,978 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:16:08,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:19:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:19:08,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:22:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:22:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:25:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:25:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:28:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:28:08,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:31:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:31:08,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:34:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:34:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:37:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:37:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:40:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:40:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:43:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:43:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:46:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:46:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:49:08,005 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:49:08,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:52:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:52:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:55:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:55:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 18:58:08,110 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 18:58:08,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:01:08,003 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:01:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:04:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:04:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:07:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:07:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:10:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:10:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:13:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:13:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:16:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:16:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:19:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:19:08,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:22:07,996 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-03 19:22:08,046 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:22:08,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:25:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:25:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:28:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:28:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:31:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:31:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:34:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:34:08,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:37:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:37:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:40:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:40:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:43:08,030 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:43:08,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:46:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:46:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:49:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:49:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:52:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:52:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:55:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:55:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 19:58:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 19:58:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:01:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:01:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:04:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:04:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:07:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:07:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:10:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:10:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:13:07,977 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:13:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:16:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:16:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:19:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:19:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:22:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:22:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:25:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:25:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:28:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:28:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:31:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:31:08,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:34:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:34:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:37:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:37:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:40:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:40:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:43:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:43:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:46:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:46:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:49:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:49:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:52:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:52:08,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:55:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:55:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 20:58:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 20:58:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:01:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:01:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:04:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:04:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:07:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:07:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:10:07,981 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:10:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:13:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:13:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:16:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:16:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:19:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:19:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:22:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:22:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:25:07,999 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-03 21:25:08,057 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:25:08,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:28:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:28:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:31:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:31:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:34:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:34:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:37:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:37:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:40:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:40:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:43:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:43:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:46:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:46:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:49:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:49:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:52:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:52:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 21:55:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:55:23,514 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_check_user/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
cx_Oracle.DatabaseError: ORA-12541: TNS:no listener

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 93, in select_check_user
    sql_result, http_status = select_verify_user(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\_UserVerifyInfo.py", line 16, in select_verify_user
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 33, in verify_access_token
    with connection.cursor() as cursor:
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 259, in cursor
    return self._cursor()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 235, in _cursor
    self.ensure_connection()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
django.db.utils.DatabaseError: ORA-12541: TNS:no listener
2024-07-03 21:55:23,524 basehttp.log_message 161 ERROR   => "POST /api/users/select_check_user/ HTTP/1.1" 500 176967
2024-07-03 21:55:23,666 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-03 21:55:26,152 log.log_response 230 ERROR   => Internal Server Error: /api/accounts/logout/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
cx_Oracle.DatabaseError: ORA-12541: TNS:no listener

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\accounts\views.py", line 39, in logout
    sql_result, http_status = select_logout(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\accounts\accountInfo.py", line 200, in select_logout
    with connection.cursor() as cursor:
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 259, in cursor
    return self._cursor()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 235, in _cursor
    self.ensure_connection()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\utils.py", line 90, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 219, in ensure_connection
    self.connect()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\base\base.py", line 200, in connect
    self.connection = self.get_new_connection(conn_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\utils\asyncio.py", line 33, in inner
    return func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\db\backends\oracle\base.py", line 233, in get_new_connection
    **conn_params,
django.db.utils.DatabaseError: ORA-12541: TNS:no listener
2024-07-03 21:55:26,154 basehttp.log_message 161 ERROR   => "POST /api/accounts/logout/ HTTP/1.1" 500 173248
2024-07-03 21:58:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-03 21:58:09,310 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 21:58:09,410 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-03 21:58:09,411 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,412 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-03 21:58:09,412 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-03 21:58:09,413 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-03 21:58:09,534 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,535 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,536 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,564 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 21:58:09,719 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-03 21:58:09,753 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-03 21:58:10,102 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3839
2024-07-03 21:58:10,677 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-03 21:58:10,733 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-03 22:01:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:01:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:04:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:04:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:07:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:07:08,265 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:10:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:10:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:13:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:13:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:16:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:16:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:19:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:19:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:22:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:22:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:25:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:25:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:28:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:28:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:31:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:31:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:34:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:34:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:37:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:37:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:40:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:40:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:43:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:43:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:46:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:46:08,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:49:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:49:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:52:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:52:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:55:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:55:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 22:58:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 22:58:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:01:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:01:08,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:04:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:04:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:07:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:07:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:10:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:10:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:13:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:13:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:16:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:16:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:19:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:19:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:22:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:22:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:25:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:25:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:28:07,985 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-03 23:28:08,034 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:28:08,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:31:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:31:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:34:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:34:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:37:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:37:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:40:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:40:08,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:43:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:43:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:46:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:46:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:49:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:49:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:52:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:52:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:55:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:55:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-03 23:58:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 5021fc08-8c03-47f2-a087-e529c8911d5a
2024-07-03 23:58:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
