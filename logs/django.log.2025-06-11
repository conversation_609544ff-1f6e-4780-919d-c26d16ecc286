2025-06-11 09:15:31,360 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 09:15:56,492 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 09:15:56,493 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-11 09:16:06,801 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-06-11 09:16:06,960 token_utils.verify_access_token  42 ERROR   => Refresh token 無效
2025-06-11 09:16:06,961 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-06-11 09:16:06,961 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 77
2025-06-11 09:16:56,635 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 09:16:56,782 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-11 09:16:56,857 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 09:16:56,980 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 09:16:57,062 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 09:16:57,068 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-11 09:16:57,072 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-11 09:16:57,074 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-11 09:16:57,202 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-11 09:16:57,531 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3631
2025-06-11 09:16:57,731 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 483
2025-06-11 09:16:57,816 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 570
2025-06-11 09:16:59,351 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-11 09:16:59,417 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 09:16:59,417 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-06-11 09:16:59,580 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 09:16:59,599 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-11 09:17:02,126 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61167
2025-06-11 09:17:09,067 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2025-06-11 09:17:09,068 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-06-11 09:17:09,211 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: 30304
2025-06-11 09:17:09,211 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno AND  DEALER = :user_id 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-06-11 09:17:09,217 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 7 條記錄
2025-06-11 09:17:09,218 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 510
2025-06-11 09:17:09,280 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-11 09:17:12,349 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2025-06-11 09:17:12,693 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: 30304
2025-06-11 09:17:12,701 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-11 09:17:15,372 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 09:17:16,513 _DownloadLetterInfo.unprotect_and_protect_docx 156 INFO    => 已打開文檔
2025-06-11 09:17:16,581 _DownloadLetterInfo.unprotect_and_protect_docx 160 INFO    => 文檔已解除保護
2025-06-11 09:17:16,581 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:19:20,007 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:19:20,007 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 1/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:19:21,128 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:19:48,651 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:19:48,849 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 09:19:49,011 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-11 09:19:49,283 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-11 09:19:49,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-11 09:19:49,406 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 09:19:49,640 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 483
2025-06-11 09:19:49,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 570
2025-06-11 09:20:11,734 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61167
2025-06-11 09:20:13,913 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-11 09:20:13,917 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: 30304
2025-06-11 09:20:13,918 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno AND  DEALER = :user_id 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-06-11 09:20:13,923 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 7 條記錄
2025-06-11 09:20:13,923 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 510
2025-06-11 09:20:15,570 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: 30304
2025-06-11 09:20:15,578 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-11 09:20:17,727 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 09:21:21,849 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:21:21,849 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 2/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:21:22,862 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:22:48,790 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:23:59,591 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:23:59,592 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 3/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:23:59,661 _DownloadLetterInfo.unprotect_and_protect_docx 177 INFO    => 文檔已關閉
2025-06-11 09:23:59,661 _DownloadLetterInfo.unprotect_and_protect_docx 182 ERROR   => unprotect_and_protect_docx 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:23:59,685 _DownloadLetterInfo.unprotect_and_protect_docx 188 INFO    => Word 應用程序已關閉
2025-06-11 09:23:59,931 _DownloadLetterInfo.unprotect_and_protect_docx 192 INFO    => COM 已取消初始化
2025-06-11 09:23:59,931 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 1/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:24:00,933 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-11 09:24:02,911 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 09:24:03,581 _DownloadLetterInfo.unprotect_and_protect_docx 156 INFO    => 已打開文檔
2025-06-11 09:24:03,705 _DownloadLetterInfo.unprotect_and_protect_docx 160 INFO    => 文檔已解除保護
2025-06-11 09:24:03,705 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:25:48,818 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:25:50,543 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:25:50,543 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 1/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:25:51,655 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:27:48,813 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:27:48,813 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 2/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:27:49,818 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:28:48,809 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:29:18,360 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:29:18,361 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 3/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:29:18,410 _DownloadLetterInfo.unprotect_and_protect_docx 177 INFO    => 文檔已關閉
2025-06-11 09:29:18,410 _DownloadLetterInfo.unprotect_and_protect_docx 182 ERROR   => unprotect_and_protect_docx 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:29:18,416 _DownloadLetterInfo.unprotect_and_protect_docx 188 INFO    => Word 應用程序已關閉
2025-06-11 09:29:21,117 _DownloadLetterInfo.unprotect_and_protect_docx 192 INFO    => COM 已取消初始化
2025-06-11 09:29:21,117 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 2/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:29:22,125 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-11 09:29:23,354 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 09:29:23,933 _DownloadLetterInfo.unprotect_and_protect_docx 156 INFO    => 已打開文檔
2025-06-11 09:29:24,055 _DownloadLetterInfo.unprotect_and_protect_docx 160 INFO    => 文檔已解除保護
2025-06-11 09:29:24,055 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:31:17,087 _DownloadLetterInfo.select_letter_file_statistics 334 INFO    => 開始 select_letter_file_statistics，用戶 ID: 30304
2025-06-11 09:31:17,087 _DownloadLetterInfo.select_letter_file_statistics 362 INFO    => 執行的 SQL 查詢:  
                SELECT DEALER, REMARKNUMBER, REMARKVALUE 
                  FROM PUDCHT_FILESTATISTICS 
                 WHERE 1 = 1 AND  PUDCNO = :pudcno AND  DEALER = :user_id 
                 ORDER BY PUDCNO, DEALER, LPAD(REMARKNUMBER, 3, '0')
            
2025-06-11 09:31:17,092 _DownloadLetterInfo.select_letter_file_statistics 376 INFO    => 成功檢索到 23 條記錄
2025-06-11 09:31:17,093 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 1570
2025-06-11 09:31:17,147 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-11 09:31:19,152 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: 30304
2025-06-11 09:31:19,162 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400229\「114年黑松部分重點產品再鋪貨獎勵」活動成果函(直營)250610h16515P.docx
2025-06-11 09:31:20,548 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 09:31:21,149 _DownloadLetterInfo.unprotect_and_protect_docx 156 INFO    => 已打開文檔
2025-06-11 09:31:21,283 _DownloadLetterInfo.unprotect_and_protect_docx 160 INFO    => 文檔已解除保護
2025-06-11 09:31:21,284 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:31:48,811 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:31:50,425 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:31:50,425 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 1/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:31:51,446 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:34:49,682 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:37:40,596 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:37:40,596 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 2/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:37:41,613 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 09:37:49,761 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:40:49,740 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:42:56,317 _DownloadLetterInfo.modify_file_content 227 ERROR   => modify_file_content 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:56,318 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 3/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:56,409 _DownloadLetterInfo.unprotect_and_protect_docx 177 INFO    => 文檔已關閉
2025-06-11 09:42:56,409 _DownloadLetterInfo.unprotect_and_protect_docx 182 ERROR   => unprotect_and_protect_docx 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:56,442 _DownloadLetterInfo.unprotect_and_protect_docx 188 INFO    => Word 應用程序已關閉
2025-06-11 09:42:59,152 _DownloadLetterInfo.unprotect_and_protect_docx 192 INFO    => COM 已取消初始化
2025-06-11 09:42:59,152 _DownloadLetterInfo.wrapper 122 ERROR   => COM 錯誤 (嘗試 3/3): (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:59,152 _DownloadLetterInfo.select_letter_download 318 ERROR   => select_letter_download 發生錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:59,153 error_utils.handle_error  13 ERROR   => select_letter_download - Unexpected error: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '無法個別存取此集合中的各列，因為表格中有垂直合併的儲存格。', 'wdmain11.chm', 25471, -2146822297), None)
2025-06-11 09:42:59,153 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
2025-06-11 09:42:59,153 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 439
2025-06-11 09:42:59,157 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 58438)

2025-06-11 09:43:49,905 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:46:49,986 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:49:49,763 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:52:49,834 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:55:49,795 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 09:57:25,441 _DownloadLetterInfo.modify_file_content 222 INFO    => 修改後的文檔已保存為 C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400229\30304_「114年黑松部分重點產品再鋪貨獎勵」活動成果函(直營)250610h16515P_7aa9fce7.docx
2025-06-11 09:57:25,525 _DownloadLetterInfo.unprotect_and_protect_docx 177 INFO    => 文檔已關閉
2025-06-11 09:57:25,525 _DownloadLetterInfo.unprotect_and_protect_docx 182 ERROR   => unprotect_and_protect_docx 發生錯誤: wdAllowOnlyReading
2025-06-11 09:57:25,531 _DownloadLetterInfo.unprotect_and_protect_docx 188 INFO    => Word 應用程序已關閉
2025-06-11 09:57:25,761 _DownloadLetterInfo.unprotect_and_protect_docx 192 INFO    => COM 已取消初始化
2025-06-11 09:57:25,762 _DownloadLetterInfo.select_letter_download 318 ERROR   => select_letter_download 發生錯誤: wdAllowOnlyReading
2025-06-11 09:57:25,762 error_utils.handle_error  13 ERROR   => select_letter_download - Unexpected error: wdAllowOnlyReading
2025-06-11 09:57:25,762 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
2025-06-11 09:57:25,763 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 87
2025-06-11 09:57:25,767 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61226)

2025-06-11 09:58:49,860 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:01:49,763 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:04:01,127 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-11 10:04:01,187 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 10:04:26,893 _DownloadLetterInfo.select_letter_download 276 INFO    => 開始 select_letter_download，用戶 ID: 30304
2025-06-11 10:04:26,920 _DownloadLetterInfo.unprotect_and_protect_docx 143 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400229\「114年黑松部分重點產品再鋪貨獎勵」活動成果函(直營)250610h16515P.docx
2025-06-11 10:04:28,358 _DownloadLetterInfo.unprotect_and_protect_docx 152 INFO    => Word 應用程序啟動成功
2025-06-11 10:04:29,230 _DownloadLetterInfo.unprotect_and_protect_docx 156 INFO    => 已打開文檔
2025-06-11 10:04:29,370 _DownloadLetterInfo.unprotect_and_protect_docx 160 INFO    => 文檔已解除保護
2025-06-11 10:04:29,370 _DownloadLetterInfo.modify_file_content 206 INFO    => 開始修改文件內容
2025-06-11 10:04:48,901 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:07:48,890 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:10:48,890 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:13:48,904 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:16:48,907 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:18:58,134 _DownloadLetterInfo.modify_file_content 222 INFO    => 修改後的文檔已保存為 C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400229\30304_「114年黑松部分重點產品再鋪貨獎勵」活動成果函(直營)250610h16515P_06375046.docx
2025-06-11 10:18:58,199 _DownloadLetterInfo.unprotect_and_protect_docx 177 INFO    => 文檔已關閉
2025-06-11 10:18:58,199 _DownloadLetterInfo.unprotect_and_protect_docx 182 ERROR   => unprotect_and_protect_docx 發生錯誤: wdAllowOnlyReading
2025-06-11 10:18:58,205 _DownloadLetterInfo.unprotect_and_protect_docx 188 INFO    => Word 應用程序已關閉
2025-06-11 10:18:58,404 _DownloadLetterInfo.unprotect_and_protect_docx 192 INFO    => COM 已取消初始化
2025-06-11 10:18:58,404 _DownloadLetterInfo.select_letter_download 318 ERROR   => select_letter_download 發生錯誤: wdAllowOnlyReading
2025-06-11 10:18:58,404 error_utils.handle_error  13 ERROR   => select_letter_download - Unexpected error: wdAllowOnlyReading
2025-06-11 10:18:58,404 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
2025-06-11 10:18:58,405 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 87
2025-06-11 10:18:58,409 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 63368)

2025-06-11 10:19:48,889 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:22:48,928 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:25:48,899 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:27:07,472 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 10:27:36,512 _DownloadLetterInfo.unprotect_and_protect_docx 177 ERROR   => 錯誤: (-2147418111, '接收者已拒絕這個呼叫。', None, None)
2025-06-11 10:27:36,679 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 135, in select_letter_download
    sql_result, http_status = select_letter_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 268, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadLetterInfo.py", line 317, in select_letter_download
    modify_file_content)
TypeError: 'NoneType' object is not iterable
2025-06-11 10:27:36,687 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 117387
2025-06-11 10:27:47,021 _DownloadLetterInfo.unprotect_and_protect_docx 177 ERROR   => 錯誤: (-2147418111, '接收者已拒絕這個呼叫。', None, None)
2025-06-11 10:27:47,188 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\views.py", line 135, in select_letter_download
    sql_result, http_status = select_letter_download(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 268, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\documents\_DownloadLetterInfo.py", line 317, in select_letter_download
    modify_file_content)
TypeError: 'NoneType' object is not iterable
2025-06-11 10:27:47,190 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 117387
2025-06-11 10:28:48,892 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:31:23,575 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 10:31:48,917 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:32:29,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:32:29,853 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 10:32:29,985 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-11 10:32:30,086 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-11 10:32:30,295 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-11 10:32:30,423 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 10:32:30,646 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1113
2025-06-11 10:32:30,726 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-11 10:32:34,055 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61167
2025-06-11 10:32:38,821 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-11 10:32:38,901 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 1570
2025-06-11 10:33:22,858 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:33:23,098 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 10:33:23,235 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-11 10:33:23,318 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-11 10:33:23,529 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-11 10:33:23,659 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 10:33:23,856 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1113
2025-06-11 10:33:23,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-11 10:33:27,152 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61167
2025-06-11 10:33:29,180 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 1570
2025-06-11 10:33:29,242 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-11 10:33:34,792 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: (註1)
2025-06-11 10:33:35,244 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註1
2025-06-11 10:33:35,775 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註2
2025-06-11 10:33:35,808 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註2點
2025-06-11 10:33:35,913 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註6
2025-06-11 10:33:35,948 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註6點
2025-06-11 10:33:35,970 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註10
2025-06-11 10:33:36,011 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註10點
2025-06-11 10:33:36,100 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註14
2025-06-11 10:33:36,133 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註14點
2025-06-11 10:33:36,247 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註3
2025-06-11 10:33:36,291 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註3%
2025-06-11 10:33:36,353 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註7
2025-06-11 10:33:36,387 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註7%
2025-06-11 10:33:36,458 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註11
2025-06-11 10:33:36,492 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註11%
2025-06-11 10:33:36,607 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註15
2025-06-11 10:33:36,766 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註15%
2025-06-11 10:33:36,838 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註4
2025-06-11 10:33:36,876 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註4%
2025-06-11 10:33:36,895 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註8
2025-06-11 10:33:36,927 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註8%
2025-06-11 10:33:36,947 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註12
2025-06-11 10:33:36,996 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註12%
2025-06-11 10:33:37,111 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註16
2025-06-11 10:33:37,148 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註16%
2025-06-11 10:33:37,234 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註5
2025-06-11 10:33:37,283 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註5元
2025-06-11 10:33:37,341 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註9
2025-06-11 10:33:37,371 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註9元
2025-06-11 10:33:37,434 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註13
2025-06-11 10:33:37,473 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註13元
2025-06-11 10:33:37,563 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註17
2025-06-11 10:33:37,590 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註17元
2025-06-11 10:33:37,646 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註18
2025-06-11 10:33:37,675 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註18元
2025-06-11 10:33:37,732 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註19
2025-06-11 10:33:37,761 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註1
2025-06-11 10:33:37,884 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註20
2025-06-11 10:33:37,909 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註20點
2025-06-11 10:33:37,982 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註21
2025-06-11 10:33:38,009 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註2
2025-06-11 10:33:38,073 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註22
2025-06-11 10:33:38,093 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註22元
2025-06-11 10:33:38,168 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註23
2025-06-11 10:33:38,186 _DownloadLetterInfo.replace_and_format 303 INFO    => 找到模式: 註2
2025-06-11 10:33:40,280 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 32075
2025-06-11 10:34:27,869 _DownloadLetterInfo.unprotect_and_protect_docx 227 ERROR   => 錯誤: (-2147352567, '發生例外狀況。', (0, 'Microsoft Word', '指令失敗', 'wdmain11.chm', 36966, -2146824090), None)
2025-06-11 10:34:28,083 _DownloadLetterInfo.select_letter_download 379 ERROR   => 文件處理失敗
2025-06-11 10:34:28,083 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
2025-06-11 10:34:28,085 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 87
2025-06-11 10:34:28,089 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 62648)

2025-06-11 10:36:23,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:39:23,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:42:24,804 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 10:42:28,556 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-11 10:42:30,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:42:32,012 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 10:42:45,786 word_queue_processor.submit_task 161 INFO    => 任務已提交: 72d0990f-259a-4085-a5b7-f4f47f8fb101
2025-06-11 10:42:45,786 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 72d0990f-259a-4085-a5b7-f4f47f8fb101
2025-06-11 10:42:46,618 word_queue_processor.submit_task 161 INFO    => 任務已提交: bb26435f-772a-4db7-b6ae-57f673881cab
2025-06-11 10:42:49,793 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: (註1)
2025-06-11 10:42:50,062 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註1
2025-06-11 10:42:51,335 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:42:51,407 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2點
2025-06-11 10:42:51,471 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註6
2025-06-11 10:42:51,516 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註6點
2025-06-11 10:42:51,610 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註10
2025-06-11 10:42:51,698 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註10點
2025-06-11 10:42:51,761 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註14
2025-06-11 10:42:51,802 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註14點
2025-06-11 10:42:51,881 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註3
2025-06-11 10:42:51,905 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註3%
2025-06-11 10:42:51,969 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註7
2025-06-11 10:42:52,005 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註7%
2025-06-11 10:42:52,076 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註11
2025-06-11 10:42:52,182 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註11%
2025-06-11 10:42:52,377 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註15
2025-06-11 10:42:52,453 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註15%
2025-06-11 10:42:52,722 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註4
2025-06-11 10:42:52,793 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註4%
2025-06-11 10:42:52,858 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註8
2025-06-11 10:42:52,920 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註8%
2025-06-11 10:42:53,002 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註12
2025-06-11 10:42:53,042 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註12%
2025-06-11 10:42:53,111 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註16
2025-06-11 10:42:53,169 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註16%
2025-06-11 10:42:53,246 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註5
2025-06-11 10:42:53,284 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註5元
2025-06-11 10:42:53,337 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註9
2025-06-11 10:42:53,404 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註9元
2025-06-11 10:42:53,440 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註13
2025-06-11 10:42:53,482 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註13元
2025-06-11 10:42:53,510 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註17
2025-06-11 10:42:53,555 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註17元
2025-06-11 10:42:53,645 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註18
2025-06-11 10:42:53,669 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註18元
2025-06-11 10:42:54,058 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註19
2025-06-11 10:42:54,095 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註1
2025-06-11 10:42:54,145 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註20
2025-06-11 10:42:54,198 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註20點
2025-06-11 10:42:54,307 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註21
2025-06-11 10:42:54,348 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:42:54,460 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註22
2025-06-11 10:42:54,510 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註22元
2025-06-11 10:42:54,610 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註23
2025-06-11 10:42:54,638 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:42:56,891 word_queue_processor._process_tasks 120 INFO    => 任務完成: 72d0990f-259a-4085-a5b7-f4f47f8fb101
2025-06-11 10:42:56,891 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: bb26435f-772a-4db7-b6ae-57f673881cab
2025-06-11 10:42:56,931 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 33209
2025-06-11 10:42:59,951 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:43:00,119 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2點
2025-06-11 10:43:00,294 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註6
2025-06-11 10:43:00,474 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註6點
2025-06-11 10:43:00,846 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註10
2025-06-11 10:43:01,186 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註10點
2025-06-11 10:43:01,433 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註14
2025-06-11 10:43:01,515 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註14點
2025-06-11 10:43:01,660 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註3
2025-06-11 10:43:01,718 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註3%
2025-06-11 10:43:01,777 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註7
2025-06-11 10:43:01,848 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註7%
2025-06-11 10:43:01,951 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註11
2025-06-11 10:43:02,008 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註11%
2025-06-11 10:43:02,100 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註15
2025-06-11 10:43:02,155 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註15%
2025-06-11 10:43:02,211 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註4
2025-06-11 10:43:02,266 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註4%
2025-06-11 10:43:02,338 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註8
2025-06-11 10:43:02,380 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註8%
2025-06-11 10:43:02,470 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註12
2025-06-11 10:43:02,496 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註12%
2025-06-11 10:43:02,558 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註16
2025-06-11 10:43:02,580 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註16%
2025-06-11 10:43:02,634 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註5
2025-06-11 10:43:02,652 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註5元
2025-06-11 10:43:02,670 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註9
2025-06-11 10:43:02,683 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註9元
2025-06-11 10:43:02,735 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註13
2025-06-11 10:43:02,751 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註13元
2025-06-11 10:43:02,789 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註17
2025-06-11 10:43:02,809 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註17元
2025-06-11 10:43:02,844 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註18
2025-06-11 10:43:02,867 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註18元
2025-06-11 10:43:02,910 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註19
2025-06-11 10:43:02,931 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註1
2025-06-11 10:43:03,011 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註20
2025-06-11 10:43:03,032 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註20點
2025-06-11 10:43:03,122 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註21
2025-06-11 10:43:03,159 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:43:03,209 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註22
2025-06-11 10:43:03,230 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註22元
2025-06-11 10:43:03,294 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註23
2025-06-11 10:43:03,351 _DownloadLetterInfo.replace_and_format 326 INFO    => 找到模式: 註2
2025-06-11 10:43:03,740 word_queue_processor._process_tasks 120 INFO    => 任務完成: bb26435f-772a-4db7-b6ae-57f673881cab
2025-06-11 10:43:03,744 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 26267
2025-06-11 10:44:03,742 word_queue_processor._process_tasks 135 WARNING => Word 應用程序無響應，重新初始化
2025-06-11 10:44:03,743 word_queue_processor._cleanup_word  93 ERROR   => 清理 Word 失敗: (-2147023174, 'RPC 伺服器無法使用。', None, None)
2025-06-11 10:44:05,035 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 10:45:23,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:48:23,834 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:51:23,861 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:54:28,857 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 10:58:02,337 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:00:23,900 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:03:23,871 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:06:23,788 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:09:24,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:12:23,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:15:23,761 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:18:23,712 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-06-11 11:18:23,835 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:21:24,005 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:24:23,833 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:27:23,949 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:30:23,827 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:33:24,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:36:24,373 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:39:23,836 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:42:24,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:44:32,439 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 11:44:35,968 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-11 11:44:37,849 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 11:44:37,849 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 11:44:37,898 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 11:44:37,898 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-11 11:44:37,976 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2025-06-11 11:44:37,976 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-06-11 11:44:37,977 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2025-06-11 11:44:38,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-11 11:44:38,125 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-11 11:44:38,457 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 11:44:46,766 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 11:44:46,907 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-11 11:44:47,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 11:44:47,114 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 11:44:47,117 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-11 11:44:47,117 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-11 11:44:47,117 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-11 11:44:47,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3631
2025-06-11 11:44:47,465 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-11 11:44:47,737 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 668
2025-06-11 11:44:47,826 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2298
2025-06-11 20:23:37,742 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-06-11 20:23:37,861 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-06-11 20:23:37,862 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2025-06-11 20:23:50,924 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:23:51,063 log.log_response 230 WARNING => Unprocessable Entity: /api/accounts/login/
2025-06-11 20:23:51,063 basehttp.log_message 161 WARNING => "POST /api/accounts/login/ HTTP/1.1" 422 87
2025-06-11 20:23:56,360 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-06-11 20:23:56,428 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 20:23:56,565 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 20:23:56,676 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 20:23:56,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-11 20:23:56,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-11 20:23:56,683 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-11 20:23:56,825 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-06-11 20:23:57,112 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3631
2025-06-11 20:23:57,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2058
2025-06-11 20:23:57,650 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-11 20:23:58,530 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2025-06-11 20:23:58,590 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,591 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,591 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,591 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,596 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,597 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,645 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-11 20:23:58,809 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 49046
2025-06-11 20:23:58,833 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 20:23:58,923 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2025-06-11 20:23:58,978 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2025-06-11 20:23:59,049 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 5059
2025-06-11 20:23:59,145 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3075
2025-06-11 20:23:59,170 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2025-06-11 20:24:00,223 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2025-06-11 20:24:00,517 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49400
2025-06-11 20:24:05,696 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2025-06-11 20:24:05,837 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 20:24:06,123 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2025-06-11 20:24:06,218 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-06-11 20:24:06,332 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 49046
2025-06-11 20:24:06,400 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-11 20:24:06,464 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 5059
2025-06-11 20:24:06,535 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2058
2025-06-11 20:24:06,537 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2025-06-11 20:24:06,620 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-11 20:24:06,623 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2025-06-11 20:24:06,684 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 3075
2025-06-11 20:24:06,748 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2025-06-11 20:26:05,322 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 20:26:08,613 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-11 20:26:11,126 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 20:29:50,502 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:29:53,918 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:31:01,918 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 20:31:04,893 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-11 20:31:06,163 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 20:31:09,368 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:31:17,796 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:31:59,420 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-11 20:32:01,120 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-11 20:32:03,524 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-11 20:32:04,985 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-11 20:32:05,129 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-06-11 20:32:05,209 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 20:32:05,358 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-11 20:32:05,439 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-11 20:32:05,445 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-11 20:32:05,446 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-11 20:32:05,446 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-11 20:32:05,581 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-06-11 20:32:05,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3631
2025-06-11 20:32:06,216 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-11 20:32:06,224 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2058
