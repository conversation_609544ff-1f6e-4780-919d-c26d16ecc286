2024-11-11 15:23:47,446 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-11-11 15:23:47,447 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-11-11 15:23:47,551 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-11-11 15:23:47,553 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-11-11 15:23:47,553 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-11-11 15:23:47,624 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:23:54,179 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-11-11 15:23:54,334 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-11-11 15:23:54,477 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-11 15:23:54,551 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-11-11 15:23:54,553 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-11-11 15:23:54,604 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-11-11 15:23:54,605 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-11-11 15:23:54,782 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-11-11 15:23:54,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4666
2024-11-11 15:23:55,201 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5195
2024-11-11 15:23:55,298 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8689
2024-11-11 15:24:03,533 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-11-11 15:24:03,587 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-11-11 15:24:03,587 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-11-11 15:24:03,788 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:24:03,790 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:24:06,939 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48835
2024-11-11 15:24:08,713 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-11-11 15:24:08,713 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-11-11 15:24:08,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:24:08,890 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:24:11,070 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:24:11,130 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:24:21,430 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2024-11-11 15:24:21,601 _DownloadBusinessNotificationInfo.select_business_notification_price_download 783 ERROR   => select_business_notification_price_download 發生錯誤: module 'win32com.gen_py.00020905-0000-0000-C000-000000000046x0x8x7' has no attribute 'MinorVersion'
2024-11-11 15:24:21,603 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-11-11 15:24:21,603 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 249
2024-11-11 15:24:54,057 _DownloadBusinessNotificationInfo.select_business_notification_price_download 783 ERROR   => select_business_notification_price_download 發生錯誤: module 'win32com.gen_py.00020905-0000-0000-C000-000000000046x0x8x7' has no attribute 'MinorVersion'
2024-11-11 15:24:54,059 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-11-11 15:24:54,059 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 249
2024-11-11 15:24:58,800 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-11-11 15:24:58,912 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-11-11 15:24:59,165 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-11 15:24:59,353 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-11-11 15:24:59,416 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-11-11 15:24:59,573 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:24:59,679 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:24:59,799 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8689
2024-11-11 15:24:59,875 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5195
2024-11-11 15:25:02,350 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48835
2024-11-11 15:25:03,495 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:25:03,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:25:04,408 _DownloadBusinessNotificationInfo.select_business_notification_price_download 783 ERROR   => select_business_notification_price_download 發生錯誤: module 'win32com.gen_py.00020905-0000-0000-C000-000000000046x0x8x7' has no attribute 'MinorVersion'
2024-11-11 15:25:04,410 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2024-11-11 15:25:04,410 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 249
2024-11-11 15:25:12,498 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-11-11 15:25:22,410 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48835
2024-11-11 15:25:24,523 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:25:24,577 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:25:26,471 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,485 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,650 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,658 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,662 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,665 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:26,668 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:27,910 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:25:28,349 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6044
2024-11-11 15:26:37,252 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48835
2024-11-11 15:26:39,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:26:39,449 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:26:43,155 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,182 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,377 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,379 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,383 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,385 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:43,387 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:44,682 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:26:46,280 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6044
2024-11-11 15:27:59,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" ************-11-11 15:30:20,607 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:30:20,863 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:31:22,036 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-11-11 15:31:22,164 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-11 15:31:22,402 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-11-11 15:31:22,442 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4666
2024-11-11 15:31:22,783 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-11-11 15:31:22,866 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" ************-11-11 15:31:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-11-11 15:31:29,186 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:31:29,268 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:32:37,202 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" ************-11-11 15:32:40,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50595
2024-11-11 15:32:42,651 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:32:42,693 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:32:45,986 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:45,990 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:45,993 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,002 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,006 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,019 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,039 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,042 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,054 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,067 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,077 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,081 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,084 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,105 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,134 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,167 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,176 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,194 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,206 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:32:46,371 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6847
2024-11-11 15:35:11,277 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-11-11 15:35:11,403 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 46039
2024-11-11 15:40:58,450 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-11-11 15:41:04,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50595
2024-11-11 15:41:06,292 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:41:06,351 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:41:08,047 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,053 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,057 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,083 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,087 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,099 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,120 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,123 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,135 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,145 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,156 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,160 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,227 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,242 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,251 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,266 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,284 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,320 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:08,329 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:41:09,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6847
2024-11-11 15:42:44,440 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50595
2024-11-11 15:42:45,779 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:42:45,841 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:42:49,697 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,701 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,706 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,717 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,730 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,753 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,784 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,788 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,794 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,804 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,810 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,813 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,818 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,836 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,860 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,870 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,880 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,892 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,916 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:49,925 _DownloadBusinessNotificationInfo.get_cell_text_and_color 428 ERROR   => get_cell_text_and_color 發生錯誤: (-**********, '發生例外狀況。', (0, 'Microsoft Word', '集合中所需的成員不存在。', 'wdmain11.chm', 25421, -**********), None)
2024-11-11 15:42:51,858 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6848
2024-11-11 15:43:37,208 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-11-11 15:43:37,400 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 5802
2024-11-11 15:44:06,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:44:06,506 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:44:07,872 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" ************-11-11 15:44:08,830 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 5802
2024-11-11 15:47:28,447 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:47:28,617 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:47:38,272 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-11-11 15:47:38,406 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-11 15:47:38,875 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-11-11 15:47:38,906 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4666
2024-11-11 15:47:39,225 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8689
2024-11-11 15:47:39,279 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5195
2024-11-11 15:47:48,014 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-11-11 15:47:48,165 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:47:48,250 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:48:01,739 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 40181
2024-11-11 15:48:17,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:48:17,443 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:48:19,440 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48835
2024-11-11 15:48:20,968 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-11-11 15:48:20,990 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-11-11 15:52:58,935 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:52:59,135 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-11-11 15:53:06,107 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" ************-11-11 15:53:06,241 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-11-11 15:53:06,418 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-11-11 15:53:06,576 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 4666
2024-11-11 15:53:06,876 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10918
2024-11-11 15:53:06,929 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 5664
2024-11-11 15:53:07,818 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-11-11 15:53:08,011 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-11-11 15:53:08,027 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-11-11 15:53:09,290 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 9446
