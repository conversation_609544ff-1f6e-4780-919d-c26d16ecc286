2024-06-12 11:34:25,253 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-12 11:34:25,331 token_utils.verify_access_token  30 INFO    => refresh_token: c55c7caa-eac1-4864-995b-bb762cabdeda
2024-06-12 11:34:25,416 token_utils.verify_access_token  66 ERROR   => access_token 不存在
2024-06-12 11:34:25,417 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-06-12 11:34:25,417 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-06-12 11:34:26,549 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-06-12 11:34:26,624 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-06-12 11:34:32,147 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-06-12 11:34:32,296 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-06-12 11:34:32,353 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:32,429 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-12 11:34:32,503 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-06-12 11:34:32,504 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-06-12 11:34:32,555 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-06-12 11:34:32,555 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-06-12 11:34:32,613 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:32,688 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:32,689 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:32,690 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:32,852 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-06-12 11:34:32,921 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2414
2024-06-12 11:34:33,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-06-12 11:34:33,480 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-06-12 11:34:34,384 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,467 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-06-12 11:34:34,525 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,526 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,526 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,577 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,580 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,580 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,630 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:34,631 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,632 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,657 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,682 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,685 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,686 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,791 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-06-12 11:34:34,847 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:34,900 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-06-12 11:34:34,954 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-06-12 11:34:35,004 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-06-12 11:34:35,015 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-06-12 11:34:35,070 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-12 11:34:35,124 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-06-12 11:34:36,707 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:36,771 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-06-12 11:34:36,835 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:36,836 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:36,837 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-06-12 11:34:36,893 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:36,895 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:36,965 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-12 11:34:37,022 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-06-12 11:34:37,032 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-12 11:34:38,773 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:38,776 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:38,844 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-06-12 11:34:38,949 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-06-12 11:34:40,223 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:41,153 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 47796
2024-06-12 11:34:50,735 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_log/ HTTP/1.1" 200 0
2024-06-12 11:34:50,786 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:51,124 basehttp.log_message 161 INFO    => "POST /api/users/select_user_log/ HTTP/1.1" 200 557
2024-06-12 11:34:54,651 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2024-06-12 11:34:54,652 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-06-12 11:34:54,705 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:54,706 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 11:34:54,794 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-06-12 11:34:54,854 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 57
2024-06-12 13:50:02,681 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-06-12 13:50:02,761 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:02,865 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-12 13:50:02,963 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-06-12 13:50:26,842 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:26,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-06-12 13:50:27,006 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:27,072 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-06-12 13:50:27,133 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-06-12 13:50:27,213 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-06-12 13:50:27,213 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-06-12 13:50:27,213 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-06-12 13:50:27,263 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:27,339 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:27,340 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:27,341 token_utils.verify_access_token  30 INFO    => refresh_token: 779456b7-5af5-4ce8-9307-5f79d365c698
2024-06-12 13:50:27,440 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-06-12 13:50:27,746 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2414
2024-06-12 13:50:27,827 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-06-12 13:50:27,920 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
