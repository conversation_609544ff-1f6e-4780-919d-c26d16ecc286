2024-09-02 11:15:50,789 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-09-02 11:15:51,212 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-09-02 11:15:51,326 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 11:15:51,448 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 11:15:51,529 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 11:15:51,605 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-09-02 11:15:51,606 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-09-02 11:15:51,607 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-09-02 11:15:51,843 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-09-02 11:15:51,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7999
2024-09-02 11:15:52,415 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-09-02 11:15:52,464 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-09-02 11:16:09,672 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-09-02 11:16:09,731 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-09-02 11:16:09,905 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 11:16:11,441 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-09-02 11:16:11,628 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 11:16:13,503 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 11:16:13,694 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 11:16:25,346 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 0
2024-09-02 11:16:25,705 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 85335
2024-09-02 11:16:30,973 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-09-02 11:16:31,088 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-02 11:16:31,486 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-02 11:18:56,485 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-09-02 11:18:56,486 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-09-02 11:18:56,486 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-09-02 11:18:56,555 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-09-02 11:19:02,474 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-09-02 11:19:02,701 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 11:19:02,839 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-02 11:19:03,117 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6675
2024-09-02 11:19:03,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2880
2024-09-02 11:19:03,325 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2775
2024-09-02 11:19:07,317 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 11:19:07,515 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 11:19:14,493 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:19:14,515 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188783
2024-09-02 11:19:45,959 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 0
2024-09-02 11:19:46,125 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/ HTTP/1.1" 200 565
2024-09-02 11:19:47,723 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:19:47,724 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188783
2024-09-02 11:19:50,006 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:19:50,007 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188783
2024-09-02 11:21:22,504 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-02 11:21:29,772 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-02 11:21:55,791 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-02 11:21:58,908 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:21:58,917 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188804
2024-09-02 11:22:17,265 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-09-02 11:22:18,703 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 41295
2024-09-02 11:22:22,007 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 0
2024-09-02 11:22:23,158 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/ HTTP/1.1" 200 26337
2024-09-02 11:22:44,535 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:22:44,536 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188804
2024-09-02 11:23:10,473 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-02 11:23:13,382 log.log_response 230 ERROR   => Internal Server Error: /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 74, in erp_hy_sdv200_main_sales_allowance_notification
    return self._handle_action('erp_hy_sdv200_main_sales_allowance_notification', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\views.py", line 48, in _handle_action
    sql_result, http_status = ALLOWANCE_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 247, in wrapper
    return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1455, in select_erp_hy_sdv200_main_sales_allowance_notification
    report = generate_sales_allowance_notification(page_data, start_rnk - 1)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\allowances\AllowanceInfo.py", line 1499, in generate_sales_allowance_notification
    "user": product_group[0]["USER200"] + ' ' + product_group[0]["CHI_NAME005"],  # 摘要
TypeError: must be str, not NoneType
2024-09-02 11:23:13,388 basehttp.log_message 161 ERROR   => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 500 188783
2024-09-02 11:24:46,035 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-09-02 11:24:49,995 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 39448
2024-09-02 11:53:22,802 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 11:53:22,872 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 11:53:26,457 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-02 11:53:26,595 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 11:53:26,753 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 11:53:27,027 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-02 11:53:27,084 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 11:53:27,284 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 11:53:27,322 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2775
2024-09-02 11:53:27,513 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2880
2024-09-02 11:53:31,984 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdv200_main_sales_allowance_notification/ HTTP/1.1" 200 39405
2024-09-02 11:56:27,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 11:59:26,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:02:27,226 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:05:27,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:08:27,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:11:27,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:14:27,204 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:17:27,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:20:27,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:23:27,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:26:27,392 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:29:27,203 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:32:27,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:35:27,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:38:27,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:41:27,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:44:27,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:47:27,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:50:27,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:53:27,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:56:27,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 12:59:27,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:00:30,102 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 13:00:30,163 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 13:02:27,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:05:27,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:08:27,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:11:27,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:14:27,164 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:17:27,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:20:27,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:23:27,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:26:27,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:29:27,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:32:26,509 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 13:32:26,510 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 13:32:26,705 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 13:32:26,737 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 13:32:26,796 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:35:26,767 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:38:27,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:41:27,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:44:27,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:47:27,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:50:27,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:53:27,025 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-02 13:53:27,166 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:56:27,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 13:59:27,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:02:27,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:05:27,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:08:27,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:08:52,193 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 14:08:52,257 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 14:11:27,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:14:27,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:17:27,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:20:27,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:23:27,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:26:27,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:29:27,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:32:27,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:35:27,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:38:27,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:41:27,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:43:24,116 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 14:43:24,171 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 14:44:27,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:47:27,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:50:27,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:53:27,190 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:56:27,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 14:59:27,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:02:27,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:05:27,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:08:27,244 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:11:27,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:14:27,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:17:27,210 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:20:27,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:23:27,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:26:27,199 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:29:27,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:32:27,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:35:27,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:38:27,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:41:27,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:44:15,531 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 15:44:15,531 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 15:44:15,697 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 15:44:15,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 15:44:27,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:47:27,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:50:27,131 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:53:27,142 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-02 15:53:27,247 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:56:26,799 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 15:59:27,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:02:27,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:05:27,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:05:39,412 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:05:39,562 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-09-02 16:05:39,731 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-09-02 16:05:39,732 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-09-02 16:05:39,733 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-09-02 16:05:39,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-09-02 16:05:39,918 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-09-02 16:05:39,973 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-09-02 16:05:40,176 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-09-02 16:05:40,315 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3334
2024-09-02 16:05:40,406 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 2880
2024-09-02 16:08:40,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:11:40,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:14:40,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:17:40,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:20:40,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:23:40,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:26:40,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:29:40,162 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:32:40,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:35:40,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:38:40,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:41:40,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:44:40,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:47:40,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:50:40,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:53:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:56:40,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 16:59:40,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:02:40,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:05:40,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:08:40,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:11:40,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:14:40,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:17:40,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:20:40,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:23:40,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:26:40,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:29:40,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:32:40,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:35:40,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:38:40,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:41:40,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:44:40,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:47:40,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:50:40,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:53:40,137 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-09-02 17:53:40,254 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:56:40,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 17:59:40,149 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:02:40,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:05:40,241 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:08:40,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:11:40,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:14:40,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:17:40,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:20:40,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:23:40,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:26:40,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:29:40,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:32:40,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:35:40,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:38:40,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:41:40,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:44:40,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:47:40,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:50:40,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:53:40,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:56:40,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 18:59:40,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:02:40,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:05:40,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:08:40,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:11:40,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:14:40,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:17:40,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-09-02 19:20:40,132 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-09-02 19:20:40,132 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
