2024-07-05 00:00:07,997 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 00:00:08,057 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:00:08,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:03:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:03:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:06:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:06:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:09:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:09:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:12:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:12:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:15:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:15:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:18:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:18:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-05 00:21:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:21:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:24:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:24:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:27:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:27:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:30:07,984 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:30:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:33:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:33:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:36:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:36:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:39:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:39:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:42:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:42:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:45:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:45:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:48:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:48:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:51:07,977 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:51:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:54:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:54:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 00:57:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 00:57:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:00:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:00:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:03:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:03:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:06:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:06:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:09:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:09:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:12:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:12:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:15:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:15:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:18:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:18:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:21:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:21:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:24:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:24:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:27:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:27:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:30:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:30:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:33:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:33:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:36:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:36:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:39:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:39:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:42:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:42:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:45:07,955 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:45:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:48:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:48:08,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:51:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:51:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:54:07,956 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:54:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 01:57:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 01:57:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:00:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:00:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:03:08,045 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 02:03:08,092 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:03:08,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:06:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:06:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:09:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:09:08,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:12:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:12:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:15:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:15:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:18:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:18:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:21:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:21:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:24:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:24:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:27:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:27:08,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:30:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:30:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:33:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:33:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:36:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:36:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:39:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:39:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:42:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:42:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:45:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:45:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:48:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:48:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:51:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:51:08,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:54:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:54:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 02:57:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 02:57:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:00:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:00:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:03:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:03:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:06:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:06:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:09:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:09:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:12:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:12:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:15:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:15:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:18:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:18:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:21:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:21:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:24:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:24:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:27:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:27:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:30:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:30:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:33:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:33:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:36:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:36:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:39:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:39:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:42:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:42:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:45:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:45:08,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:48:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:48:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:51:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:51:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:54:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:54:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 03:57:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 03:57:08,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:00:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:00:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:03:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:03:08,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:06:07,996 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 04:06:08,045 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:06:08,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:09:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:09:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-05 04:12:07,988 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:12:08,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:15:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:15:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:18:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:18:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:21:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:21:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:24:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:24:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:27:07,977 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:27:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:30:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:30:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:33:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:33:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:36:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:36:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:39:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:39:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:42:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:42:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:45:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:45:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:48:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:48:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:51:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:51:08,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:54:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:54:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 04:57:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 04:57:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:00:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:00:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:03:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:03:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:06:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:06:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:09:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:09:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:12:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:12:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:15:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:15:08,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:18:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:18:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:21:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:21:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:24:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:24:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:27:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:27:08,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:30:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:30:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:33:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:33:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:36:07,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:36:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:39:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:39:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:42:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:42:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:45:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:45:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:48:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:48:08,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:51:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:51:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:54:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:54:08,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 05:57:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 05:57:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:00:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:00:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:03:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:03:08,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:06:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:06:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:09:07,998 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 06:09:08,044 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:09:08,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:12:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:12:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:15:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:15:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:18:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:18:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:21:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:21:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:24:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:24:08,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:27:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:27:08,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:30:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:30:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:33:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:33:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:36:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:36:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:39:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:39:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:42:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:42:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:45:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:45:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:48:07,958 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:48:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:51:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:51:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:54:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:54:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 06:57:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 06:57:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:00:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:00:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:03:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:03:08,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:06:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:06:08,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:09:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:09:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:12:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:12:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:15:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:15:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:18:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:18:08,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:21:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:21:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:24:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:24:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:27:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:27:08,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:30:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:30:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:33:07,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:33:08,015 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:33:33,236 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 07:33:33,236 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 07:33:33,310 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:33:33,314 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:33:33,392 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 07:33:33,450 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-05 07:36:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:36:08,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:39:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:39:08,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:41:30,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:41:31,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:44:30,982 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:44:31,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:47:30,305 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:30,372 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:47:31,769 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:31,852 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-05 07:47:31,946 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-05 07:47:32,004 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:32,075 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 07:47:36,016 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:36,086 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 07:47:36,143 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-05 07:47:36,200 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:36,356 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-05 07:47:37,130 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:37,130 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-05 07:47:37,214 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:37,238 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 07:47:37,334 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:47:38,154 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-05 07:47:38,214 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:47:38,712 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-05 07:48:11,226 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:48:11,388 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 638
2024-07-05 07:48:13,023 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:48:13,152 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:48:48,096 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:48:48,258 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 638
2024-07-05 07:48:49,232 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:48:49,368 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:50:15,553 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:15,695 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 638
2024-07-05 07:50:16,605 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:16,717 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:50:30,305 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:30,361 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:50:39,481 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,539 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:50:39,619 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,682 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 07:50:39,741 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,743 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 07:50:39,815 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-05 07:50:39,816 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-05 07:50:39,836 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,919 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,920 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:39,920 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 07:50:39,966 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 07:50:39,984 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:40,182 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-05 07:50:40,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 07:50:40,540 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 07:50:41,397 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:41,397 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:41,515 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:50:41,523 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 07:50:45,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:46,101 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 638
2024-07-05 07:50:47,034 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:50:47,146 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:07,770 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:07,912 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 638
2024-07-05 07:51:09,011 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:09,129 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:18,019 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:18,354 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:51:18,437 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:18,650 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 07:51:18,717 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:18,718 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:18,719 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:18,720 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:19,267 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 07:51:19,276 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 07:51:19,278 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 07:51:19,324 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:19,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-05 07:51:19,618 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 07:51:19,843 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:19,844 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:19,951 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:19,958 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 07:51:20,473 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:20,966 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-05 07:51:21,953 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:22,086 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:26,435 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:26,540 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:28,667 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:28,784 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:29,375 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:29,505 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:29,900 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:30,022 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:31,412 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:31,918 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 51202
2024-07-05 07:51:31,949 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:32,073 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:36,155 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:36,647 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 221383
2024-07-05 07:51:36,776 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:36,899 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:41,768 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:41,881 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:43,476 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:43,586 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:48,236 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:48,351 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:52,198 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:52,301 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:51:54,893 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:51:55,008 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:52:27,827 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:52:27,939 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:53:13,854 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:53:13,970 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:53:16,690 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:53:16,814 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:53:19,572 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:53:19,696 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 07:54:18,454 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:54:18,511 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 07:57:18,441 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 07:57:18,509 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:00:18,447 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:00:18,533 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-05 08:03:18,434 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:03:18,492 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:06:18,434 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:06:18,504 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:09:18,460 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 08:09:18,511 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:09:18,570 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:12:18,439 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:12:18,502 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:15:18,441 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:15:18,501 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:18:18,444 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:18:18,529 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:21:18,438 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:21:18,498 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:22:48,225 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:22:48,229 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:22:48,295 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 08:22:48,361 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 08:24:18,439 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:24:18,500 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:27:18,448 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:27:18,529 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:30:18,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:30:19,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:34:07,982 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:34:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:37:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:37:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:40:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:40:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:43:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:43:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:46:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:46:08,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:49:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:49:08,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:52:08,001 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:52:08,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:55:07,991 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:55:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 08:58:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 08:58:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:01:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:01:08,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:04:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:04:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:07:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:07:08,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:10:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:10:08,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:13:07,996 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:13:08,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:16:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:16:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:19:07,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:19:08,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:22:07,962 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:22:08,025 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:25:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:25:08,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:28:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:28:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:31:07,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:31:08,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:34:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:34:08,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:37:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:37:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:40:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:40:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:43:07,964 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:43:08,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:46:07,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:46:08,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:49:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:49:08,018 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:52:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:52:08,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:55:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:55:08,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 09:58:07,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 09:58:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:01:07,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:01:08,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:04:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:04:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:07:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:07:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:10:07,992 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 10:10:08,036 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:10:08,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:13:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:13:08,027 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:16:07,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:16:08,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:19:07,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:19:08,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:21:47,623 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 10:21:47,623 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 10:21:47,697 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:47,725 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:47,726 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:47,782 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 10:21:47,841 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 10:21:47,887 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:21:52,643 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data/ HTTP/1.1" 200 0
2024-07-05 10:21:52,704 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:53,201 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data/ HTTP/1.1" 200 49071
2024-07-05 10:21:54,345 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,413 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-05 10:21:54,474 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,474 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,575 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,576 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,576 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,576 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,626 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,627 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,627 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 10:21:54,675 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,727 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,728 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,729 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,814 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:21:54,817 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:21:54,857 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 10:21:54,922 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 10:21:55,031 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 10:21:55,079 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 10:21:55,115 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 10:21:55,135 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 10:22:00,612 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:22:00,686 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-05 10:22:00,750 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-05 10:22:00,806 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:22:00,938 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:22:09,864 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 0
2024-07-05 10:22:09,918 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:22:11,780 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:24:18,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:24:19,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:25:29,023 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:25:29,226 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:25:35,006 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:25:36,448 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:26:14,031 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:14,171 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:26:15,029 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:16,424 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:26:25,817 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:25,881 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:26:25,961 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,022 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 10:26:26,092 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,094 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 10:26:26,094 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-05 10:26:26,094 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-05 10:26:26,136 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,154 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,155 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,178 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-05 10:26:26,235 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:26,241 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 10:26:26,450 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:26:26,576 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 10:26:26,687 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 10:26:33,700 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:35,084 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:26:47,614 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:47,754 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:26:48,219 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:26:49,611 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:27:11,029 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:27:11,147 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:27:12,121 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:27:13,534 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:27:37,536 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:27:37,651 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:27:38,099 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:27:39,544 basehttp.log_message 161 INFO    => "POST /api/allowances/erp_hy_sdw503_main_sales_allowance_balance/ HTTP/1.1" 200 26541
2024-07-05 10:29:26,978 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:29:27,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:32:26,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:32:27,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:35:25,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:35:26,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:38:26,966 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:38:27,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:38:38,914 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:38:38,994 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-05 10:38:39,056 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:38:39,171 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:38:40,871 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-07-05 10:38:40,928 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:05,426 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:05,510 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 10:39:05,563 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:05,587 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-05 10:39:05,589 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-05 10:39:05,661 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:05,689 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:05,748 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:39:05,806 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 10:39:05,820 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 10:39:06,662 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:06,730 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 10:39:06,784 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:06,785 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:06,785 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:06,903 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 10:39:06,909 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 10:39:07,016 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:39:08,231 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:08,318 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 10:39:08,378 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-05 10:39:08,437 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:08,596 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3171
2024-07-05 10:39:32,671 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 345437
2024-07-05 10:39:42,721 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:42,822 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-05 10:39:42,877 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:43,029 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:39:43,767 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:43,848 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 10:39:43,901 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-05 10:39:43,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:44,396 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 10:39:44,567 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:44,628 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-05 10:39:44,690 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:44,830 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:39:45,095 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:45,436 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 10:39:46,165 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:46,298 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:39:53,696 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:53,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 10:39:53,813 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:53,953 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:39:55,161 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-07-05 10:39:55,213 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:39:55,812 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 335899
2024-07-05 10:40:03,838 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:03,908 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-05 10:40:03,983 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,008 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,009 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,010 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,014 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,015 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,136 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 10:40:04,143 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:04,204 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 10:40:04,242 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:40:04,332 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 10:40:04,376 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 10:40:04,437 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 10:40:04,486 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 10:40:05,608 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-05 10:40:05,661 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:40:05,905 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-05 10:41:25,981 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:41:26,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:42:06,764 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:06,883 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:07,744 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:08,022 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 10:42:46,190 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:46,321 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:47,970 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:48,116 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:49,432 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:49,580 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:50,754 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:50,890 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:52,294 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:52,442 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:53,934 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:54,061 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:55,757 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:55,871 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:42:57,742 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:42:57,873 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 10:43:12,508 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:43:12,511 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:43:12,512 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:43:12,627 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 10:43:12,635 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 10:43:12,740 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:43:14,128 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-05 10:43:14,199 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:43:23,640 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 235199
2024-07-05 10:44:25,984 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:44:26,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:47:25,969 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:47:26,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:50:25,960 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:50:26,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:53:02,509 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:02,538 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:02,538 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:02,540 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2024-07-05 10:53:02,540 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-05 10:53:02,639 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:02,640 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:02,673 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 10:53:02,712 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 10:53:02,772 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 10:53:02,814 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-05 10:53:02,931 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 10:53:25,971 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:26,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:53:31,285 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:39,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 232731
2024-07-05 10:53:51,234 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_letter/ HTTP/1.1" 200 0
2024-07-05 10:53:51,285 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:53:51,370 basehttp.log_message 161 INFO    => "POST /api/documents/delete_letter/ HTTP/1.1" 200 69
2024-07-05 10:53:52,436 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:54:00,205 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 235199
2024-07-05 10:56:25,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:56:26,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 10:56:31,510 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:56:31,573 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 10:59:25,957 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 10:59:26,019 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:02:21,146 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:02:21,297 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:02:21,895 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:02:22,214 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:02:25,955 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:02:26,022 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:05:25,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:05:26,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:08:25,959 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:08:26,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:11:25,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:11:26,028 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:13:01,848 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:13:01,907 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:13:04,085 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:13:04,207 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:14:17,306 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:17,421 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:14:25,544 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,604 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:14:25,684 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:14:25,816 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,892 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,894 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,894 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:25,945 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:14:25,997 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:26,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:14:26,330 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:14:26,422 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:14:26,472 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:14:27,342 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:14:27,457 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:15:14,327 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,409 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:15:14,479 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,554 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:15:14,624 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,624 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,627 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,628 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:14,772 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:15:14,810 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:15:14,868 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:15,069 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:15:15,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:15:15,165 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:15:16,252 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:16,389 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:15:37,074 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:37,145 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:15:37,205 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:37,355 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3171
2024-07-05 11:15:41,496 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-05 11:15:41,603 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:15:41,747 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:17:06,569 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:06,631 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:17:06,880 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:06,938 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:17:07,065 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:07,066 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:07,067 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:07,068 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:07,133 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:17:07,194 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:07,298 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:17:07,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3171
2024-07-05 11:17:07,467 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:17:07,607 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:17:26,488 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:26,548 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:17:26,646 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:26,703 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:17:28,042 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:17:28,181 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,263 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:17:28,336 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,337 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,360 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,363 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,444 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:17:28,469 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:17:28,535 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:28,710 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3171
2024-07-05 11:17:28,804 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:17:28,936 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:17:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:17:29,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:18:46,407 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,465 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:18:46,564 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,628 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:18:46,696 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,698 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,699 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,701 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,771 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:18:46,811 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 11:18:46,862 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,862 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:46,889 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:47,035 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:18:47,038 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55781)

2024-07-05 11:18:47,043 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:18:47,045 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55297)

2024-07-05 11:18:47,139 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:18:47,143 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55987)

2024-07-05 11:18:47,209 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:18:47,216 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55295)

2024-07-05 11:18:47,290 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:18:47,294 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55296)

2024-07-05 11:18:47,991 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:18:48,225 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,290 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:18:48,419 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,470 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,471 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,472 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,582 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:18:48,638 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:48,655 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:18:48,814 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:18:48,838 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3171
2024-07-05 11:18:49,043 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:18:50,161 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:18:50,299 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:21:00,214 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,274 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:21:00,362 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,423 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:21:00,494 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,497 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,498 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,499 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,559 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:21:00,613 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:00,632 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:21:00,874 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:21:01,003 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:21:01,036 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:21:01,585 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:01,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:21:02,202 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:02,336 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:21:44,206 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:44,271 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-05 11:21:44,320 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:44,473 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:21:44,795 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:44,860 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:21:44,937 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:45,232 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:21:46,263 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:21:46,392 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:22:01,921 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:01,995 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:22:02,073 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:02,074 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:02,074 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:02,155 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:22:02,256 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:22:02,314 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:22:02,384 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:02,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:22:03,588 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:03,712 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:22:29,481 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:29,553 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 11:22:29,605 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:29,606 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:29,607 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:29,719 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:22:29,723 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:22:29,820 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:22:32,185 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-05 11:22:32,236 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:33,967 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 11:22:36,179 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:36,210 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:36,211 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:36,212 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:22:36,312 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 11:22:36,350 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:22:36,351 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:22:36,456 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:23:30,369 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,433 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:23:30,521 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,610 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:23:30,685 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,685 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,686 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,687 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,748 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 11:23:30,830 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,876 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,878 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:30,922 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:23:31,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:23:31,061 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:23:31,118 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:23:31,161 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:23:31,270 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:23:32,603 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:34,087 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 11:23:35,503 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:35,534 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:35,537 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:35,538 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:23:35,640 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 11:23:35,671 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:23:35,673 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:23:35,774 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:24:53,417 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:24:54,897 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 11:24:58,132 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-05 11:24:58,181 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:24:58,254 log.log_response 230 WARNING => Not Found: /api/documents/delete_business_notification/
2024-07-05 11:24:58,254 basehttp.log_message 161 WARNING => "POST /api/documents/delete_business_notification/ HTTP/1.1" 404 111
2024-07-05 11:25:48,878 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:48,937 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:25:49,023 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,099 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:25:49,162 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,163 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,164 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,165 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,250 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:25:49,403 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 11:25:49,455 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,458 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,458 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:49,589 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:25:49,634 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:25:49,636 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:25:49,648 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:25:49,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:25:50,461 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:51,999 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 11:25:54,434 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:54,669 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-05 11:25:55,729 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:25:57,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 11:26:04,521 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:26:04,523 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:26:04,524 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:26:04,525 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:26:04,525 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-07-05 11:26:04,575 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:26:04,647 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:26:04,653 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:26:04,765 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:26:04,833 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 11:26:04,841 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-05 11:28:37,217 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:37,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:28:41,499 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:41,591 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-05 11:28:41,653 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:41,762 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:28:44,617 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:44,703 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:28:44,756 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:44,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:28:46,409 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:46,542 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:28:49,023 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:28:49,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:29:16,930 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:29:16,931 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:29:16,932 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:29:16,991 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:29:17,095 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:29:17,145 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:29:17,687 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:29:19,164 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 11:30:09,412 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:30:09,452 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:30:09,453 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:30:09,453 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:30:09,539 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:30:09,547 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 11:30:09,629 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:30:09,683 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:30:56,242 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:30:56,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:31:01,903 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:31:02,030 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:31:49,048 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:31:49,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:32:10,520 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:32:10,588 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 11:32:10,646 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:32:10,957 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:32:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:32:12,088 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:32:21,386 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:32:21,502 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:34:49,039 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:34:49,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:37:49,047 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:37:49,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:39:04,405 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:39:04,724 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 11:39:07,181 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:39:07,295 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:40:49,048 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:40:49,108 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:42:14,291 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:42:14,414 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:43:49,049 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:43:49,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:46:49,037 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:46:49,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:48:13,265 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:48:13,385 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:49:49,049 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:49:49,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:51:19,877 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:20,031 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:51:21,991 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-05 11:51:22,158 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,234 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:51:22,359 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,387 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,388 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,389 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,446 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 11:51:22,507 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 11:51:22,509 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:22,753 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:51:22,757 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55999)

2024-07-05 11:51:22,830 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 11:51:22,837 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61006)

2024-07-05 11:51:22,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 11:51:22,952 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61007)

2024-07-05 11:51:41,734 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:51:41,852 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:52:49,029 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:52:49,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:53:05,159 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:53:05,332 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 11:55:49,051 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:55:49,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 11:57:30,922 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:31,044 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 11:57:33,526 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:33,744 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:57:36,324 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:36,456 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:57:39,773 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:39,827 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:39,828 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:39,828 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:39,894 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 11:57:39,995 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 11:57:40,051 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 11:57:40,058 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 11:57:40,234 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:57:40,398 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 11:58:03,513 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:58:03,652 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:58:17,432 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:58:17,575 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 11:58:49,042 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 11:58:49,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:01:32,235 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:32,301 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 12:01:33,471 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,529 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:01:33,626 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,686 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 12:01:33,751 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,751 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,754 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,755 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:33,827 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-05 12:01:33,829 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61948)

2024-07-05 12:01:33,963 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 12:01:33,965 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61005)

2024-07-05 12:01:34,153 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 12:01:34,160 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61947)

2024-07-05 12:01:34,367 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 12:01:34,373 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61946)

2024-07-05 12:01:34,745 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:34,746 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:34,747 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:34,869 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 12:01:34,871 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 12:01:34,977 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 12:01:37,201 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:38,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 12:01:41,430 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:41,489 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:41,508 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:41,509 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:41,601 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 12:01:41,606 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 12:01:41,615 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 12:01:41,728 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 12:01:49,025 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:01:49,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:04:49,051 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:04:49,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:07:49,040 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:07:49,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:10:49,064 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 12:10:49,115 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:10:49,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:13:49,048 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:13:49,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:16:49,044 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:16:49,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:19:49,041 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:19:49,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:22:49,046 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:22:49,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:25:49,052 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:25:49,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:28:50,011 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:28:50,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:31:50,029 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:31:50,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:34:49,989 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:34:50,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:37:49,991 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:37:50,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:40:49,976 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:40:50,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:43:49,981 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:43:50,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:46:50,008 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:46:50,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:49:49,989 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:49:50,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:52:50,003 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:52:50,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:55:50,441 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:55:50,532 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 12:58:49,988 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 12:58:50,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:01:50,155 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:01:50,265 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:02:53,696 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 13:02:53,696 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 13:02:53,778 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:02:53,782 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:02:53,850 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:02:53,908 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:04:49,046 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:04:49,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:06:50,413 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,483 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:06:50,619 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,680 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:06:50,748 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,749 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 13:06:50,749 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-05 13:06:50,775 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-05 13:06:50,873 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,878 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,878 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:50,888 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:06:50,947 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-05 13:06:50,948 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-05 13:06:50,948 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 13:06:50,998 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:51,050 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:51,051 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:06:51,094 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:06:51,154 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:06:51,160 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:06:51,228 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:06:51,265 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:06:51,359 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:08:29,211 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,270 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:08:29,367 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,427 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:08:29,487 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,488 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,489 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,528 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,564 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-05 13:08:29,622 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-05 13:08:29,679 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:29,758 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:08:29,850 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 13:08:29,926 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:08:30,057 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:08:31,756 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:33,248 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:08:37,517 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 0
2024-07-05 13:08:37,558 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:37,559 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:37,560 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:37,566 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:08:37,685 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:08:37,693 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:08:37,773 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:08:37,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:09:51,088 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:09:51,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:09:51,201 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:09:51,255 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:09:51,374 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:09:51,432 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:09:51,434 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55173)

2024-07-05 13:12:36,114 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:12:36,114 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:12:36,115 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:12:36,142 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:12:36,264 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:12:36,317 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:12:36,336 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:12:36,421 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:12:50,618 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:12:50,707 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:15:50,590 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:50,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:15:52,811 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:52,893 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:15:53,026 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,122 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:15:53,200 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,201 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,202 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,203 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,264 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:15:53,332 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,384 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,385 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:53,432 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:15:53,527 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:15:53,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:15:53,641 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:15:53,648 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:15:53,776 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:15:55,046 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:56,533 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:15:58,646 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:58,696 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:58,699 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:58,700 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:15:58,777 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:15:58,809 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:15:58,813 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:15:58,924 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:16:38,442 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,513 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:16:38,686 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,745 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:16:38,819 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,820 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,821 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,823 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,885 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:16:38,951 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,952 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,953 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:38,958 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:16:39,157 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:16:39,215 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:16:39,228 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:16:39,369 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:16:39,385 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:16:41,732 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:43,224 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:16:46,475 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:46,526 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:46,527 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:46,527 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:16:46,617 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:16:46,633 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:16:46,641 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:16:46,744 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:18:48,158 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,223 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:18:48,340 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:18:48,468 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,469 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,470 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,472 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:48,593 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:18:48,595 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 56084)

2024-07-05 13:18:48,636 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-05 13:18:48,639 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 56715)

2024-07-05 13:18:48,864 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:18:48,870 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55167)

2024-07-05 13:18:49,042 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:18:49,047 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55172)

2024-07-05 13:18:55,238 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,296 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:18:55,364 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,428 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:18:55,511 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,512 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,563 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,564 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,617 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:18:55,671 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,724 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,725 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:55,736 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:18:55,884 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:18:55,944 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:18:55,948 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:18:56,049 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:18:56,114 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:18:58,049 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:18:59,517 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:19:00,966 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-07-05 13:19:00,967 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-07-05 13:19:01,019 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:01,021 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:01,087 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-05 13:19:01,136 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-05 13:19:03,354 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:03,398 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:03,399 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:03,400 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:03,508 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:19:03,548 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:19:03,557 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:19:03,655 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:19:38,182 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:38,259 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:19:38,395 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:19:38,476 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:20:24,583 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:24,640 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:20:24,834 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:24,898 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:20:24,972 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:24,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:24,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:24,974 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:25,087 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:20:25,145 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:25,170 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:25,174 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:25,184 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:20:25,296 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:20:25,297 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:20:25,398 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:20:25,409 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:20:25,526 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:20:36,598 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:38,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:20:41,223 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:41,263 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:41,265 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:41,266 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:20:41,366 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:20:41,394 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:20:41,460 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:20:41,474 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:21:03,888 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:03,967 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:21:04,108 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,181 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:21:04,248 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,250 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,250 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,251 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,340 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:21:04,423 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:21:04,478 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,481 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,482 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:04,593 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:21:04,593 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:21:04,691 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:21:04,731 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:21:04,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:21:05,513 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:07,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:21:08,357 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:08,399 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:08,400 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:08,401 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:21:08,507 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:21:08,541 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:21:08,549 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:21:08,655 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:24:04,100 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:24:04,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:26:29,536 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:26:29,540 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:26:29,542 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:26:29,543 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:26:29,663 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:26:29,724 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:26:29,731 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:26:29,832 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:27:04,066 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:27:04,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:29:11,266 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:11,267 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:11,268 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:11,268 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:11,440 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:29:11,443 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:29:11,500 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:29:11,505 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:29:26,258 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:26,350 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:29:26,353 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 58290)

2024-07-05 13:29:28,394 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,462 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:29:28,622 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,691 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:29:28,769 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,769 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,770 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,796 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,856 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:29:28,922 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:29:28,925 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,928 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:28,955 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:29,077 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:29:29,193 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:29:29,258 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:29:29,305 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:29:29,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:29:30,091 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification/ HTTP/1.1" 200 0
2024-07-05 13:29:30,144 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:31,812 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:29:33,402 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:33,441 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:33,443 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:33,444 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:29:33,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:29:33,593 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:29:33,654 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:29:33,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:31:03,003 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:31:03,146 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,236 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:31:03,298 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,299 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,300 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,301 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,464 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:31:03,527 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,528 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,556 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:03,564 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:31:03,664 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:31:03,664 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:31:03,671 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:31:03,774 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:31:03,777 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:31:04,902 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:06,411 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:31:08,025 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:08,068 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:08,068 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:08,069 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:31:08,157 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:31:08,191 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:31:08,199 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:31:08,298 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:32:24,542 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:32:24,542 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:32:24,543 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:32:24,544 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:32:24,614 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:32:24,709 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:32:24,780 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:32:24,820 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:33:15,728 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:15,792 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:33:15,862 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:15,920 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:33:16,001 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,001 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,002 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,004 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,079 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:33:16,114 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:33:16,184 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,185 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,187 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:16,315 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:33:16,327 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:33:16,440 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:33:16,454 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:33:16,577 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:33:17,363 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:18,824 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:33:22,541 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:22,581 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:22,582 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:22,584 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:22,672 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:33:22,705 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:33:22,706 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:33:22,805 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:33:58,513 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:58,514 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:58,515 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:58,516 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:33:58,641 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:33:58,687 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:33:58,748 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:33:58,751 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:36:15,998 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:36:16,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:38:56,998 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:38:56,999 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:38:57,000 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:38:57,000 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:38:57,169 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:38:57,207 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:38:57,265 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:38:57,284 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:39:02,509 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:04,015 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:39:12,139 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:12,172 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:12,173 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:12,175 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:12,271 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:39:12,315 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:39:12,371 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:39:12,377 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:39:15,851 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:15,928 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:39:48,072 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:49,602 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:39:52,028 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/delete_business_notification/ HTTP/1.1" 200 0
2024-07-05 13:39:52,081 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:52,174 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:39:53,236 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:54,801 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 13:39:56,763 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:56,764 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:56,765 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:56,767 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:56,768 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/business_notification_serial/ HTTP/1.1" 200 0
2024-07-05 13:39:56,847 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:39:56,857 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:39:56,957 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:39:56,993 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:39:57,042 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:39:57,056 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-05 13:40:52,318 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,377 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:40:52,449 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,506 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:40:52,580 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,581 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,582 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,582 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,684 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:40:52,830 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:40:52,892 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,894 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:52,895 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:53,002 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:40:53,058 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:40:53,069 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:40:53,088 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:40:53,105 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:40:53,923 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:40:55,393 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:43:52,470 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:43:52,538 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:46:52,473 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:46:52,552 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:47:45,188 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:45,302 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:47:45,440 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:45,502 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:47:45,504 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 58784)

2024-07-05 13:47:48,635 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:48,699 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:47:48,890 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:48,947 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:47:49,044 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,095 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,096 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,098 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,153 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:47:49,218 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,270 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,271 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:49,278 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:47:49,428 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:47:49,440 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:47:49,533 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:47:49,627 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:47:49,711 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:47:50,851 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:52,351 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:47:56,725 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:56,802 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:47:57,869 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:47:59,353 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 13:48:01,301 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:48:01,303 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:48:01,304 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:48:01,304 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:48:01,305 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:48:01,431 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:48:01,469 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:48:01,478 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:48:01,531 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-05 13:48:01,625 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:50:48,844 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:50:48,942 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:51:50,005 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:51:50,006 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:51:50,007 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:51:50,007 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:51:50,084 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:51:50,189 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:51:50,266 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:51:50,321 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:52:18,991 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:18,992 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:18,993 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:18,994 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:19,108 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:52:19,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:52:19,217 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:52:19,273 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:52:19,351 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:19,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:52:45,171 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,244 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:52:45,392 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,473 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:52:45,548 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,550 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,551 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,552 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,644 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:52:45,786 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:52:45,860 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,861 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,862 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:52:45,927 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:52:45,975 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:52:46,042 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:52:46,101 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:52:46,102 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:55:45,368 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:55:45,427 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:56:21,176 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:21,261 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:56:21,384 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:21,443 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:56:21,445 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61771)

2024-07-05 13:56:23,386 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,468 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:56:23,619 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,695 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:56:23,775 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,775 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,778 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,778 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:23,852 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:56:23,941 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:56:24,010 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:24,011 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:24,037 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:24,147 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:56:24,197 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:56:24,237 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:56:24,251 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:56:24,371 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:56:25,406 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:26,890 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148410
2024-07-05 13:56:31,928 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:32,048 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:56:33,116 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:34,586 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 13:56:36,230 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:37,676 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 13:56:42,075 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:42,077 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:42,078 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:42,079 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:42,079 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:56:42,140 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:56:42,266 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:56:42,287 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:56:42,307 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-05 13:56:42,419 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:57:13,296 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:13,297 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:13,298 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:13,299 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:13,362 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:57:13,488 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:57:13,515 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:57:13,576 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:57:26,503 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_business_notification/ HTTP/1.1" 200 0
2024-07-05 13:57:26,551 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:26,641 basehttp.log_message 161 INFO    => "POST /api/documents/update_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:57:27,693 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:29,274 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148515
2024-07-05 13:57:32,095 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:32,139 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:32,140 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:32,140 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:57:32,241 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:57:32,264 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:57:32,359 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:57:32,422 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:58:45,162 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,233 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 13:58:45,325 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,395 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 13:58:45,475 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,475 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,476 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,477 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,555 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 13:58:45,604 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 13:58:45,625 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,626 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,627 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:45,766 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:58:45,853 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:58:45,901 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:58:45,931 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 13:58:46,046 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 13:58:47,359 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:48,828 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148515
2024-07-05 13:58:50,768 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:50,836 basehttp.log_message 161 INFO    => "POST /api/documents/delete_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:58:51,901 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:53,342 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 149328
2024-07-05 13:58:53,925 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:53,926 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:53,927 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:53,928 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:53,929 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:58:54,031 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:58:54,039 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:58:54,151 basehttp.log_message 161 INFO    => "POST /api/documents/business_notification_serial/ HTTP/1.1" 200 78
2024-07-05 13:58:54,160 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:58:54,245 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:59:15,023 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:15,111 basehttp.log_message 161 INFO    => "POST /api/documents/update_business_notification/ HTTP/1.1" 200 69
2024-07-05 13:59:16,173 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:17,632 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148515
2024-07-05 13:59:19,256 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:19,298 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:19,298 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:19,300 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:19,365 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:59:19,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:59:19,468 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:59:19,522 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 13:59:51,428 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:51,429 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:51,430 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:51,430 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 13:59:51,555 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 13:59:51,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 13:59:51,660 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 13:59:51,723 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:00:11,539 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:00:11,635 basehttp.log_message 161 INFO    => "POST /api/documents/update_business_notification/ HTTP/1.1" 200 69
2024-07-05 14:00:12,694 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:00:14,214 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148533
2024-07-05 14:01:45,334 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:01:45,408 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:04:13,135 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,218 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:04:13,326 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,392 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:04:13,467 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,468 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,469 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,470 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:13,618 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:04:13,665 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-05 14:04:13,849 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:04:13,855 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 63129)

2024-07-05 14:04:14,130 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:04:14,134 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61766)

2024-07-05 14:04:14,814 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:14,945 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:14,947 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:14,949 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:15,039 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:04:15,075 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:04:15,143 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:04:15,152 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:04:24,225 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:24,226 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:24,227 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:24,228 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:24,361 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:04:24,401 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:04:24,446 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:04:24,518 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:04:43,400 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:43,403 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:43,404 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:43,405 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:43,533 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:04:43,569 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:04:43,622 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:04:43,624 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:04:45,318 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:04:45,396 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:05:41,342 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:05:41,413 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:05:41,416 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 50119)

2024-07-05 14:07:45,333 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:07:45,415 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:10:45,337 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:10:45,415 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:13:45,345 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 14:13:45,397 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:13:45,456 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:15:32,683 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:32,753 basehttp.log_message 161 INFO    => "POST /api/documents/update_business_notification/ HTTP/1.1" 200 69
2024-07-05 14:15:33,813 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:35,287 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148553
2024-07-05 14:15:37,047 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:37,157 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:37,158 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:37,159 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:37,218 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:15:37,257 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:15:37,325 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:15:37,375 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:15:45,874 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:45,967 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:15:46,047 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,121 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:15:46,195 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,196 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,197 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,198 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,281 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:15:46,366 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:15:46,449 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,450 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,452 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:46,508 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:15:46,614 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:15:46,620 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:15:46,669 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:15:46,767 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:15:48,284 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:15:49,842 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148553
2024-07-05 14:16:12,228 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:12,282 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:12,283 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:12,284 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:12,370 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:16:12,420 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:16:12,437 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:16:12,535 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:16:13,872 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:13,907 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:13,909 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:13,910 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:16:14,016 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:16:14,044 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:16:14,048 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:16:14,151 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:18:46,069 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:18:46,131 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:21:46,042 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:21:46,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:23:27,885 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:23:27,965 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:23:28,048 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:23:28,109 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:23:28,175 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:23:28,321 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:23:28,323 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 51896)

2024-07-05 14:24:06,456 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,533 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:24:06,715 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,791 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:24:06,860 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,861 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,885 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,888 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:06,963 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:24:07,006 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:24:07,074 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:07,075 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:07,102 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:07,191 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:24:07,301 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:24:07,353 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:24:07,368 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:24:07,556 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:24:09,237 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:09,298 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 14:24:09,368 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:09,368 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:09,369 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:09,480 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:24:09,481 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:24:09,583 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:24:10,513 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter/ HTTP/1.1" 200 0
2024-07-05 14:24:10,562 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:19,084 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 234078
2024-07-05 14:24:29,685 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:29,687 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/letter_serial/ HTTP/1.1" 200 0
2024-07-05 14:24:29,688 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:29,689 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:29,689 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:29,738 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:24:29,798 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:24:29,801 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:24:29,917 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:24:29,950 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:24:29,989 basehttp.log_message 161 INFO    => "POST /api/documents/letter_serial/ HTTP/1.1" 200 78
2024-07-05 14:25:59,231 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/update_letter/ HTTP/1.1" 200 0
2024-07-05 14:25:59,284 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:25:59,357 basehttp.log_message 161 INFO    => "POST /api/documents/update_letter/ HTTP/1.1" 200 69
2024-07-05 14:26:00,416 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:26:09,261 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter/ HTTP/1.1" 200 230829
2024-07-05 14:26:15,403 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:26:15,482 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:26:15,483 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:26:15,484 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:26:15,559 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:26:15,595 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:26:15,609 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:26:15,727 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:27:06,705 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:06,765 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:27:48,700 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:48,702 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:48,703 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:48,764 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:27:48,861 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:27:48,918 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:27:50,071 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:50,166 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 363
2024-07-05 14:27:50,236 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-05 14:27:50,290 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:50,443 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:27:52,227 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 0
2024-07-05 14:27:52,286 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:27:52,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 14:30:06,702 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:30:06,779 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:33:06,675 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:33:06,758 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:36:06,708 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:06,829 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:36:19,467 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:19,524 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:36:52,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:52,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:52,968 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:53,033 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:36:53,145 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:36:53,200 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:36:54,255 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:55,885 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148553
2024-07-05 14:36:57,432 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:57,482 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:57,483 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:57,511 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:36:57,564 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:36:57,595 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_recipient_code_name/ HTTP/1.1" 200 41754
2024-07-05 14:36:57,670 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:36:57,734 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:38:23,927 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:38:24,011 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:38:24,014 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55138)

2024-07-05 14:39:06,677 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:39:06,743 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:40:41,244 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:40:41,312 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:41:01,940 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:02,128 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:41:03,899 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:04,037 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 14:41:35,110 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:35,282 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:41:42,605 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:42,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-05 14:41:42,732 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:42,844 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:41:43,271 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:43,358 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2024-07-05 14:41:43,438 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_dealer_group/ HTTP/1.1" 200 0
2024-07-05 14:41:43,498 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:43,833 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 14:41:45,326 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:41:45,485 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:42:06,688 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:42:06,754 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:42:33,685 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:42:33,758 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-05 14:42:33,826 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:42:33,938 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:45:07,009 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:07,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:45:17,591 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:17,754 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:45:18,687 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:18,859 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:45:20,929 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:21,050 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_dept_dealer_code_name/ HTTP/1.1" 200 30025
2024-07-05 14:45:31,701 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:31,857 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:45:36,980 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:45:37,132 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:46:21,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,037 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-05 14:46:22,102 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,104 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-05 14:46:22,228 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,229 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,230 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,230 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,231 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,327 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:46:22,331 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:22,390 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 14:46:22,439 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 14:46:22,495 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 14:46:22,547 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 14:46:22,597 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 14:46:22,720 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 14:46:23,786 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-05 14:46:23,841 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:24,066 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-05 14:46:37,456 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:37,594 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:38,542 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,545 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,546 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,546 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,547 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,549 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,670 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 14:46:38,675 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:38,712 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:46:38,762 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 14:46:38,814 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 14:46:38,862 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 14:46:38,913 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 14:46:38,955 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 14:46:40,165 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:40,242 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-07-05 14:46:40,808 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:40,958 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:43,680 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:43,758 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:46:43,824 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:44,014 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:45,654 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:45,724 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-05 14:46:45,792 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:45,923 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:46,905 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:46,972 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-07-05 14:46:47,039 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:47,152 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:47,965 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:48,025 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-07-05 14:46:48,083 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:48,203 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:48,518 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:48,521 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:48,522 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:48,634 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:46:48,637 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:46:48,736 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:46:49,064 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:49,065 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:49,066 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:49,178 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:46:49,178 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:46:49,285 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:46:49,660 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:49,841 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:46:58,926 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:59,051 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:46:59,515 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:46:59,643 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:00,347 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:00,469 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:03,147 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:47:03,314 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,375 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:47:03,450 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,450 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,451 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,452 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,577 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:47:03,621 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-05 14:47:03,702 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,704 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,705 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,706 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,843 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 14:47:03,886 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:47:03,890 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 14:47:03,895 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,898 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,946 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:03,962 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 14:47:03,969 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:47:04,006 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:47:04,057 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 14:47:04,107 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 14:47:04,155 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 14:47:05,905 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:05,980 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:47:06,105 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,181 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:47:06,258 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,260 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,261 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,262 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,387 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:47:06,429 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-05 14:47:06,494 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:06,644 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:06,707 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:47:06,758 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:47:17,805 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:17,940 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:20,993 basehttp.log_message 161 INFO    => "OPTIONS /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 0
2024-07-05 14:47:21,053 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:21,458 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 258628
2024-07-05 14:47:33,229 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:33,365 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:42,882 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:43,018 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:47:44,896 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:44,960 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-07-05 14:47:45,044 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 0
2024-07-05 14:47:45,096 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:45,171 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 14:47:48,841 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_permissions/ HTTP/1.1" 200 0
2024-07-05 14:47:49,058 basehttp.log_message 161 INFO    => "POST /api/users/select_user_permissions/ HTTP/1.1" 200 10735
2024-07-05 14:47:54,936 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:55,019 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 511
2024-07-05 14:47:55,085 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_dept/ HTTP/1.1" 200 0
2024-07-05 14:47:55,138 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:55,269 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-05 14:47:57,877 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:57,948 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-07-05 14:47:58,010 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-07-05 14:47:58,035 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-07-05 14:47:58,081 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:58,081 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:47:58,153 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-07-05 14:47:58,246 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 365
2024-07-05 14:48:03,932 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:04,008 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_user_code_name/ HTTP/1.1" 200 29694
2024-07-05 14:48:06,467 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:06,584 basehttp.log_message 161 INFO    => "POST /api/users/select_user_dept/ HTTP/1.1" 200 17568
2024-07-05 14:48:06,830 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:06,889 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:48:41,443 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:41,566 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:48:44,119 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:44,432 basehttp.log_message 161 INFO    => "POST /api/dealers/select_dealer_group/ HTTP/1.1" 200 3042
2024-07-05 14:48:48,997 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:49,100 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 659
2024-07-05 14:48:49,164 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:49,282 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:48:50,556 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:48:50,732 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:49:11,973 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:12,093 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:13,584 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:13,699 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:15,954 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:16,069 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:16,744 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-07-05 14:49:16,796 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:16,984 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 130687
2024-07-05 14:49:23,446 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:23,575 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:24,424 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 0
2024-07-05 14:49:24,483 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:24,754 basehttp.log_message 161 INFO    => "POST /api/orders/hy1_asfa_main_fushou_detail/ HTTP/1.1" 200 324444
2024-07-05 14:49:28,642 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:28,773 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:30,067 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:30,181 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:32,077 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:32,191 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:33,018 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:33,129 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:34,763 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:34,889 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:49:36,346 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:36,346 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:36,347 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:36,452 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:49:36,486 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:49:36,570 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:49:37,912 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:37,913 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:37,914 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:38,030 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:49:38,091 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:49:38,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:49:46,578 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:49:48,071 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification/ HTTP/1.1" 200 148553
2024-07-05 14:50:15,041 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:15,196 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:50:16,290 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:16,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:50:21,218 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:21,334 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:50:22,681 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 0
2024-07-05 14:50:22,733 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:22,906 basehttp.log_message 161 INFO    => "POST /api/dealers/erp_hy_ocv025_main_dealer_info/ HTTP/1.1" 200 72890
2024-07-05 14:50:27,890 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:28,015 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:50:28,859 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/erp_hy_ocv029_main_delivery_location/ HTTP/1.1" 200 0
2024-07-05 14:50:28,912 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:29,062 basehttp.log_message 161 INFO    => "POST /api/dealers/erp_hy_ocv029_main_delivery_location/ HTTP/1.1" 200 5783
2024-07-05 14:50:30,092 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:30,207 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-05 14:50:30,776 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path2/ HTTP/1.1" 200 0
2024-07-05 14:50:30,831 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:31,167 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path2/ HTTP/1.1" 200 12020
2024-07-05 14:50:39,948 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:50:40,110 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:51:06,687 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:06,753 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:51:10,523 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:10,524 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:10,525 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:10,585 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:51:10,686 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:51:10,733 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:51:12,708 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:12,709 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:12,818 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:51:12,820 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:51:14,041 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:14,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 432
2024-07-05 14:51:17,798 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:17,799 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:17,862 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2024-07-05 14:51:17,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2024-07-05 14:51:22,788 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2024-07-05 14:51:22,842 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:51:22,904 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300582\MOMO-113年7-8月促銷通報240619h16692B.docx
2024-07-05 14:51:22,905 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-05 14:51:22,905 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-05 14:52:13,776 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:52:13,777 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:52:13,841 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2024-07-05 14:52:13,903 _DownloadBusinessNotificationInfo.select_business_notification_download 246 ERROR   => File not found: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11300582\MOMO-113年7-8月促銷通報240619h16692B.docx
2024-07-05 14:52:13,904 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2024-07-05 14:52:13,904 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2024-07-05 14:52:13,967 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:52:14,349 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-05 14:54:06,691 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:54:06,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:55:02,040 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:02,041 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:02,196 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:55:02,198 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 56613)

2024-07-05 14:55:02,205 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:55:02,207 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 55112)

2024-07-05 14:55:03,472 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,529 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:55:03,610 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:55:03,787 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,840 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,841 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,842 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:03,891 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:55:03,955 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:04,007 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:04,008 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:04,009 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:55:04,164 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:55:04,217 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:55:04,228 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:55:04,323 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-05 14:55:04,405 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:55:48,521 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:48,596 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-05 14:55:48,789 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:48,848 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:55:48,924 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:48,926 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:48,927 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:48,927 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:49,036 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-05 14:55:49,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:55:49,094 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:49,095 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:49,096 token_utils.verify_access_token  30 INFO    => refresh_token: 3b230512-280f-4c23-b771-f69d40559adf
2024-07-05 14:55:49,239 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-05 14:55:49,239 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9059
2024-07-05 14:55:49,348 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 14:55:49,364 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-05 14:55:49,481 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-05 14:55:50,790 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-05 14:55:50,963 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-05 14:55:51,175 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-05 14:55:57,191 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-05 14:55:57,321 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-05 14:55:57,399 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:55:57,455 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:55:57,525 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:55:57,526 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:55:57,527 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:55:57,527 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:55:57,599 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:55:57,797 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:55:58,154 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41754
2024-07-05 14:55:58,508 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 50586
2024-07-05 14:56:45,727 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:56:45,729 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:56:45,731 token_utils.verify_access_token  30 INFO    => refresh_token: 0f1e47f4-c636-4aa7-9755-10eb9f678868
2024-07-05 14:56:45,804 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-05 14:56:46,387 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 41754
2024-07-05 14:56:46,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 50586
2024-07-05 14:59:23,169 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-05 14:59:23,317 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-05 14:59:39,589 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 126
2024-07-05 14:59:39,655 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 14:59:39,727 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 14:59:39,804 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 14:59:39,805 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 14:59:39,806 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 14:59:39,807 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 14:59:39,993 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 14:59:40,130 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 14:59:40,262 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 14:59:40,313 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:00:39,561 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:39,634 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:00:39,698 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:39,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:00:39,886 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:39,887 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:39,889 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:39,889 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:00:40,013 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 15:00:40,152 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 15:00:40,176 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:00:40,323 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:02:19,367 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:02:19,636 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:02:59,629 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:02:59,979 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:03:25,818 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:26,094 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:03:32,218 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:32,482 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:03:39,709 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:39,766 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:03:41,613 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:41,880 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:03:47,213 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:47,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:03:54,797 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:03:55,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:04:13,537 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:13,840 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:04:23,716 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:24,031 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:04:28,510 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:28,807 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:04:48,035 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:48,319 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:04:50,834 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:51,123 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:04:54,190 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:04:54,544 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:05:08,339 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:05:08,615 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:05:44,000 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:05:44,299 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:05:47,757 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:05:48,068 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:05:56,486 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:05:56,756 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 10768
2024-07-05 15:06:39,709 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:06:39,772 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:09:16,327 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-05 15:09:16,372 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:09:16,665 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:09:39,720 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:09:39,778 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:11:12,469 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:11:12,779 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:12:39,709 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:12:39,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:13:10,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:13:11,281 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:15:39,719 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:15:39,777 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:17:10,134 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:10,442 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:17:14,585 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:14,641 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:17:14,722 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 15:17:14,773 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:14,834 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:17:14,919 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:14,920 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-05 15:17:14,972 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:14,972 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-05 15:17:15,070 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:15,071 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:15,143 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 15:17:15,172 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 15:17:15,334 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:17:15,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 15:17:43,172 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,242 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:17:43,323 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,431 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:17:43,516 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,516 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,517 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,519 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:17:43,612 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 15:17:43,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 15:17:43,894 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:17:43,959 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 15:19:32,744 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:19:33,029 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:20:43,348 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:20:43,425 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:23:03,314 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:03,639 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:23:18,039 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:23:18,279 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,356 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:23:18,421 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,497 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,498 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,499 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:23:18,621 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 15:23:18,751 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 15:23:18,771 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 15:23:18,938 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:26:19,019 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:19,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:26:22,053 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:22,369 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:26:28,494 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:28,618 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:26:28,715 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:28,804 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:26:28,883 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:28,884 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:28,885 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:28,886 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:26:29,060 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3874
2024-07-05 15:26:29,095 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-07-05 15:26:29,260 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:26:29,361 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 15:27:24,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:27:24,334 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:27:51,970 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:27:52,285 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 15:29:29,020 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:29:29,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:32:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:32:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:35:28,721 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:35:28,814 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:38:28,732 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:38:28,813 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:41:28,721 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:41:28,787 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:44:28,756 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:44:28,829 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:47:28,721 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:47:28,834 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:50:28,727 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:50:28,810 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:53:28,990 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:53:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:56:29,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:56:29,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 15:57:08,857 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:57:08,931 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 15:59:28,730 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 15:59:28,794 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:02:28,734 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:02:28,800 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:05:29,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:05:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:08:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:08:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:11:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:11:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:14:29,007 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 16:14:29,066 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:14:29,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:17:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:17:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:20:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:20:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:23:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:23:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:26:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:26:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:29:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:29:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:32:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:32:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:35:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:35:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:38:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:38:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:41:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:41:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:44:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:44:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:47:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:47:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:50:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:50:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:53:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:53:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:56:29,063 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:56:29,147 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 16:59:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 16:59:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:02:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:02:29,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:05:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:05:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:08:26,723 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:08:26,807 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-05 17:08:28,716 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:08:28,778 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:10:29,446 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-05 17:10:29,492 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:10:29,796 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 17:11:28,730 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:11:28,816 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:12:25,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:12:26,265 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 17:13:19,293 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:13:19,573 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 17:13:29,312 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:13:29,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 17:13:53,286 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:13:53,577 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 11202
2024-07-05 17:13:59,360 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:13:59,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 10082
2024-07-05 17:14:28,721 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:14:28,783 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:15:45,390 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,455 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-07-05 17:15:45,657 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,659 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,659 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,660 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,660 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,660 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,704 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,704 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-05 17:15:45,843 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,844 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,844 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,845 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,846 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,926 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-07-05 17:15:45,930 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:45,953 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-05 17:15:46,014 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-05 17:15:46,064 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-05 17:15:46,131 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50891
2024-07-05 17:15:46,170 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-05 17:15:46,211 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-05 17:15:47,144 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-05 17:15:47,200 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:15:47,377 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44106
2024-07-05 17:17:28,723 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:17:28,803 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:20:28,973 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:20:29,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:23:29,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:23:29,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:26:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:26:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:29:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:29:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:32:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:32:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:35:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:35:29,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:38:28,958 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:38:29,021 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:41:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:41:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:44:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:44:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:47:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:47:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:50:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:50:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:53:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:53:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:56:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:56:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 17:59:28,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 17:59:29,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:02:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:02:29,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:05:29,011 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:05:29,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:08:28,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:08:29,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:11:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:11:29,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:14:29,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:14:29,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:17:29,038 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 18:17:29,090 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:17:29,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:20:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:20:29,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:23:29,012 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:23:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:26:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:26:29,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:29:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:29:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:32:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:32:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:35:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:35:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:38:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:38:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:41:29,019 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:41:29,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:44:29,030 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:44:29,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:47:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:47:29,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:50:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:50:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-05 18:53:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:53:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:56:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:56:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 18:59:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 18:59:29,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:02:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:02:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:05:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:05:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:08:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:08:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:11:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:11:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:14:29,003 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:14:29,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:17:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:17:29,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:20:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:20:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:23:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:23:29,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:26:29,010 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:26:29,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:29:28,980 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:29:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:32:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:32:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:35:29,007 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:35:29,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:38:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:38:29,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:41:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:41:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:44:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:44:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:47:28,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:47:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:50:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:50:29,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:53:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:53:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:56:28,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:56:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 19:59:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 19:59:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:02:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:02:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:05:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:05:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:08:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:08:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:11:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:11:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:14:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:14:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:17:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:17:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:20:29,038 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 20:20:29,093 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:20:29,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:23:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:23:29,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:26:28,993 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:26:29,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:29:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:29:29,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:32:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:32:29,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:35:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:35:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:38:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:38:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:41:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:41:29,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:44:29,010 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:44:29,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:47:28,996 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:47:29,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:50:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:50:29,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:53:29,025 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:53:29,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:56:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:56:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 20:59:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 20:59:29,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:02:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:02:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:05:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:05:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:08:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:08:29,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:11:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:11:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:14:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:14:29,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:17:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:17:29,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:20:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:20:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:23:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:23:29,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:26:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:26:29,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:29:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:29:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:32:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:32:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:35:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:35:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:38:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:38:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:41:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:41:29,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:44:28,977 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:44:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:47:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:47:29,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:50:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:50:29,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:53:28,981 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:53:29,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:56:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:56:29,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 21:59:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 21:59:29,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:02:28,985 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:02:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:05:28,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:05:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:08:28,995 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:08:29,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:11:28,984 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:11:29,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:14:29,016 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:14:29,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:17:29,006 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:17:29,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:20:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:20:29,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:23:29,001 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-05 22:23:29,051 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:23:29,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:26:28,992 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:26:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:29:29,009 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:29:29,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:32:29,008 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:32:29,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:35:28,978 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:35:29,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:38:29,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:38:29,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:41:28,994 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:41:29,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 69
2024-07-05 22:44:29,004 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:44:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:47:29,026 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:47:29,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:50:28,975 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:50:29,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:53:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:53:29,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:56:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:56:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 22:59:28,986 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 22:59:29,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:02:28,991 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:02:29,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:05:28,982 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:05:29,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:08:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:08:29,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:11:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:11:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:14:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:14:29,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:17:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:17:29,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:20:29,005 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:20:29,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:23:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:23:29,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:26:29,002 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:26:29,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:29:28,989 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:29:29,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:32:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:32:29,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:35:28,979 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:35:29,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:38:28,987 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:38:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:41:28,998 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:41:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:44:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:44:29,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:47:28,974 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:47:29,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:50:28,983 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:50:29,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:53:28,976 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:53:29,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:56:29,013 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:56:29,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
2024-07-05 23:59:28,988 token_utils.verify_access_token  30 INFO    => refresh_token: 28317b4b-5673-4ff2-a28f-db4b985b9e3a
2024-07-05 23:59:29,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 105
