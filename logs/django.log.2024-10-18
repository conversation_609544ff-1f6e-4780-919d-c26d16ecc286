2024-10-18 08:46:54,099 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 08:46:54,099 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 08:46:54,207 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-10-18 08:46:54,208 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-10-18 08:46:54,209 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-10-18 08:46:54,209 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-10-18 08:46:54,211 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-10-18 08:46:54,211 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-10-18 08:47:09,288 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-10-18 08:47:09,430 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2024-10-18 08:47:09,571 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-18 08:47:09,651 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-10-18 08:47:09,651 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 08:47:09,702 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-10-18 08:47:09,702 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-10-18 08:47:09,874 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-18 08:47:10,481 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6789
2024-10-18 08:47:10,519 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5146
2024-10-18 08:47:10,595 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-18 08:47:26,343 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-10-18 08:47:26,407 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-10-18 08:47:26,629 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19194
2024-10-18 08:47:30,604 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-18 08:47:30,670 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-10-18 08:47:30,671 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-18 08:47:30,833 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-18 08:47:30,838 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-18 08:47:31,773 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-10-18 08:47:31,934 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 11:03:37,493 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-10-18 11:03:37,655 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-10-18 11:03:37,774 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 11:03:37,900 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-18 11:03:38,228 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 11:03:38,306 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-10-18 11:03:38,307 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-10-18 11:03:38,309 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-10-18 11:03:38,462 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-10-18 11:03:38,689 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 6789
2024-10-18 11:03:38,840 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5611
2024-10-18 11:03:39,021 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 9030
2024-10-18 11:17:22,472 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-18 11:17:22,526 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-18 13:11:57,655 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 13:11:57,655 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-18 13:11:57,829 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-18 13:11:57,882 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-18 14:09:31,910 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-10-18 14:09:31,956 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-10-18 14:15:32,602 basehttp.log_message 161 INFO    => "OPTIONS /api/products/delete_prod_code_map/ HTTP/1.1" 200 0
2024-10-18 14:15:32,733 log.log_response 230 WARNING => Bad Request: /api/products/delete_prod_code_map/
2024-10-18 14:15:32,733 basehttp.log_message 161 WARNING => "POST /api/products/delete_prod_code_map/ HTTP/1.1" 400 86
2024-10-18 14:18:59,829 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2024-10-18 14:18:59,951 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 14:19:20,801 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-10-18 14:19:20,827 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-10-18 14:19:20,997 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-10-18 14:19:21,078 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-10-18 14:19:21,631 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 14:20:20,606 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 14:20:26,455 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 14:22:13,298 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
2024-10-18 14:22:15,591 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 53474
