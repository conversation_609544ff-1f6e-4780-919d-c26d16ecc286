2024-08-28 10:01:44,782 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-08-28 10:01:44,968 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-08-28 10:01:45,053 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 10:01:45,172 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:01:45,242 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 10:01:45,317 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-08-28 10:01:45,318 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-08-28 10:01:45,320 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-08-28 10:01:45,575 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:01:45,730 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7266
2024-08-28 10:01:45,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:01:46,132 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:01:49,119 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7266
2024-08-28 10:03:55,877 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7266
2024-08-28 10:03:57,213 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-08-28 10:03:57,280 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,281 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,281 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,305 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,334 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,335 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,378 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-08-28 10:03:57,526 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-08-28 10:03:57,543 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-08-28 10:03:57,608 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-08-28 10:03:57,723 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-08-28 10:03:57,778 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-08-28 10:03:57,796 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50743
2024-08-28 10:03:57,813 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-08-28 10:14:56,482 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-28 10:14:56,640 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:14:56,772 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:14:56,894 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-08-28 10:14:56,997 log.log_response 230 WARNING => Not Found: /api/blogs/select_blog/
2024-08-28 10:14:56,998 basehttp.log_message 161 WARNING => "POST /api/blogs/select_blog/ HTTP/1.1" 404 32924
2024-08-28 10:14:57,036 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:14:57,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:14:57,430 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:16:01,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:16:01,326 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:16:01,443 log.log_response 230 WARNING => Not Found: /api/blogs/select_blog/
2024-08-28 10:16:01,444 basehttp.log_message 161 WARNING => "POST /api/blogs/select_blog/ HTTP/1.1" 404 32924
2024-08-28 10:16:01,515 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:16:01,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:16:02,006 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:19:02,129 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:19:20,053 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:19:43,011 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:19:43,200 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:19:43,431 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:19:43,626 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 38, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 61, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\BlogInfo.py", line 79, in select_blog_method
    result = [transform_to_frontend_structure(row, idx + 1) for idx, row in enumerate(result)]
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\BlogInfo.py", line 79, in <listcomp>
    result = [transform_to_frontend_structure(row, idx + 1) for idx, row in enumerate(result)]
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\BlogInfo.py", line 31, in transform_to_frontend_structure
    "images": json.loads(data["IMAGES"]) if data["IMAGES"] else []
  File "C:\python3.6\lib\json\__init__.py", line 348, in loads
    'not {!r}'.format(s.__class__.__name__))
TypeError: the JSON object must be str, bytes or bytearray, not 'LOB'
2024-08-28 10:19:43,635 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 125993
2024-08-28 10:19:43,942 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:19:43,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:21:30,685 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:21:35,828 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:21:36,033 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:21:36,309 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 38, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 62, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 29, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:21:36,317 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139338
2024-08-28 10:21:36,379 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:21:36,674 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:21:36,858 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:24:21,248 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:24:28,785 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:24:28,954 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:24:29,186 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:24:29,270 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 38, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 62, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 29, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:24:29,312 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139338
2024-08-28 10:24:29,694 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:24:29,723 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:26:55,542 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:27:04,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:27:04,300 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:27:04,541 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:27:04,604 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 282
2024-08-28 10:27:05,034 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:27:05,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:29:08,590 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:29:08,815 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 282
2024-08-28 10:29:08,818 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:29:09,255 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:29:09,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:30:04,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:30:05,782 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:30:05,912 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 282
2024-08-28 10:30:06,035 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:30:06,326 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:30:06,517 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:30:11,196 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:30:11,362 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:30:11,403 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 282
2024-08-28 10:30:11,835 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:30:11,918 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:30:18,989 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:30:19,178 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:30:19,368 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 282
2024-08-28 10:30:19,459 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:30:19,750 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:30:19,820 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:33:20,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:33:35,790 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:33:43,664 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:33:43,875 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:33:44,111 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:33:44,215 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 38, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 62, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 29, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:33:44,221 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139338
2024-08-28 10:33:44,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:33:44,692 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:35:53,036 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:36:01,594 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:36:01,816 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:36:02,131 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:36:02,157 views.handle_response  28 INFO    => result: [{'id': 6, 'title': '測試000', 'content': <cx_Oracle.LOB object at 0x0000014AB62EC788>, 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': None, 'images': []}], http_status: 200
2024-08-28 10:36:02,210 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 41, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 65, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 32, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:36:02,217 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139372
2024-08-28 10:36:02,648 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:36:02,789 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:36:41,850 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:36:51,536 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:36:51,712 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:36:52,108 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 49, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 73, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 40, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:36:52,118 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139304
2024-08-28 10:36:52,135 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:36:52,579 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:36:52,637 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:37:39,043 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:37:45,251 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:37:53,007 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:37:53,243 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:37:53,562 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:37:53,607 BlogInfo.select_blog_method  57 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': <cx_Oracle.LOB object at 0x0000019EB2D80E68>, 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': None, 'images': []}]
2024-08-28 10:37:53,657 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 49, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 73, in _handle_action
    return handle_response(result, http_status)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 40, in handle_response
    json_dumps_params={'ensure_ascii': False}
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\http\response.py", line 605, in __init__
    data = json.dumps(data, cls=encoder, **json_dumps_params)
  File "C:\python3.6\lib\json\__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "C:\python3.6\lib\json\encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\python3.6\lib\json\encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\serializers\json.py", line 105, in default
    return super().default(o)
  File "C:\python3.6\lib\json\encoder.py", line 180, in default
    o.__class__.__name__)
TypeError: Object of type 'LOB' is not JSON serializable
2024-08-28 10:37:53,664 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 139304
2024-08-28 10:37:54,090 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:37:54,125 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:39:52,188 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:40:05,802 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:40:06,001 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:40:06,230 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:40:06,324 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:40:06,330 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:40:06,767 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:40:06,786 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:40:31,398 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:40:31,654 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:40:31,813 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:40:31,854 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:40:31,860 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:40:32,274 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:40:32,443 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:41:06,540 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:41:06,545 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:41:22,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:41:22,273 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:41:22,580 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:41:22,644 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:41:22,650 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:41:22,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:41:22,916 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:43:04,095 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:43:04,252 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:43:09,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:43:09,596 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:43:09,814 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:43:09,819 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:43:09,861 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:43:10,320 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:43:10,353 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:45:42,933 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:45:42,942 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:45:42,982 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:45:42,989 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:45:43,035 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:45:43,050 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:45:49,651 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:45:49,802 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:45:49,964 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 6, 'title': '測試000', 'content': '# React維修網站開發詳細教學\n\n## 1. 專案概述\n\n本專案是一個使用React Hook開發的公司維修網站。主要功能包括查詢維修中心、過濾維修中心狀態等。專案採用了現代前端開發技術和最佳實踐，旨在提供一個高效、可維護的web應用。\n\n## 2. 技術棧詳解\n\n### 2.1 React 和 React Hooks\n\nReact是一個用於構建用戶界面的JavaScript庫。在本專案中，我們主要使用了以下React Hooks：\n\n- useEffect: 用於處理副作用，如數據獲取、訂閱或手動更改DOM。\n- useMemo: 用於優化性能，通過記憶計算結果來避免不必要的重複計算。\n- useReducer: 用於管理複雜的組件狀態邏輯。\n\n### 2.2 axios\n\naxios是一個基於promise的HTTP客戶端，用於瀏覽器和node.js。在本專案中，我們使用axios發送HTTP請求到後端API。\n\n### 2.3 react-query\n\nreact-query是一個用於獲取、緩存和更新異步數據的庫。它簡化了在React應用中處理服務器狀態的過程。\n\n### 2.4 axios-mock-adapter\n\naxios-mock-adapter允許我們輕鬆模擬API響應，這在開發和測試階段非常有用。\n\n### 2.5 React-Table\n\nReact-Table是一個輕量級的、可擴展的數據表格庫。我們使用它來創建功能豐富的表格組件。\n\n## 3. 專案結構和文件詳解\n\n### 3.1 axiosInstance.jsx\n\n這個文件負責設置axios實例和模擬API響應。\n\n```jsx\nimport axios from \'axios\';\nimport MockAdapter from \'axios-mock-adapter\';\n\n// 創建axios實例\nconst axiosInstance = axios.create();\n// 創建mock適配器\nconst mock = new MockAdapter(axiosInstance);\n\n// 模擬維修中心查詢結果\nexport function mockRepairCenterQueryResults(endpoint, data) {\n    mock.onPost(endpoint).reply((config) => {\n        const { centerCode, status } = JSON.parse(config.data);\n        let filteredCenters = data;\n\n        // 根據centerCode和status過濾數據\n        if (centerCode) {\n            filteredCenters = filteredCenters.filter(c => c.id.startsWith(centerCode));\n        }\n        if (status && status.length > 0) {\n            filteredCenters = filteredCenters.filter(c => status.includes(c.status));\n        }\n\n        return [200, { data: filteredCenters }];\n    });\n}\n\n// 模擬維修中心下拉框數據\nexport function mockRepairCenterComboBox(endpoint, data) {\n    mock.onPost(endpoint).reply(() => {\n        return [200, { data }];\n    });\n}\n\n// 模擬數據\nconst mockCenters1 = [\n    { id: \'B001\', name: \'松深維修中心\', status: \'active\' },\n    // ... 其他模擬數據\n];\n\nconst mockCenters2 = [\n    { id: \'B001\', name: \'B001松深維修中心\' },\n    // ... 其他模擬數據\n];\n\n// 設置模擬endpoints\nmockRepairCenterQueryResults(\'/api/service-centers/query\', mockCenters1);\nmockRepairCenterComboBox(\'/api/other-service-centers/query\', mockCenters2);\n\nexport default axiosInstance;\n```\n\n這個文件的主要作用是：\n\n1. 創建一個axios實例\n2. 使用axios-mock-adapter創建一個模擬適配器\n3. 定義模擬API endpoints和響應邏輯\n4. 提供模擬數據\n\n### 3.2 useApiHook.jsx\n\n這個文件包含了自定義的React Hooks，用於處理API請求。\n\n```jsx\nimport { useMutation, useQuery } from "react-query";\nimport axiosInstance from "../../apis/axiosMockApi";\nimport { actionTypes } from "../../pages/BasicDataMaintenance/ServiceCenterQuery/ServiceCenterReducer";\n\n// 獲取用戶可用功能的Hook\nexport const useAPISelectUserFunction = (dispatch, type, isAuthenticated) => {\n    const selectUserFunction = async () => {\n        const response = await apiSelectUserMainMenuProgramAndPermission(API_BASE_URL, {});\n        if (response.status !== 200) {\n            throw new Error(`Request failed with status: ${response.status}`);\n        }\n        return response.data.message;\n    };\n\n    return useQuery(\'userFunction\', selectUserFunction, {\n        refetchOnWindowFocus: false,\n        enabled: isAuthenticated,\n        onSuccess: (data) => {\n            dispatch({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatch({ type: type, payload: null });\n            console.error("An error occurred:", error);\n        }\n    });\n}\n\n// 獲取其他維修中心數據的Hook\nexport const useRepairCenterComboBox = () => {\n    const fetchOtherCenters = async () => {\n        const response = await axiosInstance.post(\'/api/other-service-centers/query\');\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useQuery(\'otherCenters\', fetchOtherCenters, {\n        refetchOnWindowFocus: false,\n        cacheTime: 60 * 60 * 1000, // 緩存保持時長設置為60分鐘\n    });\n};\n\n// 查詢維修中心數據的Hook\nexport const useRepairCenterQueryResults = (dispatchJsonData, type) => {\n    const fetchCenters = async (data) => {\n        const response = await axiosInstance.post(\'/api/service-centers/query\', data);\n        if (response.status !== 200) {\n            throw new Error(`請求失敗，狀態碼: ${response.status}`);\n        }\n        return response.data.data;\n    };\n\n    return useMutation(fetchCenters, {\n        onSuccess: (data) => {\n            dispatchJsonData({ type: type, payload: data });\n        },\n        onError: (error) => {\n            dispatchJsonData({ type: actionTypes.SET_ERROR, payload: \'無法獲取維修中心數據\' });\n            console.error("查詢維修中心數據時發生錯誤:", error);\n        }\n    });\n};\n```\n\n這個文件定義了三個主要的自定義Hook：\n\n1. useAPISelectUserFunction: 用於獲取用戶可用功能\n2. useRepairCenterComboBox: 用於獲取其他維修中心數據（用於下拉選單）\n3. useRepairCenterQueryResults: 用於查詢維修中心數據\n\n這些Hook利用react-query的useQuery和useMutation來管理異步狀態和緩存。\n\n### 3.3 serviceCenterReducer.jsx\n\n這個文件定義了用於管理應用狀態的reducer。\n\n```jsx\nexport const initialState = {\n    queryResults: null,\n    errorMessage: null,\n    queryFilters: {\n        selectedServiceCenter: null,\n        statusFilters: { active: true, inactive: false }\n    },\n    serviceCenterList: {\n        availableCenters: [],\n    },\n    serviceCenterSelection: {\n        selectedCenter: { title: \'維修中心\', value: null },\n    }\n};\n\nexport const actionTypes = {\n    SET_LOADING: \'SET_LOADING\',\n    SET_QUERY_RESULTS: \'SET_QUERY_RESULTS\',\n    SET_AVAILABLE_CENTERS: \'SET_AVAILABLE_CENTERS\',\n    SET_ERROR: \'SET_ERROR\',\n    UPDATE_QUERY_FILTERS: \'UPDATE_QUERY_FILTERS\',\n    UPDATE_CENTER_SELECTION: \'UPDATE_CENTER_SELECTION\',\n    RESET_QUERY: \'RESET_QUERY\',\n};\n\nexport const serviceCenterReducer = (state, action) => {\n    switch (action.type) {\n        case actionTypes.SET_QUERY_RESULTS:\n            return { ...state, isLoading: false, queryResults: action.payload };\n        case actionTypes.SET_AVAILABLE_CENTERS:\n            return {\n                ...state,\n                serviceCenterList: {\n                    ...state.serviceCenterList,\n                    availableCenters: action.payload,\n                }\n            };\n        case actionTypes.SET_ERROR:\n            return { ...state, isLoading: false, errorMessage: action.payload };\n        case actionTypes.UPDATE_QUERY_FILTERS:\n            return { ...state, queryFilters: { ...state.queryFilters, ...action.payload } };\n        case actionTypes.UPDATE_CENTER_SELECTION:\n            return {\n                ...state,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    [action.field]: {\n                        ...state.serviceCenterSelection[action.field],\n                        value: action.value,\n                    }\n                }\n            };\n        case actionTypes.RESET_QUERY:\n            return {\n                ...state,\n                queryResults: null,\n                serviceCenterSelection: {\n                    ...state.serviceCenterSelection,\n                    selectedCenter: { title: \'維修中心\', value: null },\n                },\n                queryFilters: { selectedServiceCenter: null, statusFilters: { active: true, inactive: false } },\n            };\n        default:\n            throw new Error(\'未知的 action 類型\');\n    }\n};\n```\n\n這個reducer管理了應用的以下狀態：\n\n- 查詢結果\n- 錯誤訊息\n- 查詢過濾器\n- 可用的維修中心列表\n- 當前選擇的維修中心\n\n通過定義不同的action類型，reducer能夠根據不同的操作更新狀態。\n\n### 3.4 ServiceCenterQuerySystem.jsx\n\n這是應用的主要組件，整合了所有其他部分。\n\n```jsx\nimport React, { useEffect, useMemo, useReducer } from \'react\';\nimport { initialState, serviceCenterReducer, actionTypes } from \'./ServiceCenterReducer\';\nimport ServiceCenterTable, { createColumns } from \'./ServiceCenterTable\';\nimport \'./servicecenterquery.css\';\nimport Checkbox from "../../../components/Checkbox/Checkbox";\nimport ComboBox from "../../../components/ComboBox/ComboBox";\nimport Button from "../../../components/Button/Button";\nimport { useRepairCenterComboBox, useRepairCenterQueryResults } from "../../../utils/hook/useApiHook";\n\nconst ServiceCenterQuerySystem = () => {\n    const [state, dispatch] = useReducer(serviceCenterReducer, initialState);\n\n    const { data: otherCenters, isLoading: isLoadingOtherCenters } = useRepairCenterComboBox(dispatch, actionTypes.SET_AVAILABLE_CENTERS);\n\n    const useRepairCenterQueryResultsMutation = useRepairCenterQueryResults(dispatch, actionTypes.SET_QUERY_RESULTS);\n\n    const handleEditCenter = (centerId) => {\n        console.log(`編輯維修中心 ${centerId}`);\n        // 這裡可以實現編輯功能的邏輯\n    };\n\n    const columns = useMemo(() => createColumns(handleEditCenter), []);\n\n    useEffect(() => {\n        if (otherCenters) {\n            const formattedCenters = otherCenters.map(center => ({\n                id: center.id,\n                name: center.name\n            }));\n            dispatch({\n                type: actionTypes.SET_AVAILABLE_CENTERS,\n                payload: formattedCenters\n            });\n        }\n    }, [otherCenters]);\n\n    const handleCenterSelection = (selectedCenter) => {\n        dispatch({\n            type: actionTypes.UPDATE_CENTER_SELECTION,\n            field: \'selectedCenter\',\n            value: selectedCenter?.value\n        });\n    };\n\n    const handleStatusFilterChange = (status, isChecked) => {\n        dispatch({\n            type: actionTypes.UPDATE_QUERY_FILTERS,\n            payload: {\n                statusFilters: { ...state.queryFilters.statusFilters, [status]: isChecked === 1 }\n            }\n        });\n    };\n\n    const handleSearch = () => {\n        const selectedCenterCode = state?.serviceCenterSelection?.selectedCenter?.value?.substring(0, 4);\n        const statusFilter = Object.entries(state.queryFilters.statusFilters)\n            .filter(([_, isChecked]) => isChecked)\n            .map(([status, _]) => status);\n\n        const jsonData = {\n            centerCode: selectedCenterCode,\n            status: statusFilter\n        }\n\n        useRepairCenterQueryResultsMutation.mutate(jsonData);\n    };\n\n    const handleClear = () => {\n        dispatch({ type: actionTypes.RESET_QUERY });\n    };\n\n    if (isLoadingOtherCenters) {\n        return <p>載入中...</p>;\n    }\n\n    return (\n        <div className="p-4">\n            <div className="mb-4 flex items-center space-x-4">\n                <ComboBox\n                    className={"w-1/2"}\n                    isId={"centerSelect"}\n                    isJsonData={state.serviceCenterList.availableCenters}\n                    isComboBox={state.serviceCenterSelection.selectedCenter}\n                    isSetComboBox={handleCenterSelection}\n                />\n\n                <div className="flex items-center space-x-4">\n                    <Checkbox\n                        labelName="狀態"\n                        labelText="生效"\n                        checked={state.queryFilters.statusFilters.active ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'active\', isChecked)}\n                    />\n                    <Checkbox\n                        labelText="停用"\n                        checked={state.queryFilters.statusFilters.inactive ? 1 : 0}\n                        handleChange={(isChecked) => handleStatusFilterChange(\'inactive\', isChecked)}\n                    />\n                </div>\n            </div>\n\n            <ButtonGroup onSearch={handleSearch} onClear={handleClear} />\n\n            {\n                useRepairCenterQueryResultsMutation.isLoading ? (\n                    <div>正在查詢資料，請稍後...</div>\n                ) : state.queryResults && (\n                    <ServiceCenterTable columns={columns} data={state.queryResults} />\n                )\n            }\n        </div>\n    );\n};\n\nconst ButtonGroup = ({ onSearch, onClear }) => (\n    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-sm md:text-base lg:text-lg mt-6">\n        <Button onClick={onSearch} children={"查詢"} color={\'search\'} />\n        <Button onClick={onClear} children={"清除"} color={\'clear\'} />\n    </div>\n);\n\nexport default ServiceCenterQuerySystem;\n```\n\n這個組件是整個應用的核心，它整合了所有其他部分。以下是對其主要部分的詳細解釋：\n\n1. 狀態管理：\n    - 使用 `useReducer` 來管理複雜的狀態邏輯。\n    - 初始狀態和 reducer 函數從 `ServiceCenterReducer.jsx` 導入。\n\n2. 數據獲取：\n    - 使用 `useRepairCenterComboBox` 獲取可用的維修中心列表。\n    - 使用 `useRepairCenterQueryResults` 進行維修中心的查詢。\n\n3. 用戶交互處理：\n    - `handleCenterSelection`: 處理維修中心的選擇。\n    - `handleStatusFilterChange`: 處理狀態過濾器的變更。\n    - `handleSearch`: 執行搜索操作。\n    - `handleClear`: 重置查詢條件。\n\n4. 渲染邏輯：\n    - 條件渲染來處理加載狀態和顯示查詢結果。\n    - 使用 `useMemo` 來優化表格列的創建。\n\n5. 組件組合：\n    - 使用 `ComboBox`、`Checkbox` 和 `Button` 等可重用組件來構建UI。\n    - 將按鈕組抽離為單獨的 `ButtonGroup` 組件。\n\n## 4. 實施指南\n\n為了幫助更好地理解和實施這個專案，以下是一個逐步的實施指南：\n\n### 步驟 1: 設置專案環境\n\n1. 創建一個新的 React 專案：\n\n    ```\n    npx create-react-app repair-center-management\n    cd repair-center-management\n    ```\n\n2. 安裝所需的依賴：\n\n    ```\n    npm install axios react-query axios-mock-adapter react-table\n    ```\n\n### 步驟 2: 創建基礎文件結構\n\n在 `src` 目錄下創建以下文件和目錄：\n\n```\nsrc/\n  ├── apis/\n  │   └── axiosMockApi.js\n  ├── components/\n  │   ├── Checkbox/\n  │   ├── ComboBox/\n  │   └── Button/\n  ├── hooks/\n  │   └── useApiHook.js\n  ├── pages/\n  │   └── ServiceCenter/\n  │       ├── ServiceCenterReducer.js\n  │       ├── ServiceCenterTable.js\n  │       └── ServiceCenterQuerySystem.js\n  └── App.js\n```\n\n### 步驟 3: 實現 API 模擬 (axiosMockApi.js)\n\n按照之前提供的代碼實現 `axiosMockApi.js`。這將模擬後端 API，使前端開發可以獨立進行。\n\n### 步驟 4: 創建自定義 Hooks (useApiHook.js)\n\n實現 `useApiHook.js` 中的自定義 Hooks。這些 Hooks 將處理與 API 的交互。\n\n### 步驟 5: 實現狀態管理 (ServiceCenterReducer.js)\n\n按照之前提供的代碼實現 `ServiceCenterReducer.js`。這將定義應用的狀態結構和更新邏輯。\n\n### 步驟 6: 創建可重用組件\n\n在 `components` 目錄下創建 `Checkbox`、`ComboBox` 和 `Button` 組件。這些將在主要的 `ServiceCenterQuerySystem` 組件中使用。\n\n### 步驟 7: 實現 ServiceCenterTable 組件\n\n創建 `ServiceCenterTable.js`，使用 react-table 庫來實現可排序和可過濾的表格。\n\n### 步驟 8: 實現主要的 ServiceCenterQuerySystem 組件\n\n按照之前提供的代碼實現 `ServiceCenterQuerySystem.js`。這是將所有部分整合在一起的主要組件。\n\n### 步驟 9: 將 ServiceCenterQuerySystem 集成到 App.js\n\n在 `App.js` 中導入和渲染 `ServiceCenterQuerySystem` 組件。\n\n### 步驟 10: 樣式和 UI 優化\n\n使用 CSS 或 CSS-in-JS 或 Tailwind CSS 解決方案來美化 UI。\n\n## 5. 最佳實踐和注意事項\n\n1. 組件拆分：將大型組件拆分為更小、更可管理的部分。例如，可以將過濾器部分提取為單獨的組件。\n\n2. 性能優化：\n    - 使用 `useMemo` 和 `useCallback` 來避免不必要的重新渲染。\n    - 對於大型列表，考慮使用虛擬化技術（如 react-window）。\n\n3. 錯誤處理：實現全面的錯誤處理，包括網絡錯誤和數據驗證錯誤。\n\n4. 可訪問性：確保所有的 UI 元素都是可訪問的，包括正確的 ARIA 屬性和鍵盤導航。\n\n5. 測試：編寫單元測試和集成測試，確保應用的穩定性。\n\n6. 代碼質量：\n    - 使用 ESLint 和 Prettier 來維護代碼質量和一致性。\n    - 遵循 React 的最佳實踐，如使用函數組件和 Hooks。\n\n7. 國際化：如果需要，考慮使用 i18n 庫來支持多語言。\n\n8. 狀態管理擴展：對於更複雜的應用，考慮使用 Redux 或 MobX 等狀態管理庫。', 'author': '16613', 'created_at': '2024-08-23T17:09:29.159835', 'updated_at': '2024-08-23T17:09:29.159835', 'tags': '', 'images': []}]
2024-08-28 10:45:49,982 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 18235
2024-08-28 10:45:50,027 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:45:50,469 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:45:50,504 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:46:05,564 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/delete_blog/ HTTP/1.1" 200 0
2024-08-28 10:46:05,701 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 10:46:05,840 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 10:46:05,841 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 10:47:41,873 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/insert_blog/ HTTP/1.1" 200 0
2024-08-28 10:47:41,930 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:47:41,931 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:47:53,639 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:47:53,639 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:48:16,836 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:48:16,836 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:48:49,807 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:50:44,484 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 10:50:44,485 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 10:50:45,411 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:50:45,411 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:51:49,820 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:54:50,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:54:56,237 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:55:02,020 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:55:02,022 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:56:51,061 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 10:56:51,062 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 10:57:50,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:58:06,010 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:58:06,011 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:58:17,712 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:58:17,903 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:58:18,202 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:58:18,271 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 10:58:18,272 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 10:58:18,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 10:58:18,649 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:58:23,359 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 10:58:23,360 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 10:59:47,567 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 10:59:54,475 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 10:59:54,681 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 10:59:55,015 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 10:59:55,167 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/select_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 49, in select_blog
    return self._handle_action('blog', 'select')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
TypeError: select_blog_method() takes 1 positional argument but 4 were given
2024-08-28 10:59:55,174 basehttp.log_message 161 ERROR   => "POST /api/blogs/select_blog/ HTTP/1.1" 500 110632
2024-08-28 10:59:55,529 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 10:59:55,556 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:01:01,321 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 11:01:06,520 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:01:06,724 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:01:06,973 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:01:07,005 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:01:07,006 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:01:07,467 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:01:07,474 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:01:20,413 log.log_response 230 WARNING => Method Not Allowed: /api/blogs/insert_blog/
2024-08-28 11:01:20,414 basehttp.log_message 161 WARNING => "POST /api/blogs/insert_blog/ HTTP/1.1" 405 41
2024-08-28 11:02:36,144 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 11:02:42,472 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/insert_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 53, in insert_blog
    return self._handle_action('blog', 'create')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
KeyError: 'create'
2024-08-28 11:02:42,478 basehttp.log_message 161 ERROR   => "POST /api/blogs/insert_blog/ HTTP/1.1" 500 105324
2024-08-28 11:02:48,878 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/insert_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 53, in insert_blog
    return self._handle_action('blog', 'create')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
KeyError: 'create'
2024-08-28 11:02:48,881 basehttp.log_message 161 ERROR   => "POST /api/blogs/insert_blog/ HTTP/1.1" 500 105324
2024-08-28 11:03:08,252 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 11:03:11,861 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'0': '新', '1': '增', '2': '失', '3': '敗', '4': '!', 'CONTENT': '222222'}
2024-08-28 11:03:11,940 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/insert_blog/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 53, in insert_blog
    return self._handle_action('blog', 'insert')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 265, in wrapper
    return func(request, json_data, role, user_id)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\BlogInfo.py", line 121, in insert_blog_method
    'title': data['title'],
KeyError: 'title'
2024-08-28 11:03:11,971 basehttp.log_message 161 ERROR   => "POST /api/blogs/insert_blog/ HTTP/1.1" 500 116158
2024-08-28 11:03:56,107 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:03:56,107 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:00,069 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:00,070 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:04,057 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:04,058 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:06,044 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:06,045 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:07,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:04:24,070 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:24,071 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:29,060 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:29,061 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:36,035 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:36,036 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:41,218 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:41,219 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:04:53,055 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:04:53,055 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:05:02,307 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:05:02,308 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:05:06,729 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:05:06,888 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:05:07,108 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:05:07,152 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:05:07,153 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:05:07,615 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:05:07,666 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:05:15,384 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '0000', 'content': '111111', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:05:15,394 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:05:15,514 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': '111111', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:05:15.384503', 'tags': '', 'images': []}]
2024-08-28 11:05:15,514 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 238
2024-08-28 11:05:55,207 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/update_blog/ HTTP/1.1" 200 0
2024-08-28 11:05:55,366 basehttp.log_message 161 INFO    => "POST /api/blogs/update_blog/ HTTP/1.1" 200 57
2024-08-28 11:05:55,486 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': '111111\n2222', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:05:55.358744', 'tags': '', 'images': []}]
2024-08-28 11:05:55,486 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 244
2024-08-28 11:06:02,814 basehttp.log_message 161 INFO    => "POST /api/blogs/update_blog/ HTTP/1.1" 200 57
2024-08-28 11:06:02,972 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': '111111\n\n\n2222', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:06:02.811365', 'tags': '', 'images': []}]
2024-08-28 11:06:02,973 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 248
2024-08-28 11:06:09,496 basehttp.log_message 161 INFO    => "POST /api/blogs/update_blog/ HTTP/1.1" 200 57
2024-08-28 11:06:09,623 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': '111111\n2222', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:06:09.493655', 'tags': '', 'images': []}]
2024-08-28 11:06:09,623 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 244
2024-08-28 11:06:15,236 basehttp.log_message 161 INFO    => "POST /api/blogs/update_blog/ HTTP/1.1" 200 57
2024-08-28 11:06:15,358 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': '111111\n\n2222', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:06:15.231579', 'tags': '', 'images': []}]
2024-08-28 11:06:15,358 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 246
2024-08-28 11:06:41,707 basehttp.log_message 161 INFO    => "POST /api/blogs/update_blog/ HTTP/1.1" 200 57
2024-08-28 11:06:41,854 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 21, 'title': '0000', 'content': ' \n\nHeySong vending With AP Communication Protocol V1.0\n\n \n\n\u200b                               \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\nEdit:田紹明\n\nDate:2024-08-20\n\n \n\n \n\n \n\n \n\n1. Communication Format\n2. \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\nUART Command for AP Communication Format\n\nVMC TX Command Format (BaudRate:115200,n,8,1)\n\nVMC_TX Command List\n\n| Format TX Item | Command | 說明         | 指令總長度      | 備註                                                        |\n| -------------- | ------- | ------------ | --------------- | ----------------------------------------------------------- |\n| 01             | V       | 交易資料     | 63byte          | 固定63byte                                                  |\n| 02             | W       | 心跳信號     | 19byte          | 固定19byte,每5分鐘傳一筆,跟指令”V”互斥                      |\n| 03             | A       | 對帳交易資料 | 26byte          | 跟AP 確認傳送資料正確性                                     |\n| 04             | T       | 要求時間     | 機號(6)+指令(1) | AP回應R+14(Time)                                            |\n| 05             | FP      | 測試指令     | 依指令          | “F”測試指令開頭碼,P:針對修改機台號碼(P)做測試,由AP發出指令. |\n|                |         |              |                 |                                                             |\n\n \n\n//-------------------------------------------------------------------------------------\n\nVMC RX Command Format (BaudRate:1115200,n,8,1)\n\nAP回應指令List\n\n| Format RX Item | Command    | 說明         | 指令總長度     | 備註                                               |\n| -------------- | ---------- | ------------ | -------------- | -------------------------------------------------- |\n| 01             | 20x        | 傳送成功     | 3 byte         | X=數字  0~9                                        |\n| 02             | 40x        | 傳送失敗     | 3 byte         | X=數字  0~9                                        |\n| 03             | RA         | 回應交易資料 | 依指令         | 看範列, 完全正確回RA1111E                          |\n| RT             | 回應AP時間 | 16 byte      | RT(2)+時間(14) |                                                    |\n| 04             | P          | 修改機台號碼 | 7 byte         | VMC 收到後立即發出更新後ID心跳信號,Time-Out 5分鐘. |\n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n \n\n指令範列解說:\n\n//=01=VMC 送出信號解析========指令”V”格式(63碼ASCII)== 交易資料========\n\n傳給黑松AP的資料格式 (都是ASCII格式)\n\n| NO   | Type         | ASCII Bytes      | 說明   | 備註                                                         |\n| ---- | ------------ | ---------------- | ------ | ------------------------------------------------------------ |\n| S1   | Sent Command | V(0x56)          | 1位數  | 指令碼                                                       |\n| S2   | Sent Length  | 63               | 2位數  | 總傳送長度                                                   |\n| S3   | VMC NO       | 000002           | 6位數  | 機台號碼                                                     |\n| S4   | Time         | 20240711150100   | 14位數 | 時間                                                         |\n| S5   | Channel_NO   | 001              | 3位數  | 倉道號碼,有效範圍001~039                                     |\n| S6   | CoinALL      | 021              | 3位數  | 總投入金額                                                   |\n| S7   | CoinCHG      | 005              | 3位數  | 找零金額                                                     |\n| S8   | Barcode      | 0123456789123456 | 16位數 | 條碼資料,沒有為0….0                                          |\n| S9   | Trade_Type   | 0                | 1位數  | 0:現金,1:悠遊卡(票證),2:條碼(載具),3:信用卡                  |\n| S10  | Keep         | 00000            | 5位數  | 保留                                                         |\n| S11  | Index        | 0001             | 4位數  | 跟AP參考點,範圍0001~0450                                     |\n| S12  | CMD Chksum   | 0000 (MSB….LSB)  | 4位數  | 從S2(Sent Length )++…..+S11(Index)以Byte為單位相加,並將16進制轉為10進制並轉成ASCII,取最後共四位數,MSB  …LSB. |\n| S13  | SentEOT      | E(0x45)          | 1位數  | 結束碼                                                       |\n\n \n\n總長度共63個ASCII Bytes,\n\n資料格式說明如下:\n\nS1:Sent Head : Fix “V”.\n\nS2:Sent Length:從Sent Head到SentEOT 總bytes數,目前固定 63個bytes.\n\nS3: VMC NO:販賣機機台代號.\n\nS4: Time:此筆交易資料的時間\n\nS5: Channel_NO:掉落物的倉道號碼,超過40(含) 表示倉道為000.\n\nS6: CoinALL:總投入金額, 金額000,表示為測試用.\n\nS7: CoinCHG:找零金額.\n\nS8: Barcode:載具條碼.\n\nS9: Trade_Type:交易種類, 0:現金,1:悠遊卡(票證),2:條碼(載具),3:信用卡\n\nS10:NC:保留\n\nS11: Index:交易筆數序號,從0001~0400\n\nS12: Chksum: 從S2(Sent Length )++…..+S11(Index)Byte為單位相加,並將16進制轉為10進制並轉成ASCII,取最後共四位數,MSB …LSB.\n\nS13: SentEOT: Fix “E”.\n\n//-----------------------------\n\nAP回應指令: Format RX Item 01/02\n\n \n\n//範列**************************************************//\n\nEX:V 6300000220240711150136001016001012345678912345605432112342880E\n\n換成ASCII為:\n\n56-36 33-30 30 30 30 30 32- 32 30 32 34 30 37 31 31 31 35 30 31 33 36- 30 30 31 –\n\n30 31 36- 30 30 31- 30 31 32 33 34 35 36 37 38 39 31 32 33 34 35 36- 30 -35 34 33 32 31 -31 32 33 34 -32 38 38 30-45.\n\n指令解析:\n\nS1=56.\n\nS13=45.\n\nS12:Chksum:36+33+30+30+30+30+30+32+32+30+32+34+30+37+31+31+31+35+30 +31+33+36+30+30+31+30+31+36+30+30+31+30+31+32+33+34+35+36+37+38+39\n\n+31+32+33+34+35+36+30+35+34+33+32+31+31+32+33+34=0x0B40\n\n0x0B40 換成10進制,並轉成ASCII 為2880.\n\n//---- AP回應指令--------\n\n20x:收到正確.\n\n40x:收到不符合\n\n//================================================================\n\n//=02=VMC 送出信號解析========指令”W”格式(19碼ASCII)===== 心跳信號=\n\n| NO   | Type         | ASCII Bytes  | 說明   | 備註              |\n| ---- | ------------ | ------------ | ------ | ----------------- |\n| S1   | Sent Command | W            | 1位數, | 指令碼            |\n| S2   | VMC NO       | 000007       | 6位數, | 機台號碼          |\n| S3   | Time         | 202408210950 | 12位數 | 12位數,  不需要秒 |\n\n//-----------------------------\n\nAP回應指令: Format RX Item 01/02\n\n \n\n//範列**************************************************//\n\nEX:57 30 30 30 30 30 37 32 30 32 34 30 38 32 31 30 39 35 30\n\nS1:V\n S2: 30 30 30 30 30 37\n\nS3: 32 30 32 34 30 38 32 31 30 39 35 30 (不需要秒) \n\nASCII(W000007202408210950)\n\n不需要chksum.\n\n//----AP 回應--------\n\n20x:收到正確.\n\n40x:收到不符合\n\n//=03=VMC 送出信號解析========指令”A”格式(26碼ASCII)==== 對帳交易資料===\n\nEX:A26000006202408270005003525681008E\n\n| NO   | Type         | ASCII Bytes | 說明   | 備註                                                         |\n| ---- | ------------ | ----------- | ------ | ------------------------------------------------------------ |\n| S1   | Sent Command | A           | 1位數, | 指令碼                                                       |\n| S2   | Sent Length  | 26          | 2位數, | 總傳送長度                                                   |\n| S3   | VMC NO       | 000006      | 6位數, | 機台號碼                                                     |\n| S4   | date         | 20240827    | 8位數  | 交易日                                                       |\n| S5   | 1ST Index    | 0005        | 4位數, | 從V指令S11的那4位數,  今天第一次傳送成功的交易代號.          |\n| S6   | Sent OK Cnt  | 0035        | 4位數, | 已傳送成功筆數                                               |\n| S7   | REC Chksum   | 2568        | 4位數, | 每一筆成功傳送的chksum相加所得到,取後4位數                   |\n| S8   | CMD Chksum   | 1008        | 4位數, | 從S2+…..+S7以Byte為單位相加,並將16進制轉為10進制並轉成ASCII,取最後共四位數,MSB  …LSB. |\n| S9   | SentEOT      | E           | 1位數  | 結束碼                                                       |\n\n//-----------------------------\n\nAP回應指令: Format RX Item 03\n\nEX:RA30001234203000500100015 1199E\n\n| NO   | Type              | ASCII Bytes    | 說明                             | 備註                                                         |\n| ---- | ----------------- | -------------- | -------------------------------- | ------------------------------------------------------------ |\n| S1   | Sent Command      | R              | 1位數,                           | 開頭碼                                                       |\n| S2   | Ans Command       | A              | 1位數,                           | 回應指令碼                                                   |\n| S3   | Length ALL        | 30             | XX                               | 2位數                                                        |\n| S4   | REC Chksum Right  | 0              | 1                                | 已成功接收的Chksun,1:正確,其他NG                             |\n| S5   | REC Counter Right | 0              | 1                                | 已成功接收的筆數,1:正確,其他NG                               |\n| S6   | AP Chksum         | 1234           | 每一筆成功傳送的chksum相加所得到 | 由AP自己算出的chksum                                         |\n| S7   | PN Status         | 2              | 確認AP紀錄筆數狀態ˇ              | 1:AP收到比傳送多.  2: AP收到比傳送少  0:相等                 |\n| S8   | Cnt Diff          | 03             | 每一筆成功傳送的chksum相加所得到 | 少3筆                                                        |\n| S9   | Index             | 0005,0010,0015 | 確認AP紀錄筆數狀態ˇ              | 缺少的代號index  1/5/6交易資料, 從V指令S11的那4位數做對應    |\n| S10  | CMD Chksum        | 1199           | XXXX                             | 從S2+…..+S9以Byte為單位相加,並將16進制轉為10進制並轉成ASCII,取最後共四位數,MSB  …LSB. |\n| S11  | SentEOT           | EOT            | E(0x45)                          | 結束碼                                                       |\n\n \n\n \n\n \n\n \n\n \n\n \n\n//範列**************************************************//\n\nEX1: A26000006202408270005003525681008E\n\n說明:\n\nA:開頭碼\n\n26:總傳送長度,\n\n\u200b     000006:機台號碼\n\n20240827: 當天資料日期\n\n0005: 今天第一次傳送成功的地交易代號.\n\n0035:今天已傳送成功共有35筆資料.\n\n\u200b     2568:這35筆總chksum為2568.\n\nREC Chksum:每一筆成功傳送的chksum相加所得到.\n\nCMD Chksum=S2+~S10=2+6+0+0+0…..6+8=3F0,0x03F0=1008.\n\n// 從V指令S11的那4位數 ,Index:交易筆數序號,從0001CCCC\\\\\n\n \n\n//--------------------------\n\nAP回應說明:\n\nEX:RA300012342030005001000151199E\n\nR:開頭碼\n\nA:針對A指令做回應.\n\n30:總傳送長度30個Bytes.\n\n0:chksum 不符合.\n\n0:傳送筆數不符合.\n\n1234:AP算出已成功傳送的chksum總和.\n\n2: AP收到比傳送少\n\n03:差異有三筆.\n\n0005,0010,0015: 三筆index.\n\n1199:這次指令chksum=S2+S2+…+S9=A+3+0+….+1+5=04AF,0x04AF=1199.\n\n//-------------------------------------------\n\nEX2: 正確\n\nRA1111E:不用帶S6/S7/S8/S9/S10. 不用chksum\n\n \n\n//------------------------------------------------------------------------------\n\n//=04=VMC 送出信號解析========指令”T”格式=====要求時間==========\n\nEX:T0000007\n\n指令”T”格式(ASCII)\n\nEX:T000006\n\n| NO   | Type         | ASCII Bytes | 說明   | 備註     |\n| ---- | ------------ | ----------- | ------ | -------- |\n| S1   | Sent Command | T           | 1位數, | 指令碼   |\n| S2   | VMC NO       | 000006      | 6位數, | 機台號碼 |\n\n//-----------------------------\n\nAP回應指令: Format RX Item 03\n\n \n\n//範列**************************************************//\n\nEX: T000006\n\n說明:\n\nT:開頭碼\n\n000006:機台號碼\n\n//----AP 回應--------\n\nEX: RT20240821153526\n\n\\-------------------------------------------------------------------------\n\n//=05=AP 送出信號解析========指令”P”格式=====修改機台號碼======\n\nEX:P123456\n\n| NO   | Type         | ASCII Bytes | 說明   | 備註         |\n| ---- | ------------ | ----------- | ------ | ------------ |\n| S1   | Sent Command | P           | 1位數, | 指令碼       |\n| S2   | VMC NO       | 123456      | 6位數, | 變更機台號碼 |\n\n//-----VMC 回應指令: Format TX Item 02\n\nVMC於5分鐘內要傳一次新ID的心跳資料給AP做確認.\n\n \n\n ', 'author': '16613', 'created_at': '2024-08-28T11:05:15.384503', 'updated_at': '2024-08-28T11:06:41.692261', 'tags': '', 'images': []}]
2024-08-28 11:06:41,863 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 13729
2024-08-28 11:08:06,896 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:08:12,146 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:08:12,284 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:08:12,285 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:11:06,876 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:12:51,642 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:12:51,642 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:14:06,884 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:14:16,227 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:14:16,227 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:14:19,872 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:14:20,050 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:14:20,275 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:14:20,335 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:14:20,336 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:14:20,775 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:14:20,839 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:17:02,687 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:17:02,687 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:17:20,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:19:07,910 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:19:07,910 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:20:20,017 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:20:44,150 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:20:44,150 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:00,064 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:21:00,065 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:09,970 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '0000', 'content': '1111', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:21:09,973 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:10,115 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 22, 'title': '0000', 'content': '1111', 'author': '16613', 'created_at': '2024-08-28T11:21:09.970670', 'updated_at': '2024-08-28T11:21:09.970670', 'tags': '', 'images': []}]
2024-08-28 11:21:10,115 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 236
2024-08-28 11:21:14,686 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:15,821 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:21:15,822 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:34,772 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:21:34,993 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:21:35,039 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:21:35,039 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:21:35,493 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:21:35,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:23:00,035 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:23:00,035 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:06,584 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '000', 'content': '111', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:23:06,623 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:06,754 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 23, 'title': '000', 'content': '111', 'author': '16613', 'created_at': '2024-08-28T11:23:06.584039', 'updated_at': '2024-08-28T11:23:06.584039', 'tags': '', 'images': []}]
2024-08-28 11:23:06,754 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 234
2024-08-28 11:23:11,405 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:12,528 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:23:12,529 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:20,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:23:49,658 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:23:49,871 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:23:49,914 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:23:49,915 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:50,190 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:23:50,429 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:23:57,871 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '111', 'content': '1111', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:23:57,879 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:23:57,998 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 24, 'title': '111', 'content': '1111', 'author': '16613', 'created_at': '2024-08-28T11:23:57.872657', 'updated_at': '2024-08-28T11:23:57.872657', 'tags': '', 'images': []}]
2024-08-28 11:23:57,999 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 235
2024-08-28 11:24:00,264 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:24:01,402 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:24:01,403 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:24:34,929 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:26:20,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:26:30,499 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 11:26:35,032 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-08-28 11:26:35,191 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-08-28 11:26:42,687 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-08-28 11:26:42,909 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:26:43,051 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 7613
2024-08-28 11:26:43,415 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:26:43,450 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:26:44,966 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:26:44,966 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:26:49,792 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:26:50,137 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '1111', 'content': '1111', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:26:50,141 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:26:50,291 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 25, 'title': '1111', 'content': '1111', 'author': '16613', 'created_at': '2024-08-28T11:26:50.137628', 'updated_at': '2024-08-28T11:26:50.137628', 'tags': '', 'images': []}]
2024-08-28 11:26:50,291 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 236
2024-08-28 11:26:53,856 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:26:54,993 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:26:54,994 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:27:17,856 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:27:18,058 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:27:18,252 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:27:18,252 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:27:18,372 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:27:18,679 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:27:18,863 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:27:26,730 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '000', 'content': '0000', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:27:26,733 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:27:26,853 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 26, 'title': '000', 'content': '0000', 'author': '16613', 'created_at': '2024-08-28T11:27:26.730832', 'updated_at': '2024-08-28T11:27:26.730832', 'tags': '', 'images': []}]
2024-08-28 11:27:26,853 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 235
2024-08-28 11:27:29,458 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:27:30,594 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:27:30,594 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:29:11,382 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:29:11,524 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:29:11,524 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:29:11,641 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:29:12,020 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:29:12,031 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:29:20,240 BlogInfo.insert_blog_method 115 INFO    => create_blog_method data: {'title': '000', 'content': '0000', 'author': '16613', 'tags': '', 'images': []}
2024-08-28 11:29:20,246 basehttp.log_message 161 INFO    => "POST /api/blogs/insert_blog/ HTTP/1.1" 200 57
2024-08-28 11:29:20,373 BlogInfo.select_blog_method  58 INFO    => transformed_result: [{'id': 27, 'title': '000', 'content': '0000', 'author': '16613', 'created_at': '2024-08-28T11:29:20.240275', 'updated_at': '2024-08-28T11:29:20.240275', 'tags': '', 'images': []}]
2024-08-28 11:29:20,373 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 235
2024-08-28 11:29:22,350 basehttp.log_message 161 INFO    => "POST /api/blogs/delete_blog/ HTTP/1.1" 200 57
2024-08-28 11:29:23,463 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:29:23,463 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:29:37,021 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:29:37,021 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:30:18,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:32:11,563 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:33:18,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:35:11,520 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:36:18,040 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:38:11,550 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:39:18,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:41:11,544 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:42:18,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:44:11,561 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:45:18,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:46:06,890 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:46:07,040 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:46:07,323 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:46:07,368 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:46:07,369 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:46:08,201 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:46:08,291 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:49:08,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:49:54,116 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:49:54,164 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:49:54,165 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:49:54,330 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:49:54,597 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:49:54,683 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:50:06,184 log.log_response 230 WARNING => Not Found: /api2/upload-image/
2024-08-28 11:50:06,184 basehttp.log_message 161 WARNING => "POST /api2/upload-image/ HTTP/1.1" 404 2146
2024-08-28 11:51:46,643 log.log_response 230 WARNING => Not Found: /api2/upload-image/
2024-08-28 11:51:46,643 basehttp.log_message 161 WARNING => "POST /api2/upload-image/ HTTP/1.1" 404 2146
2024-08-28 11:52:07,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:52:26,062 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:52:26,062 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:52:44,915 log.log_response 230 WARNING => Not Found: /api/upload-image/
2024-08-28 11:52:44,916 basehttp.log_message 161 WARNING => "POST /api/upload-image/ HTTP/1.1" 404 32917
2024-08-28 11:52:54,495 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:53:24,045 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:53:24,046 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:53:31,760 log.log_response 230 WARNING => Not Found: /api/upload-upload_blog_image/
2024-08-28 11:53:31,761 basehttp.log_message 161 WARNING => "POST /api/upload-upload_blog_image/ HTTP/1.1" 404 32953
2024-08-28 11:53:54,957 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:53:54,958 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:53:58,886 log.log_response 230 WARNING => Not Found: /api/upload_blog_image/
2024-08-28 11:53:58,887 basehttp.log_message 161 WARNING => "POST /api/upload_blog_image/ HTTP/1.1" 404 32932
2024-08-28 11:55:08,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:55:27,404 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:55:27,448 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:55:27,449 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:55:27,641 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:55:27,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:55:27,945 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:55:35,981 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:55:35,981 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:55:36,071 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:55:36,205 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:55:36,272 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:55:36,272 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:55:36,488 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:55:36,540 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:55:42,272 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/upload_blog_image/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 65, in upload_blog_image
    return self._handle_action('blog', 'upload_image')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 254, in wrapper
    json_data = json.loads(request.body)
  File "C:\python3.6\lib\json\__init__.py", line 349, in loads
    s = s.decode(detect_encoding(s), 'surrogatepass')
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x89 in position 136: invalid start byte
2024-08-28 11:55:42,276 basehttp.log_message 161 ERROR   => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 500 131969
2024-08-28 11:57:58,165 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:57:58,166 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:58:03,442 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/upload_blog_image/ HTTP/1.1" 200 0
2024-08-28 11:58:03,576 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 11:58:03,577 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 11:58:28,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:58:37,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:59:09,349 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 11:59:23,568 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 11:59:23,824 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 11:59:24,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 11:59:24,094 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 11:59:24,095 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 11:59:24,543 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 11:59:24,578 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 11:59:32,380 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 11:59:32,380 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 12:02:24,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:05:24,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:08:24,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:11:24,155 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:14:24,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:17:24,061 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-28 12:17:24,200 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:20:24,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:23:24,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:26:24,145 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:29:24,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:32:24,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:35:24,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:38:24,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:41:24,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:44:24,180 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:47:24,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:50:24,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:53:24,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:56:24,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 12:59:24,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:00:04,186 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 13:00:04,332 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 13:00:10,622 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 13:00:10,622 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 13:00:29,976 basehttp.log_message 161 INFO    => "OPTIONS /api/blogs/select_blog/ HTTP/1.1" 200 0
2024-08-28 13:00:30,103 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 13:00:30,104 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:00:39,855 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 13:00:39,855 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 13:02:24,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:03:29,056 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 13:03:29,056 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:03:43,623 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 13:03:43,624 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 13:04:48,063 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 13:04:48,064 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:05:24,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:06:51,091 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-08-28 13:06:51,091 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 13:06:51,091 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-08-28 13:06:51,262 BlogInfo.select_blog_method  58 INFO    => transformed_result: []
2024-08-28 13:06:51,262 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:06:51,271 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:06:51,377 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 13:06:51,805 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 13:06:51,833 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 13:08:24,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:09:52,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:11:24,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:12:52,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:14:24,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:15:52,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:17:23,775 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:18:52,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:20:24,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:21:52,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:23:24,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:24:52,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:25:35,161 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 13:25:41,987 log.log_response 230 ERROR   => Internal Server Error: /api/blogs/upload_blog_image/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 65, in upload_blog_image
    return self._handle_action('blog', 'upload_image')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\blog\views.py", line 72, in _handle_action
    result, http_status = BLOG_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 254, in wrapper
    json_data = json.loads(request.body)
  File "C:\python3.6\lib\json\__init__.py", line 349, in loads
    s = s.decode(detect_encoding(s), 'surrogatepass')
UnicodeDecodeError: 'utf-8' codec can't decode byte 0x89 in position 153: invalid start byte
2024-08-28 13:25:41,993 basehttp.log_message 161 ERROR   => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 500 132725
2024-08-28 13:26:24,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:27:52,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:28:28,073 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 13:28:37,388 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 13:28:37,389 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 13:29:12,852 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-08-28 13:29:16,440 BlogInfo.upload_image_blog_method 171 ERROR   => No file uploaded
2024-08-28 13:29:16,441 log.log_response 230 WARNING => Bad Request: /api/blogs/upload_blog_image/
2024-08-28 13:29:16,442 basehttp.log_message 161 WARNING => "POST /api/blogs/upload_blog_image/ HTTP/1.1" 400 57
2024-08-28 13:29:23,783 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:30:52,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:31:51,085 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 13:31:55,249 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 13:31:55,293 BlogInfo.select_blog_method  59 INFO    => transformed_result: []
2024-08-28 13:31:55,294 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:31:55,346 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:31:55,807 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 13:31:55,834 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 13:33:52,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:34:56,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:36:52,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:37:56,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:39:52,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:40:56,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:42:52,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:43:56,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:44:19,180 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 13:44:19,241 BlogInfo.select_blog_method  59 INFO    => transformed_result: []
2024-08-28 13:44:19,242 basehttp.log_message 161 INFO    => "POST /api/blogs/select_blog/ HTTP/1.1" 200 57
2024-08-28 13:44:19,398 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:44:19,701 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 13:44:19,717 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 13:46:56,151 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:47:20,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:49:56,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:50:20,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:52:56,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:53:20,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:55:56,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:56:20,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:58:56,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 13:59:57,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:01:55,520 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:02:19,561 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:03:20,953 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 14:04:55,514 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:05:19,569 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:07:55,516 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:08:19,559 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:10:55,506 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:11:19,564 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:13:55,555 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:14:19,557 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:16:55,507 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:17:20,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:19:55,490 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-28 14:19:55,621 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:20:20,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:22:56,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:23:57,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:25:56,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:26:57,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:28:56,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:29:57,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:31:56,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:32:57,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:34:56,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:35:57,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:37:56,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:38:57,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:40:56,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:41:57,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:43:56,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:44:57,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:46:56,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:47:57,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:49:56,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:50:57,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:52:56,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:53:57,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:55:56,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:56:57,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:58:56,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 14:59:57,118 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:01:56,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:02:57,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:04:56,178 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:05:57,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:07:56,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:08:57,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:10:56,098 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:11:57,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:13:56,139 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:14:57,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:16:56,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:17:57,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:19:56,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:20:57,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:22:56,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:23:57,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:25:56,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:26:57,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:28:56,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:29:57,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:31:56,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:32:57,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:34:56,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:35:57,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:37:56,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:38:57,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:40:56,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:41:57,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:43:56,154 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:44:57,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:46:56,143 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:47:57,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:49:56,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:50:57,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:52:56,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:53:57,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:55:56,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:56:57,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:58:56,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 15:59:57,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:01:56,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:02:57,187 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:04:56,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:05:57,185 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:07:56,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:08:57,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:10:56,159 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:11:57,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:13:56,172 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:14:57,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:16:56,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:17:57,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:19:56,061 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-28 16:19:56,184 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:20:57,132 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:22:56,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:23:57,140 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:25:56,116 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:26:57,195 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:28:56,111 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:29:57,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:31:56,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:32:57,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:34:56,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:35:57,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:37:56,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:38:57,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:40:56,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:41:57,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:43:56,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:44:57,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:46:56,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:47:57,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:49:56,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:50:57,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:52:56,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:53:57,150 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:55:56,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:56:57,194 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:57:02,437 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 16:57:02,591 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 16:58:55,759 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 16:59:19,669 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:01:55,571 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:02:19,589 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:04:56,215 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:05:20,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:07:56,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:08:07,470 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:08:07,611 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-08-28 17:08:07,762 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-08-28 17:08:07,763 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-08-28 17:08:07,764 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-08-28 17:08:07,901 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-08-28 17:08:07,974 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-08-28 17:08:08,169 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-08-28 17:08:08,307 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19145
2024-08-28 17:08:08,528 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-08-28 17:08:08,614 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-08-28 17:08:19,570 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:10:56,202 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:11:57,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:13:56,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:14:57,158 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:16:56,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:17:57,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:19:56,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:20:57,113 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:22:56,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:23:57,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:25:56,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:26:57,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:28:56,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:29:57,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:31:56,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:32:57,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:34:56,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:35:57,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:37:56,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:38:57,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:40:56,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:41:57,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:43:56,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:44:57,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:46:56,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:47:57,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:49:56,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:50:57,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:52:56,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:53:57,217 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:55:56,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:56:57,734 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:58:56,255 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 17:59:57,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:01:56,133 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:02:57,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:04:56,174 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:05:57,179 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:07:56,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:08:57,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:10:56,144 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:11:57,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:13:56,157 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:14:57,163 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:16:56,127 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:17:57,124 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:19:56,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:20:57,061 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-08-28 18:20:57,250 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:22:56,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:23:57,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:25:56,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:26:57,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:28:56,207 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:29:57,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:31:56,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:32:57,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:34:56,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:35:57,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:37:56,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:38:57,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:40:56,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:41:57,123 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:43:56,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:44:57,128 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:46:56,119 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:47:57,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:49:56,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:50:57,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:52:56,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:53:57,181 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:55:56,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:56:57,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:58:56,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 18:59:57,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:01:56,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:02:57,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:04:56,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:05:57,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:07:56,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:08:57,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:10:56,141 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:11:57,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:13:56,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:14:57,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:16:56,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:17:57,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:19:56,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:20:57,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:22:56,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:23:57,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:25:56,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-08-28 19:26:57,066 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-08-28 19:26:57,067 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
