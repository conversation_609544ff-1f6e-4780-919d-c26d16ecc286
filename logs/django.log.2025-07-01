2025-07-01 09:34:17,257 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 09:34:17,284 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 09:34:17,381 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 09:34:17,382 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 09:34:17,388 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 省錢超市114年07月促銷通報1140626250627h16397B.docx  -  相容模式 - Word
2025-07-01 09:34:17,590 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 09:34:17,640 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 09:34:17,672 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 09:34:17,708 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 09:34:17,743 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 09:34:17,789 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 09:34:18,272 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 09:34:18,370 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 09:34:18,690 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:34:23,147 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 09:34:24,552 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 09:34:27,024 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 09:34:29,208 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 09:34:30,391 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 09:34:33,189 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 省錢超市114年07月促銷通報1140626250627h16397B.docx  -  相容模式 - Word
2025-07-01 09:34:37,700 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 09:34:37,703 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 09:34:37,745 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 09:34:37,745 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 09:34:37,750 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 09:34:37,750 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 09:34:37,751 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 09:34:37,751 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 09:34:37,751 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 09:34:37,751 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 09:34:38,256 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:34:38,531 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 09:34:46,583 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 09:34:48,902 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 09:34:49,337 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:34:50,066 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 09:34:51,608 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 09:34:51,871 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-01 09:34:52,056 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 09:34:57,661 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-01 09:34:57,740 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-01 09:34:59,376 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-01 09:35:09,395 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:35:09,616 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 143.3%
2025-07-01 09:35:10,282 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-01 09:35:17,394 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:35:19,669 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:35:19,760 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 09:35:37,753 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:35:39,869 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:35:50,402 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:35:50,406 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:36:10,422 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:36:10,545 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:36:17,407 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:36:20,724 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:36:20,814 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:36:37,777 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:36:40,915 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:36:40,919 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:36:51,456 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:36:51,539 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:37:11,177 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:37:11,195 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 09:37:17,444 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:37:21,923 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:37:22,047 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:37:37,857 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:37:41,569 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:37:41,731 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:37:52,556 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:38:12,295 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:38:12,300 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-01 09:38:17,497 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:38:22,875 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:38:22,990 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:38:37,917 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:38:42,558 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:38:42,574 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:38:53,413 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:38:53,515 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:39:12,958 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:39:13,092 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:39:17,556 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:39:24,218 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:39:24,331 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-01 09:39:37,941 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:39:43,582 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:39:43,586 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 09:39:54,884 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:39:54,888 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:40:14,032 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:40:14,064 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 09:40:17,599 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:40:25,289 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:40:25,433 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:40:37,976 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:40:44,516 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:40:44,603 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:40:55,926 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:41:15,087 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:41:17,694 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:41:26,143 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:41:26,211 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:41:38,018 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:41:45,487 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:41:45,631 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:41:56,724 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:41:56,811 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:42:16,426 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:42:17,778 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:42:27,572 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:42:27,694 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 09:42:38,119 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:42:47,129 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:42:47,218 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:42:58,171 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:43:17,754 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:43:17,757 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-01 09:43:17,795 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:43:28,446 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:43:28,492 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:43:38,138 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:43:47,902 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:43:47,905 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:43:58,919 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:43:59,057 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:44:17,810 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:44:18,161 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:44:18,249 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:44:29,714 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:44:29,908 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:44:38,147 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:44:48,678 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:44:48,776 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:45:00,648 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:45:00,719 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:45:17,871 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:45:19,474 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:45:19,644 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 09:45:31,481 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:45:31,590 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:45:38,166 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:45:50,363 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:45:50,502 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:46:02,272 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:46:02,418 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:46:17,945 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:46:21,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:46:21,278 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:46:33,225 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:46:33,340 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-01 09:46:38,247 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:46:51,973 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:46:52,117 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:47:04,100 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:47:04,211 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:47:17,980 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:47:22,889 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:47:23,077 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 09:47:34,902 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:47:38,322 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:47:53,896 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:47:53,964 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:48:05,465 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:48:05,523 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:48:18,052 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:48:24,749 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:48:24,873 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:48:36,460 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:48:36,515 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:48:38,398 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:48:55,592 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:48:55,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:49:06,987 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:49:18,090 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:49:26,353 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:49:26,487 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:49:37,139 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:49:37,142 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-01 09:49:38,456 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:49:57,430 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:49:57,556 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:50:07,281 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:50:18,106 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:50:27,942 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:50:28,085 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 09:50:37,422 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:50:38,465 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:50:58,743 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:50:58,883 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-01 09:51:07,776 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:51:07,903 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:51:18,147 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:51:29,664 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:51:29,760 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:51:38,340 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:51:38,369 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:51:38,484 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:52:00,313 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:52:08,840 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:52:08,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:52:18,187 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:52:30,450 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:52:30,453 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 09:52:38,521 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:52:39,700 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:52:39,833 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:53:00,679 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:53:00,706 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:53:10,539 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:53:10,712 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:53:18,222 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:53:31,105 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:53:31,215 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:53:38,566 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:53:41,175 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:54:01,921 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:54:01,977 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 09:54:11,320 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:54:18,273 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:54:32,774 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:54:32,828 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 09:54:38,598 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:54:41,530 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:54:41,544 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:55:03,578 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:55:03,690 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 09:55:11,966 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:55:12,010 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:55:18,302 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:55:34,423 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:55:34,522 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:55:38,693 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:55:42,494 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:55:42,615 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 09:56:05,005 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:56:05,009 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-01 09:56:13,345 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:56:13,473 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:56:18,337 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:56:35,157 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:56:38,707 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:56:44,199 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:56:44,276 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 09:57:05,424 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:57:05,443 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 09:57:14,758 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:57:18,410 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:57:35,866 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:57:36,006 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:57:38,744 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:57:45,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:57:45,251 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:58:06,547 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:58:15,898 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:58:16,040 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 09:58:18,427 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:58:36,719 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:58:36,736 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:58:38,790 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:58:46,840 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:58:46,940 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 09:59:07,142 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:59:07,243 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 09:59:17,713 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:59:17,812 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 09:59:18,454 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:59:37,742 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:59:37,835 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 09:59:38,813 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 09:59:48,453 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 09:59:48,469 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:00:08,507 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:00:18,501 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:00:19,032 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:00:19,168 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-01 10:00:38,672 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:00:38,675 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 10:00:38,858 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:00:49,713 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:00:49,759 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:01:08,966 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:01:09,007 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:01:18,572 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:01:20,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:01:20,236 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:01:38,912 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:01:39,435 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:01:39,584 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-01 10:01:46,929 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: Word
2025-07-01 10:01:50,856 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:01:50,860 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 10:02:10,842 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:02:11,080 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:02:18,661 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:02:21,072 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:02:38,953 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:02:41,782 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:02:41,785 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:02:51,923 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:02:52,033 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-01 10:02:57,052 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 1140701軟體請購單Claude Max.docx - Word
2025-07-01 10:03:04,476 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 1140701軟體請購單Claude Max.docx - Word
2025-07-01 10:03:04,540 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 1140701軟體請購單Claude Max.docx - Word
2025-07-01 10:34:46,456 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 10:34:46,459 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 10:34:46,499 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 10:34:46,500 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 10:34:46,845 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 10:34:46,900 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 10:34:46,912 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 10:34:46,947 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 10:34:46,979 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 10:34:47,003 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 10:34:47,957 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 10:34:48,101 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 10:34:48,343 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:34:52,968 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 10:34:57,520 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 10:34:58,384 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 10:34:59,364 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 10:35:01,244 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 10:35:12,144 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 10:35:12,149 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 10:35:12,193 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 10:35:12,193 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 10:35:12,198 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 10:35:12,198 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 10:35:12,198 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 10:35:12,198 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 10:35:12,198 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 10:35:12,198 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 10:35:12,742 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 10:35:12,815 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 10:35:13,267 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:35:16,729 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 10:35:18,889 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:35:19,881 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 10:35:21,770 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 10:35:23,015 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 10:35:25,110 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 10:35:27,879 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-01 10:35:35,150 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-01 10:35:35,166 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-01 10:35:35,858 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-01 10:35:38,981 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-01 10:35:44,088 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:35:44,225 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 10:35:46,537 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:35:49,223 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:35:49,365 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 10:36:12,214 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:36:14,965 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:36:14,970 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-01 10:36:20,082 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:36:20,166 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-07-01 10:36:43,493 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-01 10:36:43,493 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-07-01 10:36:43,704 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-01 10:36:43,711 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2025-07-01 10:36:43,712 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-07-01 10:36:43,712 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2025-07-01 10:36:45,402 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:36:45,564 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 10:36:46,560 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:36:50,346 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:37:02,065 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-01 10:37:02,568 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-01 10:37:03,200 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-01 10:37:03,304 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-07-01 10:37:03,304 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-07-01 10:37:03,396 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-07-01 10:37:03,424 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-07-01 10:37:04,117 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1159
2025-07-01 10:37:04,120 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-01 10:37:04,364 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3397
2025-07-01 10:37:04,516 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5940
2025-07-01 10:37:06,254 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-01 10:37:06,321 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-07-01 10:37:06,321 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-07-01 10:37:06,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-01 10:37:06,570 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-01 10:37:12,187 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51123
2025-07-01 10:37:12,293 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:37:16,285 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:37:16,341 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 133.0%
2025-07-01 10:37:20,493 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:37:36,849 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-07-01 10:37:36,849 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-07-01 10:37:37,274 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-01 10:37:37,345 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-01 10:37:42,914 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-07-01 10:37:43,306 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1040 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.docx
2025-07-01 10:37:43,306 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2025-07-01 10:37:43,307 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2025-07-01 10:37:46,592 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:37:46,668 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:37:46,699 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 10:37:50,733 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:37:50,760 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:38:12,298 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:38:16,999 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:38:17,171 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:38:21,160 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:38:21,277 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 10:38:46,610 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:38:47,843 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:38:47,985 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:38:51,743 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:39:12,302 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:39:18,451 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:39:18,463 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:39:22,055 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:39:22,113 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 10:39:46,676 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:39:48,719 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:39:48,863 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 10:39:52,908 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:39:53,026 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:40:10,743 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1078 INFO    => 嘗試使用企業級處理器
2025-07-01 10:40:10,783 _DownloadBusinessNotificationInfo.extract_data_worker 1059 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\f8906a0b-1051-436c-9dcb-9226d7779471.docx
2025-07-01 10:40:10,786 _DownloadBusinessNotificationInfo.extract_data_worker 1065 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-01 10:40:10,861 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1086 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-01 10:40:10,861 word_queue_processor.submit_task 161 INFO    => 任務已提交: c91d184b-a4a2-464b-9fb4-e6b3adee9601
2025-07-01 10:40:10,861 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: c91d184b-a4a2-464b-9fb4-e6b3adee9601
2025-07-01 10:40:10,861 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1095 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\f8906a0b-1051-436c-9dcb-9226d7779471.docx
2025-07-01 10:40:12,311 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:40:14,460 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 2) 可能不存在或已合併
2025-07-01 10:40:19,367 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:40:19,372 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:40:20,013 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1098 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-01 10:40:20,083 word_queue_processor._process_tasks 120 INFO    => 任務完成: c91d184b-a4a2-464b-9fb4-e6b3adee9601
2025-07-01 10:40:20,083 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1111 INFO    => 原始處理器執行成功
2025-07-01 10:40:20,083 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1120 INFO    => 成功提取 16 筆數據
2025-07-01 10:40:20,084 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1127 ERROR   => 保存 Excel 文件時發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 10:40:20,085 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1145 ERROR   => select_business_notification_price_download 發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 10:40:20,088 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-07-01 10:40:20,089 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 459
2025-07-01 10:40:23,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:40:23,826 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 10:40:46,703 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:40:49,785 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:40:49,894 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 10:40:54,125 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:41:12,319 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:41:20,359 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:41:20,364 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 10:41:24,443 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:41:24,520 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:41:46,716 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:41:50,676 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:41:50,933 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:41:55,162 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:41:55,250 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:42:12,412 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:42:21,626 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:42:25,787 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:42:25,805 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:42:46,732 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:42:51,879 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:42:51,910 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:42:56,257 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:42:56,361 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:43:12,482 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:43:22,405 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:43:22,530 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-01 10:43:27,111 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:43:27,207 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:43:46,764 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:43:53,132 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:43:53,135 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:43:57,810 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:44:12,488 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:44:23,351 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:44:23,367 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 10:44:28,198 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:44:28,281 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:44:46,820 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:44:53,871 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:44:54,039 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 121.9%
2025-07-01 10:44:59,028 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:45:12,507 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:45:24,506 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:45:24,509 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:45:29,494 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:45:29,512 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 10:45:46,893 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:45:54,891 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:45:54,978 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 10:45:59,877 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:45:59,976 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 10:46:12,612 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:46:25,517 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:46:25,520 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 10:46:30,489 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:46:46,958 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:46:55,891 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:46:56,015 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:47:00,881 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:47:01,004 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 10:47:12,646 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:47:26,721 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:47:26,725 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:47:31,619 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:47:46,984 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:47:57,044 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:47:57,116 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 10:48:01,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:48:02,053 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:48:12,659 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:48:27,589 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:48:27,725 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:48:32,567 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:48:47,018 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:48:58,322 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:48:58,340 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:49:02,960 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:49:03,103 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 10:49:12,676 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:49:28,701 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:49:28,815 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:49:33,597 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:49:47,057 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:49:59,684 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:49:59,820 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:50:03,957 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:50:04,018 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:50:12,698 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:50:30,328 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:50:30,428 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:50:34,437 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:50:47,123 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:51:01,061 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:51:01,076 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 10:51:04,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:51:04,922 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 10:51:12,808 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:51:31,541 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:51:31,716 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:51:35,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:51:47,168 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:52:02,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:52:02,287 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 10:52:05,877 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:52:05,965 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:52:12,875 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:52:32,665 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:52:32,769 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:52:36,447 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:52:47,188 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:53:03,245 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:53:03,248 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-01 10:53:06,841 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:53:07,007 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:53:12,964 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:53:33,617 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:53:33,737 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:53:37,754 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:53:37,867 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 10:53:47,238 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:54:04,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:54:04,318 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:54:08,470 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:54:13,034 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:54:34,615 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:54:34,701 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:54:38,883 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:54:39,010 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:54:47,312 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:55:05,260 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:55:05,387 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 10:55:09,456 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:55:13,084 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:55:35,907 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:55:35,911 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 10:55:39,838 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:55:39,930 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:55:47,404 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:56:06,319 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:56:06,434 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:56:10,432 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:56:10,435 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:56:13,176 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:56:37,136 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:56:37,140 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 91.4%
2025-07-01 10:56:40,865 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:56:40,960 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 10:56:47,466 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:57:07,353 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:57:07,356 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-01 10:57:11,581 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:57:11,584 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:57:13,223 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:57:37,768 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:57:37,884 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 10:57:41,730 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:57:47,520 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:58:08,377 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:58:08,380 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 10:58:12,142 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:58:12,254 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 10:58:13,237 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:58:38,755 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:58:38,863 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 10:58:42,833 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:58:47,586 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:59:09,455 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:59:09,458 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-01 10:59:13,195 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:59:13,309 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
ending_tasks': 0, 'processing_tasks': 0}
2025-07-01 10:59:39,812 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:59:39,953 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 10:59:44,013 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 10:59:44,168 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 10:59:47,627 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:00:11,094 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:00:11,372 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:00:13,357 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:00:14,619 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:00:41,872 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:00:41,876 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:00:44,755 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:00:47,651 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:01:12,064 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:01:12,067 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:01:13,416 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:01:14,915 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:01:14,918 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 11:01:42,236 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:01:42,253 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 11:01:45,080 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:01:47,694 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:02:12,469 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:02:12,483 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:02:13,428 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:02:15,296 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:02:15,337 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:02:42,849 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:02:42,877 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:02:45,712 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:02:45,842 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:02:47,696 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:03:13,145 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:03:13,161 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:03:13,430 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:03:16,197 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:03:43,419 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:03:43,422 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:03:46,471 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:03:46,474 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:03:47,703 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:04:13,462 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:04:13,829 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:04:13,930 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:04:16,920 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:04:17,012 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:04:44,614 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:04:44,775 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 11:04:47,593 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:04:47,707 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:04:47,707 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:05:13,489 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:05:15,531 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:05:15,617 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 11:05:18,434 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:05:18,556 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 11:05:46,362 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:05:46,492 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-07-01 11:05:47,763 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:05:49,135 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:06:13,507 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:06:16,930 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:06:16,934 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:06:19,277 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:06:47,074 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:06:47,078 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:06:47,800 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:06:49,485 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:06:49,502 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:07:13,541 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:07:17,478 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:07:17,585 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:07:20,007 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:07:20,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 11:07:51,494 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:07:51,498 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:07:51,535 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:07:51,535 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:07:51,705 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:07:51,753 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:07:51,790 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:07:51,841 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:07:51,896 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:07:51,933 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:07:52,520 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 11:07:52,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:07:53,080 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 11:07:57,726 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:07:59,072 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:08:00,261 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:08:02,022 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:08:04,073 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:08:12,171 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:08:12,175 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:08:12,217 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:08:12,217 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:08:12,222 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:08:12,223 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:08:12,223 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:08:12,223 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:08:12,223 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:08:12,224 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:08:12,811 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 11:08:12,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:08:13,291 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:08:17,580 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:08:21,003 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:08:23,790 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:08:24,356 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:08:25,837 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:08:26,474 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-01 11:08:26,755 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:08:31,642 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-01 11:08:31,655 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-01 11:08:37,979 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-01 11:08:39,521 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-01 11:08:44,151 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:08:44,263 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:08:51,566 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:08:53,978 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:08:53,999 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:09:12,228 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:09:14,633 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:09:14,803 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:09:24,238 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:09:24,255 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:09:45,002 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-01 11:09:45,049 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-01 11:09:45,085 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 60786)

2025-07-01 11:09:45,234 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 60787)

2025-07-01 11:09:45,462 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:09:45,525 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:09:45,556 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-07-01 11:09:46,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-01 11:09:46,433 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-01 11:09:46,667 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-01 11:09:46,940 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-01 11:09:47,006 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-01 11:09:47,079 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-01 11:09:47,339 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3397
2025-07-01 11:09:47,524 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5347
2025-07-01 11:09:49,211 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51123
2025-07-01 11:09:51,653 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:09:51,638 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-01 11:09:51,670 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-01 11:09:53,802 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1045 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-01 11:09:54,292 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1114 INFO    => 嘗試使用企業級處理器
2025-07-01 11:09:54,293 _DownloadBusinessNotificationInfo.extract_data_worker 1095 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\1ad4c5df-3968-465b-9499-87e1de43f9d9.docx
2025-07-01 11:09:54,297 _DownloadBusinessNotificationInfo.extract_data_worker 1101 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-01 11:09:54,367 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1122 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-01 11:09:54,367 word_queue_processor.submit_task 161 INFO    => 任務已提交: beef4cc7-c9d6-4d3b-87d5-c381279b74e8
2025-07-01 11:09:54,367 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: beef4cc7-c9d6-4d3b-87d5-c381279b74e8
2025-07-01 11:09:54,367 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1131 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\1ad4c5df-3968-465b-9499-87e1de43f9d9.docx
2025-07-01 11:09:54,533 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:09:54,552 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:09:55,805 _DownloadBusinessNotificationInfo.extract_table_data 648 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-01 11:10:01,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 2) 可能不存在或已合併
2025-07-01 11:10:04,933 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1134 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-01 11:10:05,005 word_queue_processor._process_tasks 120 INFO    => 任務完成: beef4cc7-c9d6-4d3b-87d5-c381279b74e8
2025-07-01 11:10:05,005 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1147 INFO    => 原始處理器執行成功
2025-07-01 11:10:05,005 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1156 INFO    => 成功提取 16 筆數據
2025-07-01 11:10:05,006 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1160 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-01 11:10:05,007 _DownloadBusinessNotificationInfo.save_to_excel 897 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-01 11:10:05,007 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1164 ERROR   => 保存 Excel 文件時發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 11:10:05,007 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1165 ERROR   => 錯誤發生時的 rout 值: '10228', 類型: <class 'str'>
2025-07-01 11:10:05,007 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1183 ERROR   => select_business_notification_price_download 發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 11:10:05,011 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-07-01 11:10:05,011 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 459
2025-07-01 11:10:12,269 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:10:16,253 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:10:16,452 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:10:25,029 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:10:25,049 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 11:10:47,177 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:10:47,299 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:10:51,726 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:10:55,421 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:10:55,463 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-07-01 11:11:12,274 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:11:17,590 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:11:17,615 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:11:25,707 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:11:25,725 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:11:47,817 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:11:47,822 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:11:51,781 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:11:56,023 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:11:56,134 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 11:12:12,299 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:12:18,068 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:12:18,192 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-01 11:12:26,873 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:12:26,981 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:12:46,517 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-01 11:12:48,889 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:12:49,100 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 11:12:51,838 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:12:57,635 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:12:57,679 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 11:13:12,329 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:13:23,057 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:13:23,059 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:13:23,089 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:13:23,090 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:13:23,096 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:13:23,096 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:13:23,096 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:13:23,097 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:13:23,097 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:13:23,097 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:13:23,618 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 11:13:23,795 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:13:24,303 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:13:28,270 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:13:30,059 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:13:34,212 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:13:35,922 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:13:37,098 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:13:44,366 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:13:44,390 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:13:44,509 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:13:44,510 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:13:44,515 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:13:44,515 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:13:44,515 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:13:44,516 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:13:44,516 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:13:44,516 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:13:45,084 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 11:13:45,204 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:13:45,615 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:13:50,822 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:13:52,737 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:13:55,195 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:13:55,341 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:13:55,565 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:13:58,437 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:13:59,930 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:14:02,032 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-01 11:14:10,328 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-01 11:14:10,341 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-01 11:14:11,051 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-01 11:14:13,515 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-01 11:14:16,555 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:14:16,587 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-01 11:14:23,126 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:14:26,132 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:14:44,559 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:14:46,935 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:14:47,093 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:14:56,291 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:14:56,307 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:15:17,751 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:15:17,876 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-01 11:15:23,201 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:15:26,478 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:15:26,480 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-01 11:15:44,608 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:15:48,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-01 11:15:48,243 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:15:48,390 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-01 11:15:56,878 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:15:57,028 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:16:19,334 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:16:19,518 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 91.9%
2025-07-01 11:16:23,240 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:16:27,768 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:16:27,901 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:16:44,650 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:16:50,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:16:50,550 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 11:16:58,714 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:16:58,842 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:17:21,379 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:17:21,523 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:17:23,276 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:17:29,583 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:17:29,805 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:17:44,702 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:17:52,208 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:17:52,334 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:17:56,250 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-01 11:17:56,392 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-01 11:17:56,627 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-01 11:17:56,733 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-01 11:17:56,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-01 11:17:56,994 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-01 11:17:57,256 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3397
2025-07-01 11:17:57,257 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5347
2025-07-01 11:18:00,325 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51123
2025-07-01 11:18:00,529 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:18:00,638 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 11:18:02,742 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-01 11:18:02,742 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-01 11:18:03,826 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1094 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-01 11:18:03,862 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1163 INFO    => 嘗試使用企業級處理器
2025-07-01 11:18:03,863 _DownloadBusinessNotificationInfo.extract_data_worker 1144 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\eec1b9e4-0836-4689-bbea-4711a56e5cf3.docx
2025-07-01 11:18:03,928 _DownloadBusinessNotificationInfo.extract_data_worker 1150 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-01 11:18:03,935 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1171 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-01 11:18:03,935 word_queue_processor.submit_task 161 INFO    => 任務已提交: eec7d409-e4bb-4517-a7f8-334ea285160d
2025-07-01 11:18:03,935 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: eec7d409-e4bb-4517-a7f8-334ea285160d
2025-07-01 11:18:03,936 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1180 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\eec1b9e4-0836-4689-bbea-4711a56e5cf3.docx
2025-07-01 11:18:05,479 _DownloadBusinessNotificationInfo.extract_table_data 648 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-01 11:18:15,755 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 2) 可能不存在或已合併
2025-07-01 11:18:16,932 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1183 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-01 11:18:17,021 word_queue_processor._process_tasks 120 INFO    => 任務完成: eec7d409-e4bb-4517-a7f8-334ea285160d
2025-07-01 11:18:17,021 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1196 INFO    => 原始處理器執行成功
2025-07-01 11:18:17,021 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1205 INFO    => 成功提取 16 筆數據
2025-07-01 11:18:17,021 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1209 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-01 11:18:17,022 _DownloadBusinessNotificationInfo.save_to_excel 911 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.save_to_excel 956 INFO    => 準備添加 16 筆數據到 Excel
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.save_to_excel 974 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.save_to_excel 979 ERROR   => 添加第 2 筆數據時發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.save_to_excel 980 ERROR   => 問題數據: ['PET600ml黑松沙士\x0bPET600ml黑松加鹽沙士\x0bPET600ml黑松汽水\x0bPET600ml黑松沙士清新紅柚風味\x0bPET500ml黑松汽水C&C氣泡飲(檸檬風味)', '470', '元/箱', '', '', None, '']
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1213 ERROR   => 保存 Excel 文件時發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1214 ERROR   => 錯誤發生時的 rout 值: '10228', 類型: <class 'str'>
2025-07-01 11:18:17,023 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1232 ERROR   => select_business_notification_price_download 發生錯誤: PET600ml黑松沙士PET600ml黑松加鹽沙士PET600ml黑松汽水PET600ml黑松沙士清新紅柚風味PET500ml黑松汽水C&C氣泡飲(檸檬風味) cannot be used in worksheets.
2025-07-01 11:18:17,028 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
2025-07-01 11:18:17,029 basehttp.log_message 161 ERROR   => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 459
2025-07-01 11:18:23,105 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:18:23,142 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:18:23,302 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:18:31,507 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:18:31,571 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-01 11:18:44,760 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:18:53,505 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:18:53,671 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:19:02,064 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:19:02,162 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-01 11:19:23,344 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:19:24,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:19:24,549 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:19:33,128 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:19:33,224 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-01 11:19:44,737 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:19:44,742 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:19:44,779 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:19:44,779 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:19:44,827 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:19:44,868 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:19:44,896 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:19:44,933 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:19:44,976 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:19:45,013 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:19:45,410 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-01 11:19:45,568 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:19:46,204 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:19:51,201 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:19:56,231 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:19:58,054 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:20:04,976 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:20:15,477 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:20:20,778 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:20:28,859 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-07-01 11:20:28,864 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-07-01 11:20:29,380 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-01 11:20:29,402 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-01 11:20:29,509 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-01 11:20:29,509 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-01 11:20:29,509 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-01 11:20:29,509 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-01 11:20:29,509 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-01 11:20:29,509 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-01 11:20:35,330 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:20:39,309 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-01 11:20:45,015 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:20:49,161 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-01 11:20:49,866 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-01 11:20:53,121 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-01 11:20:53,198 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:21:16,429 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-01 11:21:16,556 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:21:22,940 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-01 11:21:24,716 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:21:29,579 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:21:45,348 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:21:48,031 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:21:48,156 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-01 11:21:55,926 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:21:56,033 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:22:04,476 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-01 11:22:04,491 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-01 11:22:05,176 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-01 11:22:07,472 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-01 11:22:18,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:22:18,813 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-01 11:22:22,590 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-01 11:22:22,751 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-01 11:22:22,966 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-01 11:22:23,181 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1159
2025-07-01 11:22:23,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3397
2025-07-01 11:22:23,657 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5347
2025-07-01 11:22:24,094 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-01 11:22:24,239 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-01 11:22:24,548 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-01 11:22:26,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:22:27,121 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:22:28,108 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 51123
2025-07-01 11:22:29,534 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-01 11:22:29,580 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:22:29,618 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-01 11:22:31,428 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1109 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-01 11:22:31,468 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1178 INFO    => 嘗試使用企業級處理器
2025-07-01 11:22:31,469 _DownloadBusinessNotificationInfo.extract_data_worker 1159 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\08fd0270-5129-4806-8b5d-c4bafd1ae48d.docx
2025-07-01 11:22:31,527 _DownloadBusinessNotificationInfo.extract_data_worker 1165 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-01 11:22:31,536 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1186 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-01 11:22:31,536 word_queue_processor.submit_task 161 INFO    => 任務已提交: 9cba9622-a6e6-499b-a06f-3726c6091a18
2025-07-01 11:22:31,536 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 9cba9622-a6e6-499b-a06f-3726c6091a18
2025-07-01 11:22:31,537 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1195 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\08fd0270-5129-4806-8b5d-c4bafd1ae48d.docx
2025-07-01 11:22:33,758 _DownloadBusinessNotificationInfo.extract_table_data 649 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-01 11:22:42,950 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 2) 可能不存在或已合併
2025-07-01 11:22:45,362 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:22:49,654 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:22:49,798 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:22:53,391 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1198 INFO    => Legacy 處理器成功提取 37 筆數據
2025-07-01 11:22:53,480 word_queue_processor._process_tasks 120 INFO    => 任務完成: 9cba9622-a6e6-499b-a06f-3726c6091a18
2025-07-01 11:22:53,481 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1211 INFO    => 原始處理器執行成功
2025-07-01 11:22:53,481 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1220 INFO    => 成功提取 37 筆數據
2025-07-01 11:22:53,481 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1224 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 37
2025-07-01 11:22:53,482 _DownloadBusinessNotificationInfo.save_to_excel 912 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-01 11:22:53,482 _DownloadBusinessNotificationInfo.save_to_excel 957 INFO    => 準備添加 37 筆數據到 Excel
2025-07-01 11:22:53,482 _DownloadBusinessNotificationInfo.save_to_excel 989 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-01 11:22:53,490 _DownloadBusinessNotificationInfo.save_to_excel 1044 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-01 11:22:53,543 _DownloadBusinessNotificationInfo.save_to_excel 1046 INFO    => Excel 文件保存成功
2025-07-01 11:22:53,544 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1226 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-01 11:22:53,550 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6269
2025-07-01 11:22:58,005 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:22:58,130 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:23:20,502 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:23:20,539 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:23:28,979 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:23:29,075 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-01 11:23:29,602 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:23:45,388 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:23:51,026 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:23:51,163 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:23:59,495 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:23:59,632 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.8%
2025-07-01 11:24:21,847 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:24:21,853 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-07-01 11:24:29,629 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:24:30,474 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:24:30,510 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-01 11:24:45,452 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:24:51,999 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:24:52,011 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-01 11:24:52,510 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-01 11:24:52,582 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-01 11:24:54,284 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1109 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-01 11:24:54,873 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1178 INFO    => 嘗試使用企業級處理器
2025-07-01 11:24:54,917 _DownloadBusinessNotificationInfo.extract_data_worker 1159 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\b71a767e-5655-4d17-971b-1c4ad94c7cae.docx
2025-07-01 11:24:55,118 _DownloadBusinessNotificationInfo.extract_data_worker 1165 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-01 11:24:55,249 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1186 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-01 11:24:55,279 word_queue_processor.submit_task 161 INFO    => 任務已提交: 2a707a92-1f05-4bbe-b19d-792a0acd23cf
2025-07-01 11:24:55,279 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 2a707a92-1f05-4bbe-b19d-792a0acd23cf
2025-07-01 11:24:55,294 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1195 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\b71a767e-5655-4d17-971b-1c4ad94c7cae.docx
2025-07-01 11:24:56,618 _DownloadBusinessNotificationInfo.extract_table_data 649 INFO    => extract_table_data 被調用，rout: '10106', user_id: '31200'
2025-07-01 11:24:59,979 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (1, 3)，表格大小: (3, 2)
2025-07-01 11:25:00,594 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (2, 3)，表格大小: (3, 2)
2025-07-01 11:25:00,704 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:25:00,852 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (3, 3)，表格大小: (3, 2)
2025-07-01 11:25:01,081 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-01 11:25:01,172 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 3) 可能不存在或已合併
2025-07-01 11:25:01,346 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (6, 3) 可能不存在或已合併
2025-07-01 11:25:01,754 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (11, 3) 可能不存在或已合併
2025-07-01 11:25:02,159 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (14, 3) 可能不存在或已合併
2025-07-01 11:25:02,221 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (15, 3) 可能不存在或已合併
2025-07-01 11:25:02,412 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (17, 3) 可能不存在或已合併
2025-07-01 11:25:02,558 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (19, 3) 可能不存在或已合併
2025-07-01 11:25:02,653 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (20, 3) 可能不存在或已合併
2025-07-01 11:25:02,710 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (21, 3) 可能不存在或已合併
2025-07-01 11:25:02,943 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (24, 3) 可能不存在或已合併
2025-07-01 11:25:03,275 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (28, 3) 可能不存在或已合併
2025-07-01 11:25:03,328 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (29, 3) 可能不存在或已合併
2025-07-01 11:25:03,374 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (30, 3) 可能不存在或已合併
2025-07-01 11:25:10,782 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (41, 3) 可能不存在或已合併
2025-07-01 11:25:11,228 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (44, 3) 可能不存在或已合併
2025-07-01 11:25:11,324 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (45, 3) 可能不存在或已合併
2025-07-01 11:25:11,704 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (46, 3) 可能不存在或已合併
2025-07-01 11:25:12,020 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (48, 3) 可能不存在或已合併
2025-07-01 11:25:12,443 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (50, 3) 可能不存在或已合併
2025-07-01 11:25:13,262 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (55, 3) 可能不存在或已合併
2025-07-01 11:25:13,623 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (57, 3) 可能不存在或已合併
2025-07-01 11:25:20,176 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-01 11:25:21,821 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1198 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-01 11:25:22,027 word_queue_processor._process_tasks 120 INFO    => 任務完成: 2a707a92-1f05-4bbe-b19d-792a0acd23cf
2025-07-01 11:25:22,027 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1211 INFO    => 原始處理器執行成功
2025-07-01 11:25:22,028 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1220 INFO    => 成功提取 33 筆數據
2025-07-01 11:25:22,028 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1224 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-01 11:25:22,029 _DownloadBusinessNotificationInfo.save_to_excel 912 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-01 11:25:22,029 _DownloadBusinessNotificationInfo.save_to_excel 957 INFO    => 準備添加 33 筆數據到 Excel
2025-07-01 11:25:22,029 _DownloadBusinessNotificationInfo.save_to_excel 989 INFO    => 第一筆數據: ['2411', '237', '元/箱', '', '', '225.71', '元/箱']
2025-07-01 11:25:22,039 _DownloadBusinessNotificationInfo.save_to_excel 1044 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\114年大潤發5071檔 DM.IP 促銷通報(1140704-1140717)250625h16510B.xlsx
2025-07-01 11:25:22,144 _DownloadBusinessNotificationInfo.save_to_excel 1046 INFO    => Excel 文件保存成功
2025-07-01 11:25:22,144 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1226 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400503\114年大潤發5071檔 DM.IP 促銷通報(1140704-1140717)250625h16510B.xlsx
2025-07-01 11:25:22,150 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6420
2025-07-01 11:25:22,216 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:25:22,220 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-01 11:25:29,696 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:25:31,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:25:31,273 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-01 11:25:45,526 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:25:52,607 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:25:52,838 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-01 11:26:01,843 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:26:24,107 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:26:24,202 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-01 11:26:29,697 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:26:32,266 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:26:32,389 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-01 11:26:45,575 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:26:55,521 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:27:03,579 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:27:26,800 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:27:29,753 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:27:35,756 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:27:45,588 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-01 11:27:57,552 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-01 11:28:06,416 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
