2024-10-24 07:31:23,917 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-24 07:31:23,918 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-10-24 07:31:23,989 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-10-24 07:31:23,989 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-10-24 07:31:23,990 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2024-10-24 07:31:24,018 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2024-10-24 07:31:24,019 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2024-10-24 07:31:24,019 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
