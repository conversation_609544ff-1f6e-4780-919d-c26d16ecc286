2024-07-13 00:02:14,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:05:14,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:08:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:11:14,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:14:14,121 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:17:14,102 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:20:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:23:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:26:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:29:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:32:14,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:35:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:38:14,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:41:14,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:44:14,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:47:14,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:50:14,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:53:14,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:56:14,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 00:59:14,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:02:14,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:05:14,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:08:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:11:14,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:14:14,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:17:14,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:20:14,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:23:14,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:26:14,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:29:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:32:14,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:35:14,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:38:14,103 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:41:14,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:44:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:47:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:50:14,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:53:14,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:56:14,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 01:59:14,007 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-13 01:59:14,183 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:02:14,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:05:14,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:08:14,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:11:14,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:14:14,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:17:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:20:14,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:23:14,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:26:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:29:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:32:14,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:35:14,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:38:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:41:14,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:44:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:47:14,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:50:14,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:53:14,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:56:14,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 02:59:14,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:02:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-13 03:05:14,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:08:14,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:11:14,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:14:14,106 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:17:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:20:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:23:14,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:26:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:29:14,085 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:32:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:35:14,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:38:14,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:41:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:44:14,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:47:14,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:50:14,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:53:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:56:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 03:59:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:02:14,043 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-13 04:02:14,191 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:05:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:08:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:11:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:14:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:17:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:20:14,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:23:14,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:26:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:29:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:32:14,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:35:14,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:38:14,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:41:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:44:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:47:14,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:50:14,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:53:14,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:56:14,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 04:59:14,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:02:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:05:14,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:08:14,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:11:14,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:14:14,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:17:14,153 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:20:14,142 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:23:14,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:26:14,097 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:29:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:32:14,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:35:14,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:38:14,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:41:14,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:44:14,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:47:14,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:50:14,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:53:14,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:56:14,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 05:59:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:02:14,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:05:14,021 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-13 06:05:14,160 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:08:14,080 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:11:14,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:14:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:17:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:20:14,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:23:14,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:26:14,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:29:14,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:32:14,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:35:14,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:38:14,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:41:14,083 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:44:14,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:47:14,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:50:14,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:53:14,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-13 06:56:14,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 06:59:14,130 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:02:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:05:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:08:14,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:11:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:14:14,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:17:14,182 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:20:14,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:23:14,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:26:14,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:29:14,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:32:14,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:35:14,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:38:14,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:41:14,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:44:14,093 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:47:14,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:50:14,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:53:14,112 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:56:14,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 07:59:14,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:02:14,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:05:14,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:08:14,037 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-07-13 08:08:14,156 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:11:14,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:14:14,089 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:17:14,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:20:14,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:20:53,472 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-13 08:20:53,473 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-07-13 08:20:53,624 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 08:20:53,727 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-13 08:20:57,377 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:20:57,530 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 08:20:57,610 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-07-13 08:20:57,610 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-07-13 08:20:57,636 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-07-13 08:20:57,747 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-07-13 08:20:57,814 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-07-13 08:20:57,814 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2024-07-13 08:20:57,814 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-07-13 08:20:57,982 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 08:20:58,041 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9342
2024-07-13 08:20:58,056 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-07-13 08:20:58,160 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-13 08:20:58,349 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 08:20:58,388 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 08:23:58,167 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:26:58,148 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:28:15,016 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 08:28:21,588 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-07-13 08:28:21,769 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 08:28:21,937 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 08:28:28,220 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-07-13 08:28:29,076 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 08:28:29,233 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 08:28:29,300 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-07-13 08:28:29,454 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 08:28:29,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 08:28:29,844 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 08:28:29,902 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 08:40:04,243 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 08:40:32,199 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:40:33,145 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 08:40:33,285 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,361 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,363 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,367 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:40:33,433 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 08:40:33,612 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 08:40:33,923 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 08:40:33,938 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 08:43:33,184 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:43:33,247 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:46:33,148 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:46:33,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:49:33,161 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:49:33,225 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:52:33,996 token_utils.verify_access_token  32 INFO    => access_token: 5c0308fe-de78-451d-9baf-4ed400a3050b
2024-07-13 08:52:34,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 08:55:24,643 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 08:55:33,606 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_check_user/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 93, in select_check_user
    sql_result, http_status = select_verify_user(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\_UserVerifyInfo.py", line 16, in select_verify_user
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 38, in verify_access_token
    if current_time >= ZEREFRESHEXPI:
TypeError: '>=' not supported between instances of 'datetime.datetime' and 'str'
2024-07-13 08:55:33,611 basehttp.log_message 161 ERROR   => "POST /api/users/select_check_user/ HTTP/1.1" 500 111878
2024-07-13 08:55:33,771 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 08:56:06,311 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 08:56:23,326 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 08:56:25,510 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 08:56:25,677 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 08:56:25,680 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121512
2024-07-13 08:56:26,784 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 08:56:26,785 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121512
2024-07-13 08:56:28,889 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 08:56:28,890 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121512
2024-07-13 08:56:33,007 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 08:56:33,008 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121512
2024-07-13 09:00:00,949 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:00:03,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-13 09:00:03,913 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 09:00:03,918 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121510
2024-07-13 09:00:05,023 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 09:00:05,024 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121510
2024-07-13 09:00:07,119 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 09:00:07,121 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121509
2024-07-13 09:00:11,254 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 193, in wrapper
    json_data, role, user_id, message, status_code = validation_func(request, required_params)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 88, in _common_validation
    is_valid, message, status_code = verify_access_token(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 42, in verify_access_token
    if not access_token or current_time >= ZEREFRESHEXPI - timedelta(minutes=set_before_refresh_token_access_minutes_expi()):
TypeError: unsupported operand type(s) for -: 'str' and 'datetime.timedelta'
2024-07-13 09:00:11,256 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 121511
2024-07-13 09:03:03,852 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-07-13 09:03:07,307 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:03:10,978 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 226, in wrapper
    validation_func = _common_validation
NameError: name '_common_validation' is not defined
2024-07-13 09:03:10,989 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 111174
2024-07-13 09:03:11,415 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 09:03:11,891 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 226, in wrapper
    validation_func = _common_validation
NameError: name '_common_validation' is not defined
2024-07-13 09:03:11,892 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 111174
2024-07-13 09:03:12,955 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 226, in wrapper
    validation_func = _common_validation
NameError: name '_common_validation' is not defined
2024-07-13 09:03:12,956 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 111174
2024-07-13 09:03:15,001 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 226, in wrapper
    validation_func = _common_validation
NameError: name '_common_validation' is not defined
2024-07-13 09:03:15,003 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 111174
2024-07-13 09:03:19,043 log.log_response 230 ERROR   => Internal Server Error: /api/users/select_user_tag_program_and_permission/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 181, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\views\decorators\csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 168, in select_user_tag_program_and_permission
    return self._handle_action('user_program_and_permission', 'select2')
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\apps\users\views.py", line 86, in _handle_action
    sql_result, http_status = USER_ACTIONS[resource][action](self.request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\utils\token_utils.py", line 226, in wrapper
    validation_func = _common_validation
NameError: name '_common_validation' is not defined
2024-07-13 09:03:19,044 basehttp.log_message 161 ERROR   => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 500 111174
2024-07-13 09:03:49,712 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:03:53,405 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:03:53,409 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 50268)

2024-07-13 09:03:53,702 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 09:03:54,163 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:03:54,489 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:03:54,568 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:03:54,924 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:03:54,953 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 09:04:00,627 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 09:04:00,803 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 09:04:07,656 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 09:04:07,806 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:04:07,941 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:04:08,092 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:04:08,404 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:04:08,437 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 09:05:56,502 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:06:01,503 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-07-13 09:06:01,658 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:06:01,951 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:06:01,987 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:06:02,372 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:06:02,427 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 09:07:20,340 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2024-07-13 09:07:20,340 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 79
2024-07-13 09:07:29,709 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 09:07:29,862 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:07:30,055 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:07:30,205 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:07:30,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 09:07:30,456 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:07:38,752 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 357
2024-07-13 09:07:38,890 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-07-13 09:07:39,007 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-13 09:07:39,062 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-13 09:07:39,135 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-13 09:07:39,189 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-13 09:07:39,260 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-13 09:07:39,299 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-13 09:07:39,367 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50734
2024-07-13 09:08:08,110 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-07-13 09:08:08,374 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 49194
2024-07-13 09:08:21,255 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 973
2024-07-13 09:08:21,335 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-07-13 09:08:21,539 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 19094
2024-07-13 09:08:22,814 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50734
2024-07-13 09:08:22,850 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-07-13 09:08:22,919 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2978
2024-07-13 09:08:22,974 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-07-13 09:08:23,022 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-07-13 09:08:23,064 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-07-13 09:08:23,137 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-07-13 09:09:31,814 log.log_response 230 WARNING => Unauthorized: /api/products/erp_hy_ocv015_main_product_detail/
2024-07-13 09:09:31,814 basehttp.log_message 161 WARNING => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 401 79
2024-07-13 09:18:41,634 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:19:35,600 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:20:26,653 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 09:20:26,828 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-07-13 09:20:27,066 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:20:27,228 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:20:27,527 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:20:27,581 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-07-13 09:22:44,748 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 09:22:44,954 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-07-13 09:22:50,719 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-07-13 09:22:53,183 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-07-13 09:22:53,459 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-07-13 09:22:53,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3212
2024-07-13 09:22:53,867 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-07-13 09:22:53,917 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
