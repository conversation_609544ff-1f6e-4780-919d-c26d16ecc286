2024-05-14 00:01:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:01:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:04:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:04:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:07:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:07:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:10:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:10:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:13:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:13:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:16:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:16:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:19:11,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:19:12,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:22:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:22:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:25:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:25:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:28:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:28:12,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:31:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:31:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:34:12,013 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 00:34:12,064 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:34:12,135 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:37:11,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:37:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:40:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:40:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:43:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:43:12,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:46:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:46:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:49:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:49:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:52:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:52:12,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:55:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:55:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 00:58:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 00:58:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:01:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:01:12,059 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:04:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:04:12,168 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:07:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:07:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:10:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:10:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:13:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:13:12,152 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:16:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:16:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:19:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:19:12,171 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:22:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:22:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:25:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:25:12,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:28:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:28:12,087 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-14 01:31:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:31:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:34:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:34:12,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:37:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:37:12,036 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:40:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:40:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:43:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:43:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:46:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:46:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:49:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:49:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:52:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:52:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:55:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:55:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 01:58:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 01:58:12,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:01:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:01:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:04:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:04:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:07:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:07:12,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:10:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:10:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:13:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:13:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:16:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:16:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:19:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:19:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:22:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:22:12,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:25:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:25:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:28:11,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:28:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:31:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:31:12,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:34:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:34:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:37:12,008 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 02:37:12,058 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:37:12,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:40:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:40:12,076 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:43:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:43:12,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:46:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:46:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:49:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:49:12,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:52:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:52:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:55:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:55:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 02:58:11,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 02:58:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:01:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:01:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:04:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:04:12,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:07:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:07:12,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:10:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:10:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:13:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:13:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:16:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:16:12,082 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:19:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:19:12,126 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:22:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:22:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:25:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:25:12,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:28:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:28:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:31:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:31:12,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:34:11,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:34:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:37:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:37:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:40:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:40:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:43:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:43:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:46:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:46:12,114 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:49:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:49:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:52:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:52:12,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:55:11,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:55:12,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 03:58:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 03:58:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:01:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:01:12,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:04:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:04:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:07:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:07:12,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:10:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:10:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:13:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:13:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:16:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:16:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:19:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:19:12,122 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:22:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:22:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:25:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:25:12,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:28:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:28:12,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:31:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:31:12,092 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:34:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:34:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:37:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:37:12,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:40:12,005 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 04:40:12,056 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:40:12,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:43:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:43:12,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:46:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:46:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:49:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:49:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:52:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:52:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:55:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:55:12,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 04:58:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 04:58:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:01:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:01:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:04:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:04:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:07:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:07:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:10:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:10:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:13:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:13:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:16:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:16:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:19:11,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:19:12,146 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-14 05:22:11,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:22:12,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:25:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:25:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:28:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:28:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:31:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:31:12,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:34:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:34:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:37:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:37:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:40:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:40:12,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:43:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:43:12,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:46:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:46:12,042 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:49:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:49:12,120 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:52:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:52:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:55:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:55:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 05:58:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 05:58:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:01:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:01:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:04:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:04:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:07:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:07:12,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:10:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:10:12,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:13:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:13:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:16:11,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:16:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:19:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:19:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:22:11,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:22:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:25:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:25:12,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:28:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:28:12,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:31:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:31:12,079 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:34:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:34:12,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:37:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:37:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:40:12,019 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:40:12,115 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:43:12,012 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 06:43:12,063 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:43:12,137 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:46:11,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:46:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:49:11,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:49:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:52:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:52:12,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:55:11,983 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:55:12,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 06:58:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 06:58:12,110 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:01:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:01:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:04:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:04:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:07:11,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:07:12,052 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:10:11,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:10:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:13:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:13:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:16:11,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:16:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:19:11,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:19:12,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:22:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:22:12,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:25:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:25:12,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:28:11,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:28:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:31:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:31:12,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:34:11,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:34:12,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:36:03,951 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 07:36:03,952 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 07:36:03,995 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:36:04,038 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:36:04,070 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 07:36:04,151 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 07:37:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:37:12,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:40:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:40:12,075 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:43:11,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:43:12,064 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:46:11,998 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:46:12,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:49:12,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:49:13,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:52:12,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:52:13,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:55:11,987 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:55:12,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 07:58:11,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 07:58:12,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:01:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:01:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:04:11,985 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:04:12,212 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:07:11,989 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:07:12,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:10:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:10:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:13:11,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:13:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:16:11,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:16:12,061 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:19:11,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:19:12,048 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:22:11,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:22:12,060 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:25:12,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:25:13,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:26:05,895 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:26:05,923 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:26:05,985 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 08:26:06,042 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 08:28:12,988 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:28:13,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:31:13,025 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:31:13,107 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:34:12,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:34:13,057 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:37:12,977 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:37:13,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:40:12,990 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:40:13,063 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:43:13,036 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 08:43:13,097 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:43:13,165 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:46:12,997 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:46:13,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:49:13,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:49:13,070 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:52:13,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:52:13,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:55:13,029 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:55:13,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 08:58:12,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 08:58:13,100 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:01:13,006 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:01:13,090 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:04:12,996 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:04:13,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:07:13,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:07:13,084 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:10:13,001 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:10:13,088 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-14 09:13:13,017 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:13:13,117 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:16:13,011 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:16:13,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:19:12,996 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:19:13,073 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:22:12,993 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:22:13,101 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:25:13,035 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:25:13,134 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:28:12,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:28:13,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:31:12,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:31:13,047 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:34:12,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:34:13,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:37:13,017 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:37:13,105 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:40:13,014 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:40:13,094 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:43:13,047 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:43:13,109 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:44:19,163 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 09:44:19,163 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 09:44:19,218 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:44:19,244 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:44:19,308 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 09:44:19,365 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 09:45:39,066 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,125 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-14 09:45:39,178 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,276 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,277 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,279 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,279 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,279 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,326 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 09:45:39,327 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,328 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,443 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,445 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,446 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,447 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,554 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-14 09:45:39,558 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:39,616 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-14 09:45:39,655 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-14 09:45:39,713 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 09:45:39,811 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-14 09:45:39,847 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-14 09:45:39,878 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-14 09:45:59,755 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-05-14 09:45:59,806 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:45:59,987 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44116
2024-05-14 09:46:11,978 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:46:12,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:47:22,799 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:22,863 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-14 09:47:22,928 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-14 09:47:22,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:23,099 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 09:47:23,779 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-05-14 09:47:23,832 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:24,046 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 25108
2024-05-14 09:47:58,510 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:58,576 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-05-14 09:47:59,310 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:59,397 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-14 09:47:59,456 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:47:59,615 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 09:48:01,833 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:48:01,934 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-14 09:48:01,992 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:48:02,114 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 09:48:03,145 basehttp.log_message 161 INFO    => "OPTIONS /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 0
2024-05-14 09:48:03,190 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:48:03,445 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-14 09:48:03,506 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:48:03,615 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 70
2024-05-14 09:48:48,882 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:48:49,032 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 09:49:11,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:49:12,026 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:52:11,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:52:12,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:53:31,150 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:53:31,214 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 09:53:31,267 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:53:31,389 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 09:53:32,388 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-05-14 09:53:32,471 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:53:37,188 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 419534
2024-05-14 09:55:11,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:55:12,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 09:58:11,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 09:58:12,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:01:12,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:01:13,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:04:12,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:04:13,051 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:07:12,958 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:07:13,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:10:12,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:10:13,058 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:13:12,958 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:13:13,050 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:16:12,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:16:13,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:19:12,986 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:19:13,071 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:22:12,959 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:22:13,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:25:12,969 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:25:13,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:28:12,968 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:28:13,066 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:31:12,969 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:31:13,038 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:34:10,445 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:10,472 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:10,533 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 10:34:10,590 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 10:34:11,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:11,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:12,044 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 10:34:12,095 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:34:12,102 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:12,146 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-14 10:34:12,204 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:12,257 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 10:34:12,265 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-14 10:34:14,847 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-14 10:34:14,902 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:34:15,175 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42433
2024-05-14 10:37:11,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:37:12,034 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:39:52,698 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-14 10:39:52,748 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:52,904 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2884
2024-05-14 10:39:57,532 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,655 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,656 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,656 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,657 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,658 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,786 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-14 10:39:57,791 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,823 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 10:39:57,829 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:57,889 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-14 10:39:57,951 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-14 10:39:57,988 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-14 10:39:58,045 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-14 10:39:58,114 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-14 10:39:58,125 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-14 10:39:59,376 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:39:59,542 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44116
2024-05-14 10:40:06,251 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:06,252 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:06,316 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-14 10:40:06,414 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:40:07,064 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:07,211 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 25108
2024-05-14 10:40:11,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:12,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:40:17,765 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:17,897 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:40:19,074 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:19,248 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 25108
2024-05-14 10:40:57,637 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:57,797 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 25108
2024-05-14 10:40:58,861 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 0
2024-05-14 10:40:58,906 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/select_path/ HTTP/1.1" 200 0
2024-05-14 10:40:58,934 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:58,950 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:40:59,062 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-05-14 10:40:59,114 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-05-14 10:41:02,506 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-05-14 10:41:02,555 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 0
2024-05-14 10:41:02,582 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 0
2024-05-14 10:41:02,582 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_serial/ HTTP/1.1" 200 0
2024-05-14 10:41:02,583 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 0
2024-05-14 10:41:02,626 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,627 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,628 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,629 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,642 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,770 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-05-14 10:41:02,818 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:02,867 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-14 10:41:02,899 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:41:02,992 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-14 10:41:03,052 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:41:03,194 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:41:28,853 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/update_order_input/ HTTP/1.1" 200 0
2024-05-14 10:41:28,904 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:28,989 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-05-14 10:41:31,052 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:31,220 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 26488
2024-05-14 10:41:33,434 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,501 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:41:33,558 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,623 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 10:41:33,694 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,696 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 10:41:33,698 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,698 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-14 10:41:33,745 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,746 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-14 10:41:33,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:33,901 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-14 10:41:34,050 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:41:34,076 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 10:41:34,166 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 10:41:35,495 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:35,622 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:41:36,174 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:36,331 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 26488
2024-05-14 10:41:42,545 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:42,548 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:42,549 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:42,549 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:41:42,815 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:41:42,830 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-14 10:41:42,856 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-14 10:41:42,888 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:44:33,588 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 10:44:33,632 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:44:33,699 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:45:28,919 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:29,013 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-05-14 10:45:31,067 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:31,226 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 26489
2024-05-14 10:45:34,905 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:34,909 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:35,018 basehttp.log_message 161 INFO    => "POST /api/dealers/select_path/ HTTP/1.1" 200 1274
2024-05-14 10:45:35,065 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv029_main_order_master/ HTTP/1.1" 200 828
2024-05-14 10:45:59,952 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:59,954 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:59,955 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:59,956 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:45:59,957 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:46:00,101 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_serial/ HTTP/1.1" 200 81
2024-05-14 10:46:00,115 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_product_sales_unit/ HTTP/1.1" 200 19355
2024-05-14 10:46:00,124 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:46:00,153 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:46:00,244 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_products_set_code_name/ HTTP/1.1" 200 199
2024-05-14 10:46:00,299 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:46:00,327 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:46:00,438 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:46:00,524 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:46:00,564 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:46:00,623 basehttp.log_message 161 INFO    => "POST /api/orders/erp_hy_ocv035_can_order_products_code_name/ HTTP/1.1" 200 3678
2024-05-14 10:46:59,714 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:46:59,797 basehttp.log_message 161 INFO    => "POST /api/orders/update_order_input/ HTTP/1.1" 200 69
2024-05-14 10:47:01,870 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:47:02,059 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 28739
2024-05-14 10:47:33,582 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:47:33,652 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:50:15,253 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:50:15,323 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-05-14 10:50:33,562 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:50:33,624 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:53:33,559 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:53:33,623 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:56:33,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:56:34,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 10:57:43,594 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:57:43,673 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-14 10:57:43,730 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:57:43,879 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:57:52,303 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:57:52,370 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-14 10:57:52,428 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:57:52,554 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:58:20,903 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:20,988 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-05-14 10:58:21,089 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_data2/ HTTP/1.1" 200 0
2024-05-14 10:58:21,090 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_line_notify/ HTTP/1.1" 200 0
2024-05-14 10:58:21,145 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:21,173 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:21,252 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 10:58:21,347 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 10:58:26,841 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:26,903 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 10:58:56,193 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:56,278 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 10:58:56,279 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:56,367 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-05-14 10:58:56,425 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:56,545 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 10:58:57,779 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:58:57,889 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 209
2024-05-14 10:59:32,890 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:59:32,893 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:59:33,012 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 10:59:33,019 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 10:59:33,565 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 10:59:33,645 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:00:06,441 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:06,442 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:06,508 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:06,618 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:12,865 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:12,866 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:12,945 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:13,077 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:20,775 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:20,776 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:20,848 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:20,961 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:32,766 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:32,767 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:32,895 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:32,905 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:34,683 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:34,742 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:52,629 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:52,629 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:52,705 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:52,813 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:56,802 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:56,803 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:56,875 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:00:56,989 basehttp.log_message 161 INFO    => "POST /api/users/select_user_data2/ HTTP/1.1" 200 349
2024-05-14 11:00:58,530 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:00:58,594 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:01:38,448 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:01:38,515 basehttp.log_message 161 INFO    => "POST /api/users/select_user_line_notify/ HTTP/1.1" 200 60
2024-05-14 11:01:46,060 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:01:46,181 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:01:58,627 basehttp.log_message 161 INFO    => "OPTIONS /api/pallet/select_pallet_account/ HTTP/1.1" 200 0
2024-05-14 11:01:58,680 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:02:05,869 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 21335
2024-05-14 11:02:33,576 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:02:33,662 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:03:49,027 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:03:49,168 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:04:06,325 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:04:13,613 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 21335
2024-05-14 11:05:33,583 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:05:33,654 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:08:33,573 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:08:33,653 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:08:54,526 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:08:54,670 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:09:28,547 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:09:36,419 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 21335
2024-05-14 11:11:33,583 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:11:33,649 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:12:09,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:12:10,115 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:14:18,085 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:14:18,157 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 11:14:33,591 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:14:33,672 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:15:59,788 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 11:16:27,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:16:35,322 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 23399
2024-05-14 11:17:33,588 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:17:33,653 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:20:33,591 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:20:33,719 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:22:14,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:22:15,019 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:23:21,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:23:22,091 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:23:33,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:23:34,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:24:21,439 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:24:21,585 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:24:28,956 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:24:29,101 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:24:54,683 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:24:54,817 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:25:03,718 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:25:03,842 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:25:33,968 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:25:34,151 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:25:40,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:25:41,082 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:25:49,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:25:50,152 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:25:55,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:25:56,127 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:26:07,979 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:26:08,142 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:26:33,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:26:34,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:26:42,831 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:26:42,948 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:27:10,002 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:27:10,127 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:27:18,722 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:27:18,842 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:27:24,513 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:27:24,644 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:27:32,413 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:27:32,542 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:27:56,197 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:27:56,340 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:08,790 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:08,941 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:15,251 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:15,375 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:21,861 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:21,995 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:24,004 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:24,140 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:29,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:30,106 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:28:39,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:28:40,097 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:29:33,982 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:29:34,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:30:29,700 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:30:29,764 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-05-14 11:30:30,410 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:30:30,545 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:31:02,750 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:31:02,884 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:31:11,273 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:31:11,396 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:31:44,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:31:45,156 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:32:13,394 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:32:20,842 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 23399
2024-05-14 11:32:33,561 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:32:33,642 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:33:04,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:05,106 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:33:15,607 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:15,770 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:33:45,618 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:45,744 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:33:52,129 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,192 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:33:52,242 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,323 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 11:33:52,394 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,470 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,471 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,473 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,531 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 203
2024-05-14 11:33:52,609 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-14 11:33:52,657 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:33:52,803 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:33:52,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 11:33:52,930 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 11:35:08,948 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:35:09,084 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:35:13,865 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:35:21,123 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 23399
2024-05-14 11:36:02,663 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:36:02,793 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:36:52,269 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:36:52,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:36:54,512 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:36:54,624 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:39:52,262 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:39:52,327 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:41:18,243 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:41:18,382 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:41:42,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:41:50,237 basehttp.log_message 161 INFO    => "POST /api/pallet/select_pallet_account/ HTTP/1.1" 200 23399
2024-05-14 11:42:52,262 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:42:52,327 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:45:52,280 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:45:52,366 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:48:52,264 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:48:52,328 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:51:52,269 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:51:52,353 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:54:52,260 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:54:52,344 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:57:24,175 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 11:57:24,237 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:57:24,303 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-14 11:57:24,371 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-14 11:57:24,426 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:57:24,561 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:57:26,082 basehttp.log_message 161 INFO    => "OPTIONS /api/orders/select_order_input/ HTTP/1.1" 200 0
2024-05-14 11:57:26,139 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:57:26,289 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 28739
2024-05-14 11:57:52,242 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:57:52,318 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 11:59:54,475 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:54,539 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-14 11:59:54,613 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:54,737 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 11:59:56,907 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:56,986 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-14 11:59:57,049 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,172 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,172 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,172 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,174 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,174 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,213 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,213 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2024-05-14 11:59:57,351 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,353 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,353 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,355 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,356 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,476 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv015_products_code_name/ HTTP/1.1" 200 50663
2024-05-14 11:59:57,481 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:57,514 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 11:59:57,572 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1236
2024-05-14 11:59:57,626 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 990
2024-05-14 11:59:57,698 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2799
2024-05-14 11:59:57,734 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 2780
2024-05-14 11:59:57,799 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 1331
2024-05-14 11:59:58,526 basehttp.log_message 161 INFO    => "OPTIONS /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 0
2024-05-14 11:59:58,587 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 11:59:58,784 basehttp.log_message 161 INFO    => "POST /api/products/erp_hy_ocv015_main_product_detail/ HTTP/1.1" 200 44116
2024-05-14 12:00:52,243 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:00:52,310 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:03:52,240 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:03:52,342 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:06:52,242 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:06:52,310 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:09:52,250 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:09:52,332 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:12:52,246 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:12:52,318 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:15:52,251 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:15:52,338 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:18:52,239 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:18:52,297 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:21:52,246 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:21:52,315 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:24:52,269 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:24:52,343 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:27:52,255 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:27:52,330 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:30:52,249 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:30:52,337 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:33:52,247 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:33:52,323 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:36:52,254 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:36:52,326 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:39:52,253 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:39:52,336 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:42:52,244 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:42:52,327 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:45:52,278 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 12:45:52,328 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:45:52,410 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:48:52,249 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:48:52,311 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:51:52,255 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:51:52,348 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:54:52,242 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:54:52,329 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 12:57:52,247 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 12:57:52,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:00:52,244 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:00:52,317 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-14 13:01:19,660 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:19,688 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:19,750 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 13:01:19,822 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-14 13:01:37,815 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:37,889 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 668
2024-05-14 13:01:37,943 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:38,093 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:01:38,812 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:38,973 basehttp.log_message 161 INFO    => "POST /api/orders/select_order_input/ HTTP/1.1" 200 28739
2024-05-14 13:01:51,343 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:51,410 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 378
2024-05-14 13:01:55,517 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:55,608 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-14 13:01:55,673 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:55,838 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:01:56,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:57,051 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-14 13:01:57,101 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:01:57,238 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:02:10,935 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:11,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 13:02:11,061 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:11,228 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:02:12,727 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:12,814 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2024-05-14 13:02:12,871 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:12,897 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2024-05-14 13:02:12,949 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:13,021 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 13:02:13,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-14 13:02:13,725 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:13,788 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 13:02:13,845 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:13,847 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:02:13,968 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 13:02:13,971 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-14 13:03:52,247 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:03:52,345 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:06:52,245 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:06:52,314 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:09:52,245 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:09:52,330 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:12:52,250 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:12:52,316 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:15:52,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:15:53,086 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:18:52,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:18:53,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:21:52,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:21:53,069 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:24:52,964 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:24:53,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:27:52,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:27:53,033 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:30:52,244 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:30:52,332 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:31:26,029 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:31:26,098 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 13:32:19,111 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:32:19,177 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 13:33:52,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:33:53,032 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:36:52,956 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:36:53,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:39:52,248 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:39:52,325 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:42:52,246 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:42:52,319 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:45:52,262 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:45:52,324 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:48:52,340 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:48:52,452 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:51:21,949 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:51:21,976 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:51:22,097 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:51:22,098 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-14 13:51:52,253 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:51:52,313 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:51:58,267 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:51:58,334 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 13:51:58,395 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:51:58,509 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:53:03,556 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:03,658 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:53:03,708 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:03,791 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 13:53:03,856 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:03,931 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 13:53:03,932 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-14 13:53:03,933 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-14 13:53:03,984 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:04,036 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:04,037 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:04,048 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 13:53:04,098 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:04,135 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1797
2024-05-14 13:53:04,343 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:53:04,413 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 13:53:04,464 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 13:53:34,706 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:34,790 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:53:34,842 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:34,909 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 13:53:34,969 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:34,971 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:34,972 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:34,973 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:35,039 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 13:53:35,092 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:53:35,248 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2024-05-14 13:53:35,345 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 13:53:35,363 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 13:53:35,407 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 13:56:24,818 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:56:24,891 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 13:56:24,943 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:56:24,944 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:56:25,061 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7117
2024-05-14 13:56:25,070 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2024-05-14 13:56:34,868 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:56:34,941 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 13:59:34,848 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 13:59:34,913 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:02:34,851 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:02:35,262 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:05:34,852 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:05:34,918 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:08:34,841 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:08:34,923 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:11:34,850 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:11:34,914 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:14:34,840 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:14:34,917 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:17:34,851 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:17:34,917 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:20:34,853 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:20:34,920 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:23:34,842 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:23:34,908 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:26:34,843 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:26:34,943 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:29:29,281 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 14:29:29,282 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 14:29:29,332 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:29:29,359 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:29:29,434 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 14:29:29,499 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 14:29:34,860 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:29:34,930 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:32:34,972 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:32:35,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:36:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:36:08,056 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:38:34,850 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:38:34,933 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:42:07,960 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:42:08,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:45:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:45:08,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:48:07,991 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 14:48:08,043 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:48:08,136 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:51:07,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:51:08,046 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:54:07,980 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:54:08,077 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 14:56:34,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 14:56:34,923 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:00:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:00:08,055 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:03:07,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:03:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:06:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:06:08,029 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:08:27,908 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:08:27,934 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:08:28,003 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 15:08:28,064 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 15:08:34,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:08:35,049 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:12:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:12:08,037 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:15:07,965 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:15:08,031 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:17:34,974 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:17:35,041 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:21:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:21:08,078 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:23:34,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:23:34,920 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:26:34,857 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:26:34,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:29:34,856 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:29:34,923 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:32:34,854 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:32:34,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:32:46,207 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:32:46,506 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 42433
2024-05-14 15:35:34,970 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:35:35,043 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:39:07,972 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:39:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:42:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:42:08,067 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:45:07,975 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:45:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:45:55,664 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:45:55,691 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:45:55,748 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 15:45:55,810 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 15:48:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:48:08,054 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:51:07,963 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:51:08,039 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:53:34,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:53:35,065 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:56:34,846 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:56:34,923 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 15:59:34,855 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 15:59:34,924 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:02:34,856 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:02:34,921 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:05:54,452 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:05:54,536 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:09:07,968 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:09:08,045 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:12:07,967 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:12:08,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:14:34,966 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:14:35,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:17:28,045 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:17:28,072 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:17:28,138 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 16:17:28,192 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 16:17:34,962 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:17:35,024 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:20:34,844 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:20:35,035 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:23:34,845 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:23:34,983 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:26:34,846 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:26:35,053 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:29:34,855 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:29:34,919 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:31:24,650 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2024-05-14 16:31:24,650 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2024-05-14 16:31:24,694 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:31:24,733 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:31:24,797 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 1272
2024-05-14 16:31:24,871 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-05-14 16:31:28,822 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:31:28,822 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:31:28,913 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 1272
2024-05-14 16:31:28,950 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 87
2024-05-14 16:32:28,846 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 16:32:28,890 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:28,959 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 16:32:29,026 basehttp.log_message 161 INFO    => "OPTIONS /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 0
2024-05-14 16:32:29,080 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:29,196 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:32:31,629 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:31,695 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 16:32:31,755 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:31,868 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:32:33,398 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:33,529 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:32:34,558 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:34,672 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:32:34,840 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:34,914 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:32:36,690 basehttp.log_message 161 INFO    => "OPTIONS /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 0
2024-05-14 16:32:36,747 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:32:37,088 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:35:34,849 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:35:34,917 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:36:43,718 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:36:44,049 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:38:34,857 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:38:34,929 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:39:30,713 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 16:39:35,554 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:39:35,959 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:40:24,223 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 16:40:25,659 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:40:26,122 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:41:34,844 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:41:34,908 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:42:42,722 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 16:42:43,756 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:42:44,145 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:43:38,836 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:43:38,992 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:43:39,881 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:43:40,196 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:44:01,892 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:02,043 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:44:04,778 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:05,119 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:44:18,372 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:18,585 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:44:20,075 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:20,389 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:44:30,181 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:30,377 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:44:34,868 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:34,945 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:44:44,438 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:44,573 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:44:45,874 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:44:46,283 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:45:04,197 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:45:04,373 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:45:04,385 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:45:04,716 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:45:24,465 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:45:24,595 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:45:25,824 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:45:26,156 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:46:10,278 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:46:10,421 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:47:34,864 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:47:34,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:50:34,887 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 16:50:34,936 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:50:35,003 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:52:52,679 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:52:52,759 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 16:52:52,811 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:52:53,160 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3062
2024-05-14 16:53:13,301 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:53:13,433 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:53:14,680 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:53:15,029 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3062
2024-05-14 16:53:34,863 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:53:34,931 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 81
2024-05-14 16:53:37,871 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:53:37,990 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:53:41,085 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:53:41,428 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:54:09,447 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:54:09,591 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:54:11,156 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:54:11,516 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:54:23,393 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:54:23,733 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 16:54:31,151 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:54:31,535 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3062
2024-05-14 16:54:43,465 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:54:43,584 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 16:56:34,981 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:56:35,062 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 16:59:35,014 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 16:59:35,099 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:02:34,991 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:02:35,074 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:04:22,830 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:04:22,918 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 17:04:32,129 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:04:32,486 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3062
2024-05-14 17:04:35,471 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:04:35,863 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 17:05:34,865 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:05:34,935 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:08:34,860 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:08:34,926 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:11:34,994 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:11:35,072 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:14:34,999 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:14:35,081 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:17:34,859 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:17:34,937 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:20:30,014 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:20:30,365 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 3056
2024-05-14 17:20:34,862 token_utils.verify_access_token  30 INFO    => refresh_token: e1a35216-c2e4-485d-9d6d-4090f5d06e9e
2024-05-14 17:20:34,927 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2024-05-14 17:20:56,329 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2024-05-14 17:20:56,452 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-05-14 17:20:56,592 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2024-05-14 17:21:02,997 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2024-05-14 17:21:03,137 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2024-05-14 17:21:03,203 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:03,272 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2024-05-14 17:21:03,344 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2024-05-14 17:21:03,420 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2024-05-14 17:21:03,422 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2024-05-14 17:21:03,423 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2024-05-14 17:21:03,470 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:03,547 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:03,551 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:03,553 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:03,686 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-14 17:21:03,832 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 2884
2024-05-14 17:21:03,933 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 17:21:04,044 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 17:21:06,909 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:06,973 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 345
2024-05-14 17:21:07,039 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:07,170 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:07,795 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:07,864 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 215
2024-05-14 17:21:07,927 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:08,039 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:09,163 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:09,238 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2024-05-14 17:21:09,300 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:09,441 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:10,505 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:10,623 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:14,565 basehttp.log_message 161 INFO    => "OPTIONS /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 0
2024-05-14 17:21:14,618 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:15,288 basehttp.log_message 161 INFO    => "POST /api/invoices/hy1_psbf_main_invoice_detail/ HTTP/1.1" 200 703330
2024-05-14 17:21:25,437 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:25,570 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:26,115 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:26,238 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:21:31,140 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:21:31,512 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 48923
2024-05-14 17:23:05,362 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:05,695 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 48923
2024-05-14 17:23:11,958 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 17:23:14,847 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:15,277 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 48923
2024-05-14 17:23:22,791 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2024-05-14 17:23:23,958 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:24,371 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 48923
2024-05-14 17:23:48,476 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:48,609 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:23:55,408 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:55,534 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:23:55,953 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:23:56,270 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 48923
2024-05-14 17:24:07,596 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:24:07,923 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 52199
2024-05-14 17:26:44,835 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:26:45,166 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:26:46,893 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:26:47,247 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 52199
2024-05-14 17:27:33,649 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:27:34,002 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 52199
2024-05-14 17:28:21,168 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,290 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,291 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,294 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,295 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,297 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:28:21,432 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:28:21,446 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2024-05-14 17:28:21,593 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:28:21,788 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2024-05-14 17:28:21,845 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2024-05-14 17:28:21,900 basehttp.log_message 161 INFO    => "POST /api/allowances/hy1_asea_main_subsidy_for_station_vending/ HTTP/1.1" 200 52199
2024-05-14 17:30:15,483 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:30:15,572 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 212
2024-05-14 17:30:15,635 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:30:15,761 basehttp.log_message 161 INFO    => "POST /api/dealers/combobox_erp_hy_ocv025_dealers_code_name/ HTTP/1.1" 200 24868
2024-05-14 17:30:16,767 basehttp.log_message 161 INFO    => "OPTIONS /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 0
2024-05-14 17:30:16,825 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:31:14,174 basehttp.log_message 161 INFO    => "POST /api/payments/erp_hy_arv201_300_main_payment_detail/ HTTP/1.1" 200 2514437
2024-05-14 17:31:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:31:21,751 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:34:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:34:21,753 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:37:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:37:21,751 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:40:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:40:21,759 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:43:21,675 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:43:21,753 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:46:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:46:21,739 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:49:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:49:21,762 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:52:21,691 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:52:21,756 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:55:21,678 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:55:21,743 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 17:58:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 17:58:21,741 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:01:21,750 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:01:21,813 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:04:21,694 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:04:21,759 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:07:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:07:21,737 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:10:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:10:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:13:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:13:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:16:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:16:21,745 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:19:21,679 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:19:21,742 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:22:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:22:21,747 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:25:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:25:21,783 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:28:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:28:21,739 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:31:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:31:21,763 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:34:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:34:21,744 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:37:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:37:21,742 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:40:21,678 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:40:21,740 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:43:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:43:21,762 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:46:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:46:21,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:49:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:49:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:52:21,706 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 18:52:21,785 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:52:21,851 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:55:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:55:21,743 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 18:58:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 18:58:21,751 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:01:21,673 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:01:21,736 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:04:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:04:21,765 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:07:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:07:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:10:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:10:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:13:21,680 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:13:21,825 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:16:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:16:21,739 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:19:21,698 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:19:21,760 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:22:21,703 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:22:21,768 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:25:21,679 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:25:21,744 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:28:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:28:21,746 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:31:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:31:21,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:34:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:34:21,754 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:37:21,673 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:37:21,740 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:40:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:40:21,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:43:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:43:21,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:46:21,690 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:46:21,757 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:49:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:49:21,743 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:52:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:52:21,779 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:55:21,690 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:55:21,756 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 19:58:21,671 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 19:58:21,734 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:01:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:01:21,745 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:04:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:04:21,761 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:07:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:07:21,767 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:10:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:10:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:13:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:13:21,746 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:16:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:16:21,754 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:19:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:19:21,780 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:22:21,689 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:22:21,753 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:25:21,675 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:25:21,738 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:28:21,691 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:28:21,757 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:31:21,680 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:31:21,757 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:34:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:34:21,748 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:37:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:37:21,744 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:40:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:40:21,744 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:43:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:43:21,741 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:46:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:46:21,771 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:49:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:49:21,758 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:52:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:52:21,737 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:55:21,708 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 20:55:21,757 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:55:21,821 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 20:58:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 20:58:21,775 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:01:21,673 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:01:21,757 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:04:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:04:21,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:07:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:07:21,750 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:10:21,697 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:10:21,793 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:13:21,689 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:13:21,760 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 76
2024-05-14 21:16:21,696 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:16:21,778 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:19:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:19:21,752 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:22:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:22:21,746 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:25:21,679 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:25:21,741 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:28:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:28:21,775 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:31:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:31:21,839 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:34:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:34:21,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:37:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:37:21,758 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:40:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:40:21,742 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:43:21,672 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:43:21,736 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:46:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:46:21,746 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:49:21,685 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:49:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:52:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:52:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:55:21,695 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:55:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 21:58:21,673 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 21:58:21,739 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:01:21,690 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:01:21,753 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:04:21,694 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:04:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:07:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:07:21,766 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:10:21,680 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:10:21,812 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:13:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:13:21,747 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:16:21,691 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:16:21,773 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:19:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:19:21,776 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:22:21,692 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:22:21,787 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:25:21,715 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:25:21,780 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:28:21,691 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:28:21,756 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:31:21,689 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:31:21,789 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:34:21,675 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:34:21,764 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:37:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:37:21,806 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:40:21,691 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:40:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:43:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:43:21,746 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:46:21,697 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:46:21,769 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:49:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:49:21,788 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:52:21,684 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:52:21,790 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:55:21,690 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:55:21,768 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 22:58:21,713 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2024-05-14 22:58:21,762 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 22:58:21,828 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:01:21,717 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:01:21,820 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:04:21,696 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:04:21,763 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:07:21,682 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:07:21,751 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:10:21,689 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:10:21,766 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:13:21,678 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:13:21,785 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:16:21,689 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:16:21,755 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:19:21,679 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:19:21,765 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:22:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:22:21,738 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:25:21,681 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:25:21,767 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:28:21,674 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:28:21,772 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:31:21,697 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:31:21,760 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:34:21,677 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:34:21,761 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:37:21,683 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:37:21,794 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:40:21,687 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:40:21,776 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:43:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:43:21,733 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:46:21,688 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:46:21,752 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:49:21,688 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:49:21,771 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:52:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:52:21,749 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:55:21,686 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:55:21,776 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
2024-05-14 23:58:21,676 token_utils.verify_access_token  30 INFO    => refresh_token: bf8a8c24-d354-4064-85fa-3911b8cfed90
2024-05-14 23:58:21,745 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 109
