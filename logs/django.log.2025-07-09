2025-07-09 11:12:01,869 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 11:12:02,128 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 11:12:02,129 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 11:12:02,137 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 11:12:02,137 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 11:12:02,137 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 11:12:02,137 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 11:12:02,138 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 11:12:02,138 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 11:12:02,156 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.docx  -  相容模式 - Word
2025-07-09 11:12:02,157 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: CS_FD-1.docx  -  相容模式 - Word
2025-07-09 11:12:03,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 11:12:04,228 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 11:12:04,501 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:12:16,597 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 11:12:16,767 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 11:12:22,396 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: Word
2025-07-09 11:12:32,649 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 11:12:32,760 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 11:12:32,760 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 11:12:32,769 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 11:12:32,769 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 11:12:32,769 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 11:12:32,769 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 11:12:32,770 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 11:12:32,770 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 11:12:33,677 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 11:12:33,753 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 11:12:33,965 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 83.6%
2025-07-09 11:12:34,233 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 11:12:34,927 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:12:37,203 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 11:12:43,223 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 11:12:45,531 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 11:12:48,514 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 11:12:51,088 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 11:12:51,475 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 11:12:52,022 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 11:12:58,752 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 11:13:02,141 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:13:04,659 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:13:04,846 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:13:05,255 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:13:05,383 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:13:17,194 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 11:13:17,235 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 11:13:23,924 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 11:13:27,693 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 11:13:32,789 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:13:35,631 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:13:35,699 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:13:36,119 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:13:36,215 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 11:14:02,173 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:14:06,372 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:14:06,475 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:14:06,842 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:14:06,949 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:14:32,924 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:14:36,925 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:14:37,048 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:14:37,541 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:14:37,685 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:15:02,249 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:15:07,771 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:15:07,852 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:15:08,328 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:15:08,395 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 11:15:32,997 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:15:38,481 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:15:38,627 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:15:39,044 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:15:39,186 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:16:02,286 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:16:09,046 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:16:09,046 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:16:09,426 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:16:33,055 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:16:39,170 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:16:39,170 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:16:39,561 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:17:02,333 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:17:09,312 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:17:09,312 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:17:09,688 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:17:33,058 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:17:39,451 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:17:39,451 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 11:17:40,005 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:18:02,344 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:18:09,813 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:18:10,031 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 11:18:10,416 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:18:10,503 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:18:33,062 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:18:40,692 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:18:40,813 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 11:18:41,024 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:18:41,024 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:19:02,364 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:19:11,095 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:19:11,095 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:19:11,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:19:11,512 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:19:33,122 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:19:41,386 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:19:41,423 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:19:41,967 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:20:02,447 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:20:11,583 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:20:11,584 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:20:12,098 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:20:33,133 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:20:41,947 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:20:42,075 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:20:42,533 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:20:42,633 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:21:02,481 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:21:12,804 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:21:12,806 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:21:13,067 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:21:33,138 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:21:42,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:21:42,941 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:21:43,579 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:21:43,674 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:22:02,502 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:22:13,396 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:22:13,474 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:22:14,246 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:22:33,225 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:22:43,986 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:22:43,986 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 11:22:44,773 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:22:44,774 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 11:23:02,517 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:23:14,720 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:23:15,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:23:15,483 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:23:33,231 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:23:45,305 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:23:45,871 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:24:02,536 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:24:07,545 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 11:24:07,546 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-07-09 11:24:15,796 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:24:15,858 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:24:16,450 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:24:16,526 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 11:24:20,908 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-07-09 11:24:21,983 token_utils.verify_access_token  42 ERROR   => Refresh token 無效
2025-07-09 11:24:22,010 log.log_response 230 WARNING => Unauthorized: /api/users/select_check_user/
2025-07-09 11:24:22,142 basehttp.log_message 161 WARNING => "POST /api/users/select_check_user/ HTTP/1.1" 401 77
2025-07-09 11:24:29,376 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-09 11:24:30,064 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-09 11:24:30,206 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 11:24:30,674 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 11:24:30,766 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 11:24:30,915 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-07-09 11:24:31,040 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-07-09 11:24:31,040 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-07-09 11:24:31,449 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 11:24:31,523 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1508
2025-07-09 11:24:31,844 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 11:24:32,026 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 11:24:33,245 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:24:36,440 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 11:24:36,501 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-07-09 11:24:36,529 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-07-09 11:24:36,852 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 11:24:36,992 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 11:24:43,394 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 11:24:46,393 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:24:46,394 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:24:47,223 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:24:47,301 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:24:53,044 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-07-09 11:24:53,044 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-07-09 11:24:53,607 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 11:24:53,607 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 11:24:59,637 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-07-09 11:25:00,329 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1114 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 11:25:00,998 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1183 INFO    => 嘗試使用企業級處理器
2025-07-09 11:25:01,179 _DownloadBusinessNotificationInfo.extract_data_worker 1164 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\dafcd71c-def4-4584-942c-5c087dc006aa.docx
2025-07-09 11:25:01,313 _DownloadBusinessNotificationInfo.extract_data_worker 1170 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 11:25:01,752 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1191 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 11:25:01,810 word_queue_processor.submit_task 161 INFO    => 任務已提交: 8ff3a3c3-861a-45ae-9acc-80436ed7280a
2025-07-09 11:25:01,810 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 8ff3a3c3-861a-45ae-9acc-80436ed7280a
2025-07-09 11:25:01,810 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1200 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\dafcd71c-def4-4584-942c-5c087dc006aa.docx
2025-07-09 11:25:02,537 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:25:04,381 _DownloadBusinessNotificationInfo.extract_table_data 651 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 11:25:05,974 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (1, 3)，表格大小: (3, 2)
2025-07-09 11:25:06,219 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (2, 3)，表格大小: (3, 2)
2025-07-09 11:25:06,537 _DownloadBusinessNotificationInfo.get_cell_text_and_color 565 WARNING => 單元格索引超出範圍: (3, 3)，表格大小: (3, 2)
2025-07-09 11:25:10,965 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-09 11:25:11,011 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (4, 3) 可能不存在或已合併
2025-07-09 11:25:11,060 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (5, 3) 可能不存在或已合併
2025-07-09 11:25:11,348 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (8, 3) 可能不存在或已合併
2025-07-09 11:25:11,402 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (9, 3) 可能不存在或已合併
2025-07-09 11:25:11,439 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (10, 3) 可能不存在或已合併
2025-07-09 11:25:11,796 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (18, 3) 可能不存在或已合併
2025-07-09 11:25:11,848 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (19, 3) 可能不存在或已合併
2025-07-09 11:25:11,945 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (21, 3) 可能不存在或已合併
2025-07-09 11:25:12,087 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (23, 3) 可能不存在或已合併
2025-07-09 11:25:12,272 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (27, 3) 可能不存在或已合併
2025-07-09 11:25:12,327 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (28, 3) 可能不存在或已合併
2025-07-09 11:25:12,485 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (31, 3) 可能不存在或已合併
2025-07-09 11:25:16,558 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:25:16,558 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 11:25:16,798 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (39, 3) 可能不存在或已合併
2025-07-09 11:25:16,945 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (41, 3) 可能不存在或已合併
2025-07-09 11:25:17,186 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (44, 3) 可能不存在或已合併
2025-07-09 11:25:17,251 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (45, 3) 可能不存在或已合併
2025-07-09 11:25:17,292 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (46, 3) 可能不存在或已合併
2025-07-09 11:25:17,421 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (48, 3) 可能不存在或已合併
2025-07-09 11:25:17,538 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (50, 3) 可能不存在或已合併
2025-07-09 11:25:17,900 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (55, 3) 可能不存在或已合併
2025-07-09 11:25:17,914 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:25:18,029 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 11:25:18,079 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (57, 3) 可能不存在或已合併
2025-07-09 11:25:18,368 _DownloadBusinessNotificationInfo.get_cell_text_and_color 579 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-09 11:25:18,598 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1203 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-09 11:25:18,685 word_queue_processor._process_tasks 120 INFO    => 任務完成: 8ff3a3c3-861a-45ae-9acc-80436ed7280a
2025-07-09 11:25:18,685 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1216 INFO    => 原始處理器執行成功
2025-07-09 11:25:18,685 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1225 INFO    => 成功提取 33 筆數據
2025-07-09 11:25:18,685 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1229 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-09 11:25:18,686 _DownloadBusinessNotificationInfo.save_to_excel 917 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 11:25:18,686 _DownloadBusinessNotificationInfo.save_to_excel 962 INFO    => 準備添加 33 筆數據到 Excel
2025-07-09 11:25:18,686 _DownloadBusinessNotificationInfo.save_to_excel 994 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 11:25:18,693 _DownloadBusinessNotificationInfo.save_to_excel 1049 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 11:25:18,738 _DownloadBusinessNotificationInfo.save_to_excel 1051 INFO    => Excel 文件保存成功
2025-07-09 11:25:18,738 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1231 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 11:25:18,741 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6444
2025-07-09 11:25:33,262 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:25:46,700 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:25:46,702 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 11:25:48,825 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:25:48,825 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:26:02,545 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:26:17,171 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:26:17,276 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:26:19,032 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:26:19,033 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:26:33,309 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:26:47,778 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:26:47,779 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 11:26:49,159 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:27:02,569 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:27:17,914 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:27:17,914 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 11:27:19,286 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:27:33,387 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:27:48,042 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:27:48,042 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 11:27:49,690 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:27:49,841 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:28:02,609 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:28:18,389 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:28:18,474 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:28:20,721 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:28:20,874 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 11:28:33,435 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:28:49,232 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:28:49,361 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:28:51,347 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:28:51,347 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:28:53,320 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: ✳ Word Excel 轉換
2025-07-09 11:29:02,642 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:29:19,752 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:29:19,753 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:29:21,508 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:29:33,482 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:29:50,140 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:29:50,239 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:29:52,004 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:29:52,148 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:30:02,668 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:30:21,005 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:30:21,084 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:30:22,880 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:30:22,977 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:30:33,548 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:30:51,725 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:30:51,726 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:30:53,295 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:30:53,295 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 11:31:02,707 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:31:22,061 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:31:22,173 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:31:23,477 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:31:23,478 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 11:31:33,659 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:31:52,863 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:31:52,981 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 11:31:53,916 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:31:54,020 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:32:02,729 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:32:23,435 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:32:24,658 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:32:24,659 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:32:33,746 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:32:53,578 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:32:53,578 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:32:54,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:33:02,760 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:33:23,816 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:33:23,817 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 11:33:24,927 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:33:33,802 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:33:54,050 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:33:54,050 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-07-09 11:33:55,061 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:34:02,809 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:34:24,391 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:34:24,437 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:34:25,430 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:34:25,430 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:34:33,877 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:34:55,142 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:34:55,142 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:34:55,567 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:35:02,823 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:35:25,298 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:35:25,300 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 11:35:25,715 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:35:33,884 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:35:55,440 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:35:55,440 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:35:55,892 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:35:55,892 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:36:02,832 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:36:25,875 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:36:26,009 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:36:26,294 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:36:26,385 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 11:36:33,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:36:56,621 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:36:56,738 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:36:56,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:36:56,913 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:37:02,859 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:52:50,737 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 11:52:50,798 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 11:52:50,798 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 11:52:50,806 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 11:52:50,806 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 11:52:50,806 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.docx  -  相容模式 - Word
2025-07-09 11:52:50,806 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 11:52:50,807 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 11:52:50,808 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 11:52:50,808 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 11:52:51,531 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 11:52:51,693 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 11:52:51,797 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:52:58,857 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 11:53:00,217 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 11:53:01,265 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 11:53:03,031 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 11:53:04,702 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 11:53:07,926 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 11:53:07,966 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 11:53:07,966 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 11:53:07,973 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 11:53:07,973 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 11:53:07,973 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 11:53:07,973 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 11:53:07,974 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 11:53:07,974 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 11:53:08,811 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 11:53:08,908 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 11:53:08,908 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-07-09 11:53:14,421 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 11:53:15,851 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 11:53:17,664 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 11:53:18,889 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 11:53:20,935 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 11:53:21,923 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:53:22,365 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 11:53:29,443 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 11:53:29,468 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 11:53:30,140 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 11:53:31,910 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 11:53:39,381 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:53:39,445 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:53:50,829 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:53:52,362 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:53:52,517 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 11:54:07,983 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:54:09,984 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:54:09,984 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:54:23,046 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:54:40,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:54:40,137 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:54:50,891 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:54:53,190 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:54:54,678 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 11:54:54,750 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 11:54:59,414 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 11:54:59,769 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 11:55:00,045 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 11:55:00,170 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 11:55:00,242 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 11:55:00,386 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 11:55:00,576 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 11:55:00,681 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 11:55:02,662 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 11:55:04,762 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 11:55:04,762 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 11:55:05,618 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1284 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 11:55:05,652 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1353 INFO    => 嘗試使用企業級處理器
2025-07-09 11:55:05,654 _DownloadBusinessNotificationInfo.extract_data_worker 1334 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\6d752d8c-c0f8-4984-87e2-bd0c5c7ddd04.docx
2025-07-09 11:55:05,804 _DownloadBusinessNotificationInfo.extract_data_worker 1340 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 11:55:05,810 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1361 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 11:55:05,810 word_queue_processor.submit_task 161 INFO    => 任務已提交: 5494c82b-e2e4-4a80-8909-8aa6de34a302
2025-07-09 11:55:05,810 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 5494c82b-e2e4-4a80-8909-8aa6de34a302
2025-07-09 11:55:05,810 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1370 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\6d752d8c-c0f8-4984-87e2-bd0c5c7ddd04.docx
2025-07-09 11:55:08,023 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:55:08,914 _DownloadBusinessNotificationInfo.extract_table_data 799 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 11:55:10,603 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:55:10,784 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 11:55:12,813 _DownloadBusinessNotificationInfo.extract_table_data 826 INFO    => 表格 1 結構: 3 行 x 2 列
2025-07-09 11:55:12,830 _DownloadBusinessNotificationInfo.extract_table_data 830 WARNING => 價格列 3 超出實際列數 2，嘗試使用替代策略
2025-07-09 11:55:20,621 _DownloadBusinessNotificationInfo.extract_table_data 826 INFO    => 表格 2 結構: 58 行 x 6 列
2025-07-09 11:55:23,328 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:55:41,126 _DownloadBusinessNotificationInfo.extract_table_data 826 INFO    => 表格 3 結構: 7 行 x 3 列
2025-07-09 11:55:41,633 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1373 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 11:55:41,718 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:55:41,718 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 11:55:41,752 word_queue_processor._process_tasks 120 INFO    => 任務完成: 5494c82b-e2e4-4a80-8909-8aa6de34a302
2025-07-09 11:55:41,752 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1386 INFO    => 原始處理器執行成功
2025-07-09 11:55:41,753 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1393 WARNING => 未提取到任何數據
2025-07-09 11:55:41,753 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1399 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 0
2025-07-09 11:55:41,753 _DownloadBusinessNotificationInfo.save_to_excel 1087 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 11:55:41,753 _DownloadBusinessNotificationInfo.save_to_excel 1132 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 11:55:41,753 _DownloadBusinessNotificationInfo.save_to_excel 1219 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 11:55:41,808 _DownloadBusinessNotificationInfo.save_to_excel 1221 INFO    => Excel 文件保存成功
2025-07-09 11:55:41,808 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1401 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 11:55:41,813 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5076
2025-07-09 11:55:50,952 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:55:53,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:56:08,046 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:56:11,964 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:56:11,964 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 11:56:23,816 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:56:23,924 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:56:42,310 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:56:42,402 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 11:56:51,018 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:56:54,433 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:56:54,526 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 11:57:08,085 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:57:12,937 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:57:12,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 11:57:25,080 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:57:25,080 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 11:57:43,233 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:57:43,297 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:57:51,041 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:57:55,487 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:57:55,612 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 11:57:59,955 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 11:58:08,144 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:58:13,781 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:58:13,781 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 11:58:26,091 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:58:43,923 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:58:43,924 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 11:58:51,122 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:58:56,556 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:58:56,641 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:59:08,197 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:59:14,477 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:59:14,569 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 11:59:27,055 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:59:27,163 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 11:59:45,301 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:59:45,394 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 11:59:51,156 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 11:59:57,934 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 11:59:58,067 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:00:08,251 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:00:16,376 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:00:16,460 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:00:28,526 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:00:46,876 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:00:46,876 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:00:51,215 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:00:58,652 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:00:59,994 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:01:08,260 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:01:17,234 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:01:17,235 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:01:28,879 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:01:28,879 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:01:47,494 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:01:47,612 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 12:01:51,317 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:01:59,181 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:02:08,294 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:02:17,891 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:02:17,894 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:02:29,484 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:02:42,061 word_queue_processor._process_tasks 135 WARNING => Word 應用程序無響應，重新初始化
2025-07-09 12:02:42,201 word_queue_processor._cleanup_word  93 ERROR   => 清理 Word 失敗: (-2147023174, 'RPC 伺服器無法使用。', None, None)
2025-07-09 12:02:45,223 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 12:02:48,431 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:02:51,356 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:03:02,135 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:03:08,343 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:03:18,700 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:03:18,735 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 84.0%
2025-07-09 12:03:32,588 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:03:49,120 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:03:49,183 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:03:51,392 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:04:03,211 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:04:04,416 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:04:08,416 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:04:19,342 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:04:33,572 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:04:33,626 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:04:49,624 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:04:51,440 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:05:04,130 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:05:04,194 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:05:08,448 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:05:20,135 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:05:20,136 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:05:34,554 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:05:34,555 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:05:50,517 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:05:50,539 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:05:51,517 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:06:05,071 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:06:05,122 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:06:08,461 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:06:20,782 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:06:35,905 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:06:51,064 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:06:51,568 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:07:00,006 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:07:06,590 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:07:08,469 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:07:21,534 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:07:36,956 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:07:51,572 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:07:51,827 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:08:08,546 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:08:08,705 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:08:21,977 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:08:21,977 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:08:39,071 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:08:39,124 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:08:51,716 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:08:52,195 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:09:08,672 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:09:09,582 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:09:24,979 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:09:41,395 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:09:51,813 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:09:57,078 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:10:00,670 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:10:08,913 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:10:12,094 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:10:27,219 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:10:42,395 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:10:51,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:10:57,361 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:10:57,363 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:11:08,999 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:11:12,742 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:11:12,787 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:11:27,646 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:11:27,662 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.2%
2025-07-09 12:11:43,160 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:11:51,988 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:11:58,130 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:11:58,130 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 12:12:09,048 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:12:13,615 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:12:28,430 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:12:28,466 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 12:12:43,955 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:12:52,022 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:12:58,843 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:12:58,843 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:12:59,974 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:13:09,102 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:13:14,095 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:13:29,038 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:13:29,038 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:13:44,433 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:13:44,506 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 12:13:52,091 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:13:59,256 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:13:59,258 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:14:09,170 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:14:14,915 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:14:29,646 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:14:29,721 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 12:14:45,178 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:14:52,117 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:15:00,231 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:15:00,250 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:15:09,217 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:15:15,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:15:30,576 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:15:30,593 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:15:45,508 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:15:45,509 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:15:52,183 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:16:01,087 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:16:01,087 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:16:09,246 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:16:15,669 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:16:31,384 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:16:31,442 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:16:41,023 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:16:45,913 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:16:45,915 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:16:52,226 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:17:01,686 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:17:09,248 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:17:16,122 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:17:31,922 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:17:31,923 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:17:46,462 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:17:52,239 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:18:02,402 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:18:09,288 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:18:17,043 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:18:32,977 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:18:32,993 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 12:18:47,555 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:18:47,606 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:18:52,254 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:19:03,584 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:19:09,332 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:19:17,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:19:30,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:19:33,917 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:19:33,919 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:19:48,164 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:19:52,268 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:20:04,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:20:09,749 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:20:19,055 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:20:34,714 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:20:49,344 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:20:49,364 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:20:52,300 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:21:05,025 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:21:09,915 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:21:19,835 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:21:35,324 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:21:50,294 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:21:50,312 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:21:52,340 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:21:59,714 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:22:05,518 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:22:05,519 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:22:09,933 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:22:21,619 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:22:27,667 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:22:27,712 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:22:27,712 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:22:27,722 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:22:27,722 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:22:27,722 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:22:27,722 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:22:27,722 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:22:27,722 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:22:28,336 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:22:28,613 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:22:28,944 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:22:33,230 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:22:35,577 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:22:36,773 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:22:38,066 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:22:38,501 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:22:42,066 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:22:42,097 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:22:42,097 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:22:42,102 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:22:42,102 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:22:42,102 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:22:42,102 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:22:42,103 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:22:42,103 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:22:42,658 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:22:42,813 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:22:43,119 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 12:22:48,268 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:22:49,653 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:22:51,640 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:22:54,168 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:22:55,388 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 12:22:55,605 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:22:59,559 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:23:01,602 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 12:23:01,613 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 12:23:02,279 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 12:23:04,309 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 12:23:13,880 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:23:27,723 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:23:29,890 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:23:30,047 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 12:23:42,131 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:23:44,274 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:23:44,367 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:24:00,627 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:24:14,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:24:14,503 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-09 12:24:27,725 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:24:30,975 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:24:31,144 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:24:42,168 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:24:44,774 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:24:44,866 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:25:01,696 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:25:15,333 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:25:27,753 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:25:31,992 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:25:32,084 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:25:41,408 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:25:42,183 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:25:45,895 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:25:46,086 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:26:02,480 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:26:16,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:26:16,600 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 12:26:27,826 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:26:32,457 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 12:26:32,567 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 12:26:32,737 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:26:32,828 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:26:35,296 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:26:35,718 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 12:26:36,528 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 12:26:36,893 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 12:26:37,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 12:26:37,170 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 12:26:37,243 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 12:26:37,313 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 12:26:40,727 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 12:26:42,209 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:26:42,600 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 12:26:42,641 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 12:26:43,677 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1253 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 12:26:43,714 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1322 INFO    => 嘗試使用企業級處理器
2025-07-09 12:26:43,715 _DownloadBusinessNotificationInfo.extract_data_worker 1303 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\a3d92395-f7c8-4460-b302-392695de1e72.docx
2025-07-09 12:26:43,984 _DownloadBusinessNotificationInfo.extract_data_worker 1309 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 12:26:43,993 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1330 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 12:26:43,993 word_queue_processor.submit_task 161 INFO    => 任務已提交: fb696fbb-107e-41ff-a5a7-3bfb828311ff
2025-07-09 12:26:43,993 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: fb696fbb-107e-41ff-a5a7-3bfb828311ff
2025-07-09 12:26:43,993 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1339 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\a3d92395-f7c8-4460-b302-392695de1e72.docx
2025-07-09 12:26:46,861 _DownloadBusinessNotificationInfo.extract_table_data 768 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 12:26:46,890 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:26:47,028 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:26:50,025 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 1 結構: 3 行 x 2 列
2025-07-09 12:26:50,063 _DownloadBusinessNotificationInfo.extract_table_data 799 WARNING => 價格列 3 超出實際列數 2，嘗試使用替代策略
2025-07-09 12:26:51,194 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 2 結構: 58 行 x 6 列
2025-07-09 12:27:03,509 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:27:03,736 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:27:08,819 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 3 結構: 7 行 x 3 列
2025-07-09 12:27:09,270 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1342 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 12:27:09,394 word_queue_processor._process_tasks 120 INFO    => 任務完成: fb696fbb-107e-41ff-a5a7-3bfb828311ff
2025-07-09 12:27:09,394 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1355 INFO    => 原始處理器執行成功
2025-07-09 12:27:09,394 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1362 WARNING => 未提取到任何數據
2025-07-09 12:27:09,394 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1368 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 0
2025-07-09 12:27:09,396 _DownloadBusinessNotificationInfo.save_to_excel 1056 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 12:27:09,396 _DownloadBusinessNotificationInfo.save_to_excel 1101 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 12:27:09,396 _DownloadBusinessNotificationInfo.save_to_excel 1188 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:27:09,433 _DownloadBusinessNotificationInfo.save_to_excel 1190 INFO    => Excel 文件保存成功
2025-07-09 12:27:09,434 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1370 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:27:09,439 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5076
2025-07-09 12:27:17,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:27:17,685 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:27:27,888 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:27:34,383 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:27:34,383 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:27:42,272 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:27:48,168 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:27:48,168 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:28:04,516 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:28:18,577 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:28:18,665 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:28:27,953 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:28:34,866 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:28:34,930 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 12:28:42,294 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:28:49,259 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:28:49,259 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 12:29:05,669 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:29:05,773 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:29:19,407 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:29:19,408 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:29:27,983 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:29:35,848 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:29:36,346 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:29:36,346 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:29:42,305 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:29:49,547 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:29:49,547 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:30:06,502 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:30:06,502 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:30:19,673 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:30:28,041 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:30:36,628 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:30:42,309 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:30:50,085 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:30:50,202 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:31:06,760 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:31:21,124 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:31:21,225 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 12:31:28,104 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:31:36,890 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:31:42,311 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:31:51,836 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:31:51,976 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:32:07,420 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:32:07,547 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:32:22,120 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:32:22,120 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:32:28,137 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:32:35,458 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:32:38,252 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:32:38,357 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:32:40,057 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1253 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 12:32:40,101 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1322 INFO    => 嘗試使用企業級處理器
2025-07-09 12:32:40,102 _DownloadBusinessNotificationInfo.extract_data_worker 1303 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\266b44d5-143c-4075-9b5b-ba9be9ab554a.docx
2025-07-09 12:32:40,102 _DownloadBusinessNotificationInfo.extract_data_worker 1309 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 12:32:40,103 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1330 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 12:32:40,103 word_queue_processor.submit_task 161 INFO    => 任務已提交: 4a778c55-93b1-4671-a5f3-3666a0abe256
2025-07-09 12:32:40,103 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 4a778c55-93b1-4671-a5f3-3666a0abe256
2025-07-09 12:32:40,103 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1339 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\266b44d5-143c-4075-9b5b-ba9be9ab554a.docx
2025-07-09 12:32:41,199 _DownloadBusinessNotificationInfo.extract_table_data 768 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 12:32:41,311 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 1 結構: 3 行 x 2 列
2025-07-09 12:32:41,311 _DownloadBusinessNotificationInfo.extract_table_data 799 WARNING => 價格列 3 超出實際列數 2，嘗試使用替代策略
2025-07-09 12:32:42,327 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:32:47,542 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 2 結構: 58 行 x 6 列
2025-07-09 12:32:52,292 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:32:52,292 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 12:33:04,999 _DownloadBusinessNotificationInfo.extract_table_data 795 INFO    => 表格 3 結構: 7 行 x 3 列
2025-07-09 12:33:05,434 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1342 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 12:33:05,519 word_queue_processor._process_tasks 120 INFO    => 任務完成: 4a778c55-93b1-4671-a5f3-3666a0abe256
2025-07-09 12:33:05,520 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1355 INFO    => 原始處理器執行成功
2025-07-09 12:33:05,520 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1362 WARNING => 未提取到任何數據
2025-07-09 12:33:05,520 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1368 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 0
2025-07-09 12:33:05,521 _DownloadBusinessNotificationInfo.save_to_excel 1056 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 12:33:05,521 _DownloadBusinessNotificationInfo.save_to_excel 1101 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 12:33:05,521 _DownloadBusinessNotificationInfo.save_to_excel 1188 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:33:05,555 _DownloadBusinessNotificationInfo.save_to_excel 1190 INFO    => Excel 文件保存成功
2025-07-09 12:33:05,555 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1370 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:33:05,560 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5076
2025-07-09 12:33:09,219 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:33:09,293 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 12:33:22,619 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:33:22,619 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:33:28,157 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:33:40,013 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:33:40,131 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:33:42,340 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:33:52,960 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:33:53,040 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:34:10,661 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:34:10,661 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:34:23,436 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:34:23,437 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:34:28,200 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:34:40,855 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:34:42,345 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:34:53,562 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:35:11,228 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:35:11,305 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:35:23,990 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:35:24,109 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:35:28,226 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:35:36,300 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:35:41,869 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:35:41,969 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:35:42,371 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:35:54,869 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:35:54,962 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:36:12,546 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:36:25,377 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:36:25,377 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:36:28,299 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:36:42,439 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:36:42,732 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:36:42,733 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:36:55,613 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:36:55,614 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:37:13,130 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:37:13,131 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:37:25,826 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:37:25,826 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:37:28,366 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:37:42,484 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:37:43,318 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:37:43,319 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:37:56,015 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:37:56,016 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:38:13,544 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:38:13,545 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:38:26,213 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:38:26,213 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:38:28,402 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:38:36,344 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:38:42,547 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:38:43,975 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:38:44,079 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:38:56,582 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:38:56,684 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:39:14,923 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:39:15,063 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:39:27,401 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:39:27,671 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:39:28,424 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:39:42,606 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:39:46,086 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:39:46,178 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:39:58,427 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:39:58,626 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:40:16,778 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:40:28,457 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:40:28,939 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:40:28,939 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:40:42,622 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:40:46,993 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:40:46,993 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:40:59,149 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:40:59,151 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:41:17,162 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:41:17,162 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:41:28,510 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:41:29,299 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:41:29,299 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:41:35,982 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:41:42,629 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:41:47,295 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:41:59,439 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:41:59,439 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:42:17,424 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:42:28,552 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:42:29,581 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:42:29,581 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 12:42:42,643 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:42:47,633 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:42:47,633 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:42:59,759 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:42:59,760 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:43:17,782 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:43:28,580 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:43:29,981 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:43:42,684 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:43:48,114 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:43:48,115 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:44:00,418 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:44:00,447 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:44:18,474 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:44:18,591 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:44:28,646 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:44:30,922 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:44:31,056 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:44:36,295 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:44:42,713 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:44:49,225 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:44:49,225 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:45:01,578 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:45:01,620 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 12:45:19,413 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:45:19,413 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:45:28,704 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:45:31,758 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:45:31,758 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:45:42,770 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:45:49,543 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:46:01,928 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:46:01,929 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:46:19,768 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:46:19,768 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:46:28,726 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:46:32,167 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:46:32,167 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:46:42,874 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:46:49,973 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:46:49,974 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 12:47:02,455 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:47:02,455 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 121.9%
2025-07-09 12:47:20,519 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:47:20,645 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 12:47:29,816 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:47:29,853 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:47:29,854 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:47:29,909 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:47:29,945 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:47:29,978 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:47:30,003 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:47:30,052 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:47:30,085 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:47:30,737 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:47:30,881 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:47:30,975 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:47:35,404 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:47:36,873 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:47:38,063 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:47:39,862 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:47:40,777 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:47:45,261 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:47:45,292 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:47:45,292 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:47:45,297 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:47:45,297 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:47:45,297 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:47:45,297 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:47:45,297 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:47:45,297 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:47:46,008 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:47:46,083 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:47:46,307 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 12:47:50,580 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:47:53,742 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:47:55,956 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:47:57,381 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:47:59,249 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:47:59,320 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 12:48:01,592 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:48:05,854 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 12:48:05,867 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 12:48:06,788 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 12:48:09,642 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 12:48:16,966 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:48:16,967 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:48:29,882 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:48:31,724 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:48:45,345 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:48:47,100 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:48:47,100 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 12:49:01,859 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:49:17,479 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:49:17,540 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:49:29,937 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:49:32,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:49:32,377 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:49:45,399 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:49:48,108 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:49:48,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:50:02,793 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:50:18,453 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:50:18,557 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:50:29,971 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:50:33,092 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:50:33,177 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:50:36,818 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:50:37,280 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-09 12:50:38,086 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1508
2025-07-09 12:50:38,117 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 12:50:38,291 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 12:50:38,382 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 12:50:39,226 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 12:50:39,401 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 12:50:43,253 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 12:50:44,495 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 12:50:44,594 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 12:50:45,422 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:50:45,577 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1075 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 12:50:45,619 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1144 INFO    => 嘗試使用企業級處理器
2025-07-09 12:50:45,619 _DownloadBusinessNotificationInfo.extract_data_worker 1125 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\b3899095-248d-444b-b274-209da5800c33.docx
2025-07-09 12:50:45,894 _DownloadBusinessNotificationInfo.extract_data_worker 1131 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 12:50:45,902 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1152 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 12:50:45,902 word_queue_processor.submit_task 161 INFO    => 任務已提交: 2bc0f9ce-45ea-4a60-9d97-bd4b35ce86ad
2025-07-09 12:50:45,902 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 2bc0f9ce-45ea-4a60-9d97-bd4b35ce86ad
2025-07-09 12:50:45,902 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1161 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\b3899095-248d-444b-b274-209da5800c33.docx
2025-07-09 12:50:49,216 _DownloadBusinessNotificationInfo.extract_table_data 612 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 12:50:49,403 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:50:49,559 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 12:51:02,912 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1164 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-09 12:51:03,000 word_queue_processor._process_tasks 120 INFO    => 任務完成: 2bc0f9ce-45ea-4a60-9d97-bd4b35ce86ad
2025-07-09 12:51:03,000 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1177 INFO    => 原始處理器執行成功
2025-07-09 12:51:03,000 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1186 INFO    => 成功提取 33 筆數據
2025-07-09 12:51:03,000 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1190 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-09 12:51:03,002 _DownloadBusinessNotificationInfo.save_to_excel 878 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 12:51:03,002 _DownloadBusinessNotificationInfo.save_to_excel 923 INFO    => 準備添加 33 筆數據到 Excel
2025-07-09 12:51:03,002 _DownloadBusinessNotificationInfo.save_to_excel 955 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 12:51:03,009 _DownloadBusinessNotificationInfo.save_to_excel 1010 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:51:03,050 _DownloadBusinessNotificationInfo.save_to_excel 1012 INFO    => Excel 文件保存成功
2025-07-09 12:51:03,050 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:51:03,055 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6443
2025-07-09 12:51:03,930 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:51:04,040 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:51:20,178 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:51:20,338 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:51:30,040 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:51:34,793 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:51:34,915 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 12:52:28,559 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:52:28,597 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:52:28,597 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:52:28,605 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:52:28,605 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:52:28,605 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:52:28,605 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:52:28,606 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:52:28,606 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:52:29,136 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:52:29,282 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:52:29,532 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 12:52:34,117 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:52:36,991 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:52:39,025 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:52:41,328 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:52:41,820 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:52:44,856 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 12:52:44,897 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 12:52:44,898 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 12:52:44,902 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 12:52:44,902 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 12:52:44,902 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 12:52:44,902 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 12:52:44,903 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 12:52:44,903 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 12:52:45,568 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 12:52:45,786 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 12:52:46,012 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:52:50,113 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 12:52:53,716 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 12:52:55,174 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 12:52:56,097 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 12:52:58,065 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 12:52:58,897 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 12:53:00,356 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:53:00,472 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 12:53:06,380 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 12:53:06,394 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 12:53:07,174 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 12:53:10,456 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 12:53:16,688 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:53:16,760 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:53:28,600 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:53:31,105 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:53:31,105 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:53:41,509 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:53:44,910 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:53:47,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:53:47,625 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-07-09 12:54:01,393 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:54:01,489 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:54:18,101 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:54:18,141 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:54:28,649 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:54:32,184 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:54:32,309 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 12:54:44,253 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:54:44,704 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 12:54:44,942 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:54:45,046 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 12:54:45,193 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 12:54:45,521 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 12:54:45,609 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 12:54:45,719 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 12:54:45,769 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 12:54:47,884 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 12:54:48,883 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:54:49,013 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:54:49,072 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 12:54:49,074 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 12:54:50,388 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1051 INFO    => 嘗試使用企業級處理器
2025-07-09 12:54:50,390 _DownloadBusinessNotificationInfo.extract_data_worker 1032 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\38b6a48f-d1f9-4081-a839-5158c35147fe.docx
2025-07-09 12:54:50,713 _DownloadBusinessNotificationInfo.extract_data_worker 1038 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 12:54:50,720 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1059 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 12:54:50,720 word_queue_processor.submit_task 161 INFO    => 任務已提交: 0e79386a-dccb-498e-afb7-7bda58e9b63d
2025-07-09 12:54:50,722 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 0e79386a-dccb-498e-afb7-7bda58e9b63d
2025-07-09 12:54:50,722 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1069 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\38b6a48f-d1f9-4081-a839-5158c35147fe.docx
2025-07-09 12:55:00,487 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-09 12:55:00,529 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (4, 3) 可能不存在或已合併
2025-07-09 12:55:00,602 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (5, 3) 可能不存在或已合併
2025-07-09 12:55:00,736 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (8, 3) 可能不存在或已合併
2025-07-09 12:55:00,767 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (9, 3) 可能不存在或已合併
2025-07-09 12:55:00,795 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (10, 3) 可能不存在或已合併
2025-07-09 12:55:01,355 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (18, 3) 可能不存在或已合併
2025-07-09 12:55:01,406 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (19, 3) 可能不存在或已合併
2025-07-09 12:55:01,581 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (21, 3) 可能不存在或已合併
2025-07-09 12:55:01,735 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (23, 3) 可能不存在或已合併
2025-07-09 12:55:02,065 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (27, 3) 可能不存在或已合併
2025-07-09 12:55:02,125 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (28, 3) 可能不存在或已合併
2025-07-09 12:55:02,356 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (31, 3) 可能不存在或已合併
2025-07-09 12:55:02,779 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:55:06,940 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (39, 3) 可能不存在或已合併
2025-07-09 12:55:07,062 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (41, 3) 可能不存在或已合併
2025-07-09 12:55:07,300 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (44, 3) 可能不存在或已合併
2025-07-09 12:55:07,368 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (45, 3) 可能不存在或已合併
2025-07-09 12:55:07,421 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (46, 3) 可能不存在或已合併
2025-07-09 12:55:07,608 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (48, 3) 可能不存在或已合併
2025-07-09 12:55:07,703 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (50, 3) 可能不存在或已合併
2025-07-09 12:55:07,917 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (55, 3) 可能不存在或已合併
2025-07-09 12:55:08,013 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (57, 3) 可能不存在或已合併
2025-07-09 12:55:08,153 _DownloadBusinessNotificationInfo.get_cell_text_and_color 588 WARNING => 單元格 (3, 3) 可能不存在或已合併
2025-07-09 12:55:08,353 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1072 INFO    => Legacy 處理器成功提取 55 筆數據
2025-07-09 12:55:08,473 word_queue_processor._process_tasks 120 INFO    => 任務完成: 0e79386a-dccb-498e-afb7-7bda58e9b63d
2025-07-09 12:55:08,473 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1086 INFO    => 原始處理器執行成功
2025-07-09 12:55:08,473 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1095 INFO    => 成功提取 55 筆數據
2025-07-09 12:55:09,152 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1100 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 12:55:09,479 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7128
2025-07-09 12:55:19,670 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:55:19,810 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 12:55:28,700 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:55:32,909 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:55:45,003 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:55:50,428 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:55:50,428 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:56:03,122 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:56:03,122 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 12:56:20,557 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:56:20,557 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:56:28,756 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:56:33,275 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:56:45,005 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:56:50,699 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:56:50,699 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 12:57:03,406 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:57:20,831 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:57:20,831 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:57:28,789 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:57:33,566 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:57:45,017 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:57:45,125 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 12:57:50,998 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:57:50,998 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 12:58:03,779 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:58:03,779 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:58:21,125 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:58:21,125 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 12:58:28,844 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:58:33,958 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:58:33,958 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 12:58:45,032 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:58:51,255 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:58:51,256 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:59:04,147 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:59:04,147 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 12:59:21,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:59:21,389 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 12:59:28,877 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:59:34,360 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:59:34,360 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-07-09 12:59:45,036 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 12:59:51,543 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 12:59:51,543 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 13:00:04,491 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:00:21,826 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:00:21,826 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:00:29,014 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:00:34,627 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:00:45,064 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:00:45,333 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:00:52,106 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:00:52,107 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:01:04,920 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:01:04,920 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:01:28,789 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:01:28,825 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:01:28,826 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:01:29,013 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:01:29,039 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:01:29,075 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:01:29,099 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:01:29,147 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:01:29,191 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:01:30,058 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:01:30,209 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:01:30,504 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:01:34,220 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:01:36,725 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:01:40,398 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:01:41,527 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:01:41,693 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:01:45,221 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:01:45,283 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:01:45,283 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:01:45,289 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:01:45,289 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:01:45,289 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:01:45,290 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:01:45,290 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:01:45,290 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:01:45,961 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:01:46,132 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:01:46,435 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.8%
2025-07-09 13:01:50,862 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:01:52,048 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:01:54,447 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:02:00,008 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:02:00,363 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 13:02:00,427 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:02:01,286 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:02:01,415 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:02:08,613 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 13:02:08,628 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 13:02:13,686 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 13:02:16,032 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 13:02:17,278 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:02:17,382 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:02:28,866 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:02:32,154 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:02:32,306 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:02:45,287 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:02:48,185 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:02:48,366 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-07-09 13:03:03,092 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:03:03,186 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:03:18,852 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:03:18,853 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:03:28,920 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:03:33,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:03:45,323 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:03:46,504 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:03:46,568 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61000)

2025-07-09 13:03:47,044 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:03:47,181 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 13:03:47,345 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 13:03:47,456 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 13:03:47,689 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 13:03:47,848 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 13:03:48,032 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 13:03:48,057 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 13:03:49,095 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:03:49,199 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 13:03:50,653 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 13:03:52,869 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 13:03:52,952 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 13:03:54,970 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1100 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 13:03:55,530 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1169 INFO    => 嘗試使用企業級處理器
2025-07-09 13:03:55,628 _DownloadBusinessNotificationInfo.extract_data_worker 1150 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\afdbd557-bf31-4e39-a481-c4330cc22c80.docx
2025-07-09 13:03:55,759 _DownloadBusinessNotificationInfo.extract_data_worker 1156 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 13:03:56,338 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1177 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 13:03:56,374 word_queue_processor.submit_task 161 INFO    => 任務已提交: 865f7e51-caca-44e7-a9fb-d941ee59a18b
2025-07-09 13:03:56,387 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 865f7e51-caca-44e7-a9fb-d941ee59a18b
2025-07-09 13:03:56,426 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1187 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\afdbd557-bf31-4e39-a481-c4330cc22c80.docx
2025-07-09 13:03:58,359 _DownloadBusinessNotificationInfo.extract_table_data 622 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 13:04:03,776 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:04:10,738 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1190 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-09 13:04:10,846 word_queue_processor._process_tasks 120 INFO    => 任務完成: 865f7e51-caca-44e7-a9fb-d941ee59a18b
2025-07-09 13:04:10,846 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1204 INFO    => 原始處理器執行成功
2025-07-09 13:04:10,847 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1213 INFO    => 成功提取 33 筆數據
2025-07-09 13:04:10,847 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1217 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-09 13:04:10,847 _DownloadBusinessNotificationInfo.save_to_excel 898 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 13:04:10,847 _DownloadBusinessNotificationInfo.save_to_excel 944 INFO    => 準備添加 33 筆數據到 Excel
2025-07-09 13:04:10,847 _DownloadBusinessNotificationInfo.save_to_excel 976 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 13:04:10,854 _DownloadBusinessNotificationInfo.save_to_excel 1032 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:04:10,895 _DownloadBusinessNotificationInfo.save_to_excel 1034 INFO    => Excel 文件保存成功
2025-07-09 13:04:10,895 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1219 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:04:10,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6443
2025-07-09 13:04:19,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:04:20,051 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-07-09 13:04:28,991 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:04:33,910 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:04:45,365 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:04:50,766 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:04:50,882 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:05:04,234 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:05:04,336 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:05:21,328 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:05:21,328 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:05:29,075 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:05:35,040 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:05:35,098 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:05:45,446 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:05:51,696 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:05:51,832 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:06:05,947 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:06:06,022 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 13:06:22,690 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:06:22,750 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:06:29,153 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:06:36,519 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:06:45,529 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:06:48,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:06:53,491 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:06:53,563 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:07:06,653 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:07:23,831 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:07:23,831 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:07:29,247 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:07:36,779 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:07:45,584 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:07:53,997 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:07:53,997 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:08:06,907 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:08:24,230 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:08:24,231 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.8%
2025-07-09 13:08:29,297 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:08:37,437 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:08:37,529 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 13:08:45,637 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:08:54,398 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:08:54,398 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:09:08,234 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:09:08,235 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:09:24,764 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:09:24,854 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:09:29,307 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:09:38,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:09:45,670 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:09:48,173 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:09:55,469 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:09:55,529 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:10:08,616 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:10:08,617 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:10:36,455 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:10:36,494 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:10:36,494 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:10:36,501 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:10:36,501 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:10:36,501 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:10:36,501 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:10:36,534 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:10:36,581 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:10:37,033 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:10:37,204 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:10:37,562 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:10:41,705 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:10:44,169 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:10:46,364 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:10:48,639 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:10:49,124 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:10:52,014 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:10:52,051 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:10:52,052 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:10:52,056 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:10:52,056 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:10:52,056 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:10:52,056 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:10:52,056 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:10:52,056 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:10:52,685 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:10:52,792 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:10:53,162 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:10:57,835 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:10:59,717 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:11:01,917 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:11:05,093 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:11:06,623 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:11:06,793 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 13:11:08,386 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:11:08,498 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:11:14,241 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 13:11:14,252 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 13:11:19,288 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 13:11:22,872 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 13:11:23,920 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:11:24,000 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:11:36,525 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:11:38,806 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:11:52,084 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:11:54,436 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:12:08,943 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:12:21,510 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:12:21,793 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 13:12:22,011 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 13:12:22,112 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 13:12:22,288 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 13:12:22,438 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 13:12:22,449 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 13:12:22,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 13:12:24,786 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:12:24,838 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.5%
2025-07-09 13:12:36,558 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:12:39,243 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:12:39,333 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:12:52,089 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:12:55,418 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:12:55,500 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:13:10,208 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:13:10,298 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:13:26,124 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:13:26,186 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:13:36,566 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:13:40,926 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:13:40,975 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:13:52,113 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:13:56,709 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:13:56,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:14:11,384 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:14:26,972 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:14:27,065 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:14:36,603 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:14:41,721 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:14:41,824 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:14:52,156 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:14:57,465 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:14:57,466 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:15:12,314 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:15:22,322 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:15:27,717 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:15:27,804 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:15:36,667 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:15:42,729 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:15:42,875 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:15:52,166 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:15:58,397 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:15:58,398 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:16:13,385 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:16:13,385 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:16:28,533 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:16:28,533 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:16:36,710 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:16:43,512 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:16:52,174 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:16:58,837 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:16:59,014 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:17:13,903 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:17:29,676 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:17:29,782 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:17:36,758 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:17:44,543 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:17:44,621 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:17:52,177 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:18:00,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:18:00,315 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:18:14,911 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:18:14,911 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:18:22,219 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:18:30,490 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:18:30,491 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:18:36,777 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:18:45,047 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:18:52,222 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:19:00,851 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:19:00,940 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:19:15,477 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:19:15,593 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 13:19:31,095 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:19:31,095 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:19:36,813 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:19:45,934 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:19:45,934 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 13:19:52,247 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:20:01,260 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:20:01,261 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:20:16,062 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:20:31,407 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:20:31,407 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:20:36,858 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:20:46,200 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:20:52,279 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:21:01,826 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:21:01,892 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:21:16,617 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:21:16,733 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:21:22,002 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:21:32,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:21:32,504 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:21:36,901 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:21:47,217 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:21:52,299 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:22:02,645 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:22:02,645 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:22:17,348 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:22:32,786 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:22:32,787 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:22:36,929 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:22:47,540 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:22:52,385 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:23:03,217 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:23:03,349 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:23:18,027 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:23:18,044 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:23:33,993 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:23:37,054 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:23:48,846 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:23:52,606 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:24:04,331 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:24:19,293 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:24:23,133 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-07-09 13:24:23,511 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:24:36,673 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:24:37,122 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:24:49,685 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:24:49,724 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:24:52,621 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:25:07,031 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:25:07,186 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 109.5%
2025-07-09 13:25:21,722 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:25:37,154 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:25:37,602 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:25:37,602 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:25:52,170 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:25:52,266 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 13:25:52,649 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:26:07,924 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:26:07,992 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:26:22,683 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:26:37,199 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:26:38,318 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:26:38,398 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 13:26:52,762 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:26:52,944 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:27:08,875 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:27:08,876 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:27:22,293 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:27:23,078 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:27:37,256 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:27:39,002 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:27:39,003 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:27:52,789 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:27:53,207 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:28:09,392 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:28:09,450 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:28:23,356 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:28:37,315 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:28:40,162 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:28:40,199 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:28:52,881 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:28:53,663 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:29:10,722 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:29:24,199 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:29:24,281 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:29:37,350 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:29:40,893 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:29:40,895 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:29:52,890 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:29:54,960 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:30:11,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:30:11,248 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 13:30:22,091 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:30:25,333 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:30:37,395 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:30:41,580 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:30:41,625 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:30:52,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:30:55,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:31:11,946 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:31:11,946 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:31:25,609 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:31:37,410 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:31:42,372 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:31:42,374 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:31:52,972 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:31:55,751 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:32:12,666 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:32:12,667 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:32:25,876 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:32:37,420 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:32:43,104 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:32:53,012 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:32:56,015 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:33:13,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:33:13,271 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:33:26,154 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:33:37,434 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:33:41,213 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:33:43,401 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:33:43,402 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:33:53,071 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:33:56,579 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:33:56,692 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:34:13,529 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:34:13,530 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:34:27,326 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:34:27,438 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:34:37,453 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:34:43,659 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:34:53,149 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:34:58,087 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:34:58,088 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:35:14,070 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:35:14,206 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:35:28,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:35:37,464 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:35:44,933 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:35:44,991 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:35:53,203 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:35:59,079 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:35:59,165 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:36:15,665 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:36:15,753 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:36:29,845 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:36:29,847 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:36:37,472 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:36:41,577 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:36:46,428 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:36:46,574 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:36:53,235 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:37:00,328 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:37:00,389 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-07-09 13:37:17,476 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:37:17,629 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:37:30,879 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:37:31,014 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:37:37,495 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:37:48,138 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:37:48,138 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:37:53,324 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:38:01,519 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:38:18,486 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:38:18,618 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:38:31,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:38:37,543 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:38:48,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:38:48,990 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:38:53,425 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:39:01,780 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:39:19,117 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:39:19,118 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 13:39:31,914 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:39:37,559 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:39:41,361 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:39:49,258 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:39:49,259 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:39:53,507 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:40:02,247 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:40:02,379 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 80.1%
2025-07-09 13:40:19,399 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:40:19,399 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:40:32,750 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:40:37,568 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:40:49,536 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:40:49,536 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:40:53,617 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:41:02,880 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:41:19,867 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:41:19,868 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:41:33,018 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:41:37,603 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:41:50,004 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:41:50,004 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:41:53,660 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:42:03,158 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:42:20,438 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:42:20,873 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-07-09 13:42:22,030 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:42:33,513 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:42:33,572 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:42:37,681 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:42:51,330 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:42:51,331 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 13:42:53,722 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:43:04,093 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:43:04,150 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:43:21,686 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:43:21,854 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:43:34,284 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:43:37,754 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:43:52,070 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:43:52,070 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:43:53,732 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:44:04,425 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:44:22,208 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:44:22,208 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:44:34,561 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:44:37,816 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:44:52,340 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:44:52,342 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:44:53,758 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:44:55,127 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 13:44:55,165 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 13:44:55,698 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 13:44:55,745 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 13:44:57,239 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-07-09 13:44:57,749 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 13:44:59,669 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-07-09 13:44:59,669 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-07-09 13:45:00,411 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-07-09 13:45:00,441 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 13:45:00,544 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 13:45:01,214 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1136 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 13:45:01,703 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1205 INFO    => 嘗試使用企業級處理器
2025-07-09 13:45:01,704 _DownloadBusinessNotificationInfo.extract_data_worker 1186 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\8bd7ad92-9403-48ca-8528-4739b33446b6.docx
2025-07-09 13:45:01,851 _DownloadBusinessNotificationInfo.extract_data_worker 1192 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 13:45:01,860 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1213 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 13:45:01,860 word_queue_processor.submit_task 161 INFO    => 任務已提交: f0641ef7-5846-40d2-b437-1e4212c58134
2025-07-09 13:45:01,860 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: f0641ef7-5846-40d2-b437-1e4212c58134
2025-07-09 13:45:01,860 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1223 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\8bd7ad92-9403-48ca-8528-4739b33446b6.docx
2025-07-09 13:45:03,971 _DownloadBusinessNotificationInfo.extract_table_data 622 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 13:45:04,696 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:45:17,686 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1226 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-09 13:45:17,774 word_queue_processor._process_tasks 120 INFO    => 任務完成: f0641ef7-5846-40d2-b437-1e4212c58134
2025-07-09 13:45:17,775 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 原始處理器執行成功
2025-07-09 13:45:17,775 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1249 INFO    => 成功提取 33 筆數據
2025-07-09 13:45:17,775 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1253 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-09 13:45:17,776 _DownloadBusinessNotificationInfo.save_to_excel 934 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 13:45:17,776 _DownloadBusinessNotificationInfo.save_to_excel 980 INFO    => 準備添加 33 筆數據到 Excel
2025-07-09 13:45:17,776 _DownloadBusinessNotificationInfo.save_to_excel 1012 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 13:45:17,781 _DownloadBusinessNotificationInfo.save_to_excel 1068 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:45:17,814 _DownloadBusinessNotificationInfo.save_to_excel 1070 INFO    => Excel 文件保存成功
2025-07-09 13:45:17,814 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1255 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:45:17,818 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6443
2025-07-09 13:45:22,007 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:45:22,611 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:45:22,612 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 13:45:35,102 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:45:35,180 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-07-09 13:45:37,855 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:45:49,614 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:45:49,667 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:45:49,667 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:45:49,677 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:45:49,677 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:45:49,677 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:45:49,677 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:45:49,678 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:45:49,678 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:45:50,422 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:45:50,562 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:45:50,824 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:45:54,898 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:45:58,167 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:45:59,381 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:46:00,109 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:46:02,169 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:46:05,130 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:46:05,161 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:46:05,162 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:46:05,165 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:46:05,165 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:46:05,165 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:46:05,165 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:46:05,165 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:46:05,165 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:46:06,009 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:46:06,222 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:46:06,421 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:46:10,899 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:46:13,141 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:46:15,594 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:46:16,648 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:46:18,273 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:46:20,888 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 13:46:21,551 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:46:21,637 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:46:25,354 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 13:46:25,578 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 13:46:28,890 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 13:46:32,209 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 13:46:37,077 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:46:37,078 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:46:49,674 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:46:52,397 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:46:52,596 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:47:05,179 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:47:07,374 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:47:07,467 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:47:23,220 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:47:38,181 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:47:38,302 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:47:49,686 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:47:53,643 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:47:53,643 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 13:48:05,204 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:48:08,766 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:48:08,767 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:48:23,991 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:48:24,070 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:48:39,110 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:48:39,248 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:48:49,728 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:48:54,530 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:48:57,068 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:48:57,131 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 49508)

2025-07-09 13:48:57,527 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:48:57,995 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 13:48:58,237 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-07-09 13:48:58,239 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-07-09 13:48:58,264 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 13:48:58,415 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-07-09 13:48:58,439 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-07-09 13:48:58,966 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 13:48:59,035 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 13:48:59,043 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 13:48:59,251 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 13:48:59,400 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 13:49:00,770 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 13:49:02,959 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 13:49:03,090 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 13:49:03,896 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1136 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 13:49:03,934 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1205 INFO    => 嘗試使用企業級處理器
2025-07-09 13:49:03,934 _DownloadBusinessNotificationInfo.extract_data_worker 1186 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\97b6e5bc-94dc-433e-ae3b-8aa40380547b.docx
2025-07-09 13:49:04,160 _DownloadBusinessNotificationInfo.extract_data_worker 1192 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 13:49:04,171 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1213 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 13:49:04,173 word_queue_processor.submit_task 161 INFO    => 任務已提交: 621f705e-f162-4b31-8d76-ab037f84ba6e
2025-07-09 13:49:04,173 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 621f705e-f162-4b31-8d76-ab037f84ba6e
2025-07-09 13:49:04,173 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1223 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\97b6e5bc-94dc-433e-ae3b-8aa40380547b.docx
2025-07-09 13:49:05,242 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:49:07,430 _DownloadBusinessNotificationInfo.extract_table_data 622 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 13:49:09,843 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:49:10,012 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 13:49:21,193 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1226 INFO    => Legacy 處理器成功提取 33 筆數據
2025-07-09 13:49:21,324 word_queue_processor._process_tasks 120 INFO    => 任務完成: 621f705e-f162-4b31-8d76-ab037f84ba6e
2025-07-09 13:49:21,324 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 原始處理器執行成功
2025-07-09 13:49:21,325 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1249 INFO    => 成功提取 33 筆數據
2025-07-09 13:49:21,325 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1253 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 33
2025-07-09 13:49:21,326 _DownloadBusinessNotificationInfo.save_to_excel 934 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 13:49:21,326 _DownloadBusinessNotificationInfo.save_to_excel 980 INFO    => 準備添加 33 筆數據到 Excel
2025-07-09 13:49:21,326 _DownloadBusinessNotificationInfo.save_to_excel 1012 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 13:49:21,334 _DownloadBusinessNotificationInfo.save_to_excel 1068 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:49:21,373 _DownloadBusinessNotificationInfo.save_to_excel 1070 INFO    => Excel 文件保存成功
2025-07-09 13:49:21,374 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1255 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:49:21,380 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6443
2025-07-09 13:49:24,938 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:49:25,049 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:49:40,650 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:49:49,799 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:49:55,256 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:50:05,250 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:50:11,183 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:50:11,321 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:50:25,421 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:50:25,421 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:50:42,233 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:50:42,439 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:50:49,868 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:50:55,730 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:50:55,731 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:51:05,297 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:51:13,104 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:51:13,228 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:51:26,211 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:51:26,293 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:51:43,731 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:51:43,731 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 13:51:49,906 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:51:57,106 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:51:57,229 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:52:05,315 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:52:13,866 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:52:13,866 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:52:27,674 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:52:44,134 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:52:44,134 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-09 13:52:49,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:52:57,802 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:53:05,351 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:53:14,413 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:53:14,414 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:53:28,122 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:53:28,122 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 13:53:44,840 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:53:44,953 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-07-09 13:53:48,274 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:53:49,980 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:54:02,529 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:54:02,570 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:54:02,572 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:54:02,700 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:54:02,711 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:54:02,776 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:54:02,776 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:54:02,776 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:54:02,776 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:54:03,475 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:54:03,647 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:54:03,948 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:54:07,950 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:54:09,083 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:54:11,628 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:54:13,879 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:54:14,110 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:54:16,776 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 13:54:16,817 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 13:54:16,817 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 13:54:16,822 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 13:54:16,822 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 13:54:16,822 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 13:54:16,822 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 13:54:16,823 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 13:54:16,823 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 13:54:17,769 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 13:54:17,895 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 13:54:18,117 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 137.5%
2025-07-09 13:54:22,287 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 13:54:25,481 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 13:54:27,151 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 13:54:28,203 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 13:54:29,405 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 13:54:30,298 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 13:54:34,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:54:36,567 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 13:54:36,578 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 13:54:37,221 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 13:54:39,082 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 13:54:48,699 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:55:02,580 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:55:04,765 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:55:04,878 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:55:16,837 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:55:19,027 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:55:19,146 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:55:35,577 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:55:35,734 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 13:55:49,815 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:56:02,597 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:56:06,417 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:56:06,417 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 13:56:08,300 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:56:11,169 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:56:11,752 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 13:56:12,652 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 13:56:13,317 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 13:56:13,318 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 13:56:13,322 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 13:56:13,327 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 13:56:13,342 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 13:56:15,502 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 52319
2025-07-09 13:56:16,874 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:56:17,294 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50245
2025-07-09 13:56:18,448 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 13:56:18,531 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 13:56:19,365 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1121 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 13:56:19,401 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1190 INFO    => 嘗試使用企業級處理器
2025-07-09 13:56:19,402 _DownloadBusinessNotificationInfo.extract_data_worker 1171 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\d1fd7997-6080-4485-bad1-d0ec26f3ead4.docx
2025-07-09 13:56:19,619 _DownloadBusinessNotificationInfo.extract_data_worker 1177 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 13:56:19,627 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1198 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 13:56:19,627 word_queue_processor.submit_task 161 INFO    => 任務已提交: 5c29d5f5-45d4-48c9-a584-d771ec7f53bf
2025-07-09 13:56:19,627 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 5c29d5f5-45d4-48c9-a584-d771ec7f53bf
2025-07-09 13:56:19,627 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1208 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\d1fd7997-6080-4485-bad1-d0ec26f3ead4.docx
2025-07-09 13:56:20,140 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:56:20,141 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 13:56:22,642 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10106', user_id: '32000'
2025-07-09 13:56:32,626 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1211 INFO    => Legacy 處理器成功提取 55 筆數據
2025-07-09 13:56:32,730 word_queue_processor._process_tasks 120 INFO    => 任務完成: 5c29d5f5-45d4-48c9-a584-d771ec7f53bf
2025-07-09 13:56:32,730 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1225 INFO    => 原始處理器執行成功
2025-07-09 13:56:32,730 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1234 INFO    => 成功提取 55 筆數據
2025-07-09 13:56:32,730 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1238 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 55
2025-07-09 13:56:32,731 _DownloadBusinessNotificationInfo.save_to_excel 919 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 13:56:32,732 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 準備添加 55 筆數據到 Excel
2025-07-09 13:56:32,732 _DownloadBusinessNotificationInfo.save_to_excel 997 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 13:56:32,742 _DownloadBusinessNotificationInfo.save_to_excel 1053 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:56:32,785 _DownloadBusinessNotificationInfo.save_to_excel 1055 INFO    => Excel 文件保存成功
2025-07-09 13:56:32,786 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 13:56:32,792 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7127
2025-07-09 13:56:36,809 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:56:36,947 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 13:56:50,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:56:50,591 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:57:02,608 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:57:07,321 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:57:07,367 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 13:57:16,906 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:57:21,035 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:57:21,117 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:57:37,981 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:57:38,042 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:57:51,805 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:57:51,913 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 13:58:02,642 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:58:08,801 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:58:08,933 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:58:16,913 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:58:22,114 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:58:22,115 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 13:58:39,514 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:58:39,659 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 13:58:52,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:58:52,248 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:59:02,699 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:59:09,962 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:59:11,340 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 13:59:16,927 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 13:59:22,376 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:59:22,376 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 13:59:40,101 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:59:52,513 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 13:59:52,515 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:00:02,787 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:00:10,289 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:00:10,289 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 14:00:16,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:00:22,720 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:00:22,720 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:00:40,599 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:00:40,600 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:00:52,922 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:00:52,922 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:01:02,892 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:01:10,953 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:01:10,954 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:01:16,969 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:01:23,221 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:01:23,221 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 14:01:41,260 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:01:41,360 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:01:53,574 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:01:53,668 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:02:02,933 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:02:11,797 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:02:11,881 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:02:11,959 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:02:17,030 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:02:24,382 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:02:24,432 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:02:42,631 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:02:42,757 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:02:54,959 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:02:54,960 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:03:06,672 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:03:06,717 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:03:06,718 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:03:06,728 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:03:06,728 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:03:06,729 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:03:06,729 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:03:06,729 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:03:06,729 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:03:07,498 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:03:07,541 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:03:07,808 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:03:12,460 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:03:15,585 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:03:18,071 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:03:19,171 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:03:20,246 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:03:24,645 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:03:24,740 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:03:24,740 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:03:25,000 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:03:25,034 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:03:25,069 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:03:25,069 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:03:25,098 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:03:25,145 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:03:25,643 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:03:25,782 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:03:26,121 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:03:30,544 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:03:33,578 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:03:37,111 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:03:38,370 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:03:38,815 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:03:40,487 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:03:40,980 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 14:03:50,218 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 14:03:50,231 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 14:03:50,977 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 14:03:53,587 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 14:03:56,867 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:03:56,869 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:04:06,746 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:04:08,770 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:04:08,882 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 14:04:24,755 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:04:27,275 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:04:27,419 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 14:04:39,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:04:39,374 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:04:57,982 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:05:02,622 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:05:02,979 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 14:05:03,169 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 14:05:03,303 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 14:05:03,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 14:05:03,604 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 14:05:03,811 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 3809
2025-07-09 14:05:04,295 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 7819
2025-07-09 14:05:04,687 basehttp.log_message 161 INFO    => "OPTIONS /api/products/select_prod_code_map/ HTTP/1.1" 200 0
2025-07-09 14:05:04,901 basehttp.log_message 161 INFO    => "POST /api/products/select_prod_code_map/ HTTP/1.1" 200 5802
2025-07-09 14:05:06,822 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:05:09,944 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:05:10,400 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-07-09 14:05:10,555 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-09 14:05:10,724 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-09 14:05:24,772 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:05:28,334 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:05:28,447 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:05:40,229 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:05:40,346 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:05:59,080 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:05:59,169 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-07-09 14:06:04,773 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-07-09 14:06:05,523 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-09 14:06:06,069 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 14:06:06,172 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-07-09 14:06:06,879 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:06:07,135 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1508
2025-07-09 14:06:07,303 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4675
2025-07-09 14:06:07,412 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1053
2025-07-09 14:06:07,609 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 14:06:10,474 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 14:06:10,682 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 14:06:10,718 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 14:06:10,791 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:06:13,474 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48528
2025-07-09 14:06:14,699 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:06:14,743 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2025-07-09 14:06:21,556 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:06:21,622 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:06:24,779 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:06:26,984 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:06:27,182 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:06:28,081 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1121 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:06:28,275 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1152 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.docx
2025-07-09 14:06:28,290 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2025-07-09 14:06:28,408 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2025-07-09 14:06:29,928 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:06:29,929 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:06:30,753 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1121 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:06:30,756 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1152 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.docx
2025-07-09 14:06:30,757 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2025-07-09 14:06:30,757 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2025-07-09 14:06:41,111 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:06:41,190 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-07-09 14:07:00,075 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:07:00,075 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:07:06,933 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:07:11,776 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:07:11,776 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.9%
2025-07-09 14:07:24,794 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:07:30,298 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:07:30,425 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:07:42,086 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:07:42,289 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:08:00,843 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:08:00,844 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:08:06,941 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:08:07,669 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2025-07-09 14:08:08,082 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 181940
2025-07-09 14:08:12,817 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:08:12,818 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:08:16,145 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1121 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:08:16,178 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1190 INFO    => 嘗試使用企業級處理器
2025-07-09 14:08:16,179 _DownloadBusinessNotificationInfo.extract_data_worker 1171 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\cab26f9c-5c44-4e43-bd2c-ea8d890300e5.docx
2025-07-09 14:08:16,250 _DownloadBusinessNotificationInfo.extract_data_worker 1177 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:08:16,260 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1198 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:08:16,260 word_queue_processor.submit_task 161 INFO    => 任務已提交: ee6cc781-13f4-4632-a312-7a8537da199e
2025-07-09 14:08:16,260 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: ee6cc781-13f4-4632-a312-7a8537da199e
2025-07-09 14:08:16,262 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1208 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\cab26f9c-5c44-4e43-bd2c-ea8d890300e5.docx
2025-07-09 14:08:18,253 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年7月促銷活動', user_id: '30304'
2025-07-09 14:08:18,271 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年7月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:08:18,305 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1211 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:08:18,630 word_queue_processor._process_tasks 120 INFO    => 任務完成: ee6cc781-13f4-4632-a312-7a8537da199e
2025-07-09 14:08:18,710 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1225 INFO    => 原始處理器執行成功
2025-07-09 14:08:18,745 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1232 WARNING => 未提取到任何數據
2025-07-09 14:08:18,763 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1238 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年7月促銷活動', 數據筆數: 0
2025-07-09 14:08:18,816 _DownloadBusinessNotificationInfo.save_to_excel 919 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:08:18,832 _DownloadBusinessNotificationInfo.save_to_excel 957 WARNING => 使用默認工作表標題: '產品價格_通知東急屋超市114年7月促銷活動' (原始 rout: '通知東急屋超市114年7月促銷活動')
2025-07-09 14:08:18,868 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:08:18,921 _DownloadBusinessNotificationInfo.save_to_excel 1053 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:08:20,437 _DownloadBusinessNotificationInfo.save_to_excel 1055 INFO    => Excel 文件保存成功
2025-07-09 14:08:20,437 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:08:20,444 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5124
2025-07-09 14:08:24,812 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:08:30,981 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:08:34,194 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 通知東急屋超市114年7月促銷活動250630h16721B.docx  -  相容模式 - Word
2025-07-09 14:08:34,297 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 通知東急屋超市114年7月促銷活動250630h16721B.docx  -  相容模式 - Word
2025-07-09 14:08:42,952 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:09:01,165 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:09:01,165 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:09:06,951 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:09:13,086 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:09:24,817 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:09:31,554 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:09:31,650 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:09:43,373 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:09:43,373 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:10:02,534 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:10:02,535 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 14:10:07,051 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:10:14,079 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:10:24,859 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:10:32,733 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:10:32,734 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:10:44,252 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:10:44,252 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 14:11:03,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:11:03,234 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.2%
2025-07-09 14:11:07,189 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:11:14,380 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:11:24,980 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:11:33,895 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:11:33,896 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:11:44,743 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:11:44,836 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:11:55,208 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1121 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:11:55,239 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1190 INFO    => 嘗試使用企業級處理器
2025-07-09 14:11:55,241 _DownloadBusinessNotificationInfo.extract_data_worker 1171 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\95dd0525-b386-43a9-944f-f2dd8ca47a77.docx
2025-07-09 14:11:55,241 _DownloadBusinessNotificationInfo.extract_data_worker 1177 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:11:55,241 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1198 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:11:55,241 word_queue_processor.submit_task 161 INFO    => 任務已提交: 0063d373-6ffb-445d-963e-7711fbda1d50
2025-07-09 14:11:55,241 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 0063d373-6ffb-445d-963e-7711fbda1d50
2025-07-09 14:11:55,241 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1208 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\95dd0525-b386-43a9-944f-f2dd8ca47a77.docx
2025-07-09 14:11:55,982 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年7月促銷活動', user_id: '30304'
2025-07-09 14:11:55,982 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年7月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:11:55,982 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1211 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:11:56,113 word_queue_processor._process_tasks 120 INFO    => 任務完成: 0063d373-6ffb-445d-963e-7711fbda1d50
2025-07-09 14:11:56,113 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1225 INFO    => 原始處理器執行成功
2025-07-09 14:11:56,113 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1232 WARNING => 未提取到任何數據
2025-07-09 14:11:56,114 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1238 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年7月促銷活動', 數據筆數: 0
2025-07-09 14:11:56,115 _DownloadBusinessNotificationInfo.save_to_excel 919 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:11:56,115 _DownloadBusinessNotificationInfo.save_to_excel 957 WARNING => 使用默認工作表標題: '產品價格_通知東急屋超市114年7月促銷活動' (原始 rout: '通知東急屋超市114年7月促銷活動')
2025-07-09 14:11:56,115 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:11:56,116 _DownloadBusinessNotificationInfo.save_to_excel 1053 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:11:56,152 _DownloadBusinessNotificationInfo.save_to_excel 1055 INFO    => Excel 文件保存成功
2025-07-09 14:11:56,153 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:11:56,156 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5121
2025-07-09 14:12:04,035 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:12:04,037 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:12:07,232 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:12:15,381 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:12:15,382 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:12:25,052 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:12:34,445 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:12:34,540 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:12:45,816 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:12:45,879 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:13:05,111 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:13:05,272 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:13:07,322 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:13:16,422 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:13:25,112 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:13:35,993 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:13:36,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:13:46,925 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:13:47,039 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:14:06,630 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:14:06,631 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:14:07,383 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:14:17,606 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:14:25,127 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:14:36,763 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:14:36,765 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:14:47,763 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:14:47,763 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:15:07,106 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:15:07,156 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:15:07,441 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:15:18,564 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:15:18,697 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 80.1%
2025-07-09 14:15:25,143 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:15:37,660 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:15:37,661 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 14:15:49,494 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:15:49,494 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:16:19,013 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:16:19,056 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:16:19,056 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:16:19,066 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:16:19,066 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:16:19,066 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:16:19,066 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:16:19,066 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:16:19,066 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:16:19,645 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:16:19,685 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:16:19,942 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:16:25,292 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:16:26,832 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:16:29,492 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:16:31,193 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:16:33,282 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:16:35,957 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:16:35,977 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:16:35,979 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:16:35,982 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:16:35,982 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:16:35,982 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:16:35,982 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:16:35,982 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:16:35,982 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:16:37,070 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:16:37,272 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:16:37,621 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-09 14:16:41,862 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:16:44,803 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:16:47,484 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:16:49,672 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:16:50,511 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:16:52,057 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:16:52,749 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 14:16:59,817 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 14:16:59,825 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 14:17:00,426 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 14:17:04,207 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 14:17:08,573 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:17:08,702 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 14:17:19,086 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:17:20,874 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:17:20,955 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:17:36,018 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:17:39,362 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:17:39,455 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:17:49,461 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:17:49,595 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 14:17:49,842 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 14:17:49,937 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 14:17:50,083 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 14:17:50,242 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 14:17:50,423 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 647
2025-07-09 14:17:50,452 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4675
2025-07-09 14:17:51,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:17:51,710 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:17:53,738 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48528
2025-07-09 14:17:59,529 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:17:59,685 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:18:02,011 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:18:02,096 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:18:10,289 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:18:10,392 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:18:11,524 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1162 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:18:12,390 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1231 INFO    => 嘗試使用企業級處理器
2025-07-09 14:18:12,480 _DownloadBusinessNotificationInfo.extract_data_worker 1212 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\7e27a3f0-42cc-4205-ab01-dbdda2f8b493.docx
2025-07-09 14:18:12,717 _DownloadBusinessNotificationInfo.extract_data_worker 1218 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:18:13,121 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1239 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:18:13,171 word_queue_processor.submit_task 161 INFO    => 任務已提交: 010613cb-c2b5-4d60-85a7-efe8a0940238
2025-07-09 14:18:13,184 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 010613cb-c2b5-4d60-85a7-efe8a0940238
2025-07-09 14:18:13,245 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1249 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\7e27a3f0-42cc-4205-ab01-dbdda2f8b493.docx
2025-07-09 14:18:14,641 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年7月促銷活動', user_id: '30304'
2025-07-09 14:18:14,642 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年7月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:18:14,642 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1252 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:18:14,777 word_queue_processor._process_tasks 120 INFO    => 任務完成: 010613cb-c2b5-4d60-85a7-efe8a0940238
2025-07-09 14:18:14,777 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1266 INFO    => 原始處理器執行成功
2025-07-09 14:18:14,778 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1273 WARNING => 未提取到任何數據
2025-07-09 14:18:14,778 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1279 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年7月促銷活動', 數據筆數: 0
2025-07-09 14:18:14,779 _DownloadBusinessNotificationInfo.save_to_excel 960 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年7月促銷活動', 類型: <class 'str'>
2025-07-09 14:18:14,779 _DownloadBusinessNotificationInfo.save_to_excel 998 WARNING => 使用默認工作表標題: '產品價格_通知東急屋超市114年7月促銷活動' (原始 rout: '通知東急屋超市114年7月促銷活動')
2025-07-09 14:18:14,779 _DownloadBusinessNotificationInfo.save_to_excel 1006 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:18:14,779 _DownloadBusinessNotificationInfo.save_to_excel 1094 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:18:14,804 _DownloadBusinessNotificationInfo.save_to_excel 1096 INFO    => Excel 文件保存成功
2025-07-09 14:18:14,804 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1281 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400512\通知東急屋超市114年7月促銷活動250630h16721B.xlsx
2025-07-09 14:18:14,807 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5122
2025-07-09 14:18:19,176 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:18:22,239 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:18:36,057 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:18:41,194 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:18:41,335 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:18:52,705 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:18:52,836 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:19:07,226 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:19:07,459 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:19:11,906 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:19:12,056 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:19:13,341 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:19:13,341 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:19:19,263 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:19:23,426 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:19:36,091 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:19:42,745 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:19:42,745 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:19:53,711 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:19:53,896 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 14:20:13,133 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:20:13,249 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 14:20:19,303 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:20:24,664 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:20:24,781 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:20:36,141 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:20:43,906 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:20:43,998 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-09 14:20:49,900 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:20:54,921 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:21:14,226 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:21:14,226 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:21:19,334 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:21:25,364 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:21:25,414 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.8%
2025-07-09 14:21:36,177 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:21:44,588 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:21:44,720 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:21:46,530 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:21:46,689 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:21:56,083 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:21:56,130 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:22:15,317 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:22:15,458 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:22:19,404 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:22:26,571 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:22:36,194 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:22:36,702 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:22:36,754 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:22:40,102 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1162 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:22:40,243 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1193 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.docx
2025-07-09 14:22:40,244 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2025-07-09 14:22:40,244 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2025-07-09 14:22:46,192 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:22:46,298 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 111.6%
2025-07-09 14:22:56,700 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:23:16,748 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:23:16,748 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 143.3%
2025-07-09 14:23:19,408 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:23:26,827 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:23:36,212 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:23:46,886 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:23:46,887 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 14:23:49,909 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:23:57,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:24:17,032 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:24:17,034 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:24:19,436 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:24:27,910 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:24:36,217 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:24:47,175 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:24:47,176 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 14:24:55,230 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1162 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:24:56,131 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1231 INFO    => 嘗試使用企業級處理器
2025-07-09 14:24:56,208 _DownloadBusinessNotificationInfo.extract_data_worker 1212 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\5bbb1bc4-2dff-415a-8bf4-26528be334fd.docx
2025-07-09 14:24:56,250 _DownloadBusinessNotificationInfo.extract_data_worker 1218 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:24:56,251 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1239 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:24:56,251 word_queue_processor.submit_task 161 INFO    => 任務已提交: 28cd7d73-d05b-45c7-ba4b-901235f5d13d
2025-07-09 14:24:56,251 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 28cd7d73-d05b-45c7-ba4b-901235f5d13d
2025-07-09 14:24:56,251 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1249 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\5bbb1bc4-2dff-415a-8bf4-26528be334fd.docx
2025-07-09 14:24:57,253 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年6月促銷活動', user_id: '30304'
2025-07-09 14:24:57,254 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年6月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:24:57,254 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1252 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:24:57,489 word_queue_processor._process_tasks 120 INFO    => 任務完成: 28cd7d73-d05b-45c7-ba4b-901235f5d13d
2025-07-09 14:24:57,579 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1266 INFO    => 原始處理器執行成功
2025-07-09 14:24:57,623 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1273 WARNING => 未提取到任何數據
2025-07-09 14:24:57,656 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1279 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年6月促銷活動', 數據筆數: 0
2025-07-09 14:24:57,714 _DownloadBusinessNotificationInfo.save_to_excel 960 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:24:57,743 _DownloadBusinessNotificationInfo.save_to_excel 998 WARNING => 使用默認工作表標題: '產品價格_通知東急屋超市114年6月促銷活動' (原始 rout: '通知東急屋超市114年6月促銷活動')
2025-07-09 14:24:57,743 _DownloadBusinessNotificationInfo.save_to_excel 1006 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:24:57,744 _DownloadBusinessNotificationInfo.save_to_excel 1094 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:24:57,809 _DownloadBusinessNotificationInfo.save_to_excel 1096 INFO    => Excel 文件保存成功
2025-07-09 14:24:57,848 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1281 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:24:57,851 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5121
2025-07-09 14:24:58,686 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:24:58,812 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:25:07,719 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1162 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:25:07,753 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1231 INFO    => 嘗試使用企業級處理器
2025-07-09 14:25:07,753 _DownloadBusinessNotificationInfo.extract_data_worker 1212 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\24c7a1ee-a00e-4ff3-8918-553d16fa9da3.docx
2025-07-09 14:25:07,753 _DownloadBusinessNotificationInfo.extract_data_worker 1218 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:25:07,755 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1239 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:25:07,755 word_queue_processor.submit_task 161 INFO    => 任務已提交: 2e7bfd62-dc42-437f-b22f-ec5f6745ec03
2025-07-09 14:25:07,755 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 2e7bfd62-dc42-437f-b22f-ec5f6745ec03
2025-07-09 14:25:07,755 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1249 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\24c7a1ee-a00e-4ff3-8918-553d16fa9da3.docx
2025-07-09 14:25:08,479 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年6月促銷活動', user_id: '30304'
2025-07-09 14:25:08,481 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年6月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:25:08,481 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1252 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:25:08,791 word_queue_processor._process_tasks 120 INFO    => 任務完成: 2e7bfd62-dc42-437f-b22f-ec5f6745ec03
2025-07-09 14:25:08,791 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1266 INFO    => 原始處理器執行成功
2025-07-09 14:25:08,791 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1273 WARNING => 未提取到任何數據
2025-07-09 14:25:08,791 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1279 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年6月促銷活動', 數據筆數: 0
2025-07-09 14:25:08,793 _DownloadBusinessNotificationInfo.save_to_excel 960 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:25:08,793 _DownloadBusinessNotificationInfo.save_to_excel 998 WARNING => 使用默認工作表標題: '產品價格_通知東急屋超市114年6月促銷活動' (原始 rout: '通知東急屋超市114年6月促銷活動')
2025-07-09 14:25:08,793 _DownloadBusinessNotificationInfo.save_to_excel 1006 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:25:08,794 _DownloadBusinessNotificationInfo.save_to_excel 1094 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:25:08,824 _DownloadBusinessNotificationInfo.save_to_excel 1096 INFO    => Excel 文件保存成功
2025-07-09 14:25:08,825 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1281 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:25:08,847 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5120
2025-07-09 14:25:17,544 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:25:17,663 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:25:19,497 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:25:29,388 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:25:36,221 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:25:48,146 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:25:48,147 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:25:59,830 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:25:59,951 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:26:18,362 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:26:18,363 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:26:19,557 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:26:30,744 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:26:30,869 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:26:36,236 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:26:48,503 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:26:48,505 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:27:01,692 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:27:01,692 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:27:18,720 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:27:18,721 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:27:19,610 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:27:31,992 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:27:31,992 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 14:27:36,286 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:27:47,588 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:27:47,628 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:27:47,628 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:27:47,860 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:27:47,886 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:27:47,925 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:27:47,949 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:27:47,975 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:27:48,016 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:27:48,578 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:27:48,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:27:48,914 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:27:53,654 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:27:55,232 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:27:56,322 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:27:57,198 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:27:59,842 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:28:03,904 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:28:03,940 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:28:03,940 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:28:03,945 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:28:03,945 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:28:03,945 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:28:03,945 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:28:03,945 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:28:03,945 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:28:04,895 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:28:05,164 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:28:05,430 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 156.3%
2025-07-09 14:28:09,102 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:28:11,927 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:28:14,154 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:28:16,816 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:28:19,060 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:28:19,501 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 14:28:19,664 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:28:19,787 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:28:28,601 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 14:28:28,617 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 14:28:33,275 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 14:28:36,397 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:28:36,444 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 14:28:36,648 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 14:28:47,641 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:28:50,510 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:28:50,570 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:29:03,987 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:29:07,187 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:29:07,187 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:29:20,868 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:29:21,048 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:29:29,871 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:29:29,985 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 58857)

2025-07-09 14:29:30,818 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:29:31,257 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 14:29:31,526 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 14:29:31,948 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 14:29:31,955 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 14:29:32,061 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 14:29:32,339 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 647
2025-07-09 14:29:32,397 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4675
2025-07-09 14:29:34,103 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48528
2025-07-09 14:29:37,189 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:29:37,193 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:29:37,319 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:29:37,320 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:29:44,342 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1154 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:29:45,404 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1223 INFO    => 嘗試使用企業級處理器
2025-07-09 14:29:45,404 _DownloadBusinessNotificationInfo.extract_data_worker 1204 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\9a3980db-426c-4d2a-824e-a47c87d38774.docx
2025-07-09 14:29:45,419 _DownloadBusinessNotificationInfo.extract_data_worker 1210 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:29:45,428 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1231 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:29:45,429 word_queue_processor.submit_task 161 INFO    => 任務已提交: eb13613b-5c2f-48f4-bee0-01fe66570337
2025-07-09 14:29:45,429 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: eb13613b-5c2f-48f4-bee0-01fe66570337
2025-07-09 14:29:45,429 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1241 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\9a3980db-426c-4d2a-824e-a47c87d38774.docx
2025-07-09 14:29:47,652 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:29:47,821 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '通知東急屋超市114年6月促銷活動', user_id: '30304'
2025-07-09 14:29:47,821 _DownloadBusinessNotificationInfo.extract_table_data 625 ERROR   => 未定義 通知東急屋超市114年6月促銷活動 的位置，可用的路線: ['10106', '10212', '10247', '10228', '11201', '80901', '80902', '80904', '80905']
2025-07-09 14:29:47,821 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1244 INFO    => Legacy 處理器成功提取 0 筆數據
2025-07-09 14:29:48,637 word_queue_processor._process_tasks 120 INFO    => 任務完成: eb13613b-5c2f-48f4-bee0-01fe66570337
2025-07-09 14:29:48,637 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1258 INFO    => 原始處理器執行成功
2025-07-09 14:29:48,637 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1265 WARNING => 未提取到任何數據
2025-07-09 14:29:48,637 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1271 INFO    => 準備調用 save_to_excel，rout: '通知東急屋超市114年6月促銷活動', 數據筆數: 0
2025-07-09 14:29:48,638 _DownloadBusinessNotificationInfo.save_to_excel 960 INFO    => save_to_excel 被調用，rout 參數值: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:29:48,638 _DownloadBusinessNotificationInfo.save_to_excel 998 INFO    => 準備添加 0 筆數據到 Excel
2025-07-09 14:29:48,639 _DownloadBusinessNotificationInfo.save_to_excel 1086 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:29:48,678 _DownloadBusinessNotificationInfo.save_to_excel 1088 INFO    => Excel 文件保存成功
2025-07-09 14:29:48,678 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1273 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:29:48,684 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 5039
2025-07-09 14:29:51,826 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:29:51,874 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:30:04,068 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:30:07,625 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:30:07,625 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:30:22,301 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:30:37,930 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:30:38,001 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:30:47,690 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:30:50,181 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: ✳ Word文件處理
2025-07-09 14:30:50,279 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: ✳ Word文件處理
2025-07-09 14:30:52,573 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:30:52,683 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:31:04,114 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:31:08,456 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:31:08,456 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:31:23,342 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:31:23,480 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:31:38,882 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:31:39,004 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 14:31:47,738 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:31:54,204 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:31:54,311 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:32:04,152 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:32:09,629 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:32:09,629 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:32:24,461 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:32:40,011 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:32:40,140 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:32:47,806 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:32:54,829 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:32:54,861 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:33:04,198 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:33:10,667 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:33:10,667 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:33:25,327 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:33:25,391 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:33:40,804 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:33:40,806 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-09 14:33:47,852 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:33:55,911 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:34:04,269 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:34:11,230 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:34:11,306 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 14:34:26,261 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:34:26,293 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:34:41,729 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:34:41,729 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 143.3%
2025-07-09 14:34:47,878 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:34:56,786 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:35:04,324 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:35:12,076 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:35:12,229 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 14:35:26,967 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:35:26,967 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:35:41,407 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:35:42,805 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:35:42,805 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:35:47,970 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:35:57,105 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:36:04,363 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:36:13,261 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:36:13,389 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:36:27,461 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:36:27,523 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:36:43,971 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:36:43,971 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:36:48,029 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:36:58,117 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:36:58,118 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:37:04,389 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:37:14,113 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:37:14,115 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:37:28,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:37:28,531 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 14:37:44,493 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:37:44,581 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:37:48,096 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:37:59,005 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:37:59,006 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:38:04,425 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:38:15,256 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:38:15,419 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:38:29,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:38:29,596 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.6%
2025-07-09 14:38:31,290 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:38:45,930 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:38:45,931 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:38:48,177 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:39:00,037 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:39:04,456 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:39:16,381 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:39:16,475 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:39:30,232 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:39:30,232 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:39:47,160 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:39:47,226 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:39:48,246 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:40:00,643 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:40:00,753 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:40:04,479 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:40:17,807 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:40:17,807 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:40:31,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:40:48,323 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:40:48,402 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:40:48,417 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:41:01,735 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:41:01,766 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:41:04,491 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:41:18,551 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:41:18,552 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 14:41:31,176 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:41:31,903 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:41:48,399 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:41:48,962 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:41:49,038 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 14:42:02,445 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:42:02,497 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:42:04,499 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:42:19,299 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:42:32,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:42:33,097 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:42:48,446 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:42:49,536 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:42:49,536 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:43:03,568 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:43:04,512 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:43:16,935 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:43:16,975 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:43:16,975 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:43:16,985 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:43:16,985 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:43:16,985 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:43:17,011 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:43:17,042 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:43:17,072 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:43:17,712 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:43:17,822 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:43:18,163 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:43:23,041 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:43:24,012 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:43:26,445 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:43:28,573 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:43:29,439 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:43:32,271 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 14:43:32,305 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 14:43:32,305 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 14:43:32,311 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 14:43:32,311 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 14:43:32,311 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 14:43:32,311 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 14:43:32,312 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 14:43:32,312 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 14:43:33,001 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 14:43:33,117 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 14:43:33,517 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:43:37,799 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 14:43:40,613 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 14:43:43,520 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 14:43:44,332 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 14:43:45,975 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 14:43:46,107 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 14:43:48,995 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:43:49,119 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:43:53,158 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 14:43:53,171 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 14:43:55,646 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 14:43:57,478 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 14:44:04,047 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:44:04,050 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:44:17,008 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:44:19,685 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:44:22,689 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:44:22,848 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 14:44:23,027 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 14:44:23,625 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 14:44:24,178 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 14:44:24,194 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 14:44:24,403 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 70
2025-07-09 14:44:24,455 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 4675
2025-07-09 14:44:27,042 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 48528
2025-07-09 14:44:30,440 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:44:30,454 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:44:31,482 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1105 INFO    => 從 JSON 數據中提取的 rout: '通知東急屋超市114年6月促銷活動', 類型: <class 'str'>
2025-07-09 14:44:31,496 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1112 WARNING => rout 參數可能是文件名而不是路線代碼: '通知東急屋超市114年6月促銷活動'
2025-07-09 14:44:31,532 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1116 INFO    => 從文件名推斷路線代碼為: 10247
2025-07-09 14:44:32,321 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:44:32,474 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1208 INFO    => 嘗試使用企業級處理器
2025-07-09 14:44:32,538 _DownloadBusinessNotificationInfo.extract_data_worker 1189 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\fb3c817c-e3bf-4edf-994e-49c146b687b0.docx
2025-07-09 14:44:32,667 _DownloadBusinessNotificationInfo.extract_data_worker 1195 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:44:32,837 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1216 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:44:32,837 word_queue_processor.submit_task 161 INFO    => 任務已提交: e18d4973-08a9-4240-aa1f-5e0c9263154b
2025-07-09 14:44:32,837 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: e18d4973-08a9-4240-aa1f-5e0c9263154b
2025-07-09 14:44:32,839 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1226 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\fb3c817c-e3bf-4edf-994e-49c146b687b0.docx
2025-07-09 14:44:33,596 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10247', user_id: '30304'
2025-07-09 14:44:34,237 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:44:40,824 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1229 INFO    => Legacy 處理器成功提取 38 筆數據
2025-07-09 14:44:40,892 word_queue_processor._process_tasks 120 INFO    => 任務完成: e18d4973-08a9-4240-aa1f-5e0c9263154b
2025-07-09 14:44:40,894 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1243 INFO    => 原始處理器執行成功
2025-07-09 14:44:40,894 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1252 INFO    => 成功提取 38 筆數據
2025-07-09 14:44:40,894 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1256 INFO    => 準備調用 save_to_excel，rout: '10247', 數據筆數: 38
2025-07-09 14:44:40,895 _DownloadBusinessNotificationInfo.save_to_excel 911 INFO    => save_to_excel 被調用，rout 參數值: '10247', 類型: <class 'str'>
2025-07-09 14:44:40,895 _DownloadBusinessNotificationInfo.save_to_excel 949 INFO    => 準備添加 38 筆數據到 Excel
2025-07-09 14:44:40,895 _DownloadBusinessNotificationInfo.save_to_excel 981 INFO    => 第一筆數據: ['2425', '295', '元/箱', '', '', None, '']
2025-07-09 14:44:40,904 _DownloadBusinessNotificationInfo.save_to_excel 1037 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:44:40,949 _DownloadBusinessNotificationInfo.save_to_excel 1039 INFO    => Excel 文件保存成功
2025-07-09 14:44:40,950 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1258 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400495\通知東急屋超市114年6月促銷活動250619h16721B.xlsx
2025-07-09 14:44:40,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6381
2025-07-09 14:44:50,116 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:44:50,296 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:44:57,586 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 14:44:57,715 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 14:45:04,688 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:45:17,064 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:45:18,056 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1105 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 14:45:18,718 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1208 INFO    => 嘗試使用企業級處理器
2025-07-09 14:45:18,801 _DownloadBusinessNotificationInfo.extract_data_worker 1189 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\fea9c792-687b-479e-94fc-f270871f0772.docx
2025-07-09 14:45:18,996 _DownloadBusinessNotificationInfo.extract_data_worker 1195 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 14:45:19,311 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1216 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 14:45:19,338 word_queue_processor.submit_task 161 INFO    => 任務已提交: ab221092-9f3b-49ef-a4ea-43dbc4046590
2025-07-09 14:45:19,363 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: ab221092-9f3b-49ef-a4ea-43dbc4046590
2025-07-09 14:45:19,405 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1226 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\fea9c792-687b-479e-94fc-f270871f0772.docx
2025-07-09 14:45:20,395 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10106', user_id: '30304'
2025-07-09 14:45:20,985 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:45:32,355 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:45:33,032 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1229 INFO    => Legacy 處理器成功提取 55 筆數據
2025-07-09 14:45:33,124 word_queue_processor._process_tasks 120 INFO    => 任務完成: ab221092-9f3b-49ef-a4ea-43dbc4046590
2025-07-09 14:45:33,124 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1243 INFO    => 原始處理器執行成功
2025-07-09 14:45:33,124 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1252 INFO    => 成功提取 55 筆數據
2025-07-09 14:45:33,124 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1256 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 55
2025-07-09 14:45:33,126 _DownloadBusinessNotificationInfo.save_to_excel 911 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 14:45:33,126 _DownloadBusinessNotificationInfo.save_to_excel 949 INFO    => 準備添加 55 筆數據到 Excel
2025-07-09 14:45:33,126 _DownloadBusinessNotificationInfo.save_to_excel 981 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 14:45:33,136 _DownloadBusinessNotificationInfo.save_to_excel 1037 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 14:45:33,185 _DownloadBusinessNotificationInfo.save_to_excel 1039 INFO    => Excel 文件保存成功
2025-07-09 14:45:33,185 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1258 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 14:45:33,192 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7126
2025-07-09 14:45:35,304 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:45:35,513 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:45:51,172 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:45:51,172 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:46:06,296 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:46:06,369 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:46:17,144 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:46:21,326 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:46:21,326 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.8%
2025-07-09 14:46:32,374 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:46:37,030 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:46:37,162 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:46:51,461 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:47:07,694 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:47:07,794 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:47:17,213 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:47:21,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:47:22,858 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:47:32,456 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:47:38,589 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:47:38,677 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 14:47:51,742 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:48:09,196 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:48:09,196 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:48:17,268 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:48:21,868 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:48:32,509 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:48:39,326 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:48:39,326 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:48:51,996 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:49:09,457 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:49:09,458 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:49:17,362 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:49:22,441 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:49:22,546 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:49:32,536 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:49:39,836 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:49:39,941 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:49:53,052 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:49:53,052 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:50:10,637 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:50:10,637 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:50:17,408 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:50:23,016 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:50:23,332 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:50:23,332 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 14:50:32,579 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:50:40,979 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:50:40,981 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 14:50:53,473 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:51:11,293 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:51:11,294 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 142.0%
2025-07-09 14:51:17,415 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:51:23,613 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:51:32,624 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:51:41,484 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:51:41,484 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:51:53,744 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:52:11,653 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:52:11,654 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 14:52:17,437 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:52:23,889 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:52:32,631 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:52:41,928 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:52:41,928 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:52:54,020 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:53:12,067 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:53:12,067 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:53:17,456 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:53:23,104 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:53:24,146 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:53:32,748 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:53:42,207 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:53:42,207 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:53:54,278 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:54:12,347 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:54:12,347 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-09 14:54:17,468 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:54:24,412 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:54:32,808 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:54:42,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:54:42,482 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:54:54,817 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:54:54,963 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:55:12,891 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:55:12,981 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:55:17,506 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:55:25,597 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:55:25,701 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:55:32,845 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:55:43,733 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:55:43,866 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:55:56,174 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:55:56,299 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 14:56:14,488 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:56:14,574 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 14:56:17,576 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:56:22,956 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:56:27,229 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:56:32,878 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:56:45,210 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:56:45,267 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 14:56:57,722 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:56:57,722 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 14:57:16,173 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:57:16,232 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:57:17,606 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:57:28,105 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:57:28,271 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:57:32,955 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:57:46,973 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:57:47,003 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:57:58,919 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:57:59,099 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:58:17,620 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:58:17,649 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:58:29,361 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:58:29,361 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:58:33,023 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:58:48,365 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:58:48,395 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 108.6%
2025-07-09 14:58:59,594 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:58:59,594 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 14:59:17,743 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:59:18,660 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:59:18,660 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 14:59:23,285 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 14:59:29,983 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:59:30,090 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 14:59:33,031 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 14:59:48,828 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 14:59:48,828 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:00:00,455 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:00:00,456 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 15:00:17,800 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:00:19,076 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:00:19,076 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:00:30,971 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:00:31,061 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:00:33,046 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:00:49,299 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:00:49,301 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:01:01,561 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:01:01,590 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:01:17,845 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:01:19,885 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:01:19,919 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:01:27,449 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-09 15:01:27,667 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-07-09 15:01:31,790 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:01:31,790 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:01:33,060 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:01:50,107 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:01:50,107 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.2%
2025-07-09 15:02:02,214 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:02:02,215 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 15:02:17,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:02:20,354 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:02:20,355 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:02:32,654 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:02:32,713 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:02:33,090 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:02:50,699 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:02:50,942 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:03:03,638 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:03:03,804 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:03:17,983 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:03:21,558 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:03:21,559 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:03:33,148 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:03:34,590 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:03:34,785 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:03:51,955 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:03:52,047 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 15:04:05,431 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:04:05,431 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:04:18,043 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:04:22,755 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:04:22,832 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:04:33,153 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:04:34,955 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-09 15:04:35,231 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 15:04:35,881 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:04:35,897 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:04:36,648 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1508
2025-07-09 15:04:36,648 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 15:04:36,726 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4327
2025-07-09 15:04:36,776 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5069
2025-07-09 15:04:46,095 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-07-09 15:04:46,716 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:04:46,793 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:04:47,288 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 15:04:47,801 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:04:47,801 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:04:50,522 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50711
2025-07-09 15:04:53,426 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:04:53,514 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:05:04,340 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:05:04,435 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:05:06,563 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1105 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:05:06,741 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:05:06,833 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 15:05:07,238 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1208 INFO    => 嘗試使用企業級處理器
2025-07-09 15:05:07,313 _DownloadBusinessNotificationInfo.extract_data_worker 1189 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\dca79939-459d-4cdc-9a9f-3c79f18b4f74.docx
2025-07-09 15:05:07,375 _DownloadBusinessNotificationInfo.extract_data_worker 1195 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:05:07,489 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1216 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:05:07,490 word_queue_processor.submit_task 161 INFO    => 任務已提交: 5acb73d1-453b-491f-99ae-5c3dafb1ac6b
2025-07-09 15:05:07,490 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 5acb73d1-453b-491f-99ae-5c3dafb1ac6b
2025-07-09 15:05:07,490 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1226 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\dca79939-459d-4cdc-9a9f-3c79f18b4f74.docx
2025-07-09 15:05:08,026 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:05:10,892 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1229 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:05:10,957 word_queue_processor._process_tasks 120 INFO    => 任務完成: 5acb73d1-453b-491f-99ae-5c3dafb1ac6b
2025-07-09 15:05:10,958 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1243 INFO    => 原始處理器執行成功
2025-07-09 15:05:10,958 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1252 INFO    => 成功提取 16 筆數據
2025-07-09 15:05:10,958 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1256 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 911 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 949 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 981 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:05:10,959 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:05:10,960 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:05:10,960 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:05:10,960 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:05:10,960 _DownloadBusinessNotificationInfo.save_to_excel 960 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:05:10,963 _DownloadBusinessNotificationInfo.save_to_excel 1037 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:05:10,995 _DownloadBusinessNotificationInfo.save_to_excel 1039 INFO    => Excel 文件保存成功
2025-07-09 15:05:10,995 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1258 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:05:11,000 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6103
2025-07-09 15:05:15,392 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 180986
2025-07-09 15:05:18,117 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:05:21,309 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 200 180986
2025-07-09 15:05:24,072 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:05:24,073 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:05:32,122 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 省錢超市114年07月促銷通報1140626250627h16397B (1).docx  -  相容模式 - Word
2025-07-09 15:05:32,124 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 正在開啟 - Word
2025-07-09 15:05:32,185 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 省錢超市114年07月促銷通報1140626250627h16397B (1).docx  -  相容模式 - Word
2025-07-09 15:05:33,179 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:05:37,332 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:05:46,394 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: 省錢超市114年07月促銷通報1140626250627h16397B (1).docx  -  相容模式 - Word
2025-07-09 15:05:54,403 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:05:54,543 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:06:07,767 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:06:07,888 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 15:11:41,385 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:11:41,424 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:11:41,424 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:11:41,430 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:11:41,430 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:11:41,430 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:11:41,430 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:11:41,430 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:11:41,432 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:11:42,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:11:42,246 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:11:42,568 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-07-09 15:11:47,759 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:11:49,194 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:11:50,816 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:11:52,098 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:11:54,354 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:11:56,449 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:11:56,467 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:11:56,467 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:11:56,470 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:11:56,470 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:11:56,470 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:11:56,470 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:11:56,470 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:11:56,470 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:11:57,261 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:11:57,457 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:11:57,791 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:12:01,694 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:12:04,514 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:12:07,998 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:12:09,955 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:12:12,547 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:12:12,855 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 15:12:13,252 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:12:13,381 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:12:20,513 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 15:12:20,521 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 15:12:21,077 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 15:12:24,731 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 15:12:28,368 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:12:28,368 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:12:41,431 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:12:44,246 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:12:44,421 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:12:56,473 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:12:58,631 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:12:58,712 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 15:13:15,367 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:13:15,501 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 15:13:29,579 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:13:29,738 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:13:41,476 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:13:46,207 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:13:46,356 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:13:56,502 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:14:00,482 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:14:00,509 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:14:17,068 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:14:17,225 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:14:30,869 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:14:31,006 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:14:41,548 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:14:47,981 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:14:48,086 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:14:56,548 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:15:01,550 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:15:01,595 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:15:09,331 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:15:09,496 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 15:15:09,636 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 15:15:09,733 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 15:15:09,959 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:15:10,095 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:15:10,244 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4327
2025-07-09 15:15:10,276 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5069
2025-07-09 15:15:13,294 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50711
2025-07-09 15:15:15,626 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:15:15,627 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:15:17,037 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1091 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:15:17,065 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1194 INFO    => 嘗試使用企業級處理器
2025-07-09 15:15:17,066 _DownloadBusinessNotificationInfo.extract_data_worker 1175 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\2edbeb50-fd9b-4541-93bf-7c339ced2c69.docx
2025-07-09 15:15:17,132 _DownloadBusinessNotificationInfo.extract_data_worker 1181 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:15:17,137 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1202 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:15:17,138 word_queue_processor.submit_task 161 INFO    => 任務已提交: f029e743-ae25-4068-a62c-c50ccc33bc8e
2025-07-09 15:15:17,138 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: f029e743-ae25-4068-a62c-c50ccc33bc8e
2025-07-09 15:15:17,138 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1212 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\2edbeb50-fd9b-4541-93bf-7c339ced2c69.docx
2025-07-09 15:15:18,587 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:15:18,717 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:15:18,898 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-07-09 15:15:23,365 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1215 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:15:23,439 word_queue_processor._process_tasks 120 INFO    => 任務完成: f029e743-ae25-4068-a62c-c50ccc33bc8e
2025-07-09 15:15:23,439 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1229 INFO    => 原始處理器執行成功
2025-07-09 15:15:23,439 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1238 INFO    => 成功提取 16 筆數據
2025-07-09 15:15:23,439 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 897 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 935 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 967 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:15:23,441 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:15:23,442 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:15:23,442 _DownloadBusinessNotificationInfo.save_to_excel 946 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:15:23,444 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:15:23,470 _DownloadBusinessNotificationInfo.save_to_excel 1025 INFO    => Excel 文件保存成功
2025-07-09 15:15:23,470 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1244 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:15:23,473 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6103
2025-07-09 15:15:32,342 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:15:32,483 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:15:41,606 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:15:49,758 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:15:49,836 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:15:56,565 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:16:03,131 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:16:03,248 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:16:20,656 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:16:20,781 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:16:33,964 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:16:34,076 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:16:41,622 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:16:51,570 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:16:51,600 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:16:56,630 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:17:04,610 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:17:04,708 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:17:22,076 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:17:22,208 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:17:35,439 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:17:35,573 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:17:41,639 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:17:52,762 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:17:52,941 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:17:56,666 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:18:06,286 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:18:06,384 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:18:10,305 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:18:23,543 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:18:36,979 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:18:37,076 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:18:41,690 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:18:53,675 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:18:56,706 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:19:07,856 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:19:07,991 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:19:23,818 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:19:38,767 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:19:38,826 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:19:41,732 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:19:53,953 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:19:56,767 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:20:09,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:20:09,767 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:20:24,146 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:20:24,148 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-07-09 15:20:40,578 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:20:40,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:20:41,751 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:20:54,632 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:20:54,673 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:20:56,795 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:21:09,520 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:21:11,687 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:21:11,836 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:21:25,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:21:25,141 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 84.0%
2025-07-09 15:21:41,835 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:21:42,553 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:21:42,723 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:21:55,343 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:21:55,344 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:21:56,804 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:22:13,425 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:22:13,578 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:22:25,814 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:22:25,901 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:22:41,920 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:22:44,125 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:22:44,174 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-07-09 15:22:56,206 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:22:56,208 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 15:22:56,814 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:23:12,693 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:23:12,722 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:23:12,722 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:23:12,730 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:23:12,730 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:23:12,730 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:23:12,730 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:23:12,730 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:23:12,730 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:23:13,340 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:23:13,505 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:23:13,822 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:23:18,153 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:23:21,081 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:23:23,063 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:23:23,642 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:23:24,626 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:23:28,075 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:23:28,133 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:23:28,133 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:23:28,138 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:23:28,138 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:23:28,139 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:23:28,139 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:23:28,139 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:23:28,139 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:23:28,771 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:23:28,866 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:23:29,239 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:23:32,793 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:23:35,706 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:23:39,206 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:23:42,364 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:23:42,533 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 15:23:42,846 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:23:44,603 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:23:44,659 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:23:49,309 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 15:23:49,322 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 15:23:50,005 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 15:23:52,532 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 15:24:00,151 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:24:00,315 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:24:12,775 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:24:14,871 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:24:28,148 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:24:30,667 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:24:30,667 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:24:45,166 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:24:54,465 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-07-09 15:24:56,161 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:24:56,193 basehttp.handle_error  75 INFO    => - Broken pipe from ('************', 61259)

2025-07-09 15:24:56,301 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:24:56,735 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 15:24:57,527 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 15:24:57,826 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 15:24:57,827 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:24:57,879 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:24:58,014 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4327
2025-07-09 15:24:58,112 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5069
2025-07-09 15:25:01,072 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:25:01,147 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:25:01,194 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50711
2025-07-09 15:25:03,938 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:25:04,022 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:25:06,319 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1166 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:25:07,267 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1269 INFO    => 嘗試使用企業級處理器
2025-07-09 15:25:07,366 _DownloadBusinessNotificationInfo.extract_data_worker 1250 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\ee16005b-eae6-4c2d-9e55-9e218c3160de.docx
2025-07-09 15:25:07,466 _DownloadBusinessNotificationInfo.extract_data_worker 1256 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:25:07,886 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1277 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:25:07,915 word_queue_processor.submit_task 161 INFO    => 任務已提交: 5f681c0e-03b6-47ff-a3d6-4b0af53c48fc
2025-07-09 15:25:07,945 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 5f681c0e-03b6-47ff-a3d6-4b0af53c48fc
2025-07-09 15:25:08,019 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1287 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\ee16005b-eae6-4c2d-9e55-9e218c3160de.docx
2025-07-09 15:25:09,677 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:25:12,840 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:25:15,820 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:25:15,951 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:25:16,203 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1290 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:25:16,276 word_queue_processor._process_tasks 120 INFO    => 任務完成: 5f681c0e-03b6-47ff-a3d6-4b0af53c48fc
2025-07-09 15:25:16,277 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1304 INFO    => 原始處理器執行成功
2025-07-09 15:25:16,277 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1313 INFO    => 成功提取 16 筆數據
2025-07-09 15:25:16,277 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1317 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:25:16,277 _DownloadBusinessNotificationInfo.save_to_excel 972 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1010 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1042 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:25:16,278 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:25:16,283 _DownloadBusinessNotificationInfo.save_to_excel 1098 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:25:16,323 _DownloadBusinessNotificationInfo.save_to_excel 1100 INFO    => Excel 文件保存成功
2025-07-09 15:25:16,324 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1319 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:25:16,331 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6104
2025-07-09 15:25:28,165 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:25:31,766 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:25:31,877 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:25:46,733 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:25:46,820 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:25:50,334 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1166 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:25:51,460 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1269 INFO    => 嘗試使用企業級處理器
2025-07-09 15:25:51,491 _DownloadBusinessNotificationInfo.extract_data_worker 1250 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\b25c9325-ad84-474d-b11a-e94af7b654c9.docx
2025-07-09 15:25:51,688 _DownloadBusinessNotificationInfo.extract_data_worker 1256 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:25:51,772 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1277 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:25:51,821 word_queue_processor.submit_task 161 INFO    => 任務已提交: 07f488ba-238b-46f1-92f4-7bfc3038fc3d
2025-07-09 15:25:51,852 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 07f488ba-238b-46f1-92f4-7bfc3038fc3d
2025-07-09 15:25:51,913 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1287 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\b25c9325-ad84-474d-b11a-e94af7b654c9.docx
2025-07-09 15:25:52,673 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:26:00,714 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1290 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:26:00,799 word_queue_processor._process_tasks 120 INFO    => 任務完成: 07f488ba-238b-46f1-92f4-7bfc3038fc3d
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1304 INFO    => 原始處理器執行成功
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1313 INFO    => 成功提取 16 筆數據
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1317 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.save_to_excel 972 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.save_to_excel 1010 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.save_to_excel 1042 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:26:00,801 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:26:00,802 _DownloadBusinessNotificationInfo.save_to_excel 1021 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:26:00,806 _DownloadBusinessNotificationInfo.save_to_excel 1098 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:26:00,847 _DownloadBusinessNotificationInfo.save_to_excel 1100 INFO    => Excel 文件保存成功
2025-07-09 15:26:00,847 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1319 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:26:00,853 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6104
2025-07-09 15:26:02,630 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:26:02,794 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:26:12,842 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:26:17,562 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:26:17,647 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:26:28,180 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:26:33,380 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:26:48,154 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:27:03,775 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:27:03,868 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:27:12,875 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:27:18,487 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:27:18,590 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:27:28,198 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:27:34,563 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:27:34,677 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:27:49,165 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:27:49,270 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 15:28:05,140 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:28:05,141 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 15:28:12,915 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:28:19,942 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:28:19,942 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:28:28,222 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:28:35,269 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:28:35,270 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:29:40,319 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:29:40,372 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:29:40,372 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:29:40,381 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:29:40,382 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:29:40,382 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:29:40,382 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:29:40,382 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:29:40,382 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:29:40,989 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:29:41,162 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:29:41,436 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-07-09 15:29:45,054 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:29:48,371 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:29:49,388 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:29:50,739 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:29:51,516 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:29:56,258 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:29:56,364 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:29:56,364 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:29:56,369 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:29:56,369 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:29:56,369 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:29:56,369 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:29:56,371 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:29:56,371 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:29:57,165 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:29:57,361 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:29:57,592 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 15:30:03,299 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:30:06,269 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:30:08,377 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:30:10,907 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:30:12,214 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 15:30:12,331 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:30:12,440 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-07-09 15:30:12,504 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:30:19,760 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 15:30:19,779 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 15:30:25,393 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 15:30:28,582 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:30:28,624 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 15:30:28,766 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:30:31,523 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-07-09 15:30:31,693 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 15:30:31,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 1508
2025-07-09 15:30:32,314 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4327
2025-07-09 15:30:32,314 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5069
2025-07-09 15:30:35,581 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:30:35,682 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:30:37,882 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50711
2025-07-09 15:30:40,403 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:30:40,538 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:30:40,700 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:30:41,540 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:30:42,310 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-09 15:30:42,352 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\9ecc1e10-f82c-4911-b26b-671fd6c1db6e.docx
2025-07-09 15:30:42,535 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:30:43,114 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:30:43,146 word_queue_processor.submit_task 161 INFO    => 任務已提交: 53aadff0-a913-4119-885c-400469d7d307
2025-07-09 15:30:43,146 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 53aadff0-a913-4119-885c-400469d7d307
2025-07-09 15:30:43,136 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:30:43,180 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\9ecc1e10-f82c-4911-b26b-671fd6c1db6e.docx
2025-07-09 15:30:43,343 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:30:44,226 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:30:50,616 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:30:50,677 word_queue_processor._process_tasks 120 INFO    => 任務完成: 53aadff0-a913-4119-885c-400469d7d307
2025-07-09 15:30:50,677 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-09 15:30:50,677 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 16 筆數據
2025-07-09 15:30:50,677 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:30:50,678 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:30:50,678 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:30:50,678 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:30:50,679 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:30:50,683 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:30:50,720 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-09 15:30:50,721 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:30:50,727 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6103
2025-07-09 15:30:56,372 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:30:56,429 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:30:59,391 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:30:59,523 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 15:31:14,038 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:31:14,138 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:31:17,352 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:31:17,397 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-09 15:31:17,397 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\a53ed7ad-b648-4808-b33c-5be7527be839.docx
2025-07-09 15:31:17,397 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:31:17,398 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:31:17,398 word_queue_processor.submit_task 161 INFO    => 任務已提交: 3b84ac7d-acc1-48c0-8428-dec2ee450165
2025-07-09 15:31:17,398 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 3b84ac7d-acc1-48c0-8428-dec2ee450165
2025-07-09 15:31:17,398 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\a53ed7ad-b648-4808-b33c-5be7527be839.docx
2025-07-09 15:31:18,485 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:31:30,052 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:31:30,598 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 16 筆數據
2025-07-09 15:31:30,693 word_queue_processor._process_tasks 120 INFO    => 任務完成: 3b84ac7d-acc1-48c0-8428-dec2ee450165
2025-07-09 15:31:30,693 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-09 15:31:30,693 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 16 筆數據
2025-07-09 15:31:30,693 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 16
2025-07-09 15:31:30,694 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 16 筆數據到 Excel
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行2, 列1): PET600ML黑松沙士PET600ML黑松加鹽沙士PET600ML黑松汽水PET600ML黑...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行5, 列1): PET580ML黑松茶花綠茶PET580ML黑松茶花一番煎茶PET580ML黑松茶花焙香麥茶...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行6, 列1): PKL300ML黑松FIN補給飲料PKL300ML黑松FIN好菌補給飲...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行7, 列1): PET580ML黑松FIN補給飲料PET580ML黑松FIN好菌補給飲...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行8, 列1): CAN320ML韋恩特濃咖啡CAN320ML韋恩特濃摩卡咖啡...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行9, 列1): PET500ML韋恩FLASHBREW閃萃黑咖啡PET500ML韋恩FLASHBREW閃萃拿鐵咖啡...
2025-07-09 15:31:30,695 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行10, 列1): PET980ML乳酸菌蘋果汁PET980ML桂花烏梅汁...
2025-07-09 15:31:30,697 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行13, 列1): PKL300ML立頓原味奶茶PKL300ML立頓巧克力奶茶PKL300ML立頓草莓奶茶PKL3...
2025-07-09 15:31:30,697 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行14, 列1): PKL330ML立頓原味奶茶PKL330ML立頓巧克力奶茶PKL330ML立頓琥珀紅茶...
2025-07-09 15:31:30,697 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行15, 列1): PET535ML立頓英式奶茶PET535ML立頓萃香奶綠PET535ML立頓蜂蜜紅茶...
2025-07-09 15:31:30,697 _DownloadBusinessNotificationInfo.save_to_excel 944 WARNING => 發現垂直制表符，將其替換為空格 (行16, 列1): PET425ML立頓醇奶茶歐蕾PET425ML立頓焙茶歐蕾...
2025-07-09 15:31:30,701 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:31:30,751 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-09 15:31:30,751 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:31:30,759 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6103
2025-07-09 15:31:40,421 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:31:44,579 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:31:44,745 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:31:56,391 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:32:00,350 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:32:00,408 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-07-09 15:32:15,096 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:32:30,968 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:32:31,048 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 15:32:40,454 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:32:45,613 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:32:56,423 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:33:01,325 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:33:01,325 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-07-09 15:33:16,179 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:33:16,305 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:33:31,467 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:33:31,468 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-07-09 15:33:40,524 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:33:47,129 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:33:47,244 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 15:33:56,439 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:34:01,605 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:34:01,607 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:34:18,051 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:34:18,093 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:34:31,751 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:34:40,617 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:34:52,014 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:34:52,062 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:34:52,062 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:34:52,078 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:34:52,078 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:34:52,078 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:34:52,078 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:34:52,078 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:34:52,078 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:34:52,922 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:34:53,155 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:34:53,430 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:34:57,838 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:35:00,209 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:35:03,276 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:35:04,405 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:35:05,487 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:35:09,534 word_processor_cache._init_backend 144 INFO    => 使用 Django 緩存後端
2025-07-09 15:35:09,576 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-07-09 15:35:09,576 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-07-09 15:35:09,581 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-07-09 15:35:09,581 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-07-09 15:35:09,581 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-07-09 15:35:09,581 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-07-09 15:35:09,582 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-07-09 15:35:09,582 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-07-09 15:35:10,680 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-07-09 15:35:10,794 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-07-09 15:35:11,144 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:35:15,451 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-07-09 15:35:20,177 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-07-09 15:35:21,398 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-07-09 15:35:24,461 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:35:24,567 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-07-09 15:35:25,221 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-07-09 15:35:25,772 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-07-09 15:35:26,088 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-07-09 15:35:33,140 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-07-09 15:35:33,152 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-07-09 15:35:34,175 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-07-09 15:35:37,562 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-07-09 15:35:41,951 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:35:42,224 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-07-09 15:35:52,065 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:35:52,544 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:35:55,310 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:35:55,423 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:36:05,422 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:36:05,701 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-07-09 15:36:06,005 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-07-09 15:36:06,161 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-07-09 15:36:06,209 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-07-09 15:36:06,318 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-07-09 15:36:06,510 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 4327
2025-07-09 15:36:06,592 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 5069
2025-07-09 15:36:08,159 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50711
2025-07-09 15:36:09,610 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:36:10,200 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:36:10,216 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:36:11,067 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10228', 類型: <class 'str'>
2025-07-09 15:36:11,122 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-09 15:36:11,122 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\badf7f4c-6aad-4545-ae63-8462655e5a2f.docx
2025-07-09 15:36:11,282 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:36:11,288 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:36:11,288 word_queue_processor.submit_task 161 INFO    => 任務已提交: db95929c-c3a5-4804-878b-71ab6a6a6113
2025-07-09 15:36:11,288 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: db95929c-c3a5-4804-878b-71ab6a6a6113
2025-07-09 15:36:11,288 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\badf7f4c-6aad-4545-ae63-8462655e5a2f.docx
2025-07-09 15:36:12,844 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:36:12,844 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-07-09 15:36:13,518 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10228', user_id: '31200'
2025-07-09 15:36:21,135 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 37 筆數據
2025-07-09 15:36:21,229 word_queue_processor._process_tasks 120 INFO    => 任務完成: db95929c-c3a5-4804-878b-71ab6a6a6113
2025-07-09 15:36:21,230 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-09 15:36:21,230 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 37 筆數據
2025-07-09 15:36:21,230 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10228', 數據筆數: 37
2025-07-09 15:36:21,231 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10228', 類型: <class 'str'>
2025-07-09 15:36:21,231 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 37 筆數據到 Excel
2025-07-09 15:36:21,231 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['2422', '295', '元/箱', '', '', None, '']
2025-07-09 15:36:21,237 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:36:21,288 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-09 15:36:21,289 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400506\省錢超市114年07月促銷通報1140626250627h16397B.xlsx
2025-07-09 15:36:21,302 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6270
2025-07-09 15:36:26,189 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:36:26,311 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:36:43,025 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:36:43,026 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-07-09 15:36:48,802 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:36:48,805 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 81
2025-07-09 15:36:50,902 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10106', 類型: <class 'str'>
2025-07-09 15:36:50,942 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-09 15:36:50,942 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\7d95cf2b-0310-428d-9e40-cda91be8e69d.docx
2025-07-09 15:36:50,943 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:36:50,943 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:36:50,943 word_queue_processor.submit_task 161 INFO    => 任務已提交: a8db5ccd-04e1-466b-a3a3-2fb2b9ac42e2
2025-07-09 15:36:50,943 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: a8db5ccd-04e1-466b-a3a3-2fb2b9ac42e2
2025-07-09 15:36:50,943 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\7d95cf2b-0310-428d-9e40-cda91be8e69d.docx
2025-07-09 15:36:52,075 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:36:52,218 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10106', user_id: '31200'
2025-07-09 15:36:56,680 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:37:05,999 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 55 筆數據
2025-07-09 15:37:06,133 word_queue_processor._process_tasks 120 INFO    => 任務完成: a8db5ccd-04e1-466b-a3a3-2fb2b9ac42e2
2025-07-09 15:37:06,133 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-09 15:37:06,133 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 55 筆數據
2025-07-09 15:37:06,133 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10106', 數據筆數: 55
2025-07-09 15:37:06,134 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10106', 類型: <class 'str'>
2025-07-09 15:37:06,135 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 55 筆數據到 Excel
2025-07-09 15:37:06,135 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['1207', '534', '元/箱', '84.76', '元/4入', '508.57', '元/箱']
2025-07-09 15:37:06,145 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 15:37:06,199 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-09 15:37:06,199 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400538\114年大潤發5072檔 DM.IP 促銷通報(1140718-1140731)250708h16510B.xlsx
2025-07-09 15:37:06,208 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 7116
2025-07-09 15:37:09,667 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:37:13,168 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:37:26,814 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:37:43,487 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:37:43,625 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-07-09 15:37:50,184 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-07-09 15:37:50,321 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-07-09 15:37:51,066 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1089 INFO    => 從 JSON 數據中提取的 rout: '10212', 類型: <class 'str'>
2025-07-09 15:37:51,236 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1192 INFO    => 嘗試使用企業級處理器
2025-07-09 15:37:51,381 _DownloadBusinessNotificationInfo.extract_data_worker 1173 INFO    => 開始處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400349\97a0b420-45a8-43c6-bba0-427316fc3958.docx
2025-07-09 15:37:51,530 _DownloadBusinessNotificationInfo.extract_data_worker 1179 ERROR   => 提取表格數據時發生錯誤: Word.Application.Documents
2025-07-09 15:37:51,547 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1200 WARNING => 企業級處理器失敗，降級到原始處理器: list index out of range
2025-07-09 15:37:51,548 word_queue_processor.submit_task 161 INFO    => 任務已提交: cab59a7c-6cec-4f5c-9c11-20168118a51c
2025-07-09 15:37:51,548 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: cab59a7c-6cec-4f5c-9c11-20168118a51c
2025-07-09 15:37:51,548 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1210 INFO    => 使用 legacy 處理器處理文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400349\97a0b420-45a8-43c6-bba0-427316fc3958.docx
2025-07-09 15:37:52,107 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:37:52,180 _DownloadBusinessNotificationInfo.extract_table_data 610 INFO    => extract_table_data 被調用，rout: '10212', user_id: '31200'
2025-07-09 15:37:57,193 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:37:57,257 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:38:09,722 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:38:14,085 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:38:14,086 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 129.0%
2025-07-09 15:38:14,263 _DownloadBusinessNotificationInfo.extract_data_worker_legacy 1213 INFO    => Legacy 處理器成功提取 31 筆數據
2025-07-09 15:38:14,320 word_queue_processor._process_tasks 120 INFO    => 任務完成: cab59a7c-6cec-4f5c-9c11-20168118a51c
2025-07-09 15:38:14,320 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1227 INFO    => 原始處理器執行成功
2025-07-09 15:38:14,320 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1236 INFO    => 成功提取 31 筆數據
2025-07-09 15:38:14,320 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1240 INFO    => 準備調用 save_to_excel，rout: '10212', 數據筆數: 31
2025-07-09 15:38:14,322 _DownloadBusinessNotificationInfo.save_to_excel 895 INFO    => save_to_excel 被調用，rout 參數值: '10212', 類型: <class 'str'>
2025-07-09 15:38:14,322 _DownloadBusinessNotificationInfo.save_to_excel 933 INFO    => 準備添加 31 筆數據到 Excel
2025-07-09 15:38:14,322 _DownloadBusinessNotificationInfo.save_to_excel 965 INFO    => 第一筆數據: ['5972', '152', '元/箱', '', '', None, '']
2025-07-09 15:38:14,327 _DownloadBusinessNotificationInfo.save_to_excel 1021 INFO    => 準備保存 Excel 文件到: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400349\楓康114年05檔促銷活動通報(0502-0529) - 發文250430h37314B.xlsx
2025-07-09 15:38:14,379 _DownloadBusinessNotificationInfo.save_to_excel 1023 INFO    => Excel 文件保存成功
2025-07-09 15:38:14,380 _DownloadBusinessNotificationInfo.select_business_notification_price_download 1242 INFO    => 成功保存 Excel 文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400349\楓康114年05檔促銷活動通報(0502-0529) - 發文250430h37314B.xlsx
2025-07-09 15:38:14,391 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 6184
2025-07-09 15:38:27,712 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:38:44,347 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:38:44,505 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-07-09 15:38:52,174 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:38:57,935 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:38:57,935 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-07-09 15:39:09,796 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-07-09 15:39:15,302 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:39:15,410 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-07-09 15:39:28,301 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:39:28,394 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-07-09 15:39:34,956 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-07-09 15:39:45,878 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-07-09 15:39:45,878 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
