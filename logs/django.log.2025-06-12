2025-06-12 07:23:45,080 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-12 07:23:45,295 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-06-12 07:23:45,296 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 79
2025-06-12 07:44:50,010 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 07:44:53,461 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 07:44:53,498 word_enterprise_processor.init 215 INFO    => 企業級 Word 處理器已初始化
2025-06-12 07:44:53,527 word_processor_auto_manager.start  67 INFO    => Word 處理器自動管理器已啟動
2025-06-12 07:44:53,527 word_processor_auto_manager.<module> 389 INFO    => Word 處理器自動管理器已初始化
2025-06-12 07:44:53,527 word_enterprise_processor.<module> 448 INFO    => 自動管理器已啟動
2025-06-12 07:44:54,861 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-12 07:44:54,886 token_utils.verify_access_token  33 ERROR   => 缺少 Refresh token
2025-06-12 07:44:54,887 log.log_response 230 WARNING => Unauthorized: /api/users/select_user_tag_program_and_permission/
2025-06-12 07:44:54,888 basehttp.log_message 161 WARNING => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 401 77
2025-06-12 07:44:54,982 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-12 07:44:55,710 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:44:56,291 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 07:44:59,073 word_enterprise_processor._worker_loop 152 INFO    => Worker 0 已初始化
2025-06-12 07:45:00,414 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/login/ HTTP/1.1" 200 0
2025-06-12 07:45:00,778 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 133
2025-06-12 07:45:00,944 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-12 07:45:01,016 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-12 07:45:01,023 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-12 07:45:01,023 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-12 07:45:01,025 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-12 07:45:01,150 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 2185
2025-06-12 07:45:01,475 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3280
2025-06-12 07:45:01,615 word_enterprise_processor._worker_loop 152 INFO    => Worker 2 已初始化
2025-06-12 07:45:02,019 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 70
2025-06-12 07:45:02,040 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 2058
2025-06-12 07:45:03,208 word_enterprise_processor._worker_loop 152 INFO    => Worker 1 已初始化
2025-06-12 07:45:04,797 word_enterprise_processor._worker_loop 152 INFO    => Worker 5 已初始化
2025-06-12 07:45:06,073 word_enterprise_processor._worker_loop 152 INFO    => Worker 6 已初始化
2025-06-12 07:45:07,839 word_enterprise_processor._worker_loop 152 INFO    => Worker 7 已初始化
2025-06-12 07:45:09,682 word_enterprise_processor._worker_loop 152 INFO    => Worker 4 已初始化
2025-06-12 07:45:11,546 word_enterprise_processor._worker_loop 152 INFO    => Worker 3 已初始化
2025-06-12 07:45:13,307 word_enterprise_processor._worker_loop 152 INFO    => Worker 9 已初始化
2025-06-12 07:45:14,749 word_enterprise_processor._worker_loop 152 INFO    => Worker 17 已初始化
2025-06-12 07:45:17,249 word_enterprise_processor._worker_loop 152 INFO    => Worker 16 已初始化
2025-06-12 07:45:18,704 word_enterprise_processor._worker_loop 152 INFO    => Worker 13 已初始化
2025-06-12 07:45:19,992 word_enterprise_processor._worker_loop 152 INFO    => Worker 8 已初始化
2025-06-12 07:45:22,652 word_enterprise_processor._worker_loop 152 INFO    => Worker 18 已初始化
2025-06-12 07:45:24,072 word_enterprise_processor._worker_loop 152 INFO    => Worker 19 已初始化
2025-06-12 07:45:25,543 word_enterprise_processor._worker_loop 152 INFO    => Worker 15 已初始化
2025-06-12 07:45:27,003 word_enterprise_processor._worker_loop 152 INFO    => Worker 12 已初始化
2025-06-12 07:45:27,884 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:45:28,391 word_enterprise_processor._worker_loop 152 INFO    => Worker 14 已初始化
2025-06-12 07:45:31,391 word_enterprise_processor._worker_loop 152 INFO    => Worker 10 已初始化
2025-06-12 07:45:32,500 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 526
2025-06-12 07:45:32,568 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-06-12 07:45:32,568 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 0
2025-06-12 07:45:32,568 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-12 07:45:32,776 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 07:45:32,857 word_enterprise_processor._worker_loop 152 INFO    => Worker 11 已初始化
2025-06-12 07:45:32,956 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 07:45:33,075 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9687
2025-06-12 07:45:53,500 word_enterprise_processor._monitor_loop 233 INFO    => 系統狀態: {'available_workers': 20, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 07:46:00,088 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:46:07,203 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 508
2025-06-12 07:46:07,328 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 07:46:07,467 basehttp.log_message 161 INFO    => "POST /api/users/select_combobox_dept_code_name/ HTTP/1.1" 200 9687
2025-06-12 07:46:07,525 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 07:46:10,421 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-12 07:46:10,564 basehttp.log_message 161 INFO    => "POST /api/accounts/logout/ HTTP/1.1" 200 39
2025-06-12 07:46:15,660 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-12 07:46:15,790 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-12 07:46:15,948 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-12 07:46:16,141 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3280
2025-06-12 07:46:16,566 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1326
2025-06-12 07:46:16,615 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8178
2025-06-12 07:46:19,394 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-06-12 07:46:19,569 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 07:46:19,590 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 07:46:21,694 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50957
2025-06-12 07:46:23,702 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 0
2025-06-12 07:46:23,702 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-06-12 07:46:24,080 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification_file_statistics/ HTTP/1.1" 200 57
2025-06-12 07:46:24,136 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-12 07:46:24,595 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_price_download/ HTTP/1.1" 200 0
2025-06-12 07:46:25,416 _DownloadBusinessNotificationInfo.select_business_notification_price_download 894 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400468\博客來-114年7月促銷通報250611h16708B.docx
2025-06-12 07:46:25,417 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_price_download/
2025-06-12 07:46:25,417 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_price_download/ HTTP/1.1" 404 81
2025-06-12 07:46:31,097 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_business_notification_price_download/
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\exception.py", line 47, in inner
    response = get_response(request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\core\handlers\base.py", line 204, in _get_response
    response = response.render()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\django\template\response.py", line 105, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\response.py", line 70, in rendered_content
    ret = renderer.render(self.data, accepted_media_type, context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\renderers.py", line 723, in render
    context = self.get_context(data, accepted_media_type, renderer_context)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\renderers.py", line 654, in get_context
    raw_data_post_form = self.get_raw_data_form(data, view, 'POST', request)
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\renderers.py", line 553, in get_raw_data_form
    serializer = view.get_serializer()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\generics.py", line 108, in get_serializer
    serializer_class = self.get_serializer_class()
  File "C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\venv\lib\site-packages\rest_framework\generics.py", line 125, in get_serializer_class
    % self.__class__.__name__
AssertionError: 'DocumentViewSet' should either include a `serializer_class` attribute, or override the `get_serializer_class()` method.
2025-06-12 07:46:31,104 basehttp.log_message 161 ERROR   => "GET /api/documents/select_business_notification_price_download/ HTTP/1.1" 500 106885
2025-06-12 07:46:31,615 log.log_response 230 WARNING => Not Found: /favicon.ico
2025-06-12 07:46:31,615 basehttp.log_message 161 WARNING => "GET /favicon.ico HTTP/1.1" 404 2293
2025-06-12 07:46:32,274 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:46:36,721 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification_download/ HTTP/1.1" 200 0
2025-06-12 07:46:37,039 _DownloadBusinessNotificationInfo.select_business_notification_download 402 ERROR   => 找不到文件: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\業務通報\11400468\博客來-114年7月促銷通報250611h16708B.docx
2025-06-12 07:46:37,040 log.log_response 230 WARNING => Not Found: /api/documents/select_business_notification_download/
2025-06-12 07:46:37,040 basehttp.log_message 161 WARNING => "POST /api/documents/select_business_notification_download/ HTTP/1.1" 404 81
2025-06-12 07:46:39,999 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-12 07:46:40,129 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 07:46:40,336 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 07:46:42,628 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61790
2025-06-12 07:46:47,389 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2025-06-12 07:46:47,471 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-12 07:46:47,835 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 507
2025-06-12 07:46:48,311 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2025-06-12 07:46:49,009 _DownloadLetterInfo.select_letter_download 380 INFO    => 開始處理公文下載請求: user_id=32000, role=0
2025-06-12 07:46:49,010 _DownloadLetterInfo.select_letter_download 381 INFO    => 請求數據: {'pudcno': '11400232', 'file_name': '114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx', 'noModifyFile': 'Y'}
2025-06-12 07:46:49,010 _DownloadLetterInfo.select_letter_download 397 INFO    => 執行 SQL: 
    SELECT ATTACHMENT_NAME, FILEPASSWORD
      FROM ( SELECT FILEARTICLE ATTACHMENT_NAME, FILEPASSWORD
               FROM PUDCHT )
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME UNION ALL
    SELECT ATTACHMENT_NAME, '' FILEPASSWORD
      FROM PUDCHT_ATTACHMENT A
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME 
2025-06-12 07:46:49,010 _DownloadLetterInfo.select_letter_download 398 INFO    => SQL 參數: {'qFILE_NAME': '114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx'}
2025-06-12 07:46:49,019 _DownloadLetterInfo.select_letter_download 414 INFO    => 需要處理文件替換: file_url=C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-12 07:46:49,025 word_enterprise_processor._process_document_internal 316 ERROR   => 處理文檔失敗: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 07:46:49,025 _DownloadLetterInfo.unprotect_and_protect_docx 260 ERROR   => 處理文件時發生錯誤: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 07:46:49,026 word_queue_processor.submit_task 161 INFO    => 任務已提交: c9fa2f02-848d-48d7-a26f-ae9a13da0925
2025-06-12 07:46:49,026 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: c9fa2f02-848d-48d7-a26f-ae9a13da0925
2025-06-12 07:46:53,509 word_enterprise_processor._monitor_loop 233 INFO    => 系統狀態: {'available_workers': 20, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 07:46:53,730 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: (註1)
2025-06-12 07:46:53,974 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註1
2025-06-12 07:46:54,886 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註2
2025-06-12 07:46:54,990 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註2點
2025-06-12 07:46:55,287 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註3
2025-06-12 07:46:55,335 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註3點
2025-06-12 07:46:55,399 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註4
2025-06-12 07:46:55,430 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註4點
2025-06-12 07:46:55,485 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註5
2025-06-12 07:46:55,502 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註5%
2025-06-12 07:46:55,532 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註6
2025-06-12 07:46:55,554 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註6%
2025-06-12 07:46:55,606 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註7
2025-06-12 07:46:55,624 _DownloadLetterInfo.replace_and_format 335 INFO    => 找到模式: 註7元
2025-06-12 07:46:57,395 word_queue_processor._process_tasks 120 INFO    => 任務完成: c9fa2f02-848d-48d7-a26f-ae9a13da0925
2025-06-12 07:46:57,396 _DownloadLetterInfo.select_letter_download 419 INFO    => 文件處理成功: new_file_url=C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400232\32000_114年新產品SlimCAN240ml黑松沙士(6入x4組)上市推廣活動成果函250610h16515P.docx
2025-06-12 07:46:57,440 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 29606
2025-06-12 07:47:04,450 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:47:36,635 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:47:53,522 word_enterprise_processor._monitor_loop 233 INFO    => 系統狀態: {'available_workers': 20, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 07:48:08,805 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:48:40,975 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:48:53,536 word_enterprise_processor._monitor_loop 233 INFO    => 系統狀態: {'available_workers': 20, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 07:49:13,140 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:49:45,331 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:49:53,550 word_enterprise_processor._monitor_loop 233 INFO    => 系統狀態: {'available_workers': 20, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 07:50:17,494 word_processor_auto_manager._monitor_loop 105 ERROR   => 監控循環錯誤: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 07:50:28,713 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:25:42,437 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:25:42,438 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:25:42,496 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:25:42,496 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:25:42,506 word_error_handler._click_dialog_button  96 INFO    => 發現錯誤對話框: ✳ Word異常
2025-06-12 08:25:42,641 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:25:42,653 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:25:42,687 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:25:42,731 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:25:42,768 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:25:42,804 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:25:43,353 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:25:43,587 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:25:43,819 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 08:25:54,739 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:25:54,743 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:25:55,866 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:25:55,871 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:25:56,244 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:25:58,730 word_error_handler._click_dialog_button  96 INFO    => 發現錯誤對話框: ✳ Word異常
2025-06-12 08:26:01,093 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:26:01,098 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:26:01,147 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:26:01,148 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:26:01,153 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:26:01,153 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:26:01,153 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:26:01,154 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:26:01,154 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:26:01,154 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:26:01,680 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:26:01,811 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:26:02,099 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:26:06,349 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:26:07,484 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:26:09,607 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:26:11,517 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:26:13,155 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:26:14,564 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:26:14,668 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:26:15,108 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:26:22,097 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 08:26:24,813 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:26:29,275 basehttp.log_message 161 INFO    => "POST /api/accounts/login/ HTTP/1.1" 200 138
2025-06-12 08:26:29,418 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-12 08:26:30,142 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3280
2025-06-12 08:26:30,871 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8178
2025-06-12 08:26:30,900 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1326
2025-06-12 08:26:30,938 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-12 08:26:32,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:26:32,752 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:26:34,027 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-12 08:26:34,147 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 08:26:34,259 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 08:26:36,520 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61790
2025-06-12 08:26:39,440 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 513
2025-06-12 08:26:39,441 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-12 08:26:41,583 _DownloadLetterInfo.select_letter_download 391 INFO    => 開始處理公文下載請求: user_id=32000, role=0
2025-06-12 08:26:41,583 _DownloadLetterInfo.select_letter_download 392 INFO    => 請求數據: {'pudcno': '11400227', 'file_name': 'CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx', 'noModifyFile': 'Y'}
2025-06-12 08:26:41,584 _DownloadLetterInfo.select_letter_download 408 INFO    => 執行 SQL: 
    SELECT ATTACHMENT_NAME, FILEPASSWORD
      FROM ( SELECT FILEARTICLE ATTACHMENT_NAME, FILEPASSWORD
               FROM PUDCHT )
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME UNION ALL
    SELECT ATTACHMENT_NAME, '' FILEPASSWORD
      FROM PUDCHT_ATTACHMENT A
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME 
2025-06-12 08:26:41,584 _DownloadLetterInfo.select_letter_download 409 INFO    => SQL 參數: {'qFILE_NAME': 'CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx'}
2025-06-12 08:26:41,592 _DownloadLetterInfo.select_letter_download 425 INFO    => 需要處理文件替換: file_url=C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400227\CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 08:26:41,592 word_safe_processor.process_document_safely  37 INFO    => 嘗試處理文檔 (第 1 次): CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 08:26:41,593 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:26:41,593 word_error_handler.__enter__ 211 ERROR   => 創建 Word 應用程序失敗: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:41,593 word_safe_processor.process_document_safely  55 ERROR   => 處理文檔失敗 (第 1 次): (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:42,537 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:26:43,623 word_safe_processor.process_document_safely  37 INFO    => 嘗試處理文檔 (第 2 次): CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 08:26:43,650 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:26:43,702 word_error_handler.__enter__ 211 ERROR   => 創建 Word 應用程序失敗: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:43,704 word_safe_processor.process_document_safely  55 ERROR   => 處理文檔失敗 (第 2 次): (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:45,136 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:26:45,720 word_safe_processor.process_document_safely  37 INFO    => 嘗試處理文檔 (第 3 次): CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 08:26:45,790 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:26:45,837 word_error_handler.__enter__ 211 ERROR   => 創建 Word 應用程序失敗: (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:45,871 word_safe_processor.process_document_safely  55 ERROR   => 處理文檔失敗 (第 3 次): (-2147221008, 'CoInitialize 尚未被呼叫。', None, None)
2025-06-12 08:26:45,907 word_safe_processor.process_document_safely  59 WARNING => 所有嘗試都失敗，清理 Word 進程
2025-06-12 08:26:51,085 word_error_handler.kill_all_word_processes 254 INFO    => 已關閉 35 個 Word 進程
2025-06-12 08:26:56,193 word_safe_processor.process_document_safely  65 ERROR   => 無法處理文檔: CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 08:26:56,531 word_enterprise_processor._process_document_internal 343 ERROR   => 處理文檔失敗: (-2147417842, '應用程式所呼叫了整理給不同執行緒的介面。', None, None)
2025-06-12 08:26:56,812 _DownloadLetterInfo.unprotect_and_protect_docx 271 ERROR   => 處理文件時發生錯誤: (-2147417842, '應用程式所呼叫了整理給不同執行緒的介面。', None, None)
2025-06-12 08:26:56,913 word_queue_processor.submit_task 161 INFO    => 任務已提交: 57f6b732-6583-496d-bcad-47d9b30d94bb
2025-06-12 08:26:56,913 word_queue_processor._process_tasks 116 INFO    => 開始處理任務: 57f6b732-6583-496d-bcad-47d9b30d94bb
2025-06-12 08:26:57,083 _DownloadLetterInfo.unprotect_and_protect_docx_worker 230 ERROR   => 錯誤: (-2147023174, 'RPC 伺服器無法使用。', None, None)
2025-06-12 08:26:58,121 word_queue_processor._process_tasks 120 INFO    => 任務完成: 57f6b732-6583-496d-bcad-47d9b30d94bb
2025-06-12 08:26:58,192 _DownloadLetterInfo.select_letter_download 432 ERROR   => 文件處理失敗
2025-06-12 08:26:58,205 log.log_response 230 ERROR   => Internal Server Error: /api/documents/select_letter_download/
2025-06-12 08:26:58,564 basehttp.log_message 161 ERROR   => "POST /api/documents/select_letter_download/ HTTP/1.1" 500 87
2025-06-12 08:27:01,168 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:27:03,489 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:27:03,728 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:27:15,465 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:27:15,550 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 08:27:34,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:27:35,198 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 08:27:42,631 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:27:46,137 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:27:46,276 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:27:58,298 word_queue_processor._process_tasks 135 WARNING => Word 應用程序無響應，重新初始化
2025-06-12 08:27:58,394 word_queue_processor._cleanup_word  93 ERROR   => 清理 Word 失敗: (-2147023174, 'RPC 伺服器無法使用。', None, None)
2025-06-12 08:28:01,302 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:28:01,409 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:28:07,066 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:28:07,209 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:28:16,840 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:28:16,979 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 08:28:38,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:28:38,417 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:28:42,664 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:28:47,653 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:28:47,778 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:29:01,450 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:29:09,568 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:29:09,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:29:18,205 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:29:41,250 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:29:41,725 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 08:29:42,736 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:29:48,493 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:29:48,553 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:30:01,636 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:30:12,577 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:30:12,776 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:30:18,863 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:30:18,947 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:30:42,807 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:30:43,636 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:30:43,824 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:30:49,537 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:30:49,634 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 08:31:01,673 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:31:14,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:31:14,882 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 110.8%
2025-06-12 08:31:20,316 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:31:20,422 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:31:42,879 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:31:45,724 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:31:46,115 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:31:51,219 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:31:51,335 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:32:01,797 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:32:17,013 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:32:17,247 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 107.8%
2025-06-12 08:32:21,988 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:32:22,079 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:32:42,940 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:32:48,211 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:32:48,231 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 08:32:52,721 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:32:52,855 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:33:01,978 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:33:15,229 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:33:15,232 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:33:15,286 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:33:15,288 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:33:15,464 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:33:15,519 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:33:15,551 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:33:15,581 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:33:15,613 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:33:15,648 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:33:16,247 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:33:16,431 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:33:16,796 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:33:19,667 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:33:20,145 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:33:22,653 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:33:24,235 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:33:25,685 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:33:31,399 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:33:31,402 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:33:31,455 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:33:31,456 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:33:31,578 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:33:31,606 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:33:31,637 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:33:31,668 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:33:31,700 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:33:31,737 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:33:32,163 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:33:32,354 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:33:32,569 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:33:34,987 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:33:35,533 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:33:37,454 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:33:39,659 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:33:42,401 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:33:44,121 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:33:47,632 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:33:47,711 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:33:49,700 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 08:33:51,367 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:34:02,983 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:34:03,140 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:34:15,316 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:34:18,160 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:34:31,471 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:34:33,701 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:34:33,762 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:34:48,432 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:34:48,586 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:35:04,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:35:04,430 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 08:35:15,374 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:35:19,084 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:35:31,560 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:35:34,932 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:35:34,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:35:49,345 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:35:49,438 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 08:36:05,079 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:36:05,083 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:36:15,421 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:36:19,941 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:36:20,055 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 08:36:31,607 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:36:35,373 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:36:35,496 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:36:50,504 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:37:06,085 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:37:06,188 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-12 08:37:15,485 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:37:20,650 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:37:31,626 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:37:36,735 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:37:36,854 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-06-12 08:37:50,790 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:38:07,485 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:38:07,541 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:38:15,493 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:38:20,937 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:38:31,693 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:38:38,108 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:38:38,141 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:38:51,074 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:39:08,412 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:39:08,417 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-06-12 08:39:15,504 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:39:21,215 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:39:31,744 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:39:38,570 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:39:38,574 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:39:51,365 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:40:08,719 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:40:08,723 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:40:15,542 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:40:21,688 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:40:21,780 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:40:31,782 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:40:38,933 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:40:39,004 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:40:52,383 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:40:52,491 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 08:41:09,452 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:41:09,545 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:41:15,603 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:41:22,957 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:41:31,822 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:41:40,329 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:41:40,441 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:41:53,087 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:42:10,872 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:42:15,634 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:42:23,430 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:42:23,514 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:42:31,842 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:42:41,048 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:42:41,062 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 08:42:54,188 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:42:54,304 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:43:11,458 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:43:11,556 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 08:43:15,656 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:43:24,806 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:43:31,864 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:43:42,210 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:43:42,321 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:43:55,021 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:43:55,047 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:44:15,868 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:44:15,872 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:44:15,906 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:44:15,907 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:44:15,912 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:44:15,913 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:44:15,913 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:44:15,913 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:44:15,913 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:44:15,913 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:44:16,354 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:44:16,471 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:44:16,820 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:44:20,374 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:44:21,017 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:44:23,678 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:44:25,230 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:44:27,802 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:44:32,979 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:44:32,982 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:44:33,019 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:44:33,019 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:44:33,023 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:44:33,023 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:44:33,023 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:44:33,024 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:44:33,024 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:44:33,024 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:44:33,558 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:44:33,645 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:44:34,096 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 139.3%
2025-06-12 08:44:36,949 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:44:38,812 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:44:40,597 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:44:42,334 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:44:45,324 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:44:45,467 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:44:47,284 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:44:50,467 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 08:44:52,385 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:45:04,657 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:45:04,661 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:45:15,946 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:45:17,434 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:45:33,035 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:45:34,808 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:45:34,813 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 08:45:47,722 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:45:47,848 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 08:46:04,957 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:46:04,966 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:46:16,019 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:46:18,364 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:46:33,063 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:46:35,153 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:46:35,246 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 08:46:48,511 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:47:05,585 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:47:05,602 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 08:47:16,091 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:47:18,646 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:47:33,148 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:47:35,738 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:47:35,743 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:47:48,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:48:05,891 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:48:05,904 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:48:16,171 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:48:18,928 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:48:33,221 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:48:36,124 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:48:36,189 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:48:49,074 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:49:06,457 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:49:06,577 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 08:49:16,237 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:49:19,215 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:49:33,270 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:49:36,976 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:49:37,116 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:49:49,539 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:49:49,648 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 08:50:07,798 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:50:07,872 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:50:16,279 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:50:20,280 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:50:20,334 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 08:50:33,287 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:50:38,394 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:50:38,449 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:50:50,960 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:50:51,069 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 08:51:00,148 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:51:00,151 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:51:00,211 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:51:00,211 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:51:00,216 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:51:00,216 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:51:00,216 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:51:00,216 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:51:00,216 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:51:00,216 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:51:00,834 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:51:01,071 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:51:01,418 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:51:04,556 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:51:05,191 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:51:07,346 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:51:09,843 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:51:11,070 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:51:17,268 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:51:17,271 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:51:17,312 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:51:17,313 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:51:17,317 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:51:17,317 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:51:17,318 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:51:17,318 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:51:17,319 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:51:17,319 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:51:17,972 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:51:18,131 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:51:18,399 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 08:51:20,379 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:51:22,733 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:51:24,697 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:51:27,962 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:51:29,815 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:51:30,518 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:51:31,958 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:51:32,023 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:51:37,295 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 08:51:39,452 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:51:48,866 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:51:48,980 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:52:00,218 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:52:02,324 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:52:02,457 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:52:17,320 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:52:19,268 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:52:19,341 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:52:33,068 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:52:33,200 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:52:49,927 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:52:50,015 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:53:00,246 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:53:03,601 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:53:17,384 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:53:20,513 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:53:20,521 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:53:33,896 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:53:34,043 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:53:50,768 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:53:50,814 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.8%
2025-06-12 08:54:00,307 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:54:04,543 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:54:17,426 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:54:21,040 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:54:21,045 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 08:54:34,804 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:54:34,915 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 08:54:51,316 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:54:51,361 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-06-12 08:55:00,359 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:55:05,519 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:55:05,631 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:55:17,483 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:55:21,515 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:55:36,263 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:55:36,343 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:55:51,811 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:55:51,816 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:56:00,402 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:56:06,851 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:56:06,962 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 08:56:17,585 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:56:22,327 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:56:22,404 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:56:31,731 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:56:31,734 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:56:31,769 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:56:31,769 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:56:31,773 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:56:31,773 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:56:31,773 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:56:31,773 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:56:31,774 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:56:31,774 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:56:32,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:56:32,434 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:56:32,723 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:56:35,358 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:56:37,103 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:56:39,219 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:56:41,258 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:56:43,472 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:56:49,951 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 08:56:49,955 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 08:56:50,003 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 08:56:50,004 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 08:56:50,186 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 08:56:50,213 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 08:56:50,251 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 08:56:50,289 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 08:56:50,319 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 08:56:50,361 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 08:56:50,924 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 08:56:51,082 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 08:56:51,390 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:56:53,760 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 08:56:54,899 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 08:56:56,893 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 08:57:00,349 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 08:57:02,345 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 08:57:03,152 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:57:03,810 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 08:57:10,880 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 08:57:14,398 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 08:57:22,123 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:57:22,262 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 08:57:31,798 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:57:33,554 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:57:33,628 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 08:57:50,015 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:57:52,850 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:57:52,958 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:58:03,840 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:58:23,314 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:58:23,401 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:58:31,827 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:58:34,196 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:58:34,327 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 08:58:39,358 word_error_handler._click_dialog_button 103 INFO    => 發現錯誤對話框: ✳ Word文檔處理
2025-06-12 08:58:39,358 word_error_handler._click_dialog_button 103 INFO    => 發現錯誤對話框: ✳ Word文檔處理
2025-06-12 08:58:50,077 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:58:53,566 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:58:53,570 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 117.6%
2025-06-12 08:59:04,948 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:59:05,013 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 08:59:23,895 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:59:24,010 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 08:59:31,865 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:59:35,444 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:59:50,110 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 08:59:54,601 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 08:59:54,621 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:00:05,792 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:00:05,910 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:00:24,954 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:00:24,972 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 09:00:31,922 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:00:36,538 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:00:36,678 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-12 09:00:50,131 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:00:55,221 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:01:07,463 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:01:07,640 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 09:01:25,354 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:01:25,358 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:01:31,929 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:01:38,017 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:01:50,199 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:01:55,801 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:01:55,888 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:02:08,271 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:02:08,407 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:02:26,217 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:02:26,222 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:02:31,939 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:02:38,929 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:02:38,973 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 09:02:50,214 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:02:56,575 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:02:56,678 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:03:09,152 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:03:27,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:03:27,176 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-06-12 09:03:31,970 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:03:39,524 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:03:39,629 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 09:03:50,256 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:03:57,331 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:03:57,335 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:04:10,164 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:04:27,722 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:04:32,000 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:04:40,506 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:04:40,650 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 09:04:50,280 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:04:58,070 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:04:58,077 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 09:05:11,181 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:05:28,468 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:05:28,647 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 09:05:32,024 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:05:41,326 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:05:50,306 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:05:59,231 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:05:59,277 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:06:11,600 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:06:11,691 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:06:29,434 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:06:29,437 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:06:32,056 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:06:42,041 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:06:50,337 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:06:59,811 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:06:59,900 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 09:07:12,434 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:07:12,606 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 09:07:30,406 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:07:32,123 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:07:43,160 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:07:50,350 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:08:00,714 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:08:00,817 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:08:13,514 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:08:13,614 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:08:31,362 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:08:32,194 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:08:44,116 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:08:50,361 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:09:01,959 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:09:02,137 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 92.8%
2025-06-12 09:09:14,513 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:09:32,235 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:09:32,704 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:09:32,707 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:09:44,875 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:09:50,401 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:10:03,102 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:10:03,201 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:10:15,325 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:10:15,437 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:10:32,244 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:10:33,719 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:10:33,724 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:10:45,986 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:10:50,443 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:11:03,921 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:11:03,937 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 137.5%
2025-06-12 09:11:16,285 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:11:16,351 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:11:32,246 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:11:34,344 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:11:34,454 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:11:46,972 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:11:47,060 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:11:50,471 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:12:05,222 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:12:05,329 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 09:12:17,568 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:12:32,275 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:12:35,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:12:35,851 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:12:47,892 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:12:48,074 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:12:50,474 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:13:06,126 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:13:06,153 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:13:18,373 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:13:32,330 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:13:36,863 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:13:36,867 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:13:48,752 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:13:48,854 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:13:50,489 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:14:07,259 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:14:19,339 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:14:32,343 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:14:37,460 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:14:37,551 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:14:49,718 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:14:49,813 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:14:50,527 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:15:07,717 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:15:20,306 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:15:20,321 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 09:15:32,362 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:15:38,131 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:15:38,228 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-12 09:15:50,613 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:15:50,672 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:15:50,786 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:16:08,836 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:16:08,841 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:16:21,273 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:16:21,277 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:16:32,392 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:16:39,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:16:39,388 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 09:16:50,682 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:16:51,605 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:16:51,752 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 09:17:09,850 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:17:09,853 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:17:22,248 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:17:22,252 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:17:32,460 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:17:40,159 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:17:40,275 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:17:50,694 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:17:52,454 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:18:10,907 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:18:11,023 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 09:18:22,806 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:18:22,896 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:18:32,496 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:18:41,393 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:18:50,733 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:18:53,436 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:19:11,719 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:19:11,838 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:19:23,967 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:19:32,529 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:19:42,205 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:19:42,210 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:19:50,788 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:19:54,480 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:19:54,513 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:20:12,655 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:20:12,783 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:20:24,873 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:20:32,565 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:20:43,158 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:20:43,161 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 09:20:50,803 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:20:55,315 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:21:13,308 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:21:13,312 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:21:25,509 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:21:25,528 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:21:32,618 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:21:43,684 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:21:43,818 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-12 09:21:50,820 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:21:55,687 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:22:14,338 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:22:14,357 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 157.7%
2025-06-12 09:22:26,016 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:22:26,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:22:32,648 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:22:44,518 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:22:44,521 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:22:50,865 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:22:56,644 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:22:56,661 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:23:14,847 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:23:14,943 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:23:26,817 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:23:32,676 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:23:45,479 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:23:45,482 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:23:50,870 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:23:57,058 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:23:57,063 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 09:24:15,837 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:24:15,974 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:24:27,238 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:24:32,688 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:24:46,581 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:24:46,585 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 09:24:50,942 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:24:57,623 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:24:57,765 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 09:25:16,759 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:25:16,762 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:25:28,393 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:25:28,424 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 125.0%
2025-06-12 09:25:32,715 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:25:38,090 word_error_handler._click_dialog_button 103 INFO    => 發現錯誤對話框: ✳ Word下載問題
2025-06-12 09:25:38,090 word_error_handler._click_dialog_button 103 INFO    => 發現錯誤對話框: ✳ Word下載問題
2025-06-12 09:25:47,121 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:25:50,986 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:25:58,597 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:26:17,666 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:26:17,670 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:26:28,738 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:26:32,774 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:26:48,026 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:26:48,130 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 09:26:51,001 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:26:58,756 word_error_handler._click_dialog_button 103 INFO    => 發現錯誤對話框: ✳ Word下載問題
2025-06-12 09:26:59,132 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:27:18,657 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:27:18,687 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:27:32,810 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:27:51,038 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:57:14,904 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 09:57:14,912 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 09:57:15,001 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 09:57:15,002 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 09:57:15,189 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 09:57:15,215 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 09:57:15,256 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 09:57:15,322 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 09:57:15,349 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 09:57:15,409 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 09:57:16,177 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 09:57:16,295 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 09:57:16,407 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 09:57:19,502 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 09:57:19,886 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 09:57:22,840 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 09:57:25,311 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 09:57:28,354 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 09:57:37,801 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 09:57:37,804 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 09:57:37,848 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 09:57:37,848 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 09:57:37,987 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 09:57:38,043 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 09:57:38,058 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 09:57:38,087 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 09:57:38,127 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 09:57:38,185 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 09:57:38,644 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 09:57:38,750 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 09:57:39,091 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 09:57:41,893 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 09:57:43,229 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 09:57:45,983 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 09:57:47,099 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:57:47,110 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 09:57:49,236 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 09:57:51,893 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 09:57:53,394 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 09:58:04,766 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 09:58:07,136 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 09:58:09,396 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:58:09,403 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:58:15,046 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:58:17,627 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:58:17,928 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.2%
2025-06-12 09:58:37,861 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:58:39,609 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:58:39,646 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 09:58:48,830 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:58:48,835 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 09:59:10,124 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:59:10,288 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 09:59:15,096 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:59:19,104 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:59:19,217 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 09:59:37,925 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 09:59:40,918 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:59:41,011 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 09:59:49,569 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 09:59:49,746 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 10:00:07,000 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-12 10:00:07,001 basehttp.log_message 161 INFO    => "OPTIONS /api/accounts/logout/ HTTP/1.1" 200 0
2025-06-12 10:00:11,609 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:00:11,764 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:00:15,112 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:00:20,119 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:00:20,168 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 10:00:20,576 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_check_user/ HTTP/1.1" 200 0
2025-06-12 10:00:21,096 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-12 10:00:21,206 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 0
2025-06-12 10:00:21,368 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 57
2025-06-12 10:00:21,554 basehttp.log_message 161 INFO    => "OPTIONS /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 0
2025-06-12 10:00:21,557 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_bullet_board/ HTTP/1.1" 200 0
2025-06-12 10:00:21,558 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter2/ HTTP/1.1" 200 0
2025-06-12 10:00:21,558 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_business_notification2/ HTTP/1.1" 200 0
2025-06-12 10:00:21,767 basehttp.log_message 161 INFO    => "POST /api/users/select_user_main_menu_program_and_permission/ HTTP/1.1" 200 1792
2025-06-12 10:00:21,910 basehttp.log_message 161 INFO    => "POST /api/documents/select_bullet_board/ HTTP/1.1" 200 3280
2025-06-12 10:00:22,259 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 1834
2025-06-12 10:00:22,436 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 8178
2025-06-12 10:00:37,927 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:00:42,360 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:00:42,381 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 10:00:50,710 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:01:12,577 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:01:12,705 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 127.8%
2025-06-12 10:01:15,121 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:01:15,938 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 206
2025-06-12 10:01:15,994 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 0
2025-06-12 10:01:15,994 basehttp.log_message 161 INFO    => "OPTIONS /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 0
2025-06-12 10:01:16,127 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 10:01:16,253 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 10:01:16,264 basehttp.log_message 161 INFO    => "POST /api/users/select_user_tag_program_and_permission/ HTTP/1.1" 200 369
2025-06-12 10:01:16,417 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 10:01:16,544 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 10:01:20,906 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:01:20,938 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 10:01:23,984 basehttp.log_message 161 INFO    => "POST /api/documents/select_business_notification2/ HTTP/1.1" 200 50702
2025-06-12 10:01:28,063 basehttp.log_message 161 INFO    => "POST /api/documents/select_combobox_kind_code_name/ HTTP/1.1" 200 1213
2025-06-12 10:01:28,110 basehttp.log_message 161 INFO    => "POST /api/products/combobox_erp_hy_ocv003_combobox_code_name/ HTTP/1.1" 200 7115
2025-06-12 10:01:30,159 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter2/ HTTP/1.1" 200 61790
2025-06-12 10:01:32,148 basehttp.log_message 161 INFO    => "OPTIONS /api/users/insert_user_log/ HTTP/1.1" 200 0
2025-06-12 10:01:32,148 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 0
2025-06-12 10:01:32,946 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_file_statistics/ HTTP/1.1" 200 513
2025-06-12 10:01:32,948 basehttp.log_message 161 INFO    => "POST /api/users/insert_user_log/ HTTP/1.1" 200 57
2025-06-12 10:01:34,684 basehttp.log_message 161 INFO    => "OPTIONS /api/documents/select_letter_download/ HTTP/1.1" 200 0
2025-06-12 10:01:34,983 _DownloadLetterInfo.select_letter_download 382 INFO    => 開始處理公文下載請求: user_id=32000, role=0
2025-06-12 10:01:34,983 _DownloadLetterInfo.select_letter_download 383 INFO    => 請求數據: {'pudcno': '11400227', 'file_name': 'CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx', 'noModifyFile': 'Y'}
2025-06-12 10:01:34,983 _DownloadLetterInfo.select_letter_download 399 INFO    => 執行 SQL: 
    SELECT ATTACHMENT_NAME, FILEPASSWORD
      FROM ( SELECT FILEARTICLE ATTACHMENT_NAME, FILEPASSWORD
               FROM PUDCHT )
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME UNION ALL
    SELECT ATTACHMENT_NAME, '' FILEPASSWORD
      FROM PUDCHT_ATTACHMENT A
     WHERE 1 = 1
 AND ATTACHMENT_NAME = :qFILE_NAME 
2025-06-12 10:01:34,983 _DownloadLetterInfo.select_letter_download 400 INFO    => SQL 參數: {'qFILE_NAME': 'CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx'}
2025-06-12 10:01:34,991 _DownloadLetterInfo.select_letter_download 416 INFO    => 需要處理文件替換: file_url=C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400227\CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:34,991 word_com_safe_processor.__init__  46 INFO    => COM 安全 Word 處理器已初始化
2025-06-12 10:01:34,991 word_com_safe_processor.process_document 129 INFO    => 開始處理文檔: CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:37,541 word_com_safe_processor._ensure_word_app  79 INFO    => Word 應用程序已創建
2025-06-12 10:01:37,542 word_com_safe_processor._process_protected_document_internal 160 INFO    => 打開受保護的文檔: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400227\CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:37,931 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:01:38,767 word_com_safe_processor._process_protected_document_internal 177 INFO    => 文檔保護類型: 2
2025-06-12 10:01:38,843 word_com_safe_processor._process_protected_document_internal 184 INFO    => 已解除文檔保護
2025-06-12 10:01:39,127 word_com_safe_processor._process_protected_document_internal 195 INFO    => 開始修改文檔內容
2025-06-12 10:01:40,648 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註1)
2025-06-12 10:01:41,962 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註1
2025-06-12 10:01:43,095 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:01:43,263 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 10:01:45,223 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註2)
2025-06-12 10:01:45,563 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註2
2025-06-12 10:01:46,394 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註3)
2025-06-12 10:01:46,642 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註3
2025-06-12 10:01:46,788 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註4)
2025-06-12 10:01:46,989 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註4
2025-06-12 10:01:47,123 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註5)
2025-06-12 10:01:47,291 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註5
2025-06-12 10:01:47,505 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註6)
2025-06-12 10:01:47,594 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註6
2025-06-12 10:01:47,780 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: (註7)
2025-06-12 10:01:47,947 _DownloadLetterInfo.replace_and_format 337 INFO    => 找到模式: 註7
2025-06-12 10:01:48,398 word_com_safe_processor._process_protected_document_internal 202 INFO    => 重新保護文檔
2025-06-12 10:01:48,678 word_com_safe_processor._process_protected_document_internal 208 INFO    => 已重新保護文檔
2025-06-12 10:01:48,994 word_com_safe_processor._process_protected_document_internal 222 INFO    => 文檔處理成功: C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400227\32000_CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:49,013 word_com_safe_processor.process_document 144 INFO    => 文檔處理完成: CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:49,013 _DownloadLetterInfo.select_letter_download 421 INFO    => 文件處理成功: new_file_url=C:\Users\<USER>\PycharmProjects\HEYSONG_ERP_HY_API\uploads\公文作業\11400227\32000_CAN330ml 黑松沙士系列產品短期進貨活動獎勵金函250610h16515P.docx
2025-06-12 10:01:49,040 basehttp.log_message 161 INFO    => "POST /api/documents/select_letter_download/ HTTP/1.1" 200 26526
2025-06-12 10:01:51,281 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:01:51,402 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 10:02:14,186 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:02:14,327 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:02:15,154 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:02:22,121 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:02:37,945 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:02:44,965 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:02:44,970 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 137.5%
2025-06-12 10:02:52,487 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:03:15,244 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:03:15,398 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:03:15,467 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 10:03:21,278 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-12 10:03:22,873 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:03:22,952 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 10:03:37,995 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:03:46,163 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:03:46,242 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 10:03:53,750 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:03:53,845 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 10:04:15,308 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:04:16,805 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:04:16,870 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 10:04:24,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:04:24,314 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:04:38,044 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:04:47,262 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:04:47,358 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:04:54,500 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:05:15,324 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:05:17,536 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:05:17,550 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:05:24,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:05:38,122 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:05:48,818 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:05:49,008 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:05:55,018 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:05:55,126 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:06:15,384 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:06:22,634 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:06:29,822 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:06:29,852 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 10:06:30,020 basehttp.log_message 161 INFO    => "POST /api/users/select_check_user/ HTTP/1.1" 200 111
2025-06-12 10:06:38,197 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:06:53,089 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:06:53,130 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 88.7%
2025-06-12 10:07:00,109 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:07:15,390 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:07:23,854 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:07:30,512 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:07:38,231 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:07:54,384 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:08:01,012 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:08:15,445 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:08:24,749 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:08:31,866 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:08:38,279 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:08:55,069 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:09:03,146 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:09:03,509 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-12 10:09:15,477 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:09:25,495 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:09:25,504 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:09:34,063 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:09:34,091 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:09:38,290 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:09:56,076 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:10:04,619 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:10:15,539 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:10:28,630 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:10:35,034 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:10:38,320 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:10:59,848 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:11:05,322 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:11:15,544 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:11:30,173 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:11:30,178 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 10:11:35,643 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:11:38,421 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:12:00,494 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:12:06,610 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:12:15,581 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:12:30,987 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:12:31,119 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:12:37,429 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:12:38,443 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:13:01,556 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:13:01,562 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 10:13:07,879 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:13:07,908 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 80.1%
2025-06-12 10:13:15,636 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:13:31,777 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:13:31,825 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 10:13:38,453 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:13:38,563 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:14:02,274 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:14:10,669 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:14:15,732 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:14:33,253 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:14:38,512 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:14:41,195 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:15:03,530 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:15:03,537 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 10:15:11,672 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:15:15,763 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:15:33,908 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:15:34,105 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:15:38,527 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:15:42,034 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:15:42,132 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:16:04,483 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:16:12,682 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:16:15,772 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:16:34,841 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:16:34,881 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:16:38,668 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:16:42,949 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:17:05,386 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:17:13,517 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:17:15,782 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:17:35,701 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:17:38,683 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:17:44,040 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:18:06,525 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:18:06,559 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 90.1%
2025-06-12 10:18:14,406 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:18:15,813 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:18:37,558 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:18:38,706 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:18:45,139 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:19:08,488 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:19:15,748 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:19:15,834 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 10:19:15,869 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:19:38,721 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:19:39,596 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:19:39,665 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 10:19:46,153 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:20:10,187 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:20:15,124 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: ✳ Word Errors
2025-06-12 10:20:15,125 word_error_handler._click_dialog_button 128 INFO    => 發現錯誤對話框: ✳ Word Errors
2025-06-12 10:20:15,938 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:20:16,749 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:58:00,938 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 10:58:00,941 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 10:58:00,997 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 10:58:00,997 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 10:58:01,148 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 10:58:01,172 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 10:58:01,210 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 10:58:01,224 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 10:58:01,275 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 10:58:01,315 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 10:58:02,100 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 10:58:02,337 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 10:58:02,963 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-12 10:58:04,816 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 10:58:05,327 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 10:58:06,945 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 10:58:09,743 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 10:58:12,120 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 10:58:21,529 word_processor_cache._init_backend 147 WARNING => Django 緩存不可用: Error 10061 connecting to 127.0.0.1:6379. 無法連線，因為目標電腦拒絕連線。.
2025-06-12 10:58:21,533 word_processor_cache._init_backend 152 INFO    => 使用文件系統緩存後端
2025-06-12 10:58:21,577 word_enterprise_processor.init 232 INFO    => 企業級 Word 處理器已初始化
2025-06-12 10:58:21,578 word_error_handler.start_monitoring  49 INFO    => Word 錯誤監控已啟動
2025-06-12 10:58:21,583 word_processor_auto_manager.start  68 INFO    => Word 處理器自動管理器已啟動
2025-06-12 10:58:21,583 word_processor_auto_manager.<module> 400 INFO    => Word 處理器自動管理器已初始化
2025-06-12 10:58:21,583 word_enterprise_processor.<module> 475 INFO    => 自動管理器已啟動
2025-06-12 10:58:21,583 __init__.init_word_processor  56 INFO    => Word processor system initialized successfully
2025-06-12 10:58:21,583 word_processor_singleton.set_initialized  43 INFO    => ProcessorManager marked as initialized
2025-06-12 10:58:21,584 apps.ready  30 INFO    => Word processor initialized in Django ready()
2025-06-12 10:58:22,233 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 peak 負載配置
2025-06-12 10:58:22,327 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] 可用工作執行緒不足: 0
2025-06-12 10:58:22,671 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 10:58:25,346 word_enterprise_processor._worker_loop 168 INFO    => Worker 0 已初始化
2025-06-12 10:58:28,324 word_enterprise_processor._worker_loop 168 INFO    => Worker 4 已初始化
2025-06-12 10:58:31,100 word_enterprise_processor._worker_loop 168 INFO    => Worker 1 已初始化
2025-06-12 10:58:33,623 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:58:34,216 word_enterprise_processor._worker_loop 168 INFO    => Worker 3 已初始化
2025-06-12 10:58:34,401 autoreload.run_with_reloader 637 INFO    => Watching for file changes with StatReloader
2025-06-12 10:58:36,714 word_enterprise_processor._worker_loop 168 INFO    => Worker 2 已初始化
2025-06-12 10:58:40,415 word_com_initializer.fix_console_output 125 WARNING => 設置控制台編碼失敗: '_io.TextIOWrapper' object has no attribute 'reconfigure'
2025-06-12 10:58:40,450 word_com_initializer.initialize_word_environment 164 INFO    => Word 處理環境已初始化
2025-06-12 10:58:41,414 word_queue_processor.start_worker  55 INFO    => Word 佇列處理器已啟動
2025-06-12 10:58:42,483 word_queue_processor._initialize_word  73 INFO    => Word 應用程序已初始化
2025-06-12 10:58:53,199 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:58:53,359 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 114.7%
2025-06-12 10:59:01,030 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:59:03,829 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:59:03,834 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 89.3%
2025-06-12 10:59:21,596 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 10:59:23,912 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:59:24,001 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:59:34,021 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:59:34,026 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 10:59:54,532 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 10:59:54,604 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:00:01,098 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:00:04,215 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:00:21,644 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:00:25,202 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:00:25,232 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 86.0%
2025-06-12 11:00:35,341 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:00:55,631 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:01:01,177 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:01:05,998 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:01:21,708 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:01:25,932 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:01:25,936 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:01:36,285 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:01:36,364 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 11:01:56,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:01:56,403 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 142.0%
2025-06-12 11:02:01,178 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:02:06,649 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:02:06,696 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 87.5%
2025-06-12 11:02:21,738 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:02:27,030 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:02:27,035 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 11:02:36,918 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:02:36,952 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 85.2%
2025-06-12 11:02:57,292 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:02:57,310 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.4%
2025-06-12 11:03:01,193 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:03:07,208 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:03:07,245 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 11:03:21,765 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:03:27,639 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:03:27,656 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:03:37,522 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:03:57,880 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:03:57,898 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 11:04:01,195 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:04:07,687 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:04:21,779 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:04:28,141 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:04:28,191 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:04:37,859 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:04:58,360 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:04:58,370 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 11:05:01,197 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:05:08,091 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:05:21,800 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:05:28,711 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:05:28,829 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.3%
2025-06-12 11:05:38,418 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:05:38,528 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 11:05:59,306 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:05:59,309 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 11:06:01,235 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:06:09,094 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:06:09,125 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.7%
2025-06-12 11:06:21,830 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:06:29,641 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:06:29,656 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:06:39,283 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:07:00,297 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:07:00,337 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 116.4%
2025-06-12 11:07:01,300 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:07:09,640 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:07:09,759 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:07:21,836 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:07:30,550 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:07:30,554 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 99.4%
2025-06-12 11:07:40,342 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:07:40,358 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 100.0%
2025-06-12 11:08:00,942 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:08:01,075 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 112.5%
2025-06-12 11:08:01,375 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:08:10,562 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:08:21,852 word_enterprise_processor._monitor_loop 253 INFO    => 系統狀態: {'available_workers': 5, 'pending_tasks': 0, 'processing_tasks': 0}
2025-06-12 11:08:31,614 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
2025-06-12 11:08:31,787 word_processor_auto_manager._handle_alerts 254 WARNING => [WARNING] CPU使用率過高: 113.6%
2025-06-12 11:08:40,707 word_processor_auto_manager._auto_scale 202 INFO    => 自動切換到 normal 負載配置
