# Word 下載問題修復指南

## 問題描述
當您嘗試下載業務通知文件時，可能會遇到以下錯誤訊息：
```
Microsoft Word
Word 無法開啟已存在的檔案 (Normal.dotm)
```

## 問題原因
這個問題通常由以下原因引起：
1. **Normal.dotm 模板檔案損壞** - Word 的預設模板檔案可能已損壞
2. **COM 初始化問題** - Windows COM 組件初始化失敗
3. **權限問題** - 應用程式沒有足夠權限訪問 Word 模板目錄
4. **Word 進程衝突** - 背景中有 Word 進程正在運行

## 自動修復方案

### 方案一：使用批次檔案修復（推薦）
1. 關閉所有 Word 應用程式
2. 執行 `fix_word_environment.bat` 檔案
3. 按照螢幕提示操作
4. 重新啟動您的應用程式並嘗試下載

### 方案二：使用 Python 修復腳本
```bash
python apps/documents/word_environment_fixer.py
```

## 手動修復步驟

### 步驟 1：關閉 Word 進程
1. 按 `Ctrl + Shift + Esc` 開啟工作管理員
2. 在「處理程序」標籤中尋找 `WINWORD.EXE`
3. 如果找到，選擇它並點擊「結束工作」

### 步驟 2：備份並刪除 Normal.dotm
1. 開啟檔案總管
2. 導航到：`%USERPROFILE%\AppData\Roaming\Microsoft\Templates`
3. 如果找到 `Normal.dotm` 檔案：
   - 右鍵點擊 → 複製
   - 在同一目錄貼上並重新命名為 `Normal.dotm.backup`
   - 刪除原始的 `Normal.dotm` 檔案

### 步驟 3：清理暫存檔案
1. 按 `Win + R`，輸入 `%temp%` 並按 Enter
2. 刪除所有以 `Word` 或 `~` 開頭的檔案
3. 清空資源回收筒

### 步驟 4：重新啟動應用程式
1. 完全關閉您的 ERP 應用程式
2. 重新啟動應用程式
3. 嘗試重新下載文件

## 預防措施

### 1. 定期維護
- 每月執行一次 `fix_word_environment.bat`
- 定期清理暫存檔案

### 2. 系統設定
- 確保 Windows 更新是最新的
- 確保 Microsoft Office 是最新版本

### 3. 權限設定
- 確保應用程式以管理員權限運行（如果需要）
- 檢查防毒軟體是否阻擋了 Word 操作

## 技術細節

### 修復內容
我們的修復方案包含以下改進：

1. **COM 初始化增強**
   - 多線程模式支援
   - 自動回退機制
   - 錯誤處理改進

2. **Word 應用程式設定優化**
   - 禁用不必要的功能
   - 設定臨時模板路徑
   - 防止對話框彈出

3. **自動錯誤恢復**
   - 檢測 Normal.dotm 問題
   - 自動備份和重建
   - 重試機制

### 程式碼改進
- `word_com_initializer.py` - COM 初始化修復
- `word_environment_fixer.py` - 環境修復工具
- `word_com_safe_processor.py` - 安全的文件處理器

## 常見問題解答

### Q: 為什麼會出現這個問題？
A: 這通常是因為 Word 的模板檔案損壞或 COM 組件初始化失敗。這在伺服器環境中比較常見。

### Q: 修復後會影響我的 Word 設定嗎？
A: 不會。我們只是重建損壞的模板檔案，您的個人設定和文件不會受到影響。

### Q: 如果修復後問題仍然存在怎麼辦？
A: 請聯繫技術支援，並提供以下資訊：
- 錯誤訊息的完整截圖
- 系統版本（Windows 版本）
- Office 版本
- 修復腳本的執行結果

### Q: 這個修復是否安全？
A: 是的。我們的修復方案：
- 會先備份原始檔案
- 只修改必要的設定
- 不會影響系統穩定性

## 聯繫支援
如果您在執行修復步驟時遇到任何問題，請聯繫技術支援團隊。

---
*最後更新：2024年*
