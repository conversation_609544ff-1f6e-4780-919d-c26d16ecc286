# Word Normal.dotm 問題修復總結

## 問題描述
用戶在下載業務通知文件時遇到以下錯誤：
```
Microsoft Word
Word 無法開啟已存在的檔案 (Normal.dotm)
```

## 根本原因分析
1. **Normal.dotm 模板檔案損壞** - Word 的預設模板檔案可能已損壞或無法訪問
2. **COM 初始化問題** - Windows COM 組件在伺服器環境中初始化失敗
3. **權限問題** - 應用程式沒有足夠權限訪問 Word 模板目錄
4. **線程模型衝突** - COM 線程模型設定不當

## 實施的修復方案

### 1. 增強的 COM 初始化器 (`word_com_initializer.py`)

#### 新增功能：
- **多線程模式支援**：優先嘗試多線程模式，失敗時回退到單線程模式
- **Normal.dotm 問題修復**：自動檢測並修復損壞的 Normal.dotm 檔案
- **安全的 Word 應用程式創建**：使用 DispatchEx 避免共享問題
- **臨時模板路徑設定**：創建臨時目錄避免權限問題

#### 關鍵改進：
```python
def create_word_app_safe():
    # 防止 Normal.dotm 問題的關鍵設置
    word_app.Options.CheckGrammarAsYouType = False
    word_app.Options.CheckSpellingAsYouType = False
    word_app.Options.AutoRecover = False
    word_app.Options.SaveInterval = 0
    word_app.Options.BackgroundSave = False
    
    # 設置臨時模板路徑
    temp_dir = tempfile.mkdtemp(prefix="word_temp_")
    word_app.Options.DefaultFilePath(0) = temp_dir
```

### 2. Word 環境修復工具 (`word_environment_fixer.py`)

#### 功能特點：
- **自動診斷**：檢測 Word 環境中的常見問題
- **安全備份**：在修復前自動備份 Normal.dotm
- **進程檢查**：確保沒有 Word 進程在運行
- **權限驗證**：檢查模板目錄的讀寫權限

#### 修復流程：
1. 檢查 Word 進程狀態
2. 備份現有的 Normal.dotm
3. 刪除損壞的 Normal.dotm（讓 Word 重新創建）
4. 創建臨時模板目錄
5. 設定環境變數

### 3. 增強的 COM 安全處理器 (`word_com_safe_processor.py`)

#### 新增特性：
- **自動錯誤恢復**：檢測到 Normal.dotm 錯誤時自動修復
- **專用線程池**：使用單線程執行器確保 COM 操作的線程安全
- **智能重試機制**：失敗時自動重試，包含環境修復

#### 錯誤處理邏輯：
```python
def _ensure_word_app(self):
    try:
        # 創建 Word 應用程式
        self._word_app = win32com.client.DispatchEx("Word.Application")
        # ... 設定選項
    except Exception as e:
        if "Normal.dotm" in str(e):
            logger.info("檢測到 Normal.dotm 問題，嘗試修復...")
            self._fix_normal_dotm_and_retry()
```

### 4. 改進的主下載函數 (`_DownloadBusinessNotificationInfo.py`)

#### 增強功能：
- **自動重試機制**：失敗時最多重試 2 次
- **智能錯誤檢測**：識別 Normal.dotm 相關錯誤
- **自動環境修復**：檢測到問題時自動調用修復功能

#### 重試邏輯：
```python
while retry_count <= max_retries:
    try:
        app = create_word_app_safe()
        yield app
        break
    except Exception as e:
        if "Normal.dotm" in str(e) and retry_count < max_retries:
            if fix_word_environment():
                retry_count += 1
                continue
```

### 5. 用戶友好的修復工具

#### 批次檔案 (`fix_word_environment.bat`)
- 自動設定 UTF-8 編碼
- 檢查並關閉 Word 進程
- 備份並刪除 Normal.dotm
- 提供詳細的操作回饋

#### 測試腳本 (`test_word_fix.py`)
- 驗證所有修復組件的功能
- 提供詳細的測試報告
- 記錄測試結果到日誌檔案

## 部署指南

### 1. 立即修復（用戶端）
```bash
# 執行批次檔案
fix_word_environment.bat

# 或執行 Python 修復腳本
python apps/documents/word_environment_fixer.py
```

### 2. 驗證修復
```bash
# 執行測試腳本
python test_word_fix.py
```

### 3. 重新啟動應用程式
- 完全關閉 ERP 應用程式
- 重新啟動應用程式
- 嘗試下載功能

## 預防措施

### 1. 定期維護
- 每月執行一次環境修復腳本
- 定期清理暫存檔案
- 監控 Word 進程狀態

### 2. 系統優化
- 確保 Windows 和 Office 更新
- 設定適當的防毒軟體排除規則
- 監控系統資源使用情況

### 3. 監控和日誌
- 啟用詳細的錯誤日誌記錄
- 監控 COM 初始化失敗率
- 追蹤 Word 應用程式創建成功率

## 技術細節

### COM 線程模型
- 優先使用 `COINIT_MULTITHREADED`
- 失敗時回退到 `CoInitialize()`
- 使用專用線程池避免線程衝突

### Word 應用程式設定
- 禁用所有可能觸發對話框的功能
- 設定自動化安全級別為最高
- 使用臨時模板路徑避免權限問題

### 錯誤恢復策略
- 三層錯誤處理：檢測 → 修復 → 重試
- 自動備份重要檔案
- 漸進式降級策略

## 效果評估

### 預期改善
- **錯誤率降低**：Normal.dotm 相關錯誤應減少 90% 以上
- **自動恢復**：大部分問題可以自動修復，無需人工介入
- **用戶體驗**：下載功能更加穩定可靠

### 監控指標
- Word 應用程式創建成功率
- Normal.dotm 錯誤發生頻率
- 自動修復成功率
- 用戶投訴數量

## 後續維護

### 1. 監控和調整
- 定期檢查錯誤日誌
- 根據使用情況調整重試次數
- 優化修復腳本效能

### 2. 版本更新
- 跟進 Office 版本更新
- 適配新的 Windows 版本
- 持續改進錯誤處理邏輯

---
*修復實施日期：2024年*
*負責人：系統開發團隊*
