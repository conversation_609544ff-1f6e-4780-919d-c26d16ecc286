# -*- coding: UTF8 -*-
import os
import shutil

from docx import Document

import msoffcrypto
import pythoncom
import win32com.client
import pywintypes  # 導入 pywintypes 來處理 COM 異常

import calendar
import logging
import math
import zipfile
import xml.etree.ElementTree as ET

import cx_Oracle
import pytz
import datetime
from django.db import connection
from rest_framework import status

from utils.error_utils import handle_error


def set_before_refresh_token_access_minutes_expi():
    return 10

def set_token_access_minutes_expi():
    return 480

def set_old_token_access_minutes_expi():
    return 10

def set_token_refresh_days_expi():
    return 30

def get_taiwan_timezone():
    return pytz.timezone('Asia/Taipei')


def get_ce_today():
    # 當前日期
    now = now_datetime()

    # 當日日期
    return datetime.datetime(now.year, now.month, now.day).strftime('%Y%m%d')


def to_minguo(input_date, year_and_month_only=False):
    """
    Convert AD to ROC (Minguo) calendar.

    :param input_date: Date in 'YYYYMMDD' format or datetime.date object
    :param year_and_month_only: Return only year and month if set to True
    :return: Date in Minguo calendar format
    """
    # If the input is a string, convert to ROC year format
    if isinstance(input_date, str):
        year_ad = int(input_date[:4])
        year_minguo = year_ad - 1911
        return f"{year_minguo}{input_date[4:6]}" if year_and_month_only else f"{year_minguo}{input_date[4:]}"

    # If the input is a date object, convert to ROC year format
    elif isinstance(input_date, datetime.date):
        if input_date.year > 1911:
            minguo_year = input_date.year - 1911
            return f"{minguo_year:03}{input_date.month:02}" if year_and_month_only else f"{minguo_year:03}{input_date.month:02}{input_date.day:02}"
        else:
            raise ValueError("Year is not valid for Minguo calendar.")

    else:
        raise TypeError("Input must be either a string in 'YYYYMMDD' format or a datetime.date object.")

#
def now_datetime():
    return datetime.datetime.now()
#
def calculate_date(days=0):
    """
    Calculate the date after a certain number of days from today.

    @param days: The number of days to be added to today's date. Can be positive (for future dates)
    or negative (for past dates). Defaults to 0 (today's date).
    @return: A tuple containing today's date and the calculated date.
    """

    # 當日日期
    today = datetime.datetime(now_datetime().year, now_datetime().month, now_datetime().day)

    # 計算特定天數後的日期
    calculated_date = today + datetime.timedelta(days=days)

    return today, calculated_date

#
def get_previous_month_dates_minguo(offset=1):
    # 獲取當前日期
    current_date = now_datetime()

    # 根據偏移量計算新的月份和年份
    new_month = current_date.month - offset
    year = current_date.year
    while new_month <= 0:
        new_month += 12
        year -= 1

    start_date = datetime.datetime(year, new_month, 1)

    # 本月的最後一天
    _, last_day = calendar.monthrange(current_date.year, current_date.month)
    end_date = datetime.datetime(current_date.year, current_date.month, last_day)

    # 轉換為民國紀年
    start_date_minguo = to_minguo(start_date)
    end_date_minguo = to_minguo(end_date)

    return start_date_minguo, end_date_minguo

#
def get_previous_months_minguo(offset=1):
    # 獲取當前日期
    current_date = now_datetime()

    # 根據偏移量計算新的月份和年份
    new_month = current_date.month - offset
    year = current_date.year

    if new_month <= 0:
        year -= 1
        new_month += 12

    start_date = datetime.datetime(year, new_month, 1)

    # 本月的最後一天
    _, last_day = calendar.monthrange(current_date.year, current_date.month)
    end_date = datetime.datetime(current_date.year, current_date.month, last_day)

    # 轉換為民國紀年
    start_date_minguo = to_minguo(start_date, year_and_month_only=True)
    end_date_minguo = to_minguo(end_date, year_and_month_only=True)

    return start_date_minguo, end_date_minguo

#
def get_current_this_month_dates_minguo():

    # 本月的第一天
    start_date = datetime.datetime(now_datetime().year, now_datetime().month, 1)

    # 本月的最後一天
    _, last_day = calendar.monthrange(now_datetime().year, now_datetime().month)
    end_date = datetime.datetime(now_datetime().year, now_datetime().month, last_day)

    # 轉換為民國紀年
    start_date_minguo = to_minguo(start_date)
    end_date_minguo = to_minguo(end_date)

    return start_date_minguo, end_date_minguo

# 非民國年日
def get_previous_month_dates(offset=1):
    # 獲取當前日期
    current_date = now_datetime()

    # 根據偏移量計算新的月份和年份
    new_month = current_date.month - offset
    year = current_date.year
    while new_month <= 0:
        new_month += 12
        year -= 1

    start_date = datetime.datetime(year, new_month, 1)

    # 本月的最後一天
    _, last_day = calendar.monthrange(current_date.year, current_date.month)
    end_date = datetime.datetime(current_date.year, current_date.month, last_day)

    return start_date.strftime('%Y%m%d'), end_date.strftime('%Y%m%d')

# 非民國年月
def get_previous_months(offset=1):
    # 獲取當前日期
    current_date = now_datetime()

    # 根據偏移量計算新的月份和年份
    new_month = current_date.month - offset
    year = current_date.year

    if new_month <= 0:
        year -= 1
        new_month += 12

    start_date = datetime.datetime(year, new_month, 1)

    # 本月的最後一天
    _, last_day = calendar.monthrange(current_date.year, current_date.month)
    end_date = datetime.datetime(current_date.year, current_date.month, last_day)

    return start_date.strftime('%Y%m'), end_date.strftime('%Y%m')

#
def get_custom_date_range(base_date_str, start_month_offset, end_month_offset):
    base_date = None
    # 如果base_date_str是月份
    if len(base_date_str) == 6:
        base_date = datetime.datetime.strptime(base_date_str, '%Y%m')
    # 如果base_date_str是日期
    elif len(base_date_str) == 8:
        base_date = datetime.datetime.strptime(base_date_str, '%Y%m%d')

    # 計算起始和結束日期
    start_year = base_date.year + (base_date.month + start_month_offset - 1) // 12
    start_month = (base_date.month + start_month_offset - 1) % 12 + 1
    end_year = base_date.year + (base_date.month + end_month_offset - 1) // 12
    end_month = (base_date.month + end_month_offset - 1) % 12 + 1

    # 處理月底的情況
    start_day = 1
    end_day = calendar.monthrange(end_year, end_month)[1]

    # 建立起始和結束日期
    start_date = datetime.datetime(start_year, start_month, start_day).strftime('%Y%m%d')
    end_date = datetime.datetime(end_year, end_month, end_day).strftime('%Y%m%d')

    return start_date, end_date

def paginate_data(data, page_size, page_number):
    """
    對數據進行分頁。
    :param data: 要分頁的數據列表。
    :param page_size: 每頁顯示的記錄數。
    :param page_number: 當前頁碼。
    :return: 當前頁面的數據和分頁信息。
    """

    total_pages = math.ceil(data / page_size)

    # 防止頁碼超出範圍
    if page_number > total_pages:
        page_number = total_pages

    return page_number, total_pages

def fetch_user_dept_code_and_permissions(module_id, user_id):
    """
    查詢資料庫以獲取使用者代碼或部門代碼以及特殊許可權。
    :param module_id： 查詢的模組ID。
    :param user_id： 使用者ID。
    ：return： 使用者部門代碼和是否有特殊許可權的資訊。
    """
    context = 'fetch_user_dept_code_and_permissions'

    with connection.cursor() as cursor:
        try:
            sql = """ 
                SELECT CASE
                          WHEN SYSROLE = '0'
                              THEN USERID
                          WHEN SYSROLE = '1'
                              THEN DEPTCODE
                      END CODE,
                      CAN_SPECIAL
                 FROM USERS U, USER_PERMISSIONS UP
                WHERE U.USERID = UP.USER_ID
                  AND MODULE_ID = :qMODULD_ID
                  AND USERID = :qUSERID
            """
            # print('sql', sql, {'qMODULD_ID': module_id, 'qUSERID': user_id})
            cursor.execute(sql, {'qMODULD_ID': module_id, 'qUSERID': user_id})
            data = cursor.fetchall()

            if data is None:
                return handle_error('查無資料', status.HTTP_404_NOT_FOUND)
            else:
                col_names = [desc[0] for desc in cursor.description]
                data = [dict(zip(col_names, row)) for row in data]
                return data, None
        except cx_Oracle.IntegrityError as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

def select_get_serial(stype, sdata, user_id, json_data=None):
    context = 'select_get_serial'

    # 連線資料庫

    with connection.cursor() as cursor:
        try:
            # 取得編號
            get_serial_sql = f"SELECT SERIALNO FROM PROGRAMSERIAL WHERE PROGRAMCODE='{stype}' AND DATECODE='{sdata}' FOR UPDATE"
            # print('get_serial_sql', get_serial_sql)
            cursor.execute(get_serial_sql)
            row = cursor.fetchone()

            if row is None:  # 新增：無編號存在
                serial_no = 1
                insert_serial_sql = f"INSERT INTO PROGRAMSERIAL(PROGRAMCODE, DATECODE, SERIALNO) VALUES('{stype}', '{sdata}', {serial_no})"
                cursor.execute(insert_serial_sql)
            else:  # 修改：編號加1
                serial_no = row[0] + 1
                # print('serial_no', serial_no)
                update_serial_sql = f"UPDATE PROGRAMSERIAL SET SERIALNO={serial_no} WHERE PROGRAMCODE='{stype}' AND DATECODE='{sdata}'"
                cursor.execute(update_serial_sql)

            connection.commit()

            # stype類型是G0200000，代表是業務通知，同時新增一筆資料到BPUDCHT
            if stype == 'G0200000':
                stl_code = str(serial_no).zfill(5)
                insert_bpudcht_sql = f"INSERT INTO BPUDCHT(PUDCNO, OWNERID) VALUES('{sdata}{stl_code}','{user_id}')"
                cursor.execute(insert_bpudcht_sql)

            # stype類型是G0200000，代表是業務通知，同時新增一筆資料到BPUDCHT
            if stype == 'G0100000':
                stl_code = str(serial_no).zfill(5)
                insert_pudcht_sql = f"INSERT INTO PUDCHT(PUDCNO, OWNERID) VALUES('{sdata}{stl_code}','{user_id}')"
                cursor.execute(insert_pudcht_sql)

            # stype類型是B0100000，代表是訂單輸入，同時新增一筆資料到ORDERHT
            if stype == 'B0100000':
                dealers_code = json_data.get('dealers_code', '')
                order_date = json_data.get('order_date', '')
                delivery_date = json_data.get('delivery_date', '')
                shipment_feature = json_data.get('shipment_feature', '')
                stl_code = str(serial_no).zfill(3)

                print('dealers_code', dealers_code, 'order_date', order_date, 'delivery_date', delivery_date, 'shipment_feature', shipment_feature)

                insert_orderht_sql = f"""
                    INSERT INTO ORDERHT(ORDERNO, ORDERDATE, VENDORCODE, ODDATE, PLACECODE, OWNERID) 
                    VALUES             ('C{sdata}{stl_code}', '{order_date}', '{dealers_code}', '{delivery_date}','{shipment_feature}', '{user_id}')
                """
                cursor.execute(insert_orderht_sql)

            connection.commit()

            return sdata + stl_code

        except cx_Oracle.IntegrityError as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

# 取得使用者權限
def get_user_permissions(module_id, user_id):
    context = 'get_user_permissions'
    """
    Executes a database query to fetch user or department code and special permission flag.
    """
    sql = """ 
        SELECT CASE
                   WHEN SYSROLE = '0'
                       THEN USERID
                   WHEN SYSROLE = '1'
                       THEN DEPTCODE
               END CODE,
               CAN_SPECIAL
          FROM USERS U, USER_PERMISSIONS UP
         WHERE U.USERID = UP.USER_ID
           AND MODULE_ID = %s
           AND USERID = %s
    """
    with connection.cursor() as cursor:
        try:
            cursor.execute(sql, [module_id, user_id])
            data = cursor.fetchall()

            if not data:
                return None, "找不到資料", status.HTTP_404_NOT_FOUND

            col_names = [desc[0] for desc in cursor.description]
            data = [dict(zip(col_names, row)) for row in data]

            return data

        except cx_Oracle.IntegrityError as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

# 查詢是否已讀紀錄
def check_read_record(PARENTID, CHILDID, SOURCESERNO, USERID):
    context = 'check_read_record'

    with connection.cursor() as cursor:
        try:
            sql = """
                SELECT ACTIONTYPE
                  FROM USERLOG
                 WHERE PARENTID = :qPARENTID
                   AND CHILDID = :qCHILDID
                   AND SOURCESERNO = :qSOURCESERNO
                   AND USERID = :qUSERID
            """

            cursor.execute(sql, {'qPARENTID': PARENTID, 'qCHILDID': CHILDID, 'qSOURCESERNO': SOURCESERNO, 'qUSERID': USERID})
            data = cursor.fetchone()

            if data is None or len(data) == 0:
                return None
            else:
                return data[0]
        except cx_Oracle.IntegrityError as e:
            return handle_error(context, e, status.HTTP_500_INTERNAL_SERVER_ERROR)

# 刪除資料夾
def remove_folder(folder_path):
    """
    Removes a folder and its contents.

    :param folder_path: The path to the folder to be removed.
    """
    try:
        if os.path.exists(folder_path):
            shutil.rmtree(folder_path)
            logging.info(f"Deleted folder: {folder_path}")
    except Exception as e:
        logging.error(f"Failed to delete folder: {folder_path}" + str(e), exc_info=True)

# 刪除檔案
def remove_file(file_path):
    """
    Removes a file.

    :param file_path: The path to the file to be removed.
    """
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logging.info(f"Deleted file: {file_path}")
    except Exception as e:
        logging.error(f"Failed to delete file: {file_path}" + str(e), exc_info=True)

def is_word_protected(docx_path):
    try:
        with zipfile.ZipFile(docx_path, 'r') as z:
            # 嘗試提取word/settings.xml檔
            with z.open('word/settings.xml') as f:
                tree = ET.parse(f)
                root = tree.getroot()
                protection_tag = '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}documentProtection'
                # 檢查是否存在保護標籤
                if root.find(protection_tag) is not None:
                    return True  # 存在保護標籤，文件受保護
        return False  # 沒有找到保護標籤，文件未受保護
    except zipfile.BadZipFile:
        print("錯誤：提供的文件不是有效的ZIP檔案。")
        return False  # 文件不是有效的.docx格式，可假定未受保護
    except KeyError:
        print("錯誤：找不到word/settings.xml檔案。")
        return False  # settings.xml檔不存在，文件未受保護
    except ET.ParseError:
        print("錯誤：XML解析失敗。")
        return False  # XML解析出錯，但不影響保護狀態判斷
    except Exception as e:
        print(f"未預期的錯誤發生：{str(e)}")
        return True  # 遇到未預期錯誤時，預設為受保護，根據實際需求調整

def can_open_with_password(doc_path, password):
    # 使用 win32com 庫嘗試使用密碼打開文件
    word_app_created = False
    try:
        pythoncom.CoInitialize()
        word_app = win32com.client.gencache.EnsureDispatch("Word.Application")
        word_app_created = True
        word_app.Visible = False

        # 嘗試使用提供的密碼打開文件
        doc = word_app.Documents.Open(doc_path, False, False, False, PasswordDocument=password)

        # 檢查文件是否成功打開
        if doc:
            # 檢查文件是否設置了保護
            if doc.ProtectionType != -1:
                # 嘗試解除保護
                try:
                    doc.Unprotect(password)
                    doc.Protect(Type=doc.ProtectionType, NoReset=True, Password=password)
                    doc.Close(SaveChanges=False)
                    word_app.Quit()
                    return True
                except Exception as e:
                    print(f"無法解除文件保護: {e}")
                    doc.Close(SaveChanges=False)
                    word_app.Quit()
                    return False
            else:
                print("文件未設置保護")
                doc.Close(SaveChanges=False)
                word_app.Quit()
                return False
        else:
            word_app.Quit()
            return False

    except Exception as e:
        print(f"錯誤發生: {e}")
        logging.error(f"錯誤發生: {e}", exc_info=True)
        return False

    finally:
        # 確保在成功創建 Word 應用程序的情況下才調用 Quit 方法
        if word_app_created:
            word_app.Quit()