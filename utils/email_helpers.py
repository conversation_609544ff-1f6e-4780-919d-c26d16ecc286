# -*- coding: UTF8 -*-
from django.db import connection
from HEYSONG_ERP_HY_API.settings import SEND_EMAIL_TO_REAL_RECIPIENT

def get_letter_from_email_info(pudcno):
    with connection.cursor() as cursor:
        if SEND_EMAIL_TO_REAL_RECIPIENT:
            mode = ''
        else:
            mode = 'TEST'

        sql = """
            SELECT USERID, USERNAME, DEPTCODE,
                   CASE
                       WHEN :qMODE = 'TEST'
                           THEN '<EMAIL>'
                       ELSE EMAIL
                   END EMAIL,
                   PUDCNO_1 PUDCNO,
                   SUBJECT,
                   CASE
                       WHEN SYSROLE = '0'
                           THEN ''
                       ELSE FILEARTICLE
                   END FILEARTICLE
              FROM (SELECT USERID, USERNAME, DEPTCODE, EMAIL, SYSROLE,
                           CASE
                               WHEN SYSROLE = '0'
                                   THEN USERID
                               ELSE DEPTCODE
                           END USER_VENDORDEPT
                      FROM USERS
                     WHERE EXPIREDATE >= SYSDATE),
                   (SELECT RORV, PUDCNO_1,
                           CASE
                               WHEN VENDORCODE = 'HSG001'
                                   THEN DEPTCODE
                               ELSE VENDORCODE
                           END VENDORDEPT,
                           SUBJECT || '(公文作業)' SUBJECT,
                           FILEARTICLE
                      FROM (SELECT 'PUDCRECEIVER' RORV, PUDCNO, VENDORCODE, DEPTCODE
                              FROM PUDCHT_PUDCRECEIVER
                             WHERE PUDCNO = :qPUDCNO
                             UNION ALL
                            SELECT 'COPY' RORV, PUDCNO, VENDORCODE, DEPTCODE
                              FROM PUDCHT_COPY
                             WHERE PUDCNO = :qPUDCNO),
                           (SELECT PUDCNO PUDCNO_1, SUBJECT, FILEARTICLE FROM PUDCHT)
                     WHERE PUDCNO = PUDCNO_1)
             WHERE USER_VENDORDEPT = VENDORDEPT
        """

        parms = {'qMODE': mode, 'qPUDCNO': pudcno}

        # print('with SELECT_SQL', sql, 'with SELECT_SQL_parameters', parms)

        cursor.execute(sql, parms)

        from_email = []
        rows = cursor.fetchall()
        for row in rows:
            from_email.append(row[0:7])

    return from_email

def get_business_notification_from_email_info(pudcno):
    with connection.cursor() as cursor:
        if SEND_EMAIL_TO_REAL_RECIPIENT:
            mode = ''
        else:
            mode = 'TEST'

        sql = """
            SELECT USERID, USERNAME, DEPTCODE,
                   CASE
                       WHEN :qMODE = 'TEST'
                           THEN '<EMAIL>'
                       ELSE EMAIL
                   END EMAIL,
                   PUDCNO_1 PUDCNO,
                   SUBJECT,
                   CASE
                       WHEN SYSROLE = '0'
                           THEN ''
                       ELSE FILEARTICLE
                   END FILEARTICLE
              FROM (SELECT USERID, USERNAME, DEPTCODE, EMAIL, SYSROLE,
                           CASE
                               WHEN SYSROLE = '0'
                                   THEN USERID
                               ELSE DEPTCODE
                           END USER_VENDORDEPT
                      FROM USERS
                     WHERE EXPIREDATE >= SYSDATE),
                   (SELECT RORV, PUDCNO_1,
                           CASE
                               WHEN VENDORCODE = 'HSG001'
                                   THEN DEPTCODE
                               ELSE VENDORCODE
                           END VENDORDEPT,
                           SUBJECT || '(業務通報)' SUBJECT,
                           FILEARTICLE
                      FROM (SELECT 'PUDCRECEIVER' RORV, PUDCNO, VENDORCODE, DEPTCODE
                              FROM BPUDCHT_PUDCRECEIVER
                             WHERE PUDCNO = :qPUDCNO
                             UNION ALL
                            SELECT 'COPY' RORV, PUDCNO, VENDORCODE, DEPTCODE
                              FROM BPUDCHT_COPY
                             WHERE PUDCNO = :qPUDCNO),
                           (SELECT PUDCNO PUDCNO_1, SUBJECT, FILEARTICLE FROM BPUDCHT)
                     WHERE PUDCNO = PUDCNO_1)
             WHERE USER_VENDORDEPT = VENDORDEPT
        """

        parms = {'qMODE': mode, 'qPUDCNO': pudcno}

        # print('with SELECT_SQL', sql, 'with SELECT_SQL_parameters', parms)

        cursor.execute(sql, parms)

        from_email = []
        rows = cursor.fetchall()
        for row in rows:
            from_email.append(row[0:7])

    return from_email