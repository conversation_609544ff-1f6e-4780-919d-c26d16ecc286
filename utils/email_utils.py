import logging
import os
import smtplib
import re
from email.header import Head<PERSON>
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from celery import shared_task
from django.db import connection

# 常量定義
FROM_EMAIL = "<EMAIL>"
FROM_PASSWORD = ""
SMTP_SERVER = '*************'
SMTP_PORT = 25
DELAY_SEND_EMAIL_SECONDS = 120

MIME_TYPES = {
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.pdf': 'application/pdf',
    '.jpg': 'image/jpeg',
    '.png': 'image/png',
}

def get_mime_type(file_name):
    extension = os.path.splitext(file_name)[1]
    return MIME_TYPES.get(extension, 'application/octet-stream')

def attach_file_to_email(email_obj, file_path):
    try:
        with open(file_path, 'rb') as attachment_file:
            mime_type = get_mime_type(file_path)
            primary, sub = mime_type.split('/', 1)
            part = MIMEBase(primary, sub)
            part.set_payload(attachment_file.read())
            encoders.encode_base64(part)

            file_name = os.path.basename(file_path)
            encoded_file_name = Header(file_name, 'utf-8').encode()
            part.add_header('Content-Disposition', f'attachment; filename="{encoded_file_name}"')
            email_obj.attach(part)
            logging.info(f"成功附加檔案: {file_path}")
    except Exception as e:
        logging.exception(f"附加檔案 {file_path} 時發生錯誤: {str(e)}")

def validate_email(email):
    # 簡單格式檢查
    email_regex = re.compile(
        r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"
    )
    if not re.match(email_regex, email):
        return False, "郵箱格式錯誤"

    # 基於 SMTP 連接檢查郵箱是否存在
    domain = email.split('@')[1]
    try:
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.set_debuglevel(0)
            server.mail('')
            code, message = server.rcpt(email)
            if code != 250:
                return False, f"郵件伺服器拒絕: {message.decode()}"
    except Exception as e:
        return False, f"郵件伺服器檢查錯誤: {str(e)}"

    return True, "郵箱有效"

@shared_task(bind=True)
def send_report_email(self, from_type, pudcno, email_infos, **kwargs):
    file_path = kwargs.get('file_path')
    from_email = kwargs.get('from_email', FROM_EMAIL)
    from_password = kwargs.get('from_password', FROM_PASSWORD)
    smtp_server = kwargs.get('smtp_server', SMTP_SERVER)
    smtp_port = kwargs.get('smtp_port', SMTP_PORT)

    logging.info(f"開始執行郵件發送任務，pudcno: {pudcno}")

    successful_sends = []
    failed_sends = []

    try:
        logging.info(f"為 pudcno: {pudcno} 獲取到 {len(email_infos)} 條郵件資訊")

        with smtplib.SMTP(smtp_server, smtp_port) as server:
            if from_password:
                server.login(from_email, from_password)

            for email_info in email_infos:
                to_email = email_info[3]

                # 驗證郵箱
                is_valid, message = validate_email(to_email)
                if not is_valid:
                    failed_sends.append((to_email, message))
                    logging.error(f"郵箱驗證失敗: {to_email}, 原因: {message}")
                    continue

                subject = email_info[5]
                body = f'受文者   : {email_info[1]}\n公文編號 : {email_info[4]}\n主旨     : {email_info[5]}\n'
                filename = email_info[6]

                msg = MIMEMultipart()
                msg['From'] = from_email
                msg['To'] = to_email
                msg['Subject'] = subject
                msg.attach(MIMEText(body, 'plain'))

                if filename:
                    full_file_path = os.path.join(file_path, filename)
                    if os.path.exists(full_file_path) and os.access(full_file_path, os.R_OK):
                        attach_file_to_email(msg, full_file_path)
                    else:
                        logging.error(f"檔案不存在或無法讀取: {full_file_path}")

                try:
                    server.send_message(msg)
                    successful_sends.append(to_email)
                    logging.info(f"成功發送郵件給: {to_email}")
                except smtplib.SMTPException as e:
                    failed_sends.append((to_email, str(e)))
                    logging.error(f"發送郵件給 {to_email} 時發生 SMTP 錯誤: {str(e)}")
                except Exception as e:
                    failed_sends.append((to_email, str(e)))
                    logging.error(f"發送郵件給 {to_email} 時發生未知錯誤: {str(e)}")

    except Exception as e:
        logging.exception(f"send_report_email 函數中發生錯誤: {str(e)}")

    # 記錄摘要
    total_emails = len(email_infos)
    success_count = len(successful_sends)
    failure_count = len(failed_sends)

    logging.info(f"郵件發送摘要 (pudcno {pudcno}):")
    logging.info(f"總郵件數: {total_emails}")
    logging.info(f"成功發送: {success_count}")
    logging.info(f"發送失敗: {failure_count}")

    # 更新資料庫中的發送狀態
    update_email_send_status(from_type, pudcno, total_emails, success_count, failed_sends)

    # 如果有任何失敗，發送錯誤詳情給公文建立者
    if failed_sends:
        send_error_report(from_type, pudcno, failed_sends)

    return f"pudcno {pudcno} 的 {total_emails} 封郵件中成功發送了 {success_count} 封"

def update_email_send_status(from_type, pudcno, total_emails, success_count, failed_sends):
    try:
        with connection.cursor() as cursor:
            if from_type == '業務通報':
                table_name = 'BPUDCHT'
            elif from_type == '公文作業':
                table_name = 'PUDCHT'
            else:
                logging.error(f"未知的 from_type: {from_type}")
                return

            email_sent_status = 'S'
            email_sent_error = ''
            if len(failed_sends) > 0:
                email_sent_status = 'E'
                email_sent_error = f"{success_count}/{total_emails}"

            sql = f"""
                UPDATE {table_name}
                SET READONLY = :email_sent_status, EMAIL_SENT_ERROR = :email_sent_error
                WHERE PUDCNO = :pudcno
            """
            cursor.execute(sql, {
                'pudcno': pudcno,
                'email_sent_status': email_sent_status,
                'email_sent_error': email_sent_error
            })

        logging.info(f"已更新 pudcno {pudcno} 的郵件發送狀態")
    except Exception as e:
        logging.exception(f"更新郵件發送狀態時發生錯誤: {str(e)}")

def send_error_report(from_type, pudcno, failed_sends):
    try:
        # 獲取公文建立者的郵箱地址
        creator_email = get_creator_email(from_type, pudcno)

        if not creator_email:
            logging.error(f"無法獲取 pudcno {pudcno} 的建立者郵箱地址")
            return

        subject = f"公文 {pudcno} 郵件發送錯誤報告"
        body = f"公文編號 {pudcno} 的部分郵件發送失敗。以下是詳細錯誤資訊：\n\n"
        for email, error in failed_sends:
            body += f"收件人: {email}\n錯誤資訊: {error}\n\n"

        msg = MIMEMultipart()
        msg['From'] = FROM_EMAIL
        msg['To'] = creator_email
        msg['Subject'] = subject
        msg.attach(MIMEText(body, 'plain'))

        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            if FROM_PASSWORD:
                server.login(FROM_EMAIL, FROM_PASSWORD)
            server.send_message(msg)

        logging.info(f"已發送錯誤報告給公文建立者 {creator_email}")
    except Exception as e:
        logging.exception(f"發送錯誤報告時發生錯誤: {str(e)}")

def get_creator_email(from_type, pudcno):
    try:
        with connection.cursor() as cursor:
            if from_type == '業務通報':
                table_name = 'BPUDCHT'
            elif from_type == '公文作業':
                table_name = 'PUDCHT'
            else:
                logging.error(f"未知的 from_type: {from_type}")
                return None

            sql = f"""
                SELECT u.EMAIL
                FROM {table_name} p
                JOIN USERS u ON p.OWNERID = u.USERID
                WHERE p.PUDCNO = :pudcno
            """
            cursor.execute(sql, {'pudcno': pudcno})
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        logging.exception(f"獲取公文建立者郵箱時發生錯誤: {str(e)}")
        return None
