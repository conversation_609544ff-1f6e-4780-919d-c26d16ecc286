import json
import logging
import threading
import queue
from datetime import datetime, timedelta
import uuid
from json import JSONDecodeError

from django.http import JsonResponse
from django.db import connection, transaction
from pytz import timezone
from rest_framework import status

from HEYSONG_ERP_HY_API.settings import strCORS_URL
from utils.error_utils import handle_error
from utils.main_utils import get_taiwan_timezone, set_token_access_minutes_expi, \
    set_before_refresh_token_access_minutes_expi, set_old_token_access_minutes_expi

# 使用 threading.Condition 來實現 token 刷新的同步
token_refresh_condition = threading.Condition()
refresh_queue = queue.Queue()
is_token_refreshing = False

# 驗證 access_token 的函數
def verify_access_token(request, method='POST'):
    global is_token_refreshing

    refresh_token = request.headers.get('X-Refresh-Token')

    # 僅在 POST 方法下檢查 Refresh token
    if method == 'POST':
        if not refresh_token:
            logging.error('缺少 Refresh token')
            return False, '缺少 Refresh token', status.HTTP_401_UNAUTHORIZED

        # 檢查 refresh_token 是否存在和有效
        with connection.cursor() as cursor:
            cursor.execute("SELECT ZEREFRESHEXPI FROM WBZE WHERE ZEREFRESHTOKEN = %s", [refresh_token])
            row = cursor.fetchone()

            if not row:
                logging.error('Refresh token 無效')
                return False, 'Refresh token 無效', status.HTTP_401_UNAUTHORIZED

            current_time = datetime.now(get_taiwan_timezone())
            ZEREFRESHEXPI = row[0]

            if str(current_time) >= ZEREFRESHEXPI:
                logging.error('Refresh token 已過期')
                return False, 'Refresh token 已過期', status.HTTP_401_UNAUTHORIZED

    # 根據請求方法獲取 access_token
    access_token = request.COOKIES.get('access_token')
    if not access_token:
        return False, 'access_token 不存在', status.HTTP_401_UNAUTHORIZED

    current_time = datetime.now(timezone('Asia/Taipei'))

    with connection.cursor() as cursor:
        cursor.execute(
            "SELECT USERID, USERNAME, SYSROLE, ZEACCESSTOKEN, ZEACCESSEXPI, OLD_ACCESSTOKEN, OLD_ACCESSEXPI "
            "  FROM USERS, WBZE"
            " WHERE USERID = ZEUSER AND (ZEACCESSTOKEN = %s OR (OLD_ACCESSTOKEN = %s AND OLD_ACCESSEXPI > %s))",
            [access_token, access_token, current_time - timedelta(minutes=set_old_token_access_minutes_expi())])
        row = cursor.fetchone()

        if not row:
            return False, 'Access token 無效', status.HTTP_403_FORBIDDEN

        USERID, USERNAME, SYSROLE, ZEACCESSTOKEN, ZEACCESSEXPI, OLD_ACCESSTOKEN, OLD_ACCESSEXPI = row
        ZEACCESSEXPI_dt = datetime.strptime(ZEACCESSEXPI[:26], "%Y-%m-%d %H:%M:%S.%f")
        ZEACCESSEXPI_dt = timezone('Asia/Taipei').localize(ZEACCESSEXPI_dt)

        if OLD_ACCESSEXPI:
            OLD_ACCESSEXPI_dt = datetime.strptime(OLD_ACCESSEXPI[:26], "%Y-%m-%d %H:%M:%S.%f")
            OLD_ACCESSEXPI_dt = timezone('Asia/Taipei').localize(OLD_ACCESSEXPI_dt)

        # 檢查 access_token 是否是最新的，且仍在有效期內
        if access_token == ZEACCESSTOKEN and current_time < ZEACCESSEXPI_dt:
            return True, {'userId': USERID, 'name': USERNAME, 'role': SYSROLE}, status.HTTP_200_OK

        # 檢查舊的 access_token 是否仍在有效期內
        if access_token == OLD_ACCESSTOKEN and OLD_ACCESSEXPI_dt and current_time < OLD_ACCESSEXPI_dt:
            # 舊token 仍有效，但需要更新為新token
            return True, create_response(USERID, USERNAME, SYSROLE, ZEACCESSTOKEN, ZEACCESSEXPI_dt), status.HTTP_200_OK

        # Token 已過期或需要刷新，嘗試刷新
        with token_refresh_condition:
            if not is_token_refreshing:
                is_token_refreshing = True
                refresh_result, new_access_token, new_expi = refresh_access_token(USERID, refresh_token)
                is_token_refreshing = False
                if refresh_result:
                    process_refresh_queue(new_access_token, new_expi)
                    token_refresh_condition.notify_all()
                    return True, create_response(USERID, USERNAME, SYSROLE, new_access_token, new_expi), status.HTTP_200_OK
                else:
                    return False, 'Refresh token 無效', status.HTTP_401_UNAUTHORIZED
            else:
                refresh_queue.put(request)
                token_refresh_condition.wait()
                return verify_access_token(request)


def create_response(user_id, username, sysrole, access_token, expi):
    response_data = {'message': {'userId': user_id, 'name': username, 'role': sysrole}}
    response = JsonResponse(response_data)
    response.set_cookie('access_token', str(access_token), expires=expi, httponly=True, samesite='Strict')
    response["Access-Control-Allow-Credentials"] = "true"
    response["Access-Control-Allow-Origin"] = strCORS_URL
    return response

# 刷新 access_token 的函數
@transaction.atomic
def refresh_access_token(user_id, refresh_token):
    global is_token_refreshing

    with connection.cursor() as cursor:
        cursor.execute("SELECT ZEREFRESHEXPI, ZEACCESSTOKEN, OLD_ACCESSTOKEN FROM WBZE WHERE ZEUSER = %s AND ZEREFRESHTOKEN = %s", [user_id, refresh_token])
        row = cursor.fetchone()

        new_access_token = None
        new_access_expi = None

        if row:
            ZEREFRESHEXPI, current_access_token, old_access_token = row
            current_time = datetime.now(get_taiwan_timezone())

            if str(current_time) >= ZEREFRESHEXPI:
                return False, current_access_token, ZEREFRESHEXPI

            new_access_token = uuid.uuid4()
            new_access_expi = current_time + timedelta(minutes=set_token_access_minutes_expi())
            cursor.execute(
                "UPDATE WBZE SET ZEACCESSTOKEN = %s, ZEACCESSEXPI = %s, OLD_ACCESSTOKEN = %s, OLD_ACCESSEXPI = %s WHERE ZEUSER = %s AND ZEREFRESHTOKEN = %s",
                [new_access_token, new_access_expi, current_access_token, current_time + timedelta(minutes=set_old_token_access_minutes_expi()), user_id, refresh_token])

        return True, new_access_token, new_access_expi

# 處理刷新佇列的函數
def process_refresh_queue(new_access_token, new_expi):
    while not refresh_queue.empty():
        queued_request = refresh_queue.get()
        if new_access_token:
            queued_request.COOKIES['access_token'] = new_access_token
            verify_access_token(queued_request)

def wait_for_refresh():
    with token_refresh_condition:
        while is_token_refreshing:
            token_refresh_condition.wait()

def _common_validation(request, required_params):
    # 驗證訪問令牌
    is_valid, message, status_code = verify_access_token(request)
    if not is_valid:
        return None, None, None, message, status_code

    # 加載 JSON 數據
    try:
        json_data = json.loads(request.body)
    except JSONDecodeError:
        return None, None, None, '無效的 JSON 格式', status.HTTP_400_BAD_REQUEST

    # 跳過參數檢查是否只傳入 None required_params
    if required_params != (None,):
        for param in required_params:
            if param not in json_data:
                return None, None, None, f'{param}不存在', status.HTTP_400_BAD_REQUEST

    # 檢查 message 是否為 JsonResponse
    if isinstance(message, JsonResponse):
        # 從 JsonResponse 對象解析 JSON 內容
        try:
            response_data = json.loads(message.content)
            message = response_data.get('message', {})
            role = message.get('role')
        except json.JSONDecodeError:
            return None, None, None, 'JsonResponse 解析失敗', status.HTTP_500_INTERNAL_SERVER_ERROR
    else:
        # 提取 role
        role = message.get('role')

    # 提取 user_id
    user_id = message.get('userId', None)

    return json_data, role, user_id, None, None

def _common_validation_for_list(request, required_params):
    # 驗證訪問令牌
    is_valid, message, status_code = verify_access_token(request)
    if not is_valid:
        return None, None, None, message, status_code

    try:
        json_data_list = json.loads(request.body)
        assert isinstance(json_data_list, list)
    except (JSONDecodeError, AssertionError):
        return None, None, None, '無效的 JSON 格式', status.HTTP_400_BAD_REQUEST

    # 跳過參數檢查是否只傳入 None required_params
    if required_params != (None,):
        for json_data in json_data_list:
            for param in required_params:
                if param not in json_data:
                    return None, None, None, f'{param} 不存在', status.HTTP_400_BAD_REQUEST

    # 檢查 message 是否為 JsonResponse
    if isinstance(message, JsonResponse):
        # 從 JsonResponse 對象解析 JSON 內容
        try:
            response_data = json.loads(message.content)
            message = response_data.get('message', {})
            role = message.get('role')
        except json.JSONDecodeError:
            return None, None, None, 'JsonResponse 解析失敗', status.HTTP_500_INTERNAL_SERVER_ERROR
    else:
        # 提取 role
        role = message.get('role')

    # 提取 user_id
    user_id = message['userId']

    return json_data_list, role, user_id, None, None

def validate_access_token_and_params_with_pagination(*required_params):
    def decorator(func):
        def wrapper(request):

            json_data = json.loads(request.body)
            if isinstance(json_data, list):
                validation_func = _common_validation_for_list
            else:
                validation_func = _common_validation

            json_data, role, user_id, message, status_code = validation_func(request, required_params)

            if message:
                return message, status_code

            # 處理分頁參數
            page_size = int(json_data.get("pageSize", 100))
            page_number = int(json_data.get("pageIndex", 1))
            start_rnk = (page_number - 1) * page_size + 1
            end_rnk = page_number * page_size

            return func(request, json_data, role, user_id, page_size, page_number, start_rnk, end_rnk)

        return wrapper

    return decorator

def validate_access_token_and_params(*required_params):
    def decorator(func):
        def wrapper(request):

            json_data = json.loads(request.body)
            if isinstance(json_data, list):
                validation_func = _common_validation_for_list
            else:
                validation_func = _common_validation

            json_data, role, user_id, message, status_code = validation_func(request, required_params)
            if message:
                return message, status_code

            # 不處理分頁參數
            return func(request, json_data, role, user_id)

        return wrapper

    return decorator

def get_pagination_params(context, start_rnk, end_rnk):

    params = {}

    # 定義預設值參數列表
    default_params_mapping = {
        "pageIndex": "qpage_number",
        "pageSize": "qpage_size",
    }

    # 提供預設值
    default_values = {
        "pageIndex": start_rnk,  # 預設為第一頁
        "pageSize": end_rnk,  # 預設為每頁20筆
    }

    # 檢查是否有缺少的參數
    for frontend_param, query_param in default_params_mapping.items():
        if frontend_param in default_values:
            params[query_param] = default_values[frontend_param]
        else:
            return handle_error(context, f"缺少參數 {frontend_param}", status.HTTP_400_BAD_REQUEST)
    return params