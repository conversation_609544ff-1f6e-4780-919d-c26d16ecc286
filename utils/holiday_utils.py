import cx_Oracle
from django.db import connection
from datetime import datetime, timedelta


def get_holidays(start_date, end_date):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT holiday_date, days_off
            FROM taiwan_holidays
            WHERE holiday_date BETWEEN :start_date AND :end_date
            ORDER BY holiday_date
        """, {'start_date': start_date, 'end_date': end_date})
        return cursor.fetchall()


def is_weekend(date):
    return date.weekday() >= 5  # 5 = Saturday, 6 = Sunday


def calculate_delivery_date(base_days=2):
    today = datetime.now().date()
    end_date = today + timedelta(days=base_days + 7)  # 額外加一週，以防遇到長假期
    holidays = get_holidays(today, end_date)

    delivery_date = today
    days_count = 0
    holiday_dict = {h[0]: h[1] for h in holidays}

    while days_count < base_days:
        delivery_date += timedelta(days=1)
        if delivery_date in holiday_dict:
            delivery_date += timedelta(days=holiday_dict[delivery_date] - 1)
        elif not is_weekend(delivery_date):
            days_count += 1

    # 確保最終日期不是週末或假日
    while is_weekend(delivery_date) or delivery_date in holiday_dict:
        delivery_date += timedelta(days=1)

    return today, delivery_date