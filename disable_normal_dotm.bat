@echo off
chcp 65001 >nul 2>&1
echo === Disabling Normal.dotm ===

echo Stopping any running Word processes...
taskkill /f /im WINWORD.EXE >nul 2>&1

echo Finding Normal.dotm location...
set TEMPLATES_PATH=%USERPROFILE%\AppData\Roaming\Microsoft\Templates
set NORMAL_DOTM=%TEMPLATES_PATH%\Normal.dotm

echo Templates path: %TEMPLATES_PATH%
echo Normal.dotm path: %NORMAL_DOTM%

echo Creating templates directory if not exists...
if not exist "%TEMPLATES_PATH%" mkdir "%TEMPLATES_PATH%"

echo Backing up and removing Normal.dotm...
if exist "%NORMAL_DOTM%" (
    echo Backing up Normal.dotm...
    copy "%NORMAL_DOTM%" "%NORMAL_DOTM%.backup" >nul
    echo Deleting Normal.dotm...
    del "%NORMAL_DOTM%" >nul
    echo Normal.dotm removed
) else (
    echo Normal.dotm does not exist
)

echo Creating empty read-only Normal.dotm...
echo. > "%NORMAL_DOTM%"
attrib +R "%NORMAL_DOTM%"
echo Empty read-only Normal.dotm created

echo Creating Word startup directory...
set STARTUP_PATH=%USERPROFILE%\AppData\Roaming\Microsoft\Word\STARTUP
if not exist "%STARTUP_PATH%" mkdir "%STARTUP_PATH%"

echo Testing Word functionality...
python -c "
import pythoncom, win32com.client
try:
    pythoncom.CoInitialize()
    app = win32com.client.DispatchEx('Word.Application')
    app.Visible = False
    app.DisplayAlerts = 0
    doc = app.Documents.Add()
    doc.Close(SaveChanges=False)
    app.Quit()
    print('Word test successful - Normal.dotm disabled')
except Exception as e:
    print(f'Word test failed: {e}')
finally:
    try: pythoncom.CoUninitialize()
    except: pass
"

echo === Normal.dotm disabling completed ===
echo Please restart your application and test the download function.
echo To restore Normal.dotm, rename Normal.dotm.backup to Normal.dotm
pause
