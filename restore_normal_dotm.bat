@echo off
chcp 65001 >nul 2>&1
echo === Restoring Normal.dotm ===

echo Stopping any running Word processes...
taskkill /f /im WINWORD.EXE >nul 2>&1

set TEMPLATES_PATH=%USERPROFILE%\AppData\Roaming\Microsoft\Templates
set NORMAL_DOTM=%TEMPLATES_PATH%\Normal.dotm
set BACKUP_DOTM=%TEMPLATES_PATH%\Normal.dotm.backup

echo Templates path: %TEMPLATES_PATH%
echo Normal.dotm path: %NORMAL_DOTM%
echo Backup path: %BACKUP_DOTM%

echo Removing read-only empty Normal.dotm...
if exist "%NORMAL_DOTM%" (
    attrib -R "%NORMAL_DOTM%"
    del "%NORMAL_DOTM%"
    echo Removed read-only Normal.dotm
)

echo Restoring from backup...
if exist "%BACKUP_DOTM%" (
    copy "%BACKUP_DOTM%" "%NORMAL_DOTM%"
    echo Restored Normal.dotm from backup
) else (
    echo No backup found, letting Word create new Normal.dotm
)

echo === Normal.dotm restoration completed ===
echo Please restart your application
pause
