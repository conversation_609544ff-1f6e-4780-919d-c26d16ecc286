###
POST http://************:8000/api/users/select_check_user/
Content-Type: application/json
<PERSON>ie: access_token=3ae06951-9912-46e8-81b7-dfde7ee4584b

{

}


###
POST http://************:8000/api/users/select_user/
Content-Type: application/json
<PERSON>ie: access_token=e5ff0db1-685f-43b0-ae95-13225c455d8e

{
  "CustomId": "001"
}


###
POST http://************:8000/api/accounts/login/
Content-Type: application/json
<PERSON>ie: access_token=' '

{
  "username": "002",
  "password": "0206"
}


###
POST http://************:8000/api/products/combobox_erp_hy_ocv015_products_code_name/
Content-Type: application/json
Cookie: access_token=78ab7cb9-3d6c-4c17-8629-1e8ef8d59d84

{
}


###
POST http://************:8000/api/products/combobox_erp_hy_ocv003_combobox_code_name/
Content-Type: application/json
<PERSON>ie: access_token=0c39b49f-f0cb-4257-8e12-93283ab1c4ec

{
  "code_type003": "05"
}


###
POST http://************:8000/api/products/erp_hy_ocv015_main_product_detail/
Content-Type: application/json
Cookie: access_token=46f6335e-3ea4-4cfa-9599-a55d3c91006f

{
  "size": "100",
  "page": "1"
}


###
POST http://************:8000/api/payments/erp_hy_arv201_300_main_payment_detail/
Content-Type: application/json
Cookie: access_token=f2ec5f0f-7db0-454d-8632-b8e093069987

{
    "dealers_code": "30809"
}

###
POST http://************:8000/api/dealers/combobox_erp_hy_ocv025_dealers_code_name/
Content-Type: application/json
Cookie: access_token=e7a0cfed-3754-40af-a53b-b6e2d2f68abd

{
}


###
POST http://************:8000/api/allowances/erp_hy_ocv211_214_main_allowance_detail_schedule_b/
Content-Type: application/json
Cookie: access_token=f68b4120-9a84-4048-a591-65431783f4df

{
  "dealers_code": "30809"
}


###
POST http://************:8000/api/allowances/erp_hy_sdv250_main_proof_of_single_allowance/
Content-Type: application/json
Cookie: access_token=54c340ff-4284-4210-959e-e5df50b467b9

{
  "dealers_code": "30809"
}


###
POST http://************:8000/api/allowances/erp_hy_sdv200_main_sales_allowance_notification/
Content-Type: application/json
Cookie: access_token=f0e04525-e7b4-46e1-95fa-178b613f1636

{
  "dealers_code": "30809",
  "due_start_date": "20230701",
  "due_end_date": "20230710"
}

###
POST http://************:8000/api/orders/hy1_asfa_main_fushou_detail/
Content-Type: application/json
Cookie: access_token=9e70ab89-3a5d-4a6f-b9ef-ed3870352a19

{


}


###
POST http://************:8000/api/orders/erp_hy_ocv029_main_order_master/
Content-Type: application/json
Cookie: access_token=9e70ab89-3a5d-4a6f-b9ef-ed3870352a19

{
    "dealers_code": "30401"
}


###
POST http://************:8000/api/orders/erp_hy_ocv035_can_oreder_products_code_name/
Content-Type: application/json
Cookie: access_token=e58fc007-4bae-49fc-9ebf-c7dc66024755

{
    "delivery_date": "30401",
    "product_features": "A"
}


###
POST http://************:8000/api/orders/hy1_asfa_main_fushou_download/
Content-Type: application/json
Cookie: access_token=0c81db8a-438a-415e-ab53-7257ed995c32

{

}


###
POST http://************:8000/api/invoices/hy1_psbf_main_invoice_detail/
Content-Type: application/json
Cookie: access_token=54cd8446-68a4-409f-9325-3c9a513897e8

{
  "dealers_type" : ["40100", "41100"]
}


###
POST http://************:8000/api/allowances/hy1_asea_main_subsidy_for_station_vending/
Content-Type: application/json
Cookie: access_token=0d039068-538b-4ea5-8aa5-81d1b3fe7591

{
    "dealers_type": "40101",
    "start_date1": "11207",
    "start_date2": "11207",
    "end_date1": "1120799"
}



###
POST http://************:8000/api/dealers/erp_hy_ocv025_main_dealer_info/
Content-Type: application/json
Cookie: access_token=a693ec2b-0c90-47db-bc95-07c2041ac471

{
}


###
POST http://************:8000/api/dealers/erp_hy_ocv029_main_delivery_location/
Content-Type: application/json
Cookie: access_token=fa92afda-2d8b-42d2-a5ad-7fca7c7d30dc

{
}


###
POST http://************:8000/api/dealers/erp_hy_ocv035_main_marketable_products/
Content-Type: application/json
Cookie: access_token=b97156da-dde3-4f04-983e-7c6c9319203c

{
    "products_code": "99999"
}


###
POST http://************:8000/api/products/combobox_erp_hy_ocv003_products_set_code_name/
Content-Type: application/json
Cookie: access_token=384b43d5-4327-454b-abe6-8f73983d648b

{
}


###
POST http://************:8000/api/dealers/insert_truck/
Content-Type: application/json
Cookie: access_token=2dc7631d-3c31-4275-87d9-f8eb43e64859

{
    "car_code": "C19",
    "car_name": "19棧板",
    "car_qty_board": "19",
    "hub_type": null,
    "owner_id": "16613"
}


###
POST http://************:8000/api/dealers/select_path/
Content-Type: application/json
Cookie: access_token=d4120eaf-87ea-4675-a4ef-5c852ee2f986

{
    "dealers_code": "32000"
}


###
POST http://************:8000/api/dealers/select_path2/
Content-Type: application/json
Cookie: access_token=384b43d5-4327-454b-abe6-8f73983d648b

{

}


###
POST http://************:8000/api/users/select_department/
Content-Type: application/json
Cookie: access_token=614fd92b-34b2-4d9c-b0d3-06ab215c98e5

{

}


###
POST http://************:8000/api/products/select_prodsalesunit/
Content-Type: application/json
Cookie: access_token=ee379dc0-fa38-4bea-93ce-e5c846fcc860

{
    "product_code": "1214"
}


###
POST http://************:8000/api/users/select_user_tag_program_and_permission/
Content-Type: application/json
Cookie: access_token=a9cd6c37-3f4c-48c1-98ef-49c59e393a96

{
    "program_id": "A0100000"

}


###
POST http://************:8000/api/users/select_user_data/
Content-Type: application/json
Cookie: access_token=58d7ba71-e5b8-4e93-b285-e28829dd391c

{

}

###
POST http://************:8000/api/users/select_user_data2/
Content-Type: application/json
Cookie: access_token=8b9059c2-1b8d-4949-add4-4cd999f8187f

{
    "user_code": "h16613"
}


###
POST http://************:8000/api/dealers/select_dealer_group/
Content-Type: application/json
Cookie: access_token=75956856-6ec3-41a3-a99d-03361282f155

{

}


###
POST http://************:8000/api/documents/select_bullet_board/
Content-Type: application/json
Cookie: access_token=a7d24b05-ef49-4aa9-a530-c71be7c60b42

{

}


###
POST http://************:8000/api/documents/select_business_notification/
Content-Type: application/json
Cookie: access_token=7ded9343-b53c-4be2-9ee5-d2a55f94614f

{

}

###
POST http://************:8000/api/documents/select_business_notification2/
Content-Type: application/json
Cookie: access_token=7ded9343-b53c-4be2-9ee5-d2a55f94614f

{

}

###
POST http://************:8000/api/documents/select_business_notification_upload/
Content-Type: multipart/form-data
Cookie: access_token=a7d24b05-ef49-4aa9-a530-c71be7c60b42

{

}


###
POST http://************:8000/api/documents/select_business_notification_download/
Content-Type: application/json
Cookie: access_token=baaccbdc-7e7a-48ef-bd51-3fc4cdfc5753

{
    "file_type" : "link",
    "file_name" : "預告自112年12月1日(三)起黑松CAN245碳酸系列產品價格調整通知，敬請配合辦理231012h16467B.doc"
}


###
POST http://************:8000/api/documents/business_notification_serial/
Content-Type: application/json
Cookie: access_token=5251f6b2-9fdc-4507-8d31-84146ff06547

{
}


###
POST http://************:8000/api/documents/business_notification_email/
Content-Type: application/json
Cookie: access_token=aedcc024-b614-4650-a46a-5188db8d817e

{
}

###
POST https://b2bapi.heysong.dev/api/documents/select_bullet_board/
Content-Type: application/json
Cookie: access_token=288b240c-7540-4ad1-bc1f-de79e5b5f56d

{

}


###
POST http://************:8000/api/pallet/select_pallet_account/
Content-Type: application/json
Cookie: access_token=c28ca3c8-595d-4f1a-8e06-7115ceebf86f

{
      "begin_date": "********",
      "end_date": "********"

}


###
POST http://************:8000/api/orders/select_shipment_order/
Content-Type: application/json
Cookie: access_token=c001326f-9057-45ea-b3a0-3c349234484c

{
      "begin_date": "********",
      "end_date": "********",
      "dealers_code": "00408"

}

###
POST http://************:8000/api/orders/select_pre_shipment_order/
Content-Type: application/json
Cookie: access_token=6671e967-f96b-4025-920c-cfaec7172aba

{
      "begin_date": "********",
      "end_date": "********"
}


###
POST http://************:8000/api/orders/select_order_product_sales_unit/
Content-Type: application/json
Cookie: access_token=9e70ab89-3a5d-4a6f-b9ef-ed3870352a19

{
      "product_place": "A"
}


###
POST http://************:8000/api/orders/select_order_serial/
Content-Type: application/json
Cookie: access_token=a9ff11c1-7795-439c-9cf3-a73514730589

{
}


###
POST http://************:8000/api/orders/update_order_input/
Content-Type: application/json
Cookie: access_token=ce62fcba-f606-4941-a740-b9577860a5c3

{
    "order_no": "C1121204001",
    "dealer_code": "74270",
    "order_date": "20231204",
    "delivery_date": "20231206",
    "product_features": "A",
    "total_order_quantity": 16,
    "total_actual_quantity": 2904,
    "ready_only": "0",
    "details": [
        {
            "index": 1,
            "item_number": "1214",
            "sales_category": " ",
            "order_quantity": 1,
            "actual_quantity": 144,
            "merge_table": "",
            "note": "",
            "sales_unit": "01",
            "sales_unit_conversion": 144,
            "sales_unit_displayName": "棧板"
        },
        {
            "index": 2,
            "item_number": "2027",
            "sales_category": " ",
            "order_quantity": 5,
            "actual_quantity": 1320,
            "merge_table": "",
            "note": "",
            "sales_unit": "01",
            "sales_unit_conversion": 264,
            "sales_unit_displayName": "棧板"
        },
        {
            "index": 3,
            "item_number": "1334",
            "sales_category": " ",
            "order_quantity": 10,
            "actual_quantity": 1440,
            "merge_table": "",
            "note": "",
            "sales_unit": "01",
            "sales_unit_conversion": 144,
            "sales_unit_displayName": "棧板"
        }
    ]
}


###
POST http://************:8000/api/orders/select_order_input/
Content-Type: application/json
Cookie: access_token=507b4a0f-4168-4053-9523-c0366420fa2d

{

}


###
POST http://************:8000/api/invoices/update_hy1_psbf_main_invoice_detail/
Content-Type: application/json
Cookie: access_token=05cc4d50-3dcc-4824-9b04-9c0b59856085

[
  {
    "dealers_type":[
      "40200"
    ],
    "dealers_code":"00204",
    "yyyymm":"11210",
    "issued_function_type":"1",
    "invoice_type":"C"
  },
  {
    "dealers_type":[
      "40200"
    ],
    "dealers_code":"00408",
    "yyyymm":"11210",
    "issued_function_type":"1",
    "invoice_type":"C"
  }
]


###
POST http://************:8000/api/users/select_user_log/
Content-Type: application/json
Cookie: access_token=78ab7cb9-3d6c-4c17-8629-1e8ef8d59d84

{
    "parent_id": "G0200000",
    "child_id": "G002_1",
    "serial_no": "11200399"

}


###
POST http://************:8000/api/allowances/erp_hy_sdv200_main_allowance_detail_schedule_a/
Content-Type: application/json
Cookie: access_token=baf301cc-8c10-40cb-9851-87c91e8470c7

{
    "due_start_date": "2024/02/01",
    "due_end_date": "2024/02/29",
    "dealers_code": "30809"
}

###
POST http://************:8000/api/allowances/erp_hy_sdw503_main_sales_allowance_balance/
Content-Type: application/json
Cookie: access_token=baf301cc-8c10-40cb-9851-87c91e8470c7

{
    "due_start_date": "2024/02/01",
    "due_end_date": "2024/02/29",
    "dealers_code": "30809"
}

###
POST http://************:8000/api/documents/select_business_notification_file_statistics/
Content-Type: application/json
Cookie: access_token=5c10b366-27e1-4978-bd0e-623f552b9008

{
    "pudcno": "11201018"
}

###
POST https://b2bapi.heysong.dev:8999/api/documents/select_business_notification_file_statistics/
Content-Type: application/json
Cookie: access_token=288b240c-7540-4ad1-bc1f-de79e5b5f56d
X-Refresh-Token: 288b240c-7540-4ad1-bc1f-de79e5b5f56d

{
    "pudcno": "11201018"
}

### 查詢是否有綁定Line Notify
POST http://************:8000/api/users/select_user_line_notify/
Content-Type: application/json
Cookie: access_token=17ff504e-6f8f-4fd0-b1c2-ba1d9d47482c

{

}


### 綁定Line Notify
POST http://************:8000/api/users/update_user_line_notify/
Content-Type: application/json

{
}

### 解除綁定Line Notify
POST https://notify-api.line.me/api/revoke
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer zxuNC7J8wGh9TIPOrHe37DwAS9fT7JwmbBrIXOu8nVy

### 發送Line Notify訊息
POST https://notify-api.line.me/api/notify
Authorization: Bearer 7ghNH1uNoGVbRDfaw0znCe3YlvMcCAqkXY2LjhFz8l1
Content-Type: application/x-www-form-urlencoded

message=Hello World


### 查詢Line Notify狀態
GET https://notify-api.line.me/api/status
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer zxuNC7J8wGh9TIPOrHe37DwAS9fT7JwmbBrIXOu8nVy


###
POST https://www-vc.einvoice.nat.gov.tw/BIZAPIVAN/biz
Content-Type: application/x-www-form-urlencoded

version=1.0&action=bcv&barCode=/14H7NV2&TxID=0001&appId=EINV9202404181964
