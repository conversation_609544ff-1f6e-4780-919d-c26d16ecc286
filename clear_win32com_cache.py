#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
�M�z win32com �w�s�}��
�ѨM COM �w�s�l�a���D
"""

import os
import sys
import shutil
import logging
import tempfile

# �]�m��x
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def clear_win32com_cache():
    """�M�z win32com �w�s"""
    
    logger.info("�}�l�M�z win32com �w�s...")
    
    try:
        # ��k1�G�ϥ� win32com ���ت��M�z��k
        try:
            import win32com.client
            win32com.client.gencache.is_readonly = False
            win32com.client.gencache.Rebuild()
            logger.info("? �ϥΤ��ؤ�k���ؽw�s���\")
        except Exception as e:
            logger.warning(f"���ؤ�k����: {str(e)}")
        
        # ��k2�G��ʧR���w�s�ؿ�
        try:
            # ����w�s�ؿ����|
            temp_dir = tempfile.gettempdir()
            gen_py_dir = os.path.join(temp_dir, "gen_py")
            
            if os.path.exists(gen_py_dir):
                logger.info(f"���w�s�ؿ�: {gen_py_dir}")
                
                # �R����ӥؿ�
                shutil.rmtree(gen_py_dir)
                logger.info("? ��ʧR���w�s�ؿ����\")
            else:
                logger.info("�w�s�ؿ����s�b�A�L�ݲM�z")
                
        except Exception as e:
            logger.warning(f"��ʧR������: {str(e)}")
        
        # ��k3�G�M�z�Τ�S�w���w�s
        try:
            import win32api
            import win32con
            
            # ����Τ��{�ɥؿ�
            user_temp = win32api.GetTempPath()
            user_gen_py = os.path.join(user_temp, "gen_py")
            
            if os.path.exists(user_gen_py):
                logger.info(f"���Τ�w�s�ؿ�: {user_gen_py}")
                shutil.rmtree(user_gen_py)
                logger.info("? �M�z�Τ�w�s���\")
                
        except Exception as e:
            logger.warning(f"�M�z�Τ�w�s����: {str(e)}")
        
        # ��k4�G�M�z Python �]�����w�s
        try:
            import win32com
            package_path = os.path.dirname(win32com.__file__)
            gen_py_package = os.path.join(package_path, "gen_py")
            
            if os.path.exists(gen_py_package):
                # �u�M�z�ʺA�ͦ������A�O�d __init__.py
                for item in os.listdir(gen_py_package):
                    if item != "__init__.py" and item != "__pycache__":
                        item_path = os.path.join(gen_py_package, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                
                logger.info("? �M�z�]�w�s���\")
                
        except Exception as e:
            logger.warning(f"�M�z�]�w�s����: {str(e)}")
        
        logger.info("win32com �w�s�M�z����")
        return True
        
    except Exception as e:
        logger.error(f"�M�z�w�s�ɵo�Ϳ��~: {str(e)}")
        return False

def test_word_creation_after_clear():
    """�M�z����� Word �Ы�"""
    
    logger.info("���� Word �Ы�...")
    
    try:
        import pythoncom
        import win32com.client
        
        # ��l�� COM
        pythoncom.CoInitialize()
        
        # �Ы� Word ���ε{�� - �ϥ� Dispatch �Ӥ��O DispatchEx
        logger.info("�Ы� Word ���ε{��...")
        word_app = win32com.client.Dispatch("Word.Application")
        
        # �򥻳]�m
        word_app.Visible = False
        word_app.DisplayAlerts = 0  # ����]�m
        
        # ���հ򥻥\��
        logger.info("���ճЫؤ���...")
        doc = word_app.Documents.Add()
        doc.Close(SaveChanges=False)
        
        # ���� Word
        word_app.Quit()
        
        logger.info("? Word ���զ��\")
        return True
        
    except Exception as e:
        error_msg = str(e)
        if "Normal.dotm" in error_msg:
            logger.error(f"? ���M�X�{ Normal.dotm ���~: {error_msg}")
        else:
            logger.error(f"? Word ���ե���: {error_msg}")
        return False
    
    finally:
        try:
            pythoncom.CoUninitialize()
        except:
            pass

def main():
    """�D���"""
    
    logger.info("=== win32com �w�s�M�z�u�� ===")
    
    # �M�z�w�s
    clear_success = clear_win32com_cache()
    
    if clear_success:
        logger.info("\n=== ���� Word �\�� ===")
        test_success = test_word_creation_after_clear()
        
        if test_success:
            logger.info("\n? �w�s�M�z�M���ճ����\�I")
            logger.info("��ĳ�G")
            logger.info("1. ���s�Ұʱz�����ε{��")
            logger.info("2. ���դU���\��")
            return True
        else:
            logger.error("\n?? �w�s�M�z���\�A�� Word ���ե���")
            logger.error("�i��ݭn�G")
            logger.error("1. ���s�w�� pywin32")
            logger.error("2. �ˬd Office �w��")
            return False
    else:
        logger.error("\n? �w�s�M�z����")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("�ާ@�Q�Τᤤ�_")
        sys.exit(1)
    except Exception as e:
        logger.error(f"�o�ͥ��w�������~: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
