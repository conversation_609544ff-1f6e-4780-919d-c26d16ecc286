@echo off
chcp 65001 >nul 2>&1
echo === Testing Word Creation Methods ===

echo Testing Dispatch method...
python -c "
import pythoncom, win32com.client
try:
    pythoncom.CoInitialize()
    app = win32com.client.Dispatch('Word.Application')
    app.Visible = False
    app.DisplayAlerts = 0
    doc = app.Documents.Add()
    doc.Close(SaveChanges=False)
    app.Quit()
    print('SUCCESS: Dispatch method works')
except Exception as e:
    if 'Normal.dotm' in str(e):
        print('FAILED: Dispatch method - Normal.dotm error')
    else:
        print(f'FAILED: Dispatch method - {e}')
finally:
    try: pythoncom.CoUninitialize()
    except: pass
"

echo.
echo Testing DispatchEx method...
python -c "
import pythoncom, win32com.client
try:
    pythoncom.CoInitialize()
    app = win32com.client.DispatchEx('Word.Application')
    app.Visible = False
    app.DisplayAlerts = 0
    doc = app.Documents.Add()
    doc.Close(SaveChanges=False)
    app.Quit()
    print('SUCCESS: DispatchEx method works')
except Exception as e:
    if 'Normal.dotm' in str(e):
        print('FAILED: DispatchEx method - Normal.dotm error')
    else:
        print(f'FAILED: DispatchEx method - {e}')
finally:
    try: pythoncom.CoUninitialize()
    except: pass
"

echo.
echo Testing gencache method...
python -c "
import pythoncom, win32com.client.gencache
try:
    pythoncom.CoInitialize()
    app = win32com.client.gencache.EnsureDispatch('Word.Application')
    app.Visible = False
    app.DisplayAlerts = 0
    doc = app.Documents.Add()
    doc.Close(SaveChanges=False)
    app.Quit()
    print('SUCCESS: gencache method works')
except Exception as e:
    if 'Normal.dotm' in str(e):
        print('FAILED: gencache method - Normal.dotm error')
    else:
        print(f'FAILED: gencache method - {e}')
finally:
    try: pythoncom.CoUninitialize()
    except: pass
"

echo.
echo === Test completed ===
pause
